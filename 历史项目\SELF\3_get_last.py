import requests
import csv
from bs4 import BeautifulSoup
import time
import threading
import random

def read_urls_from_csv(file_path):
    urls = []
    with open(file_path, 'r', newline='', encoding='utf-8') as file:
        reader = csv.reader(file)
        next(reader)
        for row in reader:
            category = row[0].strip()
            account_url = row[2].strip()
            token_url = row[3].strip()
            if account_url:
                urls.append((category, account_url))
            if token_url:
                urls.append((category, token_url))
    return urls

def fetch_webpage(url):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Referer": "https://optimistic.etherscan.io/"
    }
    cookie = {}
    with open('1_login_cookies.csv', 'r', newline='', encoding='utf-8') as cookiefile:
        reader = csv.reader(cookiefile)
        for row in reader:
            if row:
                cookie[row[0]] = row[1]

    try:
        response = requests.get(url, headers=headers, cookies=cookie)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None

def parse_webpage(html):
    soup = BeautifulSoup(html, 'html.parser')
    data_rows = soup.find_all('tr', class_='odd')
    results = []
    for row in data_rows:
        cells = row.find_all('td')
        if len(cells) >= 4:
            address_span = cells[0].find('span', {'data-highlight-target': True})
            address = address_span['data-highlight-target'] if address_span else "地址信息缺失"
            name_tag = cells[1].text.strip()
            balance = cells[2].text.strip()
            txn_count = cells[3].text.strip()
            results.append([address, name_tag, balance, txn_count])
    return results

def thread_task(urls, all_results):
    for category, url in urls:
        print(f"正在处理: {category} - {url}")
        html = fetch_webpage(url)
        if html:
            results = parse_webpage(html)
            all_results.extend(results)
        else:
            print(f"Failed to fetch or parse webpage for URL: {url}")
        time.sleep(random.uniform(1,3))

def process_urls(urls, all_results):
    split_index = len(urls) // 2
    thread1 = threading.Thread(target=thread_task, args=(urls[:split_index], all_results))
    thread2 = threading.Thread(target=thread_task, args=(urls[split_index:], all_results))
    thread1.start()
    thread2.start()
    thread1.join()
    thread2.join()

def save_results_to_csv(results, output_file):
    with open(output_file, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['Address', 'Name Tag', 'Balance', 'Txn Count'])
        writer.writerows(results)
    print("数据已保存到新的CSV文件。")

if __name__ == "__main__":
    all_results = []
    urls = read_urls_from_csv("2_url_data.csv")
    process_urls(urls, all_results)
    save_results_to_csv(all_results, 'last_scraped_data.csv')
    print("Processing complete.")