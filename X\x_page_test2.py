# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2025/2/11 13:45
@Python  :  Python3.7
@Desc    :  根据关键词搜索推文
"""


"""测试记录:1-3秒内的随机等待小数 大约拿到500条数据后会被检测到太多请求"""
import requests
import pandas as pd
import json
import os
import time
import random
from loguru import logger
from datetime import datetime


class XSpider:
    def __init__(self, max_pages=None):
        """经测试后所必须携带的参数"""
        self.max_pages = max_pages
        self.headers = {
            "authority": "x.com",
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "referer": "",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
            "x-client-transaction-id": "FEinLxQM2NKp5GAzgmdTK+jIXTBwtuGBKYuyM48ZIH/CuCU/LiBQl4tA8oqOfUJ5eOf1SBdW3vIh1VYICrGtwXw63hoHFw",
            "x-client-uuid": "8410d1a5-7157-47a9-b7b8-baf0d5e2a6b6",
            "x-csrf-token": "943e14a2beac215943524662566e356597f92d17b75954133956a44cd6d295c075fc47ea76eb1f139ed9939e6ba213be985d14995e9e389078ffc700a069e464e15dfe1f24ed4f859af56027eebbe811",
            "x-twitter-active-user": "yes"
        }
        self.cookies = {
            "guest_id": "v1%3A173934162973663415",
            "auth_token": "f9f6cf95d26fa758ff727399674bb2029f17687f",
            "ct0": "943e14a2beac215943524662566e356597f92d17b75954133956a44cd6d295c075fc47ea76eb1f139ed9939e6ba213be985d14995e9e389078ffc700a069e464e15dfe1f24ed4f859af56027eebbe811",
        }

        self.base_params = {
            "variables": "{\"rawQuery\":\"\",\"count\":20,\"querySource\":\"typed_query\",\"product\":\"Top\"}",
            "features": "{\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"responsive_web_graphql_exclude_directive_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"rweb_video_timestamps_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":true,\"responsive_web_enhance_cards_enabled\":false}"
        }

    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间到标准格式"""
        try:
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}")
            return None

    def fetch_data(self, keyword, cursor=None):
        """发送请求获取数据"""
        url = "https://x.com/i/api/graphql/QGMTWxm841rbDndB-yQhIw/SearchTimeline"
        variables = {
            "rawQuery": keyword,
            "count": 20,
            "querySource": "typed_query",
            "product": "Top"
        }
        if cursor:
            variables["cursor"] = cursor

        params = self.base_params.copy()
        params["variables"] = json.dumps(variables)
        self.headers["referer"] = f"https://x.com/search?q={keyword}&src=typed_query"

        try:
            response = requests.get(
                url,
                headers=self.headers,
                cookies=self.cookies,
                params=params
            )
            response.raise_for_status()
            logger.success(f"请求成功 [{response.status_code}]")
            # logger.info(json.dumps(response.json(), indent=4, ensure_ascii=False))

            return response.json()
        except Exception as e:
            logger.error(f"请求失败: {e}")
            return None

    def extract_entries(self, data):
        """响应数据中提取目标字段"""
        extracted = []
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get(
                'timeline', {}).get('instructions', [])

            for instruction in instructions:
                # entries = []

                if instruction.get('type') == 'TimelineAddEntries':
                    for entry in instruction.get('entries', []):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineItem':
                            item_content = content.get('itemContent', {})
                            tweet_result = item_content.get('tweet_results', {}).get('result', {})
                            user_result = tweet_result.get('core', {}).get('user_results', {}).get('result', {})
                            legacy_user = user_result.get('legacy', {})
                            legacy_tweet = tweet_result.get('legacy', {})

                            # 测试：editable_until_msecs为毫秒时间戳
                            edit_control = tweet_result.get('edit_control', {})
                            editable_until_msecs = edit_control.get('editable_until_msecs')
                            if editable_until_msecs:
                                try:
                                    # 时间戳转换
                                    editable_time = datetime.fromtimestamp(int(editable_until_msecs) / 1000).strftime(
                                        '%Y-%m-%d %H:%M:%S')
                                except Exception as e:
                                    logger.warning(f"时间转换失败: {e}")
                                    editable_time = None
                            else:
                                editable_time = None

                            # 媒体数据
                            media = legacy_tweet.get('entities', {}).get('media', [{}])
                            media_info = media[0] if media else {}

                            extracted.append({
                                'name': legacy_user.get('name'),
                                'rest_id': user_result.get('rest_id'),
                                'is_blue_verified': user_result.get('is_blue_verified', False),
                                'verified_type': user_result.get('verified_type'),
                                'normal_followers_count': legacy_user.get('normal_followers_count', 0),
                                'editable_until': editable_time,
                                'description': legacy_user.get('description'),
                                'default_profile_image': legacy_user.get('default_profile_image', False),
                                'followers_count': legacy_user.get('followers_count', 0),
                                'media_url_https': media_info.get('media_url_https')
                            })
            return extracted
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return []

    def get_next_cursor(self, data):
        """游标提取 必须提取到每一次请求后返回json数据中的cursor 作为下一次请求(翻页操作)中的载荷参数"""
        try:
            # 深度遍历所有可能的路径
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get(
                'timeline', {}).get('instructions', [])

            # 调试日志：打印指令结构
            logger.debug(f"指令数量: {len(instructions)}")

            for instruction in instructions:
                # 处理 TimelineAddEntries 和 TimelineReplaceEntry 两种类型
                entries = []
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                elif instruction.get('type') == 'TimelineReplaceEntry':
                    entry = instruction.get('entry')
                    if entry:
                        entries = [entry]

                # 调试日志：打印当前处理的条目
                logger.debug(f"处理 {instruction.get('type')} 指令，发现 {len(entries)} 个条目")

                for entry in entries:
                    entry_id = entry.get('entryId')
                    # 同时检查 cursor-bottom-0 和替换条目
                    if entry_id == 'cursor-bottom-0' or (isinstance(entry_id, str) and 'cursor-bottom' in entry_id):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineCursor':
                            cursor_value = content.get('value')
                            if cursor_value:
                                logger.success("成功提取到cursor")
                                return cursor_value
            logger.warning("未找到有效游标")
            return None
        except Exception as e:
            logger.error(f"游标解析异常: {str(e)}")
            return None

    def save_to_csv(self, data, filename):
        if not data:
            return

        try:
            df = pd.DataFrame(data)
            df.replace({pd.NA: None}, inplace=True)

            # 处理文件存在性
            file_exists = os.path.exists(filename)
            df.to_csv(
                filename,
                mode='a' if file_exists else 'w',
                header=not file_exists,
                index=False,
                encoding='utf-8-sig'
            )
            logger.success(f"成功保存 {len(data)} 条数据到 {filename}")
        except Exception as e:
            logger.error(f"保存失败: {e}")

    def run(self):

        with open('X/x_keyword/keywords.txt', 'r', encoding='utf-8') as f:
            keywords = [k.strip() for k in f if k.strip()]

        for keyword in keywords:
            logger.info(f"开始处理关键词: {keyword}")
            next_cursor = None
            page = 1
            max_retry = 3
            previous_cursor = None  # 新增变量记录前次游标

            while True:
                # 添加页数限制检查
                if self.max_pages and page > self.max_pages:
                    logger.info(f"已达到设置的最大页数 {self.max_pages}，停止翻页")
                    break

                logger.info(f"正在爬取第 {page} 页...")
                data = None

                for attempt in range(max_retry):
                    data = self.fetch_data(keyword, next_cursor)
                    if data:
                        break
                    logger.warning(f"第 {attempt + 1} 次重试...")
                    time.sleep(5)

                if not data:
                    logger.error("请求失败，停止翻页")
                    break

                extracted = self.extract_entries(data)
                if extracted:
                    self.save_to_csv(extracted, f'X/x_output/{keyword}.csv')
                else:
                    logger.warning("未提取到有效数据")

                # cursor翻页处理
                new_cursor = self.get_next_cursor(data)
                # if not new_cursor:  # 仅当没有新游标时停止
                #     logger.info("已到达最后一页")
                #     break

                # # 循环终止条件
                # if not new_cursor or (self.max_pages and page >= self.max_pages):
                #     logger.info("已到达最后一页或达到设置的最大页数")
                #     break

                new_cursor = self.get_next_cursor(data)
                
                # 修改后的循环终止条件
                stop_reason = None
                if new_cursor == previous_cursor:  # 新增游标相同判断
                    stop_reason = "游标与上一页相同"
                elif not new_cursor:
                    stop_reason = "没有新游标"
                elif self.max_pages and page >= self.max_pages:
                    stop_reason = f"达到设置的最大页数 {self.max_pages}"

                if stop_reason:
                    logger.info(f"停止翻页: {stop_reason}")
                    break

                # 更新游标记录
                previous_cursor = new_cursor  # 保存当前游标用于下次比较
                next_cursor = new_cursor
                page += 1

                # # 更新游标并继续
                # next_cursor = new_cursor
                # page += 1

                # 随机等待
                wait_time = random.uniform(3, 6)
                logger.info(f"等待 {wait_time:.1f} 秒后继续")
                time.sleep(wait_time)

            logger.success(f"完成关键词 {keyword} 的采集")


if __name__ == "__main__":
    spider = XSpider(max_pages=100)
    spider.run()
