import time
import random
import json
import requests
from loguru import logger
from lxml import etree
from project_002.Spiders.utils import DB_BASE
import csv
import os
import sys


class BatchRequestSpider(DB_BASE):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
        }
        self.seed_key = 'op_token_urls'
        self.batch_size = 10
        self.request_interval = 3

    def fetch_and_process_urls(self):
        while self.get_scard(self.seed_key) > 0:
            urls = []
            for _ in range(self.batch_size):
                seed = self.get_seed(self.seed_key)
                if seed:
                    full_url = seed['url']
                    if '-------' in full_url:
                        _, url = full_url.split('-------')
                        url = url.strip()
                    else:
                        url = full_url.strip()

                    if not url.startswith('http'):
                        url = f'https://optimistic.etherscan.io{url}'
                    urls.append(url)
                else:
                    break

            for url in urls:
                logger.info(f"Requesting URL: {url}")
                response = self.req_page(url)
                if response:
                    self.process_response(response)
                # time.sleep(self.request_interval + random.uniform(0, 2))
            logger.info(f"等待 {self.request_interval} 秒")
            time.sleep(self.request_interval + random.uniform(0, 2))

    def req_page(self, url):
        try:
            response = requests.get(url, headers=self.headers, timeout=20)
            # logger.info(f'验证url:{url}')
            if response.status_code == 200:
                return response
            else:
                logger.error(f"HTTP error {response.status_code} for URL: {url}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for URL {url}: {e}")
        return None
    
    def save_to_phoenix(item: dict):
        with phoenixdb.connect("http://**************:8765", autocommit=False) as conn:
            with conn.cursor() as cursor:
                keys = ','.join(list(item.keys()))
                values = ','.join(['?'] * len(item))
                data = list(item.values())
                sql = f"UPSERT INTO COMMON.CONTRACT_INFO({keys}) VALUES({values})"
                cursor.execute(sql, data)
            conn.commit()
            logger.info(f'save success, CHAIN_NAME:{item["CHAIN_NAME"]}, ADDRESS:{item["ADDRESS"]}')

    def process_response(self, response):
        html = etree.HTML(response.text)
        contract_abi = html.xpath('//pre[@id="js-copytextarea2"]/text()')
        contract_name = html.xpath('//div[2]/div[1]/div[1]/div[2]/span/text()')
        if contract_abi:
            # logger.info(f"已经拿到合约ABI: {contract_abi[0]}")
            logger.info("已经拿到合约ABI")
            seed = self.get_seed(self.seed_key)
            if seed:
                name_abi = seed['url'].split(' ------- ')[0]
                name_contract = contract_name
                chain_name = "Optimistic"
                # 数据库写入
                item = {
                    'TOKEN_NAME': name_abi,
                    'CHAIN_NAME': chain_name,
                    'CONTRACT_NAME': name_contract,
                    'CONTRACT_ABI': contract_abi[0]
                }
                self.save_to_phoenix(item)
                logger.info("数据已成功插入数据库，程序即将退出")
                sys.exit()
        else:
            logger.warning("未能找到合约ABI")

    def run(self):
        logger.info("开始批量请求种子URL")
        self.fetch_and_process_urls()
        logger.info("所有请求已完成")

if __name__ == '__main__':
    spider = BatchRequestSpider()
    spider.run()