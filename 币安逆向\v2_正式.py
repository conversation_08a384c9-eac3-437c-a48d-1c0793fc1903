from curl_cffi import requests
import json

proxies = {
            "http": "http://127.0.0.1:7897",
            "https": "http://127.0.0.1:7897"
        }

timeout = 10

headers = {
    "referer": "https://www.binance.com/",
}
url = "https://fe4385362baa.b3f58947.us-west-1.token.awswaf.com/fe4385362baa/306922cde096/8b22eb923d34/verify"
data = {
    "challenge": {
        "input": "eyJ2ZXJzaW9uIjoxLCJ1YmlkIjoiYmM0ZjE1N2MtY2QyZi00YjRjLWE0ZmEtMTkxOTI5OTAwNGQzIiwiYXR0ZW1wdF9pZCI6ImU5ZTg2NDIxLWNlZTAtNDA5ZC1iZDEyLWIxNDExNWEzNDI2ZSIsImNyZWF0ZV90aW1lIjoiMjAyNS0wNy0xOFQwNzoxNTowMS4xOTY0NjQ1NDhaIiwiZGlmZmljdWx0eSI6NCwiY2hhbGxlbmdlX3R5cGUiOiJIYXNoY2FzaFNjcnlwdCJ9",
        "hmac": "s8tQg3MprNetZP9NXCQoOGgWPVKWmP2aKiersovo3/Y=",
        "region": "us-west-1"
    },
    "solution": "2",
    "signals": [
        {
            "name": "KramerAndRio",
            "value": {
                "Present": "+TjkZGWcrzI2tbA3::acb08a3ef576920babf1996210f4208a::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"
            }
        }
    ],
    "checksum": "24BE9FCE",
    "existing_token": None,
    "client": "Browser",
    "domain": "www.binance.com",
    "metrics": [
        {
            "name": "2",
            "value": 1.100000023841858,
            "unit": "2"
        },
        {
            "name": "100",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "101",
            "value": 1,
            "unit": "2"
        },
        {
            "name": "102",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "103",
            "value": 8,
            "unit": "2"
        },
        {
            "name": "104",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "105",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "106",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "107",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "108",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "undefined",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "110",
            "value": 1,
            "unit": "2"
        },
        {
            "name": "111",
            "value": 9,
            "unit": "2"
        },
        {
            "name": "112",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "undefined",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "3",
            "value": 6.199999928474426,
            "unit": "2"
        },
        {
            "name": "7",
            "value": 0,
            "unit": "4"
        },
        {
            "name": "1",
            "value": 27.100000023841858,
            "unit": "2"
        },
        {
            "name": "4",
            "value": 15.799999952316284,
            "unit": "2"
        },
        {
            "name": "5",
            "value": 0,
            "unit": "2"
        },
        {
            "name": "6",
            "value": 42.89999997615814,
            "unit": "2"
        },
        {
            "name": "0",
            "value": 60131.699999928474,
            "unit": "2"
        },
        {
            "name": "8",
            "value": 1,
            "unit": "4"
        }
    ],
    "goku_props": {
        "key": "AQIDAHjcYu/GjX+QlghicBgQ/7bFaQZ+m5FKCMDnO+vTbNg96AHtoT4eeAiCuEb3iCpFNJXaAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMXdnuWGzLuGJvXPgKAgEQgDvI0K2ncXPOX52oYdKuq9VNkHFfnzDZSmQtqjE74xv8P4yGXnrVknPh4OkaaImoTfc3CrgWgCt2mieu7Q==",
        "iv": "EkQdSgAvQwAAK/Hg",
        "context": "YTQxqCu5+Qlj2uKhsXpwGjVk4kL1f9n0QH964jIN0JwIJPYUaE/UnxKsMTI8PEk+coB43GijeQME6+0sJIFpqraqbwcj/zFvDQeDf0CWpe4jAlfj8jbAc38Eo9HYVaigpOGU6OF3HX14XzVL5ErPokbSyaPHbhOXCyhK0/zMhqTDSIfrBDP4+chiNcx5EIAALm5gJ4FFabgaE3vF4LXvFuUjYUklvNFt9E7UF2KtWStNvAR62q7LBtteefskEtJiIOAw9bw3bK0tjwGwJ9ZQew1+09P9TcOTAlAm/QPSIUTlZH/kfZLQK5gsVgFxK8EgJLoFtsMb4HuJWgNH/JtiW6a/2wDMsrvQZA8EdwUXcs5sikz7GR82D/BcGQCjZ2aSUfJ+8FcCjXO+ENp9Uan1AnigCCU="
    }
}
data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, data=data, proxies=proxies, timeout=timeout, impersonate="chrome116")

print(response.text)
print(response)