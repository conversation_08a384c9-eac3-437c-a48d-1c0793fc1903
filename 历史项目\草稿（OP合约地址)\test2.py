import requests
from lxml import etree
from loguru import logger
from datetime import date
import time
import random
import sqlite3
from project_002.Spiders.utils import DB_BASE

class TokenSchduler(DB_BASE):
    def __init__(self):
        self.base_url = ''
        self.token_url = f'{self.base_url}/tokens'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
        }
        self.seed_key = 'op_token_urls'
        self.crawl_date = date.today()


    def freach_tokens(self, page_num=1):
        params = {
            "ps":"100",
            "p":str(page_num)
        }
        logger.info(params)
        response = requests.get(url=self.token_url, headers=self.headers, params=params)
        if response.status_code == 200:
            return response.text
        else:
            raise Exception(f'获取地址失败:{self.token_url}, status_code:{response.status_code}')


    def prase_tokens(self, html_content):
        tree =etree.HTML(html_content)
        token_links = tree.xpath('//*[@id="ContentPlaceHolder1_tblErc20Tokens"]/table/tbody/tr/td[2]/a/@href')
        token_names = tree.xpath('//table/tbody/tr/td/a/div/div/text()')
        

    
    def clear_seed_list(self):
        pass


    def run(self):
        pass

if __name__ == '__main__':
    spider = TokenSchduler()
    spider.run()