import logging

import requests
import sqlite3
import json
import datetime
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor

from celery.schedules import crontab

# 数据库路径
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "the_new_code_data"  # 修改为正确的表名

# 请求配置
MAX_RETRIES = 5
RETRY_INTERVAL = 2  # 秒
REQUEST_INTERVAL = 1  # 请求间隔秒数

# 线程配置
THREAD_COUNT = 2  # 修改为单线程

# 线程锁，用于打印日志和数据库访问
PRINT_LOCK = threading.Lock()
DB_LOCK = threading.Lock()


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with PRINT_LOCK:
        print(*args, **kwargs)


def get_token_addresses_from_db():
    """从数据库获取需要查询的token地址列表"""
    try:
        conn = sqlite3.connect(DB_PATH, check_same_thread=False)
        cursor = conn.cursor()
        
        # 查询存在且非空的token地址
        cursor.execute(f"SELECT code_address FROM {DB_TABLE} WHERE code_address IS NOT NULL AND code_address != ''")
        addresses = cursor.fetchall()
        conn.close()
        
        # 提取地址列表
        return [addr[0] for addr in addresses]
    except sqlite3.Error as e:
        thread_safe_print(f"从数据库获取token地址列表时出错: {e}")
        return []


def get_all_token_addresses():
    """从数据库中获取所有Token Address（保持兼容性）"""
    addresses = get_token_addresses_from_db()
    # 转换为旧格式以兼容现有代码
    return [(i, addr) for i, addr in enumerate(addresses)]

def fetch_token_price(token_data, thread_id):
    """获取特定代币的当前价格，使用重试机制"""
    row_id, token_address = token_data

    url = "https://www.valuescan.ai/api/v1/dex/market/current-price"
    headers = {
        "Content-Type": "application/json"
    }
    data = [
        {
            "chainName": "SOL",
            "tokenContractAddress": token_address
        }
    ]

    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            thread_safe_print(
                f"[线程-{thread_id}] 请求发送 (尝试 {retry_count + 1}/{MAX_RETRIES}): URL={url}, 数据={json.dumps(data)}")

            # 添加超时参数
            response = requests.post(url, headers=headers, json=data, timeout=REQUEST_INTERVAL)

            if response.status_code == 200:
                return response.json()
            else:
                thread_safe_print(
                    f"[线程-{thread_id}] API请求失败: 状态码 {response.status_code}, 响应: {response.text}")
                # 如果是服务器错误，继续重试
                if 500 <= response.status_code < 600:
                    thread_safe_print(f"[线程-{thread_id}] 服务器错误，将在{RETRY_INTERVAL}秒后重试...")
                    time.sleep(RETRY_INTERVAL)
                    retry_count += 1
                    continue
                else:
                    # 对于客户端错误，还是尝试重试
                    thread_safe_print(f"[线程-{thread_id}] 客户端错误，将在{RETRY_INTERVAL}秒后重试...")
                    time.sleep(RETRY_INTERVAL)
                    retry_count += 1
                    continue

        except requests.exceptions.Timeout:
            thread_safe_print(f"[线程-{thread_id}] 请求超时（超过{REQUEST_INTERVAL}秒），将重试...")
            retry_count += 1
            # 超时后直接重试，不需要额外延迟
            continue

        except requests.exceptions.ConnectionError:
            thread_safe_print(f"[线程-{thread_id}] 连接错误，将在{RETRY_INTERVAL}秒后重试...")
            time.sleep(RETRY_INTERVAL)
            retry_count += 1
            continue

        except Exception as e:
            thread_safe_print(f"[线程-{thread_id}] 请求发生错误: {e}")
            thread_safe_print(f"[线程-{thread_id}] 将在{RETRY_INTERVAL}秒后重试...")
            time.sleep(RETRY_INTERVAL)
            retry_count += 1
            continue

    # 如果所有重试都失败
    thread_safe_print(f"[线程-{thread_id}] 已达到最大重试次数({MAX_RETRIES})，跳过此代币")
    return None


def update_database_with_price(token_address, price):
    """简化的数据库更新函数"""
    try:
        conn = sqlite3.connect(DB_PATH, check_same_thread=False)
        cursor = conn.cursor()
        
        cursor.execute(f"""
            UPDATE {DB_TABLE} 
            SET ha_price = ? 
            WHERE code_address = ?
        """, (price, token_address))
        
        conn.commit()
        conn.close()
        
        if cursor.rowcount > 0:
            thread_safe_print(f"[HA价格更新] {token_address} - 价格: {price}")
        
    except sqlite3.Error as e:
        thread_safe_print(f"[数据库错误] 更新token {token_address} 失败: {e}")


def update_database(row_id, price, timestamp):
    """更新数据库中指定行的HA和HA Time列（保持兼容性）"""
    with DB_LOCK:  # 使用锁确保线程安全的数据库访问
        conn = None
        try:
            # 将Unix时间戳转换为可读时间格式
            standard_time = datetime.datetime.fromtimestamp(int(timestamp) / 1000).strftime('%Y-%m-%d %H:%M:%S')

            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute(f"UPDATE {DB_TABLE} SET [data HA] = ?, [HA Time] = ? WHERE rowid = ?",
                           (price, standard_time, row_id))
            conn.commit()
            return True
        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库错误: {e}")
            return False
        finally:
            if conn:
                conn.close()


def worker_thread(token_batch, thread_id):
    """工作线程函数，处理一批token"""
    thread_safe_print(f"[线程-{thread_id}] 启动，负责处理 {len(token_batch)} 个代币")

    success_count = 0
    total_count = len(token_batch)

    for i, token_data in enumerate(token_batch):
        row_id, token_address = token_data
        thread_safe_print(f"[线程-{thread_id}] 处理第 {i + 1}/{total_count} 个代币: {token_address}")

        # 调用API获取价格数据
        response_data = fetch_token_price(token_data, thread_id)
        if not response_data or response_data.get("code") != 200:
            thread_safe_print(
                f"[线程-{thread_id}] 获取价格失败: {json.dumps(response_data) if response_data else 'No response'}")
            continue

        # 从响应中提取price和time
        try:
            data_item = response_data.get("data", [])[0]
            price = data_item.get("pricechange_volume")
            timestamp = data_item.get("time")

            if price and timestamp:
                # 更新数据库
                if update_database(row_id, price, timestamp):
                    thread_safe_print(
                        f"[线程-{thread_id}] 成功更新代币 {token_address} 的价格: {price}, 时间: {timestamp}")
                    success_count += 1
                    thread_safe_print(
                        f"[线程-{thread_id}] 进度: {i + 1}/{total_count} ({(i + 1) / total_count * 100:.2f}%)")
                else:
                    thread_safe_print(f"[线程-{thread_id}] 更新数据库失败")
            else:
                thread_safe_print(f"[线程-{thread_id}] 响应数据不完整: {data_item}")
        except (IndexError, KeyError) as e:
            thread_safe_print(f"[线程-{thread_id}] 解析响应数据错误: {e}, 响应: {response_data}")

        # 添加延迟以避免频繁请求，但在多线程环境下可以适当减少延迟
        if i < total_count - 1:  # 最后一个不需要延迟
            time.sleep(REQUEST_INTERVAL)  # 并发环境下降低延迟时间

    thread_safe_print(f"[线程-{thread_id}] 完成! 成功处理 {success_count}/{total_count} 个代币")
    return success_count


def process_tokens_with_threads():
    """使用多线程处理所有token数据"""
    # 获取所有token数据
    all_tokens = get_all_token_addresses()
    total_count = len(all_tokens)

    if not all_tokens:
        thread_safe_print("未找到任何Token地址，请检查数据库")
        return 0, 0

    thread_safe_print(f"总共有 {total_count} 个代币需要处理，将使用 {THREAD_COUNT} 个线程并行处理")

    # 使用线程池执行任务
    success_counts = []
    with ThreadPoolExecutor(max_workers=THREAD_COUNT) as executor:
        # 提交任务并收集Future对象
        futures = [executor.submit(worker_thread, [all_tokens[i]], i + 1) for i in range(total_count)]

        # 等待所有任务完成并收集结果
        for future in futures:
            success_counts.append(future.result())

    # 计算总成功数
    total_success = sum(success_counts)

    return total_count, total_success


def main():
    thread_safe_print("=" * 60)
    thread_safe_print("SOL代币价格获取工具 (多线程版)")
    thread_safe_print("=" * 60)
    thread_safe_print("数据库路径:", DB_PATH)
    thread_safe_print("数据库表名:", DB_TABLE)
    thread_safe_print("线程数量:", THREAD_COUNT)
    thread_safe_print("最大重试次数:", MAX_RETRIES)
    thread_safe_print("请求间隔:", REQUEST_INTERVAL, "秒")
    thread_safe_print("=" * 60)

    # 获取价格并实时更新数据库
    start_time = time.time()
    total_count, success_count = process_tokens_with_threads()

    # 输出结果
    thread_safe_print("\n更新完成!")
    thread_safe_print(f"总计: {total_count} 个Token")
    thread_safe_print(f"成功更新数据库: {success_count} 个")

    end_time = time.time()
    duration = end_time - start_time
    thread_safe_print(f"程序运行耗时: {duration:.2f} 秒")


def get_token_historical_price_ha(token_address):
    """
    从valuescan.ai API获取token的历史价格数据
    
    Args:
        token_address: token地址
        
    Returns:
        dict: 包含price和timestamp的字典，获取失败时返回None
    """
    try:
        url = "https://www.valuescan.ai/api/v1/dex/market/current-price"
        headers = {
            "Content-Type": "application/json"
        }
        data = [
            {
                "chainName": "SOL",
                "tokenContractAddress": token_address
            }
        ]
        
        response = requests.post(url, headers=headers, json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200 and "data" in result:
                data_list = result.get("data", [])
                if data_list and len(data_list) > 0:
                    data_item = data_list[0]
                    price = data_item.get("pricechange_volume")
                    timestamp = data_item.get("time")
                    
                    if price and timestamp:
                        return {
                            "pricechange_volume": price,
                            "timestamp": timestamp
                        }
        
        return None

    except Exception as e:
        thread_safe_print(f"获取HA价格数据失败: {e}")
        return None


def worker_thread_new(token_addresses, thread_id):
    """
    单线程处理代币价格获取和数据库更新

    Args:
        token_addresses: 代币地址列表
        thread_id: 线程ID
        
    Returns:
        int: 成功处理的数量
    """
    success_count = 0
    for token_address in token_addresses:
        try:
            # 请求间隔
            time.sleep(REQUEST_INTERVAL)
            
            # 获取价格数据
            price_data = get_token_historical_price_ha(token_address)
            
            if price_data:
                # 更新数据库
                update_database_with_price(token_address, price_data["pricechange_volume"])
                success_count += 1
            else:
                thread_safe_print(f"[线程-{thread_id}] 获取价格失败: {token_address}")

        except Exception as e:
            thread_safe_print(f"[线程-{thread_id}] 处理token {token_address} 时出错: {e}")
    
    thread_safe_print(f"[线程-{thread_id}] 完成处理 {success_count}/{len(token_addresses)} 个代币")
    return success_count


def main_new():
    """新的主函数"""
    start_time = time.time()

    # 获取需要处理的代币地址列表
    token_addresses = get_token_addresses_from_db()
    total_count = len(token_addresses)
    
    if total_count == 0:
        thread_safe_print("没有找到需要处理的代币地址")
        return 0, 0

    thread_safe_print(f"总共有 {total_count} 个代币需要处理，将使用 {THREAD_COUNT} 个线程并行处理")

    # 使用线程池执行任务
    success_counts = []
    with ThreadPoolExecutor(max_workers=THREAD_COUNT) as executor:
        # 提交任务并收集Future对象
        futures = [executor.submit(worker_thread_new, token_addresses, 1)]

        # 等待所有任务完成并收集结果
        for future in futures:
            try:
                success_counts.append(future.result())
            except Exception as e:
                thread_safe_print(f"任务执行失败: {e}")
                success_counts.append(0)

    # 计算总计数
    total_success = sum(success_counts)
    end_time = time.time()
    total_time = end_time - start_time

    thread_safe_print(f"\n=== 执行完成 ===")
    thread_safe_print(f"总处理代币数量: {total_count}")
    thread_safe_print(f"成功处理数量: {total_success}")
    thread_safe_print(f"失败数量: {total_count - total_success}")
    thread_safe_print(f"成功率: {(total_success / total_count * 100):.2f}%")
    thread_safe_print(f"总耗时: {total_time:.2f} 秒")

    return total_count, total_success


if __name__ == "__main__":
    thread_safe_print("=" * 60)
    thread_safe_print("HA代币价格获取工具 (单线程版)")
    thread_safe_print("=" * 60)
    thread_safe_print("数据库路径:", DB_PATH)
    thread_safe_print("数据库表名:", DB_TABLE)
    thread_safe_print("线程数量:", THREAD_COUNT)
    thread_safe_print("最大重试次数:", MAX_RETRIES)
    thread_safe_print("请求间隔:", REQUEST_INTERVAL, "秒")
    thread_safe_print("=" * 60)

    try:
        main_new()
    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        import traceback

        thread_safe_print(f"程序运行出错: {e}")
        thread_safe_print(traceback.format_exc())

    thread_safe_print("程序运行结束")
