<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWS WAF Token生成器 - JavaScript版本测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .info-section {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }

        .control-panel {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }

        .success {
            border-left: 4px solid #28a745;
            background: #d4edda;
            color: #155724;
        }

        .error {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .warning {
            border-left: 4px solid #ffc107;
            background: #fff3cd;
            color: #856404;
        }

        .progress {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            color: #0c5460;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .code-block {
            background: #2d3748;
            color: #a0aec0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .highlight {
            color: #68d391;
            font-weight: bold;
        }

        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🔐 AWS WAF Token生成器 - JavaScript完整版</h1>

        <div class="info-section">
            <h3><span class="emoji">🎯</span>项目概述</h3>
            <p>基于币安网站逆向工程的完整JavaScript实现，整合了浏览器设备指纹、HashcashSHA2算法和工作量证明机制。</p>
            <p><strong>算法特点：</strong></p>
            <ul>
                <li>🔥 算法ID: h7b0c470f0cfe3a80a9e26526ad185f484f6817d0832712a4a67f</li>
                <li>⚡ 难度级别: 8 (前2个十六进制字符必须为0)</li>
                <li>🧠 哈希函数: SHA-256</li>
                <li>💾 内存参数: 128</li>
            </ul>
        </div>

        <div class="control-panel">
            <h3><span class="emoji">🚀</span>控制面板</h3>
            <button id="generateBtn" onclick="generateToken()">生成AWS WAF Token</button>
            <button id="testBtn" onclick="testDeviceFingerprint()" disabled>测试设备指纹</button>
            <button id="benchmarkBtn" onclick="runBenchmark()" disabled>性能基准测试</button>
            <button id="clearBtn" onclick="clearResults()">清空结果</button>
        </div>

        <div class="stats" id="statsContainer" style="display: none;">
            <div class="stat-item">
                <div class="stat-value" id="statNonce">-</div>
                <div class="stat-label">解决方案 (Nonce)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="statAttempts">-</div>
                <div class="stat-label">尝试次数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="statTime">-</div>
                <div class="stat-label">耗时 (ms)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="statHashRate">-</div>
                <div class="stat-label">哈希速率 (hash/s)</div>
            </div>
        </div>

        <div class="result-box" id="resultContainer">
            <strong>📋 执行日志：</strong><br>
            准备就绪，点击"生成AWS WAF Token"开始测试...
        </div>

        <div class="info-section">
            <h3><span class="emoji">💡</span>使用说明</h3>
            <div class="code-block">
                <span class="highlight">// 在浏览器控制台中使用：</span>
                const generator = new AWSWAFTokenGenerator();
                const result = await generator.generateToken("https://api.binance.com");
                console.log("生成的Token:", result.token);

                <span class="highlight">// 或者直接运行测试：</span>
                await testAWSWAFTokenGeneration();
            </div>
        </div>

        <div class="info-section warning">
            <h3><span class="emoji">⚠️</span>重要提醒</h3>
            <ul>
                <li>🔬 <strong>仅供学习研究：</strong>本工具仅用于技术研究和学习目的</li>
                <li>📜 <strong>遵守服务条款：</strong>使用时请遵守相关网站的服务条款</li>
                <li>🚫 <strong>禁止恶意使用：</strong>不得用于恶意攻击或违法行为</li>
                <li>⏱️ <strong>合理频率：</strong>避免过于频繁的Token生成请求</li>
            </ul>
        </div>
    </div>

    <!-- 引入AWS WAF Token生成器 -->
    <script src="complete_js_aws_waf_decoder.js"></script>

    <script>
        let generator = null;
        let isGenerating = false;

        // 页面加载完成后初始化
        window.addEventListener('load', function () {
            try {
                generator = new AWSWAFTokenGenerator();
                updateLog("✅ AWS WAF Token生成器初始化成功！", "success");

                // 启用其他按钮
                document.getElementById('testBtn').disabled = false;
                document.getElementById('benchmarkBtn').disabled = false;

            } catch (error) {
                updateLog("❌ 初始化失败: " + error.message, "error");
            }
        });

        // 生成Token主函数
        async function generateToken() {
            if (isGenerating) {
                updateLog("⚠️ 正在生成中，请稍候...", "warning");
                return;
            }

            isGenerating = true;
            document.getElementById('generateBtn').disabled = true;
            document.getElementById('generateBtn').textContent = "生成中...";

            try {
                updateLog("🚀 开始生成AWS WAF Token...", "progress");
                updateLog("📍 目标域名: https://api.binance.com");

                const startTime = performance.now();
                const result = await generator.generateToken("https://api.binance.com");
                const totalTime = performance.now() - startTime;

                if (result) {
                    updateLog("🎉 Token生成成功！", "success");
                    updateLog(`📊 详细信息:`);
                    updateLog(`  - 解决方案: ${result.solution.nonce}`);
                    updateLog(`  - 哈希值: ${result.solution.hash}`);
                    updateLog(`  - 尝试次数: ${result.solution.attempts}`);
                    updateLog(`  - 算法耗时: ${result.solution.timeMs.toFixed(2)}ms`);
                    updateLog(`  - 总耗时: ${totalTime.toFixed(2)}ms`);
                    updateLog(`  - Token长度: ${result.token.length} 字符`);
                    updateLog(`  - Token预览: ${result.token.substring(0, 80)}...`);

                    // 更新统计数据
                    updateStats(result.solution, totalTime);

                    // 测试Token使用
                    await testTokenUsage(result.token);

                } else {
                    updateLog("❌ Token生成失败", "error");
                }

            } catch (error) {
                updateLog("❌ 生成过程出错: " + error.message, "error");
                console.error("Token generation error:", error);
            } finally {
                isGenerating = false;
                document.getElementById('generateBtn').disabled = false;
                document.getElementById('generateBtn').textContent = "生成AWS WAF Token";
            }
        }

        // 测试设备指纹
        async function testDeviceFingerprint() {
            try {
                updateLog("🔍 正在测试设备指纹生成...", "progress");

                const fingerprint = generator.generateDeviceFingerprint();

                updateLog("📱 设备指纹生成成功:", "success");
                updateLog(`  - 屏幕分辨率: ${fingerprint.raw.screen_resolution}`);
                updateLog(`  - Canvas指纹: ${fingerprint.raw.canvas_code}`);
                updateLog(`  - WebGL供应商: ${fingerprint.raw.webgl_vendor}`);
                updateLog(`  - 音频指纹: ${fingerprint.raw.audio}`);
                updateLog(`  - 浏览器指纹: ${fingerprint.raw.fingerprint}`);
                updateLog(`  - 设备名称: ${fingerprint.raw.device_name}`);
                updateLog(`  - 编码后长度: ${fingerprint.encoded.length} 字符`);

            } catch (error) {
                updateLog("❌ 设备指纹测试失败: " + error.message, "error");
            }
        }

        // 性能基准测试
        async function runBenchmark() {
            try {
                updateLog("⚡ 开始性能基准测试...", "progress");

                const rounds = 3;
                const results = [];

                for (let i = 1; i <= rounds; i++) {
                    updateLog(`📊 第 ${i}/${rounds} 轮测试...`);

                    const startTime = performance.now();
                    const result = await generator.generateToken("https://test.example.com");
                    const endTime = performance.now();

                    if (result) {
                        const testResult = {
                            round: i,
                            nonce: result.solution.nonce,
                            attempts: result.solution.attempts,
                            timeMs: endTime - startTime,
                            hashRate: result.solution.attempts / ((endTime - startTime) / 1000)
                        };

                        results.push(testResult);
                        updateLog(`  ✅ 第${i}轮: ${testResult.attempts}次尝试, ${testResult.timeMs.toFixed(2)}ms, ${testResult.hashRate.toFixed(0)} hash/s`);
                    }
                }

                // 计算平均值
                const avgAttempts = results.reduce((sum, r) => sum + r.attempts, 0) / results.length;
                const avgTime = results.reduce((sum, r) => sum + r.timeMs, 0) / results.length;
                const avgHashRate = results.reduce((sum, r) => sum + r.hashRate, 0) / results.length;

                updateLog("📈 基准测试完成:", "success");
                updateLog(`  📊 平均尝试次数: ${avgAttempts.toFixed(1)}`);
                updateLog(`  ⏱️ 平均耗时: ${avgTime.toFixed(2)}ms`);
                updateLog(`  🚀 平均哈希速率: ${avgHashRate.toFixed(0)} hash/s`);

            } catch (error) {
                updateLog("❌ 基准测试失败: " + error.message, "error");
            }
        }

        // 测试Token使用
        async function testTokenUsage(token) {
            try {
                updateLog("🔬 测试Token有效性...", "progress");

                const response = await fetch("https://api.binance.com/api/v3/ping", {
                    headers: {
                        'x-aws-waf-token': token,
                        'User-Agent': navigator.userAgent,
                        'Accept': 'application/json'
                    }
                });

                updateLog(`📡 API响应状态: ${response.status}`);

                if (response.ok) {
                    updateLog("✅ Token验证成功！API访问正常", "success");
                } else if (response.status === 405) {
                    updateLog("⚠️ 收到405响应，可能需要重新生成Token", "warning");
                } else {
                    updateLog(`⚠️ 收到${response.status}响应，Token可能需要调整`, "warning");
                }

            } catch (error) {
                updateLog("❌ Token测试失败: " + error.message, "error");
            }
        }

        // 更新统计数据
        function updateStats(solution, totalTime) {
            document.getElementById('statNonce').textContent = solution.nonce;
            document.getElementById('statAttempts').textContent = solution.attempts;
            document.getElementById('statTime').textContent = solution.timeMs.toFixed(0);
            document.getElementById('statHashRate').textContent = Math.round(solution.attempts / (solution.timeMs / 1000));

            document.getElementById('statsContainer').style.display = 'grid';
        }

        // 更新日志
        function updateLog(message, type = "") {
            const container = document.getElementById('resultContainer');
            const timestamp = new Date().toLocaleTimeString();

            // 添加类型样式
            let className = "";
            switch (type) {
                case "success": className = " success"; break;
                case "error": className = " error"; break;
                case "warning": className = " warning"; break;
                case "progress": className = " progress"; break;
            }

            if (container.textContent.includes("准备就绪")) {
                container.innerHTML = `<strong>📋 执行日志：</strong><br>`;
            }

            container.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            container.scrollTop = container.scrollHeight;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('resultContainer').innerHTML = `
                <strong>📋 执行日志：</strong><br>
                准备就绪，点击"生成AWS WAF Token"开始测试...
            `;
            document.getElementById('statsContainer').style.display = 'none';
        }
    </script>
</body>

</html>