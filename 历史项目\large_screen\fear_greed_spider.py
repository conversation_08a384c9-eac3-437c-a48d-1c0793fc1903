from loguru import logger
from lxml import etree
from settings import *
from Spiders.Spider import Spider_Crawler_Request
from datetime import datetime

class FearAndGreed(Spider_Crawler_Request):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.crawl_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def parse_data(self):
        url = "https://alternative.me/crypto/fear-and-greed-index/"
        headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        }
        response = self.get(url, headers=headers)
        if response.status_code == 200:
            logger.info('请求成功，正在获取贪婪值和恐惧值...')
            html_content = response.content
            tree = etree.HTML(html_content)

            the_dates = tree.xpath('//*[@id="main"]/section/div/div[3]/div[2]/div/div/div/div[1]/div[1]/text()')
            the_values = tree.xpath('//*[@id="main"]/section/div/div[3]/div[2]/div/div/div/div[2]/div/text()')

            if len(the_dates) == len(the_values):
                data_dict = dict(zip(the_dates, the_values))
                now_value = data_dict.get("Now", None)
                yesterday_value = data_dict.get("Yesterday", None)
                last_week_value = data_dict.get("Last week", None)
                last_month_value = data_dict.get("Last month", None)

                sql_replace_data = """
                REPLACE INTO fear_greed (id, now, yesterday, last_week, last_month, crawl_date)
                VALUES (1, %s, %s, %s, %s, %s)
                """
                self.insert_mysql(sql=sql_replace_data, v_list=[now_value, yesterday_value, last_week_value, last_month_value, self.crawl_date])
                logger.info(f'数据操作: Now: {now_value}, Yesterday: {yesterday_value}, Last week: {last_week_value}, Last month: {last_month_value}')
            else:
                logger.error("提取的数据数量不一致")
        else:
            logger.error(f"请求失败，状态码：{response.status_code}")

    def main(self):
        self.parse_data()

if __name__ == '__main__':
    FearAndGreed().main()