'''
非小号 热度数据获取
'''

import requests
import pymysql
from loguru import logger
import redis
import time
import random
import pymysql
from pymysql.cursors import DictCursor



class HOTSOCIAL_SPIDER():
    def __init__(self):

        self.url = "https://dncapi.flink1.com/api/v3/coin/hotsocial"
        self.headers = {
            "authority": "dncapi.flink1.com",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "referer": "https://www.feixiaohao.com/",
            "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.params = {
            "coincode": "ethereum",
            "webp": "1"
        }
        self.proxy = {
            "http": "socks5://127.0.0.1:33211",
            "https": "socks5://127.0.0.1:33211"
        }
        self.mysql_config = {
            'host': '**************',
            'port': 33060,
            'user': 'root',
            'password': '12345678',
            'db': 'fxh_data',
            'charset': 'utf8',
            'cursorclass': DictCursor
        }
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            password='123456',
            db=2,
            decode_responses=True
        )

    def get_code_from_redis(self):
        """从Redis中获取并移除一个code"""
        try:
            code = self.redis_client.spop('feixiaohao:coin_codes')
            if code:
                logger.info(f'从Redis获取到code: {code}')
                return code
            return None
        except Exception as e:
            logger.error(f'从Redis获取code失败: {e}')
            return None


    def fetch_data(self):
        try:
            response = requests.get(url=self.url, headers=self.headers, params=self.params, proxies=self.proxy)
            logger.info(f'请求状态码：{response.status_code}')
            data = response.json()
            return data
        except Exception as e:
            logger.error(f'获取数据时出错：{e}')
            return None


    def parse_pairs(self, data):
        parsed_pairs = []
        data_detail = data.get('data', {}).get('pairs', [])
        for data_need in data_detail:
            append_data = {
                "name": data_need.get('name', ''),
                "24-H_trading_volume": '',
                "24-H_amount": '',
                "percent": data_need.get('percent', ''),
            }
            logger.info(f'解析后的交易对数据：{append_data}')
            parsed_pairs.append(append_data)
        logger.success(f'共获取到{len(parsed_pairs)}个交易对数据')
        if parsed_pairs:
            self.insert_pairs_data(parsed_pairs)
        else:
            logger.warning('没有获取到交易对数据')


    def parse_exchanges(self, data):
        parsed_exchanges = []
        data_detail = data.get('data', {}).get('exchanges', [])
        for data_need in data_detail:
            append_data = {
                'name': data_need.get('native_name', ''),
                "24-H_trading_volume": '',
                "24-H_amount": '',
                "percent": data_need.get('percent', '')
            }
            logger.info(f'解析后的交易所数据：{append_data}')
            parsed_exchanges.append(append_data)
        logger.success(f'共获取到{len(parsed_exchanges)}个交易对数据')
        if parsed_exchanges:
            self.insert_exchanges_data(parsed_exchanges)
        else:
            logger.warning('没有获取到交易所数据')



    def insert_pairs_data(self, parsed_pairs_data):
        pass


    def insert_exchanges_data(self, parsed_exchanges_data):
        pass



    def run(self):
        pass


if __name__ == '__main__':
    pass
'''
目前需要考虑数据如何存储
一共有两万多个币种，每个币种都有热门交易数据 且每个数据都不一样 所以无法使用多对多的关系
'''
