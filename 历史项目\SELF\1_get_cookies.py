from DrissionPage import ChromiumPage
from DrissionPage import SessionPage
import time
import csv

class CloudSpider:

    def __init__(self):
        self.page = ChromiumPage()

    def login(self):
        self.page.get("https://optimistic.etherscan.io/login")
        time.sleep(5)

        self.page.ele('#ContentPlaceHolder1_txtUserName', timeout=15).clear()
        self.page.ele('#ContentPlaceHolder1_txtUserName', timeout=15).input('Krickliu')
        time.sleep(1)

        self.page.ele('#ContentPlaceHolder1_txtPassword', timeout=15).clear()
        self.page.ele('#ContentPlaceHolder1_txtPassword', timeout=15).input('DOMYBEST0922')
        time.sleep(1)

        self.page.ele('#ContentPlaceHolder1_chkRemember', timeout=15).click()
        time.sleep(3)

        self.page.ele('#ContentPlaceHolder1_btnLogin', timeout=15).click()
        time.sleep(5)

    # def save_headers_and_cookies(self):

    #     cookies = self.page.cookies(all_domains=True)
    #     print("Fetched cookies:", cookies)

    #     if cookies:
    #         with open('cookies.txt', 'w', encoding='utf-8') as file:
    #             for cookie in cookies:
    #                 cookie_details = f"Name: {cookie['name']}, Value: {cookie['value']}, Domain: {cookie['domain']}, Path: {cookie['path']}, Expires: {cookie.get('expiry', '')}\n"
    #                 file.write(cookie_details)
    #                 print("Writing cookie:", cookie_details)

    #     else:
    #         print("No cookies to save.")

    # 以txt格式保存cookie
    # def save_headers_and_cookies(self):
        
    #     cookies = self.page.cookies(all_domains=True)
    #     print("Fetched cookies:", cookies)

    #     if cookies:
    #         with open('1_saved_cookies.txt', 'w', encoding='utf-8') as file:
    #             for cookie in cookies:
    #                 cookie_details = f"Name: {cookie.get('name', '')}, Value: {cookie.get('value', '')}, Domain: {cookie.get('domain', '')}, Path: {cookie.get('path', '')}, Expires: {cookie.get('expiry', '')}\n"
    #                 file.write(cookie_details)
    #                 print("Writing cookie:", cookie_details)
    #         print("Cookies have been written to cookies.txt.")
    #     else:
    #         print("No cookies to save.")


    # 以csv格式保存文件
    # def save_headers_and_cookies(self):
    #     cookies = self.page.cookies(all_domains=True)
    #     print("Fetched cookies:", cookies)

        # if cookies:
        #     with open('1_login_cookies.csv', 'w', newline='', encoding='utf-8') as file:
        #         csv_writer = csv.writer(file)
        #         csv_writer.writerow(['Name', 'Value', 'Domain', 'Path', 'Expires'])
        #         for cookie in cookies:
        #             name = cookie.get('name', '')
        #             value = cookie.get('value', '')
        #             domain = cookie.get('domain', '')
        #             path = cookie.get('path', '')
        #             expires = cookie.get('expiry', '')
        #             csv_writer.writerow([name, value, domain, path, expires])
        #     print("Cookies have been written to cookies.csv.")
        # else:
        #     print("No cookies to save.")

    def save_headers_and_cookies(self):
        cookies = self.page.cookies(all_domains=True)
        headers = self.page._headers
        print("Fetched cookies:", cookies)
        print("Fetched headers:", headers)

        if cookies:
            with open('1_login_cookies.csv', 'w', newline='', encoding='utf-8') as file:
                csv_writer = csv.writer(file)
                csv_writer.writerow(['Name', 'Value', 'Domain', 'Path', 'Expires'])
                for cookie in cookies:
                    name = cookie.get('name', '')
                    value = cookie.get('value', '')
                    domain = cookie.get('domain', '')
                    path = cookie.get('path', '')
                    expires = cookie.get('expiry', '')
                    csv_writer.writerow([name, value, domain, path, expires])
            print("Cookies have been written to cookies.csv.")
        else:
            print("No cookies to save.")

        if headers:
            with open('1_login_headers.csv', 'w', newline='', encoding='utf-8') as file:
                csv_writer = csv.writer(file)
                csv_writer.writerow(['Header', 'Value'])
                for header, value in headers.items():
                    csv_writer.writerow([header, value])
            print("Headers have been written to headers.csv.")
        else:
            print("No headers to save.")

    def close(self):
        self.page.close()

if __name__ == "__main__":
    spider = CloudSpider()
    spider.login()
    spider.save_headers_and_cookies()
    spider.close()