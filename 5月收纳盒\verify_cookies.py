import redis
import requests
import json
import time
from loguru import logger
from utils import DB_BASE

redis_client = redis.Redis(
    host='**************',
    port=6379,
    db=9,
    password='123456',
    decode_responses=True
)

proxy_api = "http://dgjm6ipcchif:hidzbgr325hn@*************:2333"
proxies_api = {
    'http': proxy_api,
    'https': proxy_api,
}

db_base = DB_BASE()

SITES_CONFIG = {
    'bsc': {
        'cookie_key': 'cf_cookies:bscscan',
        'verify_url': 'https://bscscan.com/tokens'
    }
}


def verify_single_site(site_name, config):
    """验证单个网站的cookie"""
    try:
        cookies_json = redis_client.get(config['cookie_key'])
        if not cookies_json:
            logger.error(f"{site_name} - 未在Redis中找到cookie")
            db_base.send_to_fs(f"\n{site_name}平台cookie未在Redis中找到，需要重新生成...")
            return False

        cookies_data = json.loads(cookies_json)
        
        user_agent = cookies_data.get('user_agent')
        if not user_agent:
            logger.error(f"{site_name} - 未在cookie中找到user agent")
            # db_base.send_to_fs(f"\n{site_name}平台cookie中未找到user agent，需要重新生成...")
            return False
            
        cookies = {k: v for k, v in cookies_data.items() if k != 'user_agent'}
        
        headers = {'User-Agent': user_agent}

        max_retries = 5
        for retry in range(1, max_retries + 1):
            logger.info(f"{site_name} - 第{retry}次尝试验证URL: {config['verify_url']}")
            
            try:
                if site_name == 'bsc':
                    response = requests.get(
                        url=config['verify_url'],
                        headers=headers,
                        cookies=cookies,
                        timeout=10
                    )
                else:
                    response = requests.get(
                        url=config['verify_url'],
                        headers=headers,
                        cookies=cookies,
                        proxies=proxies_api,
                        timeout=10
                    )

                logger.info(f"{site_name} - 第{retry}次尝试状态码: {response.status_code}")

                if response.status_code == 200:
                    logger.info(f"{site_name} - 第{retry}次尝试验证成功！")
                    return True
                
                if retry < max_retries:
                    logger.warning(f"{site_name} - 第{retry}次尝试失败，等待5秒后重试...")
                    time.sleep(5)
                else:
                    logger.error(f"{site_name} - 所有{max_retries}次尝试均失败！最后状态码: {response.status_code}")
                    # db_base.send_to_fs(f"\n{site_name}平台cookie已经失效（状态码: {response.status_code}），正在重新生成...")
                    return False
            
            except Exception as e:
                if retry < max_retries:
                    logger.warning(f"{site_name} - 第{retry}次尝试出错: {str(e)}，等待5秒后重试...")
                    time.sleep(5)
                else:
                    logger.error(f"{site_name} - 所有{max_retries}次尝试均出错！最后错误: {str(e)}")
                    # 发送飞书提示消息，包含错误信息
                    # db_base.send_to_fs(f"\n{site_name}平台cookie已经失效（错误: {str(e)[:100]}...），正在重新生成...")
                    return False
        
        return False

    except Exception as e:
        logger.error(f"{site_name} - 验证过程中出错: {str(e)}")
        # 发送飞书提示消息，包含错误信息
        # db_base.send_to_fs(f"\n{site_name}平台cookie验证过程出错（错误: {str(e)[:100]}...），正在重新生成...")
        return False


def verify_all_cookies():
    """验证所有网站的cookie"""
    results = {}
    invalid_sites = []

    for site_name, config in SITES_CONFIG.items():
        logger.info(f"\n开始验证 {site_name} 的cookie...")
        is_valid = verify_single_site(site_name, config)
        results[site_name] = is_valid

        if not is_valid:
            invalid_sites.append(site_name)

    logger.info("\n=== Cookie验证总结报告 ===")
    logger.info(f"总计检查站点数: {len(SITES_CONFIG)}")
    logger.info(f"有效cookie数: {len(SITES_CONFIG) - len(invalid_sites)}")
    logger.info(f"无效cookie数: {len(invalid_sites)}")

    if invalid_sites:
        logger.warning("以下站点的cookie无效或验证失败：")
        for site in invalid_sites:
            logger.warning(f"- {site}")
    else:
        logger.success("所有站点的cookie都有效！")

    return results


if __name__ == "__main__":
    verify_all_cookies()
