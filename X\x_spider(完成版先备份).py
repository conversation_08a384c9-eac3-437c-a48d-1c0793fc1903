import requests
import pandas as pd
import json
import os
import time
import random
from loguru import logger
from datetime import datetime

class XSpider:
    def __init__(self, max_pages=None, start_time=None):

        self.max_pages = max_pages
        self.start_time = start_time

        self.headers = {
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
            "x-csrf-token": "983ef3bb09837e612f3c3a8176f9f2a997c830a876c4496eb74e8b831cca620747e5d55ef18f0798c4e1ec4894b35c87811854c65428cc8d05dabb9822c4ef4f807c420fc5d6f84e9361999cd28d2491"
        }
        self.cookies = {
            "auth_token": "cce3e0c611c76bde939841029ad9af3945377089",
            "ct0": "983ef3bb09837e612f3c3a8176f9f2a997c830a876c4496eb74e8b831cca620747e5d55ef18f0798c4e1ec4894b35c87811854c65428cc8d05dabb9822c4ef4f807c420fc5d6f84e9361999cd28d2491"
        }
        self.base_params = {
            "variables": "{\"rawQuery\":\"Publishing compliant stablecoins\",\"count\":20,\"querySource\":\"typed_query\",\"product\":\"Top\"}",
            "features": "{\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"responsive_web_graphql_exclude_directive_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"rweb_video_timestamps_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":false,\"responsive_web_enhance_cards_enabled\":false}"
        }   
        self.proxy = {
            "http": "socks5://127.0.0.1:33211",
            "https": "socks5://127.0.0.1:33211"
        }

    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间格式到标准格式"""
        try:
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}")
            return None


    def fetch_data(self, keyword, cursor=None):
        """发送请求获取数据"""
        url = "https://x.com/i/api/graphql/U3QTLwGF8sZCHDuWIMSAmg/SearchTimeline"
        variables = {
            "rawQuery": keyword,
            "count": 20,
            "querySource": "typed_query",
            "product": "Top"
        }

        if cursor:
            variables["cursor"] = cursor

        params = self.base_params.copy()
        params["variables"] = json.dumps(variables, ensure_ascii=False)

        try:
            response = requests.get(
                url,
                headers=self.headers,
                cookies=self.cookies,
                params=params,
                proxies=self.proxy,
                timeout=10
            )
            response.raise_for_status()
            logger.success(f"请求成功 [{response.status_code}]")
        
            return response.json()
        except Exception as e:
            logger.error(f"请求失败: {e}")
            return None

    def extract_entries(self, data, keyword, start_time=None):

        tweet_batch = []
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    for entry in instruction.get('entries', []):
                        try:
                            result = entry.get('content', {}).get('itemContent', {}).get('tweet_results', {}).get('result', {})
                            if not result:
                                continue

                            user_result = result.get('core', {}).get('user_results', {}).get('result', {})
                            legacy_user = user_result.get('legacy', {})

                            tweet_result = result.get('legacy', {})
                            views_count = result.get('views', {})

                            edit_control = result.get('edit_control', {})
                            editable_until_msecs = edit_control.get('editable_until_msecs')
                            editable_time = None
                            if editable_until_msecs:
                                try:
                                    editable_time = datetime.fromtimestamp(
                                        int(editable_until_msecs) / 1000
                                    ).strftime('%Y-%m-%d %H:%M:%S')
                                except Exception as e:
                                    logger.warning(f"时间转换失败: {e}")

                            if start_time and editable_time:
                                if editable_time < start_time:
                                    logger.debug(f"推文时间 {editable_time} 早于起始时间 {start_time}，跳过保存")
                                    continue

                            tweet_data = {
                                'username': legacy_user.get('screen_name'),
                                'keyword': keyword,
                                'tweet_text': tweet_result.get('full_text', ''),
                                'tweet_time': editable_time,
                                'views': views_count.get('count', 0),
                                'favorites': tweet_result.get('favorite_count', 0),
                                'tweet_id': tweet_result.get('id_str'),
                                'link': f'https://twitter.com/{legacy_user.get("screen_name")}/status/{tweet_result.get("id_str")}'
                            }

                            tweet_batch.append(tweet_data)

                        except Exception as e:
                            logger.error(f"处理单条数据时出错: {e}")
                            continue

            return tweet_batch
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return []


    def get_next_cursor(self, data):
        """更健壮的游标提取方法"""
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [])
            
            logger.debug(f"指令数量: {len(instructions)}")
            
            for instruction in instructions:
                entries = []
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                elif instruction.get('type') == 'TimelineReplaceEntry':
                    entry = instruction.get('entry')
                    if entry:
                        entries = [entry]
                
                logger.debug(f"处理 {instruction.get('type')} 指令，发现 {len(entries)} 个条目")
                
                for entry in entries:
                    entry_id = entry.get('entryId')
                    if entry_id == 'cursor-bottom-0' or (isinstance(entry_id, str) and 'cursor-bottom' in entry_id):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineCursor':
                            cursor_value = content.get('value')
                            if cursor_value:
                                logger.success(f"成功提取游标: {cursor_value[:10]}...")
                                return cursor_value
            logger.warning("未找到有效游标")
            return None
        except Exception as e:
            logger.error(f"游标解析异常: {str(e)}")
            return None


    def save_to_csv(self, data, filename="X/x_output/all_tweets.csv"):
        """保存所有数据到同一个CSV文件"""
        if not data:
            return

        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            
            df = pd.DataFrame(data)
            df.replace({pd.NA: None}, inplace=True)
            df = df.applymap(lambda x: x.replace('\n', ' ').replace('\r', ' ') if isinstance(x, str) else x)
            file_exists = os.path.exists(filename)
            df.to_csv(
                filename,
                mode='a' if file_exists else 'w',
                header=not file_exists,
                index=False,
                encoding='utf-8-sig'
            )
            logger.success(f"成功保存 {len(data)} 条数据到 {filename}")
        except Exception as e:
            logger.error(f"保存失败: {e}")


    def run(self):
        """主运行方法"""
        with open('X/x_keyword/keywords.txt', 'r', encoding='utf-8') as f:
            keywords = [k.strip() for k in f if k.strip()]

        for keyword in keywords:
            logger.info(f"开始处理关键词: {keyword}")
            next_cursor = None
            page = 1
            max_retry = 3

            while True:
                if self.max_pages and page > self.max_pages:
                    logger.info(f"已达到设置的最大页数 {self.max_pages}，停止翻页")
                    break

                logger.info(f"正在爬取第 {page} 页...")
                data = None

                for attempt in range(max_retry):
                    data = self.fetch_data(keyword, next_cursor)
                    if data:
                        break
                    logger.warning(f"第 {attempt+1} 次重试...")
                    time.sleep(5)

                if not data:
                    logger.error("请求失败，停止翻页")
                    break

                extracted = self.extract_entries(data, keyword, self.start_time)
                if extracted:
                    # 直接调用save_to_csv，不再传入关键词相关的文件名
                    self.save_to_csv(extracted)
                else:
                    logger.warning("未提取到有效数据")
                
                new_cursor = self.get_next_cursor(data)
                
                # 调试日志：打印新旧游标对比
                # logger.debug(f"旧游标: {next_cursor} | 新游标: {new_cursor}")
                
                if not new_cursor or (self.max_pages and page >= self.max_pages):
                    logger.info("已到达最后一页或达到设置的最大页数")
                    break
                
                next_cursor = new_cursor
                page += 1
                
                wait_time = random.uniform(25, 35)
                logger.info(f"等待 {wait_time:.1f} 秒后继续")
                time.sleep(wait_time)

            logger.success(f"完成关键词 {keyword} 的采集")

if __name__ == "__main__":
    start_time = "2025-01-01 00:00:00"
    spider = XSpider(max_pages=4, start_time=start_time)
    spider.run()