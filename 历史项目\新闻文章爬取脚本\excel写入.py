import pandas as pd
import os

def csv_to_excel(input_directory, output_directory):
    # 确保输出目录存在，如果不存在则创建
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)
    
    # 遍历指定目录下的所有CSV文件
    for filename in os.listdir(input_directory):
        if filename.endswith('.csv'):
            # 读取CSV文件
            csv_path = os.path.join(input_directory, filename)
            df = pd.read_csv(csv_path)
            
            # 创建Excel文件路径
            excel_filename = filename.replace('.csv', '.xlsx')
            excel_path = os.path.join(output_directory, excel_filename)
            
            # 将数据写入Excel文件
            df.to_excel(excel_path, index=False)
            print(f"已将 {csv_path} 写入到 {excel_path}")

# 使用函数
input_directory = 'D:\python码云\吴说-友商动态'
output_directory = 'D:\python码云\吴说-友商动态excel'
csv_to_excel(input_directory, output_directory)