2025-03-25 16:13:33.960 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:13:33.961 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:13:33.961 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000028882581932745', 'gmgn_time': '2025-03-24 09:57:41', 'gmgn_amount_base': '52169', 'gmgn_amount_quote': '0.011324881', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '2NkpGLi1hpeXA91seEqYcT5yoEdp3ReXcjeiyhE2jm791cm1AtgrvZ4VMsHGZqgsYsvSjJkwstrMeFXaSasJ4ZqS'}
2025-03-25 16:13:33.961 | INFO     | __main__:run:152 - 第 1 页: 成功提取到 50 条数据
2025-03-25 16:13:34.593 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:13:34.593 | INFO     | __main__:run:169 - 等待 2.49 秒后获取下一页...
2025-03-25 16:13:37.084 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:13:37.084 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000028445329054726', 'gmgn_time': '2025-03-24 09:57:03', 'gmgn_amount_base': '92584', 'gmgn_amount_quote': '0.019810308', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '2nu5T7sykxndiHMVtMnY73YPgzzKvRSjK9bSMJjzMMjFeHevvCMAGpd6moBG3FygWa8tmCiXEgF3DHmGg19bgioc'}
2025-03-25 16:13:37.084 | INFO     | __main__:run:152 - 第 2 页: 成功提取到 50 条数据
2025-03-25 16:13:37.708 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:13:37.708 | INFO     | __main__:run:169 - 等待 2.83 秒后获取下一页...
2025-03-25 16:13:40.541 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:13:40.541 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000028364303057342', 'gmgn_time': '2025-03-24 09:56:37', 'gmgn_amount_base': '54251', 'gmgn_amount_quote': '0.011563777', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '2bZb79C5wj72HbzoXT5c34R5pFZVcNor1gvJCGxCAVdtnZgwzaJ4SzcBpVaAia6NxZPYGN9u66wUui2MBRqHyHEP'}
2025-03-25 16:13:40.541 | INFO     | __main__:run:152 - 第 3 页: 成功提取到 50 条数据
2025-03-25 16:13:41.194 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:13:41.195 | INFO     | __main__:run:169 - 等待 2.80 秒后获取下一页...
2025-03-25 16:13:44.002 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:13:44.003 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000027978604437357', 'gmgn_time': '2025-03-24 09:55:55', 'gmgn_amount_base': '57292', 'gmgn_amount_quote': '0.012026035', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '35z96jgf1Xtugh8kmXAGD3CR8MwvUxZRM1uqSCsvowQ7jm9FCp2PSQY5zDLPgtuhBZNtJsJTmj9cpTt62jYcPqFV'}
2025-03-25 16:13:44.003 | INFO     | __main__:run:152 - 第 4 页: 成功提取到 50 条数据
2025-03-25 16:13:44.676 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:13:44.677 | INFO     | __main__:run:169 - 等待 2.89 秒后获取下一页...
2025-03-25 16:13:47.569 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:13:47.569 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000027554241220452', 'gmgn_time': '2025-03-24 09:55:21', 'gmgn_amount_base': '53240', 'gmgn_amount_quote': '0.011010942', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '5buRXLmohPnnmBYRbBADJJ3RWmc5UYg8n1B1xgCiaCQeGXBF8H3janEidyN5AAJDMdBLZ1bvCjy5f28LXPSRuUaA'}
2025-03-25 16:13:47.570 | INFO     | __main__:run:152 - 第 5 页: 成功提取到 50 条数据
2025-03-25 16:13:48.189 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:13:48.189 | INFO     | __main__:run:169 - 等待 2.63 秒后获取下一页...
2025-03-25 16:13:50.824 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:13:50.824 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000027174236891208', 'gmgn_time': '2025-03-24 09:54:49', 'gmgn_amount_base': '58742', 'gmgn_amount_quote': '0.011973215', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '3twZ68Fz4e917RVRugnFbfinYUZ3yJqc15vphL7q7nFWEt3cnCbcsWnpJXEFL6Z4LBTUB3M7wANNSYXKuWaVFJHx'}
2025-03-25 16:13:50.825 | INFO     | __main__:run:152 - 第 6 页: 成功提取到 50 条数据
2025-03-25 16:13:51.471 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:13:51.471 | INFO     | __main__:run:169 - 等待 2.10 秒后获取下一页...
2025-03-25 16:13:53.570 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:13:53.570 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000027153066312592', 'gmgn_time': '2025-03-24 09:54:20', 'gmgn_amount_base': '55042', 'gmgn_amount_quote': '0.011217045', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '4WxZzxeRZZcifWRtBo5qZH5w3mhHhVZpuvtZPiKodWf1e1vy2XnnsmbTpsBXGgF4jwhkwGSr8F4Pzc4tRKrsVhDJ'}
2025-03-25 16:13:53.571 | INFO     | __main__:run:152 - 第 7 页: 成功提取到 50 条数据
2025-03-25 16:13:54.210 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:13:54.211 | INFO     | __main__:run:169 - 等待 2.94 秒后获取下一页...
2025-03-25 16:13:57.148 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:13:57.149 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000026675462000352', 'gmgn_time': '2025-03-24 09:53:50', 'gmgn_amount_base': '57948', 'gmgn_amount_quote': '0.011594582', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '3HnZaeCC1Q3u9D4tzm2wL88kDzv9cMUqjYMhUCjpeBhBPKzPDZSd2yw8dvAzaMXPNHV974M58yQ73ZH3XfLXkNcq'}
2025-03-25 16:13:57.149 | INFO     | __main__:run:152 - 第 8 页: 成功提取到 50 条数据
2025-03-25 16:13:57.900 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:13:57.900 | INFO     | __main__:run:169 - 等待 2.64 秒后获取下一页...
2025-03-25 16:14:00.544 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:14:00.544 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000026449238528265', 'gmgn_time': '2025-03-24 09:53:16', 'gmgn_amount_base': '55931', 'gmgn_amount_quote': '0.011093606', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '5MtzCJDKL4VyuWgS7m7jQw9c7Pfw79LUqqqqe18MuKCxzorzSnxJWDKPuJqW4LtpCgrCZDJfJh5oMvY78ngQhisa'}
2025-03-25 16:14:00.545 | INFO     | __main__:run:152 - 第 9 页: 成功提取到 50 条数据
2025-03-25 16:14:01.903 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:14:01.903 | INFO     | __main__:run:169 - 等待 2.61 秒后获取下一页...
2025-03-25 16:14:04.511 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:14:04.511 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.00002606543769405', 'gmgn_time': '2025-03-24 09:52:41', 'gmgn_amount_base': '62430', 'gmgn_amount_quote': '0.012189253', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '3ehTvWvKM3LecuxGfnNrC2tgZnaviW9zZuSvjCcZ9YdmB8xLYdgWG7sRgS12YGyBQSRz51HK2P8VWjTb7iWTkdYw'}
2025-03-25 16:14:04.512 | INFO     | __main__:run:152 - 第 10 页: 成功提取到 50 条数据
2025-03-25 16:14:05.298 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:14:05.299 | INFO     | __main__:run:169 - 等待 2.98 秒后获取下一页...
2025-03-25 16:14:08.278 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:14:08.278 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000025651635078888', 'gmgn_time': '2025-03-24 09:52:06', 'gmgn_amount_base': '64862', 'gmgn_amount_quote': '0.012457445', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '3hyMPvVvEM4jkoeEUu7YjAVgFgiw3MAhS8nvLZVufMh2LwDnTbL14uRLnZcFnfudgeeEqVariB3aYY8BZk7EVcG7'}
2025-03-25 16:14:08.278 | INFO     | __main__:run:152 - 第 11 页: 成功提取到 50 条数据
2025-03-25 16:14:09.129 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:14:09.130 | INFO     | __main__:run:169 - 等待 2.57 秒后获取下一页...
2025-03-25 16:14:11.698 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:14:11.698 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000025187719746099', 'gmgn_time': '2025-03-24 09:51:38', 'gmgn_amount_base': '63842', 'gmgn_amount_quote': '0.012038889', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': 'KhAzPjnXafowjGj7A71knP3ZCUhMzSCwqNYJwH8aQpyaC5XxjRhv6fSuPstNusB39tcMgs64teorpXwuVoaqHZu'}
2025-03-25 16:14:11.698 | INFO     | __main__:run:152 - 第 12 页: 成功提取到 50 条数据
2025-03-25 16:14:12.426 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:14:12.426 | INFO     | __main__:run:169 - 等待 2.08 秒后获取下一页...
2025-03-25 16:14:14.506 | INFO     | __main__:parse_data:94 - 成功解析 50 条数据
2025-03-25 16:14:14.506 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000024471217901268', 'gmgn_time': '2025-03-24 09:51:03', 'gmgn_amount_base': '65392', 'gmgn_amount_quote': '0.011989375', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': '3ffuTHmBbScetDDuhvSvGGYgRLXHUsB27aPPaP3feik39SyfsXJFnfufC3jTPcFtdQw2mxGSt1VCQNUfkwCEceCr'}
2025-03-25 16:14:14.506 | INFO     | __main__:run:152 - 第 13 页: 成功提取到 50 条数据
2025-03-25 16:14:16.143 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:14:16.144 | INFO     | __main__:run:169 - 等待 2.63 秒后获取下一页...
2025-03-25 16:14:18.779 | INFO     | __main__:parse_data:94 - 成功解析 27 条数据
2025-03-25 16:14:18.780 | DEBUG    | __main__:parse_data:97 - 数据样例: {'gmgn_price': '0.000024013239208512', 'gmgn_time': '2025-03-24 09:50:33', 'gmgn_amount_base': '68323', 'gmgn_amount_quote': '0.012295088', 'gmgn_swap_type': 'buy', 'gmgn_tx_hash': 'yWTExRADnqVtEuBJXfGZkux4xEARcyAv1imfkxMitdEpRpSzfkSfqC1xCbmqAatTtNXo52UrHMLJHyRjX2ZDqwD'}
2025-03-25 16:14:18.780 | INFO     | __main__:run:152 - 第 14 页: 成功提取到 27 条数据
2025-03-25 16:14:18.780 | INFO     | __main__:run:164 - 没有下一页数据，爬取结束
2025-03-25 16:14:18.780 | SUCCESS  | __main__:run:178 - 爬取完成，共获取 677 条数据
2025-03-25 16:14:18.784 | SUCCESS  | __main__:save_to_csv:127 - 成功保存 677 条数据到文件: gmgn_price_data_1742890458.csv
2025-03-25 16:22:48.829 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:22:48.830 | INFO     | __main__:parse_data:104 - 成功解析 50 条数据
2025-03-25 16:22:48.830 | DEBUG    | __main__:parse_data:107 - 数据样例: {'gmgn_price': '0.000028882581932745', 'gmgn_amount_base': '52169', 'gmgn_amount_quote': '0.011324881', 'gmgn_tx_hash': '2NkpGLi1hpeXA91seEqYcT5yoEdp3ReXcjeiyhE2jm791cm1AtgrvZ4VMsHGZqgsYsvSjJkwstrMeFXaSasJ4ZqS', 'gmgn_time': '2025-03-24 09:57:41', 'gmgn_swap_type': '1'}
2025-03-25 16:22:48.830 | INFO     | __main__:run:162 - 第 1 页: 成功提取到 50 条数据
2025-03-25 16:22:50.366 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:22:50.367 | INFO     | __main__:run:179 - 等待 2.15 秒后获取下一页...
2025-03-25 16:22:52.519 | INFO     | __main__:parse_data:104 - 成功解析 50 条数据
2025-03-25 16:22:52.519 | DEBUG    | __main__:parse_data:107 - 数据样例: {'gmgn_price': '0.000028445329054726', 'gmgn_amount_base': '92584', 'gmgn_amount_quote': '0.019810308', 'gmgn_tx_hash': '2nu5T7sykxndiHMVtMnY73YPgzzKvRSjK9bSMJjzMMjFeHevvCMAGpd6moBG3FygWa8tmCiXEgF3DHmGg19bgioc', 'gmgn_time': '2025-03-24 09:57:03', 'gmgn_swap_type': '1'}
2025-03-25 16:22:52.520 | INFO     | __main__:run:162 - 第 2 页: 成功提取到 50 条数据
2025-03-25 16:22:57.559 | ERROR    | __main__:run:194 - 运行出错: Failed to perform, curl: (35) Recv failure: Connection was reset. See https://curl.se/libcurl/c/libcurl-errors.html first for more details.
2025-03-25 16:24:18.062 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:24:18.065 | INFO     | __main__:parse_data:104 - 成功解析 50 条数据
2025-03-25 16:24:18.065 | DEBUG    | __main__:parse_data:107 - 数据样例: {'gmgn_price': '0.000028882581932745', 'gmgn_amount_base': '52169', 'gmgn_amount_quote': '0.011324881', 'gmgn_tx_hash': '2NkpGLi1hpeXA91seEqYcT5yoEdp3ReXcjeiyhE2jm791cm1AtgrvZ4VMsHGZqgsYsvSjJkwstrMeFXaSasJ4ZqS', 'gmgn_time': '2025-03-24 09:57:41', 'gmgn_swap_type': '1'}
2025-03-25 16:24:18.066 | INFO     | __main__:run:179 - 第 1 页: 成功提取并保存 50 条数据
2025-03-25 16:24:18.693 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:24:18.694 | INFO     | __main__:run:196 - 等待 2.59 秒后获取下一页...
2025-03-25 16:24:21.283 | INFO     | __main__:parse_data:104 - 成功解析 50 条数据
2025-03-25 16:24:21.283 | DEBUG    | __main__:parse_data:107 - 数据样例: {'gmgn_price': '0.000028445329054726', 'gmgn_amount_base': '92584', 'gmgn_amount_quote': '0.019810308', 'gmgn_tx_hash': '2nu5T7sykxndiHMVtMnY73YPgzzKvRSjK9bSMJjzMMjFeHevvCMAGpd6moBG3FygWa8tmCiXEgF3DHmGg19bgioc', 'gmgn_time': '2025-03-24 09:57:03', 'gmgn_swap_type': '1'}
2025-03-25 16:24:21.284 | INFO     | __main__:run:179 - 第 2 页: 成功提取并保存 50 条数据
2025-03-25 16:24:23.866 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:24:23.867 | INFO     | __main__:run:196 - 等待 3.71 秒后获取下一页...
2025-03-25 16:24:27.581 | INFO     | __main__:parse_data:104 - 成功解析 50 条数据
2025-03-25 16:24:27.581 | DEBUG    | __main__:parse_data:107 - 数据样例: {'gmgn_price': '0.000028364303057342', 'gmgn_amount_base': '54251', 'gmgn_amount_quote': '0.011563777', 'gmgn_tx_hash': '2bZb79C5wj72HbzoXT5c34R5pFZVcNor1gvJCGxCAVdtnZgwzaJ4SzcBpVaAia6NxZPYGN9u66wUui2MBRqHyHEP', 'gmgn_time': '2025-03-24 09:56:37', 'gmgn_swap_type': '1'}
2025-03-25 16:24:27.583 | INFO     | __main__:run:179 - 第 3 页: 成功提取并保存 50 条数据
2025-03-25 16:24:28.650 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:24:28.651 | INFO     | __main__:run:196 - 等待 3.87 秒后获取下一页...
2025-03-25 16:24:32.523 | INFO     | __main__:parse_data:104 - 成功解析 50 条数据
2025-03-25 16:24:32.523 | DEBUG    | __main__:parse_data:107 - 数据样例: {'gmgn_price': '0.000027978604437357', 'gmgn_amount_base': '57292', 'gmgn_amount_quote': '0.012026035', 'gmgn_tx_hash': '35z96jgf1Xtugh8kmXAGD3CR8MwvUxZRM1uqSCsvowQ7jm9FCp2PSQY5zDLPgtuhBZNtJsJTmj9cpTt62jYcPqFV', 'gmgn_time': '2025-03-24 09:55:55', 'gmgn_swap_type': '1'}
2025-03-25 16:24:32.524 | INFO     | __main__:run:179 - 第 4 页: 成功提取并保存 50 条数据
2025-03-25 16:24:34.263 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:24:34.263 | INFO     | __main__:run:196 - 等待 2.81 秒后获取下一页...
2025-03-25 16:24:37.076 | INFO     | __main__:parse_data:104 - 成功解析 50 条数据
2025-03-25 16:24:37.076 | DEBUG    | __main__:parse_data:107 - 数据样例: {'gmgn_price': '0.000027554241220452', 'gmgn_amount_base': '53240', 'gmgn_amount_quote': '0.011010942', 'gmgn_tx_hash': '5buRXLmohPnnmBYRbBADJJ3RWmc5UYg8n1B1xgCiaCQeGXBF8H3janEidyN5AAJDMdBLZ1bvCjy5f28LXPSRuUaA', 'gmgn_time': '2025-03-24 09:55:21', 'gmgn_swap_type': '1'}
2025-03-25 16:24:37.077 | INFO     | __main__:run:179 - 第 5 页: 成功提取并保存 50 条数据
2025-03-25 16:24:40.787 | INFO     | __main__:fetch_data:60 - 请求成功,状态码: 200
2025-03-25 16:24:40.788 | INFO     | __main__:run:196 - 等待 3.43 秒后获取下一页...
2025-03-25 16:24:44.214 | INFO     | __main__:parse_data:104 - 成功解析 50 条数据
2025-03-25 16:24:44.214 | DEBUG    | __main__:parse_data:107 - 数据样例: {'gmgn_price': '0.000027174236891208', 'gmgn_amount_base': '58742', 'gmgn_amount_quote': '0.011973215', 'gmgn_tx_hash': '3twZ68Fz4e917RVRugnFbfinYUZ3yJqc15vphL7q7nFWEt3cnCbcsWnpJXEFL6Z4LBTUB3M7wANNSYXKuWaVFJHx', 'gmgn_time': '2025-03-24 09:54:49', 'gmgn_swap_type': '1'}
2025-03-25 16:24:44.216 | INFO     | __main__:run:179 - 第 6 页: 成功提取并保存 50 条数据
2025-03-25 16:24:49.223 | ERROR    | __main__:run:207 - 运行出错: Failed to perform, curl: (35) Recv failure: Connection was reset. See https://curl.se/libcurl/c/libcurl-errors.html first for more details.
2025-03-25 16:27:51.401 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:27:51.403 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:27:51.404 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000028882581932745', 'gmgn_amount_base': '52169', 'gmgn_amount_quote': '0.011324881', 'gmgn_tx_hash': '2NkpGLi1hpeXA91seEqYcT5yoEdp3ReXcjeiyhE2jm791cm1AtgrvZ4VMsHGZqgsYsvSjJkwstrMeFXaSasJ4ZqS', 'gmgn_time': '2025-03-24 09:57:41', 'gmgn_swap_type': '1'}
2025-03-25 16:27:51.404 | INFO     | __main__:run:212 - 第 1 页: 成功提取并保存 50 条数据
2025-03-25 16:27:51.817 | ERROR    | __main__:fetch_data:78 - 请求失败,状态码: 403
2025-03-25 16:27:53.388 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:27:53.390 | INFO     | __main__:run:229 - 等待 2.60 秒后获取下一页...
2025-03-25 16:27:55.987 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:27:55.987 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000028445329054726', 'gmgn_amount_base': '92584', 'gmgn_amount_quote': '0.019810308', 'gmgn_tx_hash': '2nu5T7sykxndiHMVtMnY73YPgzzKvRSjK9bSMJjzMMjFeHevvCMAGpd6moBG3FygWa8tmCiXEgF3DHmGg19bgioc', 'gmgn_time': '2025-03-24 09:57:03', 'gmgn_swap_type': '1'}
2025-03-25 16:27:55.987 | INFO     | __main__:run:212 - 第 2 页: 成功提取并保存 50 条数据
2025-03-25 16:27:56.431 | ERROR    | __main__:fetch_data:78 - 请求失败,状态码: 403
2025-03-25 16:27:57.088 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:27:57.089 | INFO     | __main__:run:229 - 等待 3.84 秒后获取下一页...
2025-03-25 16:28:00.927 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:00.927 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000028364303057342', 'gmgn_amount_base': '54251', 'gmgn_amount_quote': '0.011563777', 'gmgn_tx_hash': '2bZb79C5wj72HbzoXT5c34R5pFZVcNor1gvJCGxCAVdtnZgwzaJ4SzcBpVaAia6NxZPYGN9u66wUui2MBRqHyHEP', 'gmgn_time': '2025-03-24 09:56:37', 'gmgn_swap_type': '1'}
2025-03-25 16:28:00.928 | INFO     | __main__:run:212 - 第 3 页: 成功提取并保存 50 条数据
2025-03-25 16:28:01.535 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:28:01.536 | INFO     | __main__:run:229 - 等待 2.42 秒后获取下一页...
2025-03-25 16:28:03.955 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:03.955 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000027978604437357', 'gmgn_amount_base': '57292', 'gmgn_amount_quote': '0.012026035', 'gmgn_tx_hash': '35z96jgf1Xtugh8kmXAGD3CR8MwvUxZRM1uqSCsvowQ7jm9FCp2PSQY5zDLPgtuhBZNtJsJTmj9cpTt62jYcPqFV', 'gmgn_time': '2025-03-24 09:55:55', 'gmgn_swap_type': '1'}
2025-03-25 16:28:03.956 | INFO     | __main__:run:212 - 第 4 页: 成功提取并保存 50 条数据
2025-03-25 16:28:04.680 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:28:04.681 | INFO     | __main__:run:229 - 等待 3.65 秒后获取下一页...
2025-03-25 16:28:08.331 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:08.332 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000027554241220452', 'gmgn_amount_base': '53240', 'gmgn_amount_quote': '0.011010942', 'gmgn_tx_hash': '5buRXLmohPnnmBYRbBADJJ3RWmc5UYg8n1B1xgCiaCQeGXBF8H3janEidyN5AAJDMdBLZ1bvCjy5f28LXPSRuUaA', 'gmgn_time': '2025-03-24 09:55:21', 'gmgn_swap_type': '1'}
2025-03-25 16:28:08.333 | INFO     | __main__:run:212 - 第 5 页: 成功提取并保存 50 条数据
2025-03-25 16:28:09.000 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:28:09.001 | INFO     | __main__:run:229 - 等待 2.47 秒后获取下一页...
2025-03-25 16:28:11.476 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:11.477 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000027174236891208', 'gmgn_amount_base': '58742', 'gmgn_amount_quote': '0.011973215', 'gmgn_tx_hash': '3twZ68Fz4e917RVRugnFbfinYUZ3yJqc15vphL7q7nFWEt3cnCbcsWnpJXEFL6Z4LBTUB3M7wANNSYXKuWaVFJHx', 'gmgn_time': '2025-03-24 09:54:49', 'gmgn_swap_type': '1'}
2025-03-25 16:28:11.478 | INFO     | __main__:run:212 - 第 6 页: 成功提取并保存 50 条数据
2025-03-25 16:28:12.149 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:28:12.152 | INFO     | __main__:run:229 - 等待 2.08 秒后获取下一页...
2025-03-25 16:28:14.237 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:14.238 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000027153066312592', 'gmgn_amount_base': '55042', 'gmgn_amount_quote': '0.011217045', 'gmgn_tx_hash': '4WxZzxeRZZcifWRtBo5qZH5w3mhHhVZpuvtZPiKodWf1e1vy2XnnsmbTpsBXGgF4jwhkwGSr8F4Pzc4tRKrsVhDJ', 'gmgn_time': '2025-03-24 09:54:20', 'gmgn_swap_type': '1'}
2025-03-25 16:28:14.239 | INFO     | __main__:run:212 - 第 7 页: 成功提取并保存 50 条数据
2025-03-25 16:28:14.878 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:28:14.879 | INFO     | __main__:run:229 - 等待 3.18 秒后获取下一页...
2025-03-25 16:28:18.061 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:18.061 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000026675462000352', 'gmgn_amount_base': '57948', 'gmgn_amount_quote': '0.011594582', 'gmgn_tx_hash': '3HnZaeCC1Q3u9D4tzm2wL88kDzv9cMUqjYMhUCjpeBhBPKzPDZSd2yw8dvAzaMXPNHV974M58yQ73ZH3XfLXkNcq', 'gmgn_time': '2025-03-24 09:53:50', 'gmgn_swap_type': '1'}
2025-03-25 16:28:18.062 | INFO     | __main__:run:212 - 第 8 页: 成功提取并保存 50 条数据
2025-03-25 16:28:18.701 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:28:18.701 | INFO     | __main__:run:229 - 等待 3.89 秒后获取下一页...
2025-03-25 16:28:22.592 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:22.594 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000026449238528265', 'gmgn_amount_base': '55931', 'gmgn_amount_quote': '0.011093606', 'gmgn_tx_hash': '5MtzCJDKL4VyuWgS7m7jQw9c7Pfw79LUqqqqe18MuKCxzorzSnxJWDKPuJqW4LtpCgrCZDJfJh5oMvY78ngQhisa', 'gmgn_time': '2025-03-24 09:53:16', 'gmgn_swap_type': '1'}
2025-03-25 16:28:22.597 | INFO     | __main__:run:212 - 第 9 页: 成功提取并保存 50 条数据
2025-03-25 16:28:23.196 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:28:23.197 | INFO     | __main__:run:229 - 等待 2.21 秒后获取下一页...
2025-03-25 16:28:25.405 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:25.406 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.00002606543769405', 'gmgn_amount_base': '62430', 'gmgn_amount_quote': '0.012189253', 'gmgn_tx_hash': '3ehTvWvKM3LecuxGfnNrC2tgZnaviW9zZuSvjCcZ9YdmB8xLYdgWG7sRgS12YGyBQSRz51HK2P8VWjTb7iWTkdYw', 'gmgn_time': '2025-03-24 09:52:41', 'gmgn_swap_type': '1'}
2025-03-25 16:28:25.407 | INFO     | __main__:run:212 - 第 10 页: 成功提取并保存 50 条数据
2025-03-25 16:28:26.044 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:28:26.045 | INFO     | __main__:run:229 - 等待 3.85 秒后获取下一页...
2025-03-25 16:28:29.900 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:29.900 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000025651635078888', 'gmgn_amount_base': '64862', 'gmgn_amount_quote': '0.012457445', 'gmgn_tx_hash': '3hyMPvVvEM4jkoeEUu7YjAVgFgiw3MAhS8nvLZVufMh2LwDnTbL14uRLnZcFnfudgeeEqVariB3aYY8BZk7EVcG7', 'gmgn_time': '2025-03-24 09:52:06', 'gmgn_swap_type': '1'}
2025-03-25 16:28:29.901 | INFO     | __main__:run:212 - 第 11 页: 成功提取并保存 50 条数据
2025-03-25 16:28:30.565 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:28:30.566 | INFO     | __main__:run:229 - 等待 3.73 秒后获取下一页...
2025-03-25 16:28:34.292 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:28:34.292 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000025187719746099', 'gmgn_amount_base': '63842', 'gmgn_amount_quote': '0.012038889', 'gmgn_tx_hash': 'KhAzPjnXafowjGj7A71knP3ZCUhMzSCwqNYJwH8aQpyaC5XxjRhv6fSuPstNusB39tcMgs64teorpXwuVoaqHZu', 'gmgn_time': '2025-03-24 09:51:38', 'gmgn_swap_type': '1'}
2025-03-25 16:28:34.293 | INFO     | __main__:run:212 - 第 12 页: 成功提取并保存 50 条数据
2025-03-25 16:28:39.312 | WARNING  | __main__:fetch_data:82 - 第 1 次请求失败: Failed to perform, curl: (35) Recv failure: Connection was reset. See https://curl.se/libcurl/c/libcurl-errors.html first for more details., 5 秒后重试...
2025-03-25 16:28:49.327 | WARNING  | __main__:fetch_data:82 - 第 2 次请求失败: Failed to perform, curl: (35) Recv failure: Connection was reset. See https://curl.se/libcurl/c/libcurl-errors.html first for more details., 10 秒后重试...
2025-03-25 16:29:04.336 | ERROR    | __main__:fetch_data:85 - 已重试 3 次仍然失败: Failed to perform, curl: (35) Recv failure: Connection was reset. See https://curl.se/libcurl/c/libcurl-errors.html first for more details.
2025-03-25 16:29:04.336 | INFO     | __main__:run:224 - 没有下一页数据，爬取结束
2025-03-25 16:29:04.336 | SUCCESS  | __main__:run:243 - 爬取完成，共获取并保存 600 条数据到文件: gmgn_price_data_1742891270.csv
2025-03-25 16:32:35.913 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:32:35.915 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:32:35.915 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000028882581932745', 'gmgn_amount_base': '52169', 'gmgn_amount_quote': '0.011324881', 'gmgn_tx_hash': '2NkpGLi1hpeXA91seEqYcT5yoEdp3ReXcjeiyhE2jm791cm1AtgrvZ4VMsHGZqgsYsvSjJkwstrMeFXaSasJ4ZqS', 'gmgn_time': '2025-03-24 09:57:41', 'gmgn_swap_type': '1'}
2025-03-25 16:32:35.916 | INFO     | __main__:run:213 - 第 1 页: 成功提取并保存 50 条数据
2025-03-25 16:32:35.916 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwOTQ2MDkzNDAwMDA=
2025-03-25 16:32:36.335 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:32:36.335 | INFO     | __main__:run:230 - 等待 3.01 秒后获取下一页...
2025-03-25 16:32:39.348 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:32:39.348 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000028445329054726', 'gmgn_amount_base': '92584', 'gmgn_amount_quote': '0.019810308', 'gmgn_tx_hash': '2nu5T7sykxndiHMVtMnY73YPgzzKvRSjK9bSMJjzMMjFeHevvCMAGpd6moBG3FygWa8tmCiXEgF3DHmGg19bgioc', 'gmgn_time': '2025-03-24 09:57:03', 'gmgn_swap_type': '1'}
2025-03-25 16:32:39.349 | INFO     | __main__:run:213 - 第 2 页: 成功提取并保存 50 条数据
2025-03-25 16:32:39.349 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwODgxMDMyMzAwMDA=
2025-03-25 16:32:40.133 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:32:40.133 | INFO     | __main__:run:230 - 等待 3.22 秒后获取下一页...
2025-03-25 16:32:43.357 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:32:43.357 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000028364303057342', 'gmgn_amount_base': '54251', 'gmgn_amount_quote': '0.011563777', 'gmgn_tx_hash': '2bZb79C5wj72HbzoXT5c34R5pFZVcNor1gvJCGxCAVdtnZgwzaJ4SzcBpVaAia6NxZPYGN9u66wUui2MBRqHyHEP', 'gmgn_time': '2025-03-24 09:56:37', 'gmgn_swap_type': '1'}
2025-03-25 16:32:43.358 | INFO     | __main__:run:213 - 第 3 页: 成功提取并保存 50 条数据
2025-03-25 16:32:43.358 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwNzcwMTM1ODAwMDA=
2025-03-25 16:32:43.806 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:32:43.807 | INFO     | __main__:run:230 - 等待 2.04 秒后获取下一页...
2025-03-25 16:32:45.849 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:32:45.849 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000027978604437357', 'gmgn_amount_base': '57292', 'gmgn_amount_quote': '0.012026035', 'gmgn_tx_hash': '35z96jgf1Xtugh8kmXAGD3CR8MwvUxZRM1uqSCsvowQ7jm9FCp2PSQY5zDLPgtuhBZNtJsJTmj9cpTt62jYcPqFV', 'gmgn_time': '2025-03-24 09:55:55', 'gmgn_swap_type': '1'}
2025-03-25 16:32:45.850 | INFO     | __main__:run:213 - 第 4 页: 成功提取并保存 50 条数据
2025-03-25 16:32:45.855 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwNjg3MDkxODAwMDA=
2025-03-25 16:32:46.263 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:32:46.265 | INFO     | __main__:run:230 - 等待 2.29 秒后获取下一页...
2025-03-25 16:32:48.555 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:32:48.555 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000027554241220452', 'gmgn_amount_base': '53240', 'gmgn_amount_quote': '0.011010942', 'gmgn_tx_hash': '5buRXLmohPnnmBYRbBADJJ3RWmc5UYg8n1B1xgCiaCQeGXBF8H3janEidyN5AAJDMdBLZ1bvCjy5f28LXPSRuUaA', 'gmgn_time': '2025-03-24 09:55:21', 'gmgn_swap_type': '1'}
2025-03-25 16:32:48.556 | INFO     | __main__:run:213 - 第 5 页: 成功提取并保存 50 条数据
2025-03-25 16:32:48.556 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwNjA4MDEwNDAwMDA=
2025-03-25 16:32:48.994 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:32:48.995 | INFO     | __main__:run:230 - 等待 3.28 秒后获取下一页...
2025-03-25 16:32:52.276 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:32:52.276 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000027174236891208', 'gmgn_amount_base': '58742', 'gmgn_amount_quote': '0.011973215', 'gmgn_tx_hash': '3twZ68Fz4e917RVRugnFbfinYUZ3yJqc15vphL7q7nFWEt3cnCbcsWnpJXEFL6Z4LBTUB3M7wANNSYXKuWaVFJHx', 'gmgn_time': '2025-03-24 09:54:49', 'gmgn_swap_type': '1'}
2025-03-25 16:32:52.277 | INFO     | __main__:run:213 - 第 6 页: 成功提取并保存 50 条数据
2025-03-25 16:32:52.278 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwNTM2MTg1MzAwMDA=
2025-03-25 16:32:52.687 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:32:52.688 | INFO     | __main__:run:230 - 等待 3.12 秒后获取下一页...
2025-03-25 16:32:55.806 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:32:55.806 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000027153066312592', 'gmgn_amount_base': '55042', 'gmgn_amount_quote': '0.011217045', 'gmgn_tx_hash': '4WxZzxeRZZcifWRtBo5qZH5w3mhHhVZpuvtZPiKodWf1e1vy2XnnsmbTpsBXGgF4jwhkwGSr8F4Pzc4tRKrsVhDJ', 'gmgn_time': '2025-03-24 09:54:20', 'gmgn_swap_type': '1'}
2025-03-25 16:32:55.807 | INFO     | __main__:run:213 - 第 7 页: 成功提取并保存 50 条数据
2025-03-25 16:32:55.808 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwNDYyMTQ4NjAwMDA=
2025-03-25 16:32:56.206 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:32:56.206 | INFO     | __main__:run:230 - 等待 2.07 秒后获取下一页...
2025-03-25 16:32:58.275 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:32:58.276 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000026675462000352', 'gmgn_amount_base': '57948', 'gmgn_amount_quote': '0.011594582', 'gmgn_tx_hash': '3HnZaeCC1Q3u9D4tzm2wL88kDzv9cMUqjYMhUCjpeBhBPKzPDZSd2yw8dvAzaMXPNHV974M58yQ73ZH3XfLXkNcq', 'gmgn_time': '2025-03-24 09:53:50', 'gmgn_swap_type': '1'}
2025-03-25 16:32:58.276 | INFO     | __main__:run:213 - 第 8 页: 成功提取并保存 50 条数据
2025-03-25 16:32:58.277 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwMzc2MDQ4NzAwMDA=
2025-03-25 16:32:58.749 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:32:58.749 | INFO     | __main__:run:230 - 等待 2.70 秒后获取下一页...
2025-03-25 16:33:01.452 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:33:01.452 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000026449238528265', 'gmgn_amount_base': '55931', 'gmgn_amount_quote': '0.011093606', 'gmgn_tx_hash': '5MtzCJDKL4VyuWgS7m7jQw9c7Pfw79LUqqqqe18MuKCxzorzSnxJWDKPuJqW4LtpCgrCZDJfJh5oMvY78ngQhisa', 'gmgn_time': '2025-03-24 09:53:16', 'gmgn_swap_type': '1'}
2025-03-25 16:33:01.453 | INFO     | __main__:run:213 - 第 9 页: 成功提取并保存 50 条数据
2025-03-25 16:33:01.453 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwMjg5MDI2MTAwMDA=
2025-03-25 16:33:01.804 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:33:01.805 | INFO     | __main__:run:230 - 等待 3.50 秒后获取下一页...
2025-03-25 16:33:05.303 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:33:05.304 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.00002606543769405', 'gmgn_amount_base': '62430', 'gmgn_amount_quote': '0.012189253', 'gmgn_tx_hash': '3ehTvWvKM3LecuxGfnNrC2tgZnaviW9zZuSvjCcZ9YdmB8xLYdgWG7sRgS12YGyBQSRz51HK2P8VWjTb7iWTkdYw', 'gmgn_time': '2025-03-24 09:52:41', 'gmgn_swap_type': '1'}
2025-03-25 16:33:05.305 | INFO     | __main__:run:213 - 第 10 页: 成功提取并保存 50 条数据
2025-03-25 16:33:05.305 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwMjA2MDQ4NDAwMDA=
2025-03-25 16:33:05.708 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:33:05.709 | INFO     | __main__:run:230 - 等待 2.75 秒后获取下一页...
2025-03-25 16:33:08.460 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:33:08.460 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000025651635078888', 'gmgn_amount_base': '64862', 'gmgn_amount_quote': '0.012457445', 'gmgn_tx_hash': '3hyMPvVvEM4jkoeEUu7YjAVgFgiw3MAhS8nvLZVufMh2LwDnTbL14uRLnZcFnfudgeeEqVariB3aYY8BZk7EVcG7', 'gmgn_time': '2025-03-24 09:52:06', 'gmgn_swap_type': '1'}
2025-03-25 16:33:08.461 | INFO     | __main__:run:213 - 第 11 页: 成功提取并保存 50 条数据
2025-03-25 16:33:08.461 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwMTMwMDE1NjAwMDA=
2025-03-25 16:33:09.220 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:33:09.221 | INFO     | __main__:run:230 - 等待 3.38 秒后获取下一页...
2025-03-25 16:33:12.605 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:33:12.605 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000025187719746099', 'gmgn_amount_base': '63842', 'gmgn_amount_quote': '0.012038889', 'gmgn_tx_hash': 'KhAzPjnXafowjGj7A71knP3ZCUhMzSCwqNYJwH8aQpyaC5XxjRhv6fSuPstNusB39tcMgs64teorpXwuVoaqHZu', 'gmgn_time': '2025-03-24 09:51:38', 'gmgn_swap_type': '1'}
2025-03-25 16:33:12.606 | INFO     | __main__:run:213 - 第 12 页: 成功提取并保存 50 条数据
2025-03-25 16:33:12.607 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzcwMDQyMTMxMjAwMDA=
2025-03-25 16:33:12.981 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:33:12.982 | INFO     | __main__:run:230 - 等待 3.65 秒后获取下一页...
2025-03-25 16:33:16.628 | INFO     | __main__:parse_data:125 - 成功解析 50 条数据
2025-03-25 16:33:16.628 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000024471217901268', 'gmgn_amount_base': '65392', 'gmgn_amount_quote': '0.011989375', 'gmgn_tx_hash': '3ffuTHmBbScetDDuhvSvGGYgRLXHUsB27aPPaP3feik39SyfsXJFnfufC3jTPcFtdQw2mxGSt1VCQNUfkwCEceCr', 'gmgn_time': '2025-03-24 09:51:03', 'gmgn_swap_type': '1'}
2025-03-25 16:33:16.631 | INFO     | __main__:run:213 - 第 13 页: 成功提取并保存 50 条数据
2025-03-25 16:33:16.631 | INFO     | __main__:get_next_page:138 - 获取下一页数据: MzI4NzY5OTY3MTQyMjAwMDA=
2025-03-25 16:33:17.055 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:33:17.056 | INFO     | __main__:run:230 - 等待 3.30 秒后获取下一页...
2025-03-25 16:33:20.356 | INFO     | __main__:parse_data:125 - 成功解析 27 条数据
2025-03-25 16:33:20.357 | DEBUG    | __main__:parse_data:128 - 数据样例: {'gmgn_price': '0.000024013239208512', 'gmgn_amount_base': '68323', 'gmgn_amount_quote': '0.012295088', 'gmgn_tx_hash': 'yWTExRADnqVtEuBJXfGZkux4xEARcyAv1imfkxMitdEpRpSzfkSfqC1xCbmqAatTtNXo52UrHMLJHyRjX2ZDqwD', 'gmgn_time': '2025-03-24 09:50:33', 'gmgn_swap_type': '1'}
2025-03-25 16:33:20.359 | INFO     | __main__:run:213 - 第 14 页: 成功提取并保存 27 条数据
2025-03-25 16:33:20.359 | INFO     | __main__:run:225 - 没有下一页数据，爬取结束
2025-03-25 16:33:20.359 | SUCCESS  | __main__:run:244 - 爬取完成，共获取并保存 677 条数据到文件: gmgn_price_data_1742891555.csv
2025-03-25 16:41:03.870 | ERROR    | __main__:fetch_data:78 - 请求失败,状态码: 500
2025-03-25 16:41:04.768 | ERROR    | __main__:fetch_data:78 - 请求失败,状态码: 500
2025-03-25 16:41:05.125 | ERROR    | __main__:fetch_data:78 - 请求失败,状态码: 500
2025-03-25 16:41:05.125 | ERROR    | __main__:run:197 - 获取第一页数据失败
2025-03-25 16:41:22.503 | ERROR    | __main__:fetch_data:78 - 请求失败,状态码: 500
2025-03-25 16:41:22.910 | ERROR    | __main__:fetch_data:78 - 请求失败,状态码: 500
2025-03-25 16:41:23.365 | ERROR    | __main__:fetch_data:78 - 请求失败,状态码: 500
2025-03-25 16:41:23.365 | ERROR    | __main__:run:197 - 获取第一页数据失败
2025-03-25 16:42:01.332 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:01.345 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:01.348 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000003827243360224', 'gmgn_amount_usd': '0.23568547338', 'gmgn_amount_base': '61581', 'gmgn_amount_quote': '0.001771938', 'gmgn_tx_hash': '4G9kjwfhnYUpjoeXAQsHm4MwKfQoCEJE41ckjWBjyQkBpgUvhcQ9iRTfmgg6wRv8jfW1QBq8VztVdZGoPxfnYLPr', 'gmgn_time': '2025-03-24 09:59:40', 'gmgn_swap_type': '2'}
2025-03-25 16:42:01.349 | INFO     | __main__:run:216 - 第 1 页: 成功提取并保存 50 条数据
2025-03-25 16:42:01.350 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcxMzI2MTA0OTAwMDE=
2025-03-25 16:42:01.759 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:01.760 | INFO     | __main__:run:233 - 等待 3.12 秒后获取下一页...
2025-03-25 16:42:04.882 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:04.882 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.00000385125187804', 'gmgn_amount_usd': '0.26927182848', 'gmgn_amount_base': '69918', 'gmgn_amount_quote': '0.002024448', 'gmgn_tx_hash': '5WGDc6Sa6NG1JqozmSWDzvE6bsWTwH8UBTCms6xTcQhkQ1FdxuqFmPVtSz1XJdURBXH9Wf725zsMo4FkBPgk8mmw', 'gmgn_time': '2025-03-24 09:59:40', 'gmgn_swap_type': '2'}
2025-03-25 16:42:04.883 | INFO     | __main__:run:216 - 第 2 页: 成功提取并保存 50 条数据
2025-03-25 16:42:04.883 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcxMzI2MDk5MjAwMDM=
2025-03-25 16:42:05.342 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:05.342 | INFO     | __main__:run:233 - 等待 2.54 秒后获取下一页...
2025-03-25 16:42:07.887 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:07.888 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000004084354323822', 'gmgn_amount_usd': '3.08800059504', 'gmgn_amount_base': '756056', 'gmgn_amount_quote': '0.023216304', 'gmgn_tx_hash': '4uJpXzF1rXGvn9LSsGiZsUZEfHVpK5f8yjBgDHSajBpu7VqyGAaEp7FzzM8eQuDC13STi4h5iT4uYNRKHM5Ujij7', 'gmgn_time': '2025-03-24 09:59:40', 'gmgn_swap_type': '2'}
2025-03-25 16:42:07.889 | INFO     | __main__:run:216 - 第 3 页: 成功提取并保存 50 条数据
2025-03-25 16:42:07.889 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcxMzI2MDg0OTAwMDE=
2025-03-25 16:42:08.322 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:08.322 | INFO     | __main__:run:233 - 等待 3.26 秒后获取下一页...
2025-03-25 16:42:11.584 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:11.584 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000008467250936045', 'gmgn_amount_usd': '93.55465566279', 'gmgn_amount_base': '11049000', 'gmgn_amount_quote': '0.703365579', 'gmgn_tx_hash': 'Gm3JNzWAzBiBd2FUMwVVYFnnTbJijjjbN8pcDY8QuL5SHCb7Lsx2fcxy2yN6we8CK2newUv2M15UhqAjqMzFSZ6', 'gmgn_time': '2025-03-24 09:59:40', 'gmgn_swap_type': '2'}
2025-03-25 16:42:11.585 | INFO     | __main__:run:216 - 第 4 页: 成功提取并保存 50 条数据
2025-03-25 16:42:11.586 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcxMjg2MTQ3MzAwMDA=
2025-03-25 16:42:12.103 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:12.104 | INFO     | __main__:run:233 - 等待 2.95 秒后获取下一页...
2025-03-25 16:42:15.058 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:15.058 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.00003083609210832', 'gmgn_amount_usd': '1.47288593952', 'gmgn_amount_base': '47765', 'gmgn_amount_quote': '0.011077662', 'gmgn_tx_hash': '2ucPHaZUcNZZ5jHbyxKmgMAG6h7hr66Bt59qBhdV2HJTRV7XUMswR8UDGor8fRXt8v1SMcmh4EpRnFVQVSsMh99Y', 'gmgn_time': '2025-03-24 09:59:22', 'gmgn_swap_type': '1'}
2025-03-25 16:42:15.059 | INFO     | __main__:run:216 - 第 5 页: 成功提取并保存 50 条数据
2025-03-25 16:42:15.060 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcxMTk5MDAwMjAwMDA=
2025-03-25 16:42:15.530 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:15.531 | INFO     | __main__:run:233 - 等待 2.90 秒后获取下一页...
2025-03-25 16:42:18.434 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:18.435 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000030155335905123', 'gmgn_amount_usd': '1.47040433397', 'gmgn_amount_base': '48761', 'gmgn_amount_quote': '0.011056503', 'gmgn_tx_hash': '3VXYUF3cQ7PbJWexm6teWd7WGfQYNeX2zrFeabmUD3hKM2pQUWm6HAJqq544q2SRxLHEmXsiExtKWgFGTs8zYPH3', 'gmgn_time': '2025-03-24 09:58:48', 'gmgn_swap_type': '1'}
2025-03-25 16:42:18.436 | INFO     | __main__:run:216 - 第 6 页: 成功提取并保存 50 条数据
2025-03-25 16:42:18.436 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcxMTExMDE2NzAwMDA=
2025-03-25 16:42:18.851 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:18.851 | INFO     | __main__:run:233 - 等待 3.23 秒后获取下一页...
2025-03-25 16:42:22.081 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:22.081 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.00002970337663904', 'gmgn_amount_usd': '1.66582476836', 'gmgn_amount_base': '56082', 'gmgn_amount_quote': '0.012526882', 'gmgn_tx_hash': '46CTF4NWMBti88Ehqfb5nPjd3xfDjtaRmdtx7MxFBuf7LBV8XCaXH4sUm8SxV7WixRhZDjsEP9N1bR4TjpJhQY1q', 'gmgn_time': '2025-03-24 09:58:12', 'gmgn_swap_type': '1'}
2025-03-25 16:42:22.083 | INFO     | __main__:run:216 - 第 7 页: 成功提取并保存 50 条数据
2025-03-25 16:42:22.084 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcxMDM1MTM0MzAwMDA=
2025-03-25 16:42:22.494 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:22.495 | INFO     | __main__:run:233 - 等待 3.23 秒后获取下一页...
2025-03-25 16:42:25.952 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:25.953 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000028882581932745', 'gmgn_amount_usd': '1.50677541705', 'gmgn_amount_base': '52169', 'gmgn_amount_quote': '0.011324881', 'gmgn_tx_hash': '2NkpGLi1hpeXA91seEqYcT5yoEdp3ReXcjeiyhE2jm791cm1AtgrvZ4VMsHGZqgsYsvSjJkwstrMeFXaSasJ4ZqS', 'gmgn_time': '2025-03-24 09:57:41', 'gmgn_swap_type': '1'}
2025-03-25 16:42:25.957 | INFO     | __main__:run:216 - 第 8 页: 成功提取并保存 50 条数据
2025-03-25 16:42:25.961 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwOTQ2MDkzNDAwMDA=
2025-03-25 16:42:26.330 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:26.330 | INFO     | __main__:run:233 - 等待 3.10 秒后获取下一页...
2025-03-25 16:42:29.435 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:29.440 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000028445329054726', 'gmgn_amount_usd': '2.63358234552', 'gmgn_amount_base': '92584', 'gmgn_amount_quote': '0.019810308', 'gmgn_tx_hash': '2nu5T7sykxndiHMVtMnY73YPgzzKvRSjK9bSMJjzMMjFeHevvCMAGpd6moBG3FygWa8tmCiXEgF3DHmGg19bgioc', 'gmgn_time': '2025-03-24 09:57:03', 'gmgn_swap_type': '1'}
2025-03-25 16:42:29.441 | INFO     | __main__:run:216 - 第 9 页: 成功提取并保存 50 条数据
2025-03-25 16:42:29.442 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwODgxMDMyMzAwMDA=
2025-03-25 16:42:30.068 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:30.068 | INFO     | __main__:run:233 - 等待 3.04 秒后获取下一页...
2025-03-25 16:42:33.111 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:33.111 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000028364303057342', 'gmgn_amount_usd': '1.53879180539', 'gmgn_amount_base': '54251', 'gmgn_amount_quote': '0.011563777', 'gmgn_tx_hash': '2bZb79C5wj72HbzoXT5c34R5pFZVcNor1gvJCGxCAVdtnZgwzaJ4SzcBpVaAia6NxZPYGN9u66wUui2MBRqHyHEP', 'gmgn_time': '2025-03-24 09:56:37', 'gmgn_swap_type': '1'}
2025-03-25 16:42:33.113 | INFO     | __main__:run:216 - 第 10 页: 成功提取并保存 50 条数据
2025-03-25 16:42:33.113 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwNzcwMTM1ODAwMDA=
2025-03-25 16:42:33.512 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:33.512 | INFO     | __main__:run:233 - 等待 3.86 秒后获取下一页...
2025-03-25 16:42:37.376 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:37.377 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000027978604437357', 'gmgn_amount_usd': '1.60295020515', 'gmgn_amount_base': '57292', 'gmgn_amount_quote': '0.012026035', 'gmgn_tx_hash': '35z96jgf1Xtugh8kmXAGD3CR8MwvUxZRM1uqSCsvowQ7jm9FCp2PSQY5zDLPgtuhBZNtJsJTmj9cpTt62jYcPqFV', 'gmgn_time': '2025-03-24 09:55:55', 'gmgn_swap_type': '1'}
2025-03-25 16:42:37.377 | INFO     | __main__:run:216 - 第 11 页: 成功提取并保存 50 条数据
2025-03-25 16:42:37.379 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwNjg3MDkxODAwMDA=
2025-03-25 16:42:37.769 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:37.771 | INFO     | __main__:run:233 - 等待 3.51 秒后获取下一页...
2025-03-25 16:42:41.282 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:41.282 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000027554241220452', 'gmgn_amount_usd': '1.46698780266', 'gmgn_amount_base': '53240', 'gmgn_amount_quote': '0.011010942', 'gmgn_tx_hash': '5buRXLmohPnnmBYRbBADJJ3RWmc5UYg8n1B1xgCiaCQeGXBF8H3janEidyN5AAJDMdBLZ1bvCjy5f28LXPSRuUaA', 'gmgn_time': '2025-03-24 09:55:21', 'gmgn_swap_type': '1'}
2025-03-25 16:42:41.286 | INFO     | __main__:run:216 - 第 12 页: 成功提取并保存 50 条数据
2025-03-25 16:42:41.286 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwNjA4MDEwNDAwMDA=
2025-03-25 16:42:41.653 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:41.654 | INFO     | __main__:run:233 - 等待 2.57 秒后获取下一页...
2025-03-25 16:42:44.221 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:44.221 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000027174236891208', 'gmgn_amount_usd': '1.5962690238', 'gmgn_amount_base': '58742', 'gmgn_amount_quote': '0.011973215', 'gmgn_tx_hash': '3twZ68Fz4e917RVRugnFbfinYUZ3yJqc15vphL7q7nFWEt3cnCbcsWnpJXEFL6Z4LBTUB3M7wANNSYXKuWaVFJHx', 'gmgn_time': '2025-03-24 09:54:49', 'gmgn_swap_type': '1'}
2025-03-25 16:42:44.222 | INFO     | __main__:run:216 - 第 13 页: 成功提取并保存 50 条数据
2025-03-25 16:42:44.222 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwNTM2MTg1MzAwMDA=
2025-03-25 16:42:44.636 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:44.637 | INFO     | __main__:run:233 - 等待 3.10 秒后获取下一页...
2025-03-25 16:42:47.733 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:47.734 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000027153066312592', 'gmgn_amount_usd': '1.4945590758', 'gmgn_amount_base': '55042', 'gmgn_amount_quote': '0.011217045', 'gmgn_tx_hash': '4WxZzxeRZZcifWRtBo5qZH5w3mhHhVZpuvtZPiKodWf1e1vy2XnnsmbTpsBXGgF4jwhkwGSr8F4Pzc4tRKrsVhDJ', 'gmgn_time': '2025-03-24 09:54:20', 'gmgn_swap_type': '1'}
2025-03-25 16:42:47.735 | INFO     | __main__:run:216 - 第 14 页: 成功提取并保存 50 条数据
2025-03-25 16:42:47.735 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwNDYyMTQ4NjAwMDA=
2025-03-25 16:42:48.754 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:48.755 | INFO     | __main__:run:233 - 等待 3.11 秒后获取下一页...
2025-03-25 16:42:51.865 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:51.865 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000026675462000352', 'gmgn_amount_usd': '1.54578967224', 'gmgn_amount_base': '57948', 'gmgn_amount_quote': '0.011594582', 'gmgn_tx_hash': '3HnZaeCC1Q3u9D4tzm2wL88kDzv9cMUqjYMhUCjpeBhBPKzPDZSd2yw8dvAzaMXPNHV974M58yQ73ZH3XfLXkNcq', 'gmgn_time': '2025-03-24 09:53:50', 'gmgn_swap_type': '1'}
2025-03-25 16:42:51.869 | INFO     | __main__:run:216 - 第 15 页: 成功提取并保存 50 条数据
2025-03-25 16:42:51.869 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwMzc2MDQ4NzAwMDA=
2025-03-25 16:42:52.347 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:52.348 | INFO     | __main__:run:233 - 等待 2.40 秒后获取下一页...
2025-03-25 16:42:54.747 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:54.747 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000026449238528265', 'gmgn_amount_usd': '1.4793323601', 'gmgn_amount_base': '55931', 'gmgn_amount_quote': '0.011093606', 'gmgn_tx_hash': '5MtzCJDKL4VyuWgS7m7jQw9c7Pfw79LUqqqqe18MuKCxzorzSnxJWDKPuJqW4LtpCgrCZDJfJh5oMvY78ngQhisa', 'gmgn_time': '2025-03-24 09:53:16', 'gmgn_swap_type': '1'}
2025-03-25 16:42:54.748 | INFO     | __main__:run:216 - 第 16 页: 成功提取并保存 50 条数据
2025-03-25 16:42:54.748 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwMjg5MDI2MTAwMDA=
2025-03-25 16:42:55.332 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:55.332 | INFO     | __main__:run:233 - 等待 2.54 秒后获取下一页...
2025-03-25 16:42:57.873 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:42:57.873 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.00002606543769405', 'gmgn_amount_usd': '1.6272652755', 'gmgn_amount_base': '62430', 'gmgn_amount_quote': '0.012189253', 'gmgn_tx_hash': '3ehTvWvKM3LecuxGfnNrC2tgZnaviW9zZuSvjCcZ9YdmB8xLYdgWG7sRgS12YGyBQSRz51HK2P8VWjTb7iWTkdYw', 'gmgn_time': '2025-03-24 09:52:41', 'gmgn_swap_type': '1'}
2025-03-25 16:42:57.874 | INFO     | __main__:run:216 - 第 17 页: 成功提取并保存 50 条数据
2025-03-25 16:42:57.875 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwMjA2MDQ4NDAwMDA=
2025-03-25 16:42:58.273 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:42:58.274 | INFO     | __main__:run:233 - 等待 3.02 秒后获取下一页...
2025-03-25 16:43:01.291 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:43:01.292 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000025651635078888', 'gmgn_amount_usd': '1.6638163542', 'gmgn_amount_base': '64862', 'gmgn_amount_quote': '0.012457445', 'gmgn_tx_hash': '3hyMPvVvEM4jkoeEUu7YjAVgFgiw3MAhS8nvLZVufMh2LwDnTbL14uRLnZcFnfudgeeEqVariB3aYY8BZk7EVcG7', 'gmgn_time': '2025-03-24 09:52:06', 'gmgn_swap_type': '1'}
2025-03-25 16:43:01.298 | INFO     | __main__:run:216 - 第 18 页: 成功提取并保存 50 条数据
2025-03-25 16:43:01.298 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwMTMwMDE1NjAwMDA=
2025-03-25 16:43:01.733 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:43:01.734 | INFO     | __main__:run:233 - 等待 3.64 秒后获取下一页...
2025-03-25 16:43:05.379 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:43:05.380 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000025187719746099', 'gmgn_amount_usd': '1.60803440373', 'gmgn_amount_base': '63842', 'gmgn_amount_quote': '0.012038889', 'gmgn_tx_hash': 'KhAzPjnXafowjGj7A71knP3ZCUhMzSCwqNYJwH8aQpyaC5XxjRhv6fSuPstNusB39tcMgs64teorpXwuVoaqHZu', 'gmgn_time': '2025-03-24 09:51:38', 'gmgn_swap_type': '1'}
2025-03-25 16:43:05.381 | INFO     | __main__:run:216 - 第 19 页: 成功提取并保存 50 条数据
2025-03-25 16:43:05.381 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzcwMDQyMTMxMjAwMDA=
2025-03-25 16:43:05.759 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:43:05.760 | INFO     | __main__:run:233 - 等待 2.61 秒后获取下一页...
2025-03-25 16:43:08.374 | INFO     | __main__:parse_data:126 - 成功解析 50 条数据
2025-03-25 16:43:08.375 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000024471217901268', 'gmgn_amount_usd': '1.60022188125', 'gmgn_amount_base': '65392', 'gmgn_amount_quote': '0.011989375', 'gmgn_tx_hash': '3ffuTHmBbScetDDuhvSvGGYgRLXHUsB27aPPaP3feik39SyfsXJFnfufC3jTPcFtdQw2mxGSt1VCQNUfkwCEceCr', 'gmgn_time': '2025-03-24 09:51:03', 'gmgn_swap_type': '1'}
2025-03-25 16:43:08.379 | INFO     | __main__:run:216 - 第 20 页: 成功提取并保存 50 条数据
2025-03-25 16:43:08.382 | INFO     | __main__:get_next_page:139 - 获取下一页数据: MzI4NzY5OTY3MTQyMjAwMDA=
2025-03-25 16:43:08.604 | ERROR    | __main__:fetch_data:78 - 请求失败,状态码: 403
2025-03-25 16:43:09.012 | INFO     | __main__:fetch_data:74 - 请求成功,状态码: 200
2025-03-25 16:43:09.014 | INFO     | __main__:run:233 - 等待 2.94 秒后获取下一页...
2025-03-25 16:43:11.958 | INFO     | __main__:parse_data:126 - 成功解析 27 条数据
2025-03-25 16:43:11.958 | DEBUG    | __main__:parse_data:129 - 数据样例: {'gmgn_price_usd': '0.000024013239208512', 'gmgn_amount_usd': '1.64065654272', 'gmgn_amount_base': '68323', 'gmgn_amount_quote': '0.012295088', 'gmgn_tx_hash': 'yWTExRADnqVtEuBJXfGZkux4xEARcyAv1imfkxMitdEpRpSzfkSfqC1xCbmqAatTtNXo52UrHMLJHyRjX2ZDqwD', 'gmgn_time': '2025-03-24 09:50:33', 'gmgn_swap_type': '1'}
2025-03-25 16:43:11.959 | INFO     | __main__:run:216 - 第 21 页: 成功提取并保存 27 条数据
2025-03-25 16:43:11.959 | INFO     | __main__:run:228 - 没有下一页数据，爬取结束
2025-03-25 16:43:11.960 | SUCCESS  | __main__:run:247 - 爬取完成，共获取并保存 1027 条数据到文件: gmgn_price_data_1742892120.csv
