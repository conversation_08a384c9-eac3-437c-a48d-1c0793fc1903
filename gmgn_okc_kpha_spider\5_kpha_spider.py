import requests
import json
from datetime import datetime, timezone, timedelta
import sqlite3
from loguru import logger


proxy = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

headers = {
    "authority": "valuescan.ai",
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json;charset=UTF-8",
    "origin": "https://valuescan.ai",
    "referer": "https://valuescan.ai/token/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}

class KPHASpider:
    def __init__(self):
        self.url = "https://valuescan.ai/api/kline/history"
        self.db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        self.time_tolerance = 0.1 # 设置时间误差为2秒

    def parse_data(self):
        data = {
            "symbol": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
            "resolution": "1S",
            "end": "2025-05-12 14:25:00",  # 这里可以修改查询的结束时间
            "size": 44  # 查询数据量
        }
        
        data = json.dumps(data, separators=(',', ':'))
        response = requests.post(self.url, headers=headers, data=data, proxies=proxy)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                logger.info(f'访问成功,状态码:{response.status_code},正在获取数据')
                # 打印第一条和最后一条数据的时间，方便确认
                if result["data"]["kline"]:
                    first_item = result["data"]["kline"][0]
                    last_item = result["data"]["kline"][-1]
                    logger.info(f'第一条数据时间: {first_item[6]} (API返回时间)')
                    logger.info(f'最后一条数据时间: {last_item[6]} (API返回时间)')
                self.save_to_sqlite(result["data"]["kline"])
            else:
                logger.error(f'请求失败,错误信息:{result.get("msg")}')
        else:
            logger.error(f'请求失败,状态码:{response.status_code}')

    def find_closest_timestamp(self, cursor, target_time, target_datetime):
        """查找最接近的时间戳记录（误差2秒内）"""
        time_range = timedelta(seconds=self.time_tolerance)
        start_time = target_datetime - time_range
        end_time = target_datetime + time_range
        
        query = """
            SELECT id, timestamp 
            FROM gmgn_data_2 
            WHERE token_symbol = 'SOL'
            AND timestamp BETWEEN ? AND ?
            ORDER BY ABS(strftime('%s', timestamp) - ?)
            LIMIT 1
        """
        
        # 转换为本地时间字符串
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.execute(query, (
            start_time_str,
            end_time_str,
            int(target_datetime.timestamp())
        ))
        
        return cursor.fetchone()

    def save_to_sqlite(self, data_list):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 记录匹配和未匹配的数量
        matched_count = 0
        unmatched_count = 0
        
        for item in data_list:
            # API返回的时间格式: ["1747031352000","0.00007155446136378394",...,"2025-05-12 10:49:13"]
            # 从API返回的时间字符串中获取时间
            api_time_str = item[6]  # 第7个元素是格式化的时间字符串
            timestamp = int(item[0]) / 1000  # 第1个元素是时间戳
            
            # 解析API返回的时间字符串为datetime对象
            dt = datetime.strptime(api_time_str, '%Y-%m-%d %H:%M:%S')
            # 添加北京时区信息
            beijing_tz = timezone(timedelta(hours=8))
            dt = dt.replace(tzinfo=beijing_tz)
            
            # 查找最接近的时间戳记录
            closest_record = self.find_closest_timestamp(cursor, timestamp, dt)
            
            if closest_record:
                record_id, record_time = closest_record
                # 更新找到的记录
                cursor.execute("""
                    UPDATE gmgn_data_2 
                    SET ha_open_price = ?,
                        ha_high_price = ?,
                        ha_low_price = ?,
                        ha_close_price = ?
                    WHERE id = ?
                """, (
                    float(item[1]),  # ha_open_price
                    float(item[2]),  # ha_high_price
                    float(item[3]),  # ha_low_price
                    float(item[4]),  # ha_close_price
                    record_id
                ))
                logger.info(f'更新记录: HA时间={api_time_str}, 匹配SOL时间={record_time}, 开盘价={item[1]}')
                matched_count += 1
            else:
                logger.warning(f'未找到匹配记录: {api_time_str}')
                unmatched_count += 1
        
        conn.commit()
        
        # 打印匹配统计
        logger.info(f'\n数据匹配统计:')
        logger.info(f'成功匹配并更新的记录数: {matched_count}')
        logger.info(f'未找到匹配的记录数: {unmatched_count}')
        
        # 验证数据更新情况
        cursor.execute("""
            SELECT COUNT(*) FROM gmgn_data_2 
            WHERE ha_open_price IS NOT NULL 
            AND token_symbol = 'SOL'
        """)
        total_records_with_ha = cursor.fetchone()[0]
        logger.info(f'数据库中包含HA价格的SOL记录总数: {total_records_with_ha}')
        
        conn.close()

if __name__ == '__main__':
    spider = KPHASpider()
    spider.parse_data()