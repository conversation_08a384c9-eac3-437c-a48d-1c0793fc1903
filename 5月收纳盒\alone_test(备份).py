'''
非小号 基本盘信息获取
'''

import requests
from loguru import logger
import redis
import threading
import time
import random
import pymysql
from pymysql.cursors import DictCursor
from lxml import etree
import re


class BASE_INFOMATION_SPIDER():
    def __init__(self):
        self.url = "https://www.feixiaohao.com/currencies/ethereum/"
        self.headers = {
            "authority": "www.feixiaohao.com",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "max-age=0",
            "referer": "https://www.feixiaohao.com/",
            "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.mysql_config = {
            'host': '***************',
            'port': 3306,
            'user': 'spider',
            'password': 'ha13579.',
            'db': 'spider',
            'charset': 'utf8',
            'cursorclass': DictCursor
        }
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            password='123456',
            db=2,
            decode_responses=True
        )
        self.proxies = {
            "http": "http://127.0.0.1:33210",
            "https": "http://127.0.0.1:33210"
        }

        self.thread_local = threading.local()
        self.request_limit = 8
        self.wait_time_range = (3, 6)

        self.init_database()

    def init_database(self):
        """初始化数据库表结构"""
        conn = pymysql.connect(**self.mysql_config)
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS fxh_basic_data (
                        code VARCHAR(50) PRIMARY KEY,
                        online_time VARCHAR(50),
                        first_opening_price VARCHAR(50),
                        crowdfunding_price VARCHAR(50),
                        return_on_investment VARCHAR(50),
                        highest_price VARCHAR(50),
                        highest_date VARCHAR(50),
                        lowest_price VARCHAR(50),
                        lowest_date VARCHAR(50),
                        issue_method VARCHAR(100),
                        public_chain VARCHAR(100),
                        maximum_supply VARCHAR(50),
                        maximum_supply_market_value VARCHAR(50),
                        current_supply_quantity VARCHAR(50),
                        market_cap_total VARCHAR(50),
                        circulation VARCHAR(50),
                        famc VARCHAR(50),
                        circulation_rate VARCHAR(50),
                        market_cap_percent VARCHAR(50),
                        exchange_listcount VARCHAR(50),
                        holders VARCHAR(50),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)
                conn.commit()
                logger.success("数据表创建成功")
        except Exception as e:
            logger.error(f"创建数据库表时出错: {e}")
            conn.rollback()
        finally:
            conn.close()

    def get_code_from_redis(self):
        """从Redis中获取并移除一个code"""
        try:
            # logger.debug("尝试从Redis获取code")
            code = self.redis_client.spop('feixiaohao:coin_codes')
            # logger.debug(f"获取到的code类型: {type(code)}, 值: {code}")
            
            if code:
                if isinstance(code, bytes):
                    code = code.decode('utf-8')
                logger.info(f'从Redis获取到code: {code}')
                return code
            else:
                all_keys = self.redis_client.keys('*')
                logger.debug(f"Redis中的所有key: {all_keys}")

                key_type = self.redis_client.type('feixiaohao:coin_codes')
                logger.debug(f"feixiaohao:coin_codes的类型: {key_type}")

                if key_type == b'set':
                    size = self.redis_client.scard('feixiaohao:coin_codes')
                    logger.debug(f"集合大小: {size}")
                
                return None
        except Exception as e:
            logger.error(f'从Redis获取code失败: {str(e)}')
            import traceback
            logger.error(f'错误详情: {traceback.format_exc()}')
            return None


    def get_db_connection(self):
        """为每个线程获取独立的数据库连接"""
        if not hasattr(self.thread_local, 'connection') or self.thread_local.connection.open == False:
            self.thread_local.connection = pymysql.connect(
                **self.mysql_config,
                autocommit=False,
                connect_timeout=10,
                read_timeout=30,
                write_timeout=30
            )
        return self.thread_local.connection


    def fetch_data(self, code):
        try:
            url = f"https://www.feixiaohao.com/currencies/{code}/"
            response = requests.get(url=url, headers=self.headers, proxies=self.proxies)
            logger.info(f'请求URL: {url}, 状态码：{response.status_code}')
            return response.text
        except Exception as e:
            logger.error(f'获取数据时出错：{e}')
            return None

    def clean_text(self, text):
        """清理文本数据"""
        if not text:
            return ''
        text = re.sub(r'\s+', ' ', str(text))
        return text.strip()


    def parse_data(self, html_data, code=None):
        time.sleep(0.3)
        if not html_data:
            return None

        def convert_scientific_notation(value):
            """转换科学计数法为普通小数"""
            try:
                if isinstance(value, str) and ('e' in value.lower() or 'E' in value):
                    float_value = float(value)
                    return f'{float_value:.20f}'.rstrip('0').rstrip('.')
                return value
            except (ValueError, TypeError):
                return value

        def format_decimal(value):
            """格式化数值，处理科学计数法和以小数点开头的数字"""
            try:
                if not isinstance(value, str) or value == "null":
                    return value

                if 'e' in value.lower() or 'E' in value:
                    return convert_scientific_notation(value)

                if value.startswith('.'):
                    return f"0{value}"

                return value
            except (ValueError, TypeError):
                return value

        def format_percentage(value):
            """将小数转换为百分比格式"""
            try:
                if value == "null" or not value:
                    return "null"

                if value.startswith('.'):
                    value = '0' + value

                float_value = float(value)
                percentage = f"{float_value:.2f}%"
                # logger.info(f"百分比转换: {value} -> {percentage}")
                return percentage
            except (ValueError, TypeError):
                logger.warning(f"百分比转换失败: {value}")
                return "null"

        try:
            tree = etree.HTML(html_data)
            
            # 流通量
            circulation_xpath = '//div[contains(@class, "celltit") and contains(text(), "流通量")]/following-sibling::div[@class="val"]/text()'
            circulation_value = tree.xpath(circulation_xpath)

            # famc_info
            famc_info_xpath = '//div[@class="mainPrice"]//div[@class="pricechange_volume"]//span[@class="val"]//span/@attr-data-num'
            famc_info_value = tree.xpath(famc_info_xpath)
            
            if not famc_info_value:
                # 备用xpath
                backup_xpath = '//div[contains(@class, "mainPrice")]//span[contains(@class, "val")]//span/@attr-data-num'
                famc_info_value = tree.xpath(backup_xpath)
            
            # famc_info
            famc_info_processed = "null"
            if famc_info_value:
                try:
                    famc_info = famc_info_value[0].strip()
                    if famc_info != '--' and not re.match(r'^[a-zA-Z]$', famc_info):
                        famc_info_processed = format_decimal(famc_info)
                        # logger.info(f"处理后的famc_info值: {famc_info_processed}")
                except Exception as e:
                    logger.error(f"处理famc_info时出错: {e}")

            # xpath提取首日开盘价，添加多个xpath模式
            first_price_xpaths = [
                '//span[contains(text(), "首日开盘价")]/following-sibling::span[@class="info_val"]/span/@attr-data-num',
                '//span[contains(text(), "首日开盘价")]/following-sibling::span[@class="info_val"]/span/@title',
                '//span[text()="首日开盘价"]/following-sibling::span[@class="info_val"]/span/@attr-data-num',
                '//span[contains(text(), "首日开盘价")]/following-sibling::span/span/@attr-data-num'
            ]
            
            first_price_value = None
            for xpath in first_price_xpaths:
                value = tree.xpath(xpath)
                if value:
                    first_price_value = value
                    # logger.info(f"使用xpath模式 '{xpath}' 成功提取到首日开盘价: {value}")
                    break
            
            if not first_price_value:
                # logger.info("通过xpath未能提取到首日开盘价，尝试使用正则表达式")
                first_price_patterns = [
                    r'首日开盘价[^>]+>[^>]+attr-data-num="([^"]+)"',
                    r'首日开盘价[^>]+>[^>]+title="([^"]+)"'
                ]
                
                for pattern in first_price_patterns:
                    match = re.search(pattern, html_data)
                    if match:
                        first_price_value = [match.group(1)]
                        logger.info(f"使用正则表达式成功提取到首日开盘价: {first_price_value}")
                        break

            # logger.info(f"最终提取到的首日开盘价: {first_price_value}")

            # 尝试用xpath提取众筹价格
            crowdfunding_price_xpath = '//span[contains(text(), "众筹价格")]/following-sibling::span[@class="info_val"]/span/@attr-data-num'
            crowdfunding_price_value = tree.xpath(crowdfunding_price_xpath)
            # logger.info(f"XPath提取到的众筹价格: {crowdfunding_price_value}")

            # 修改投资回报率的xpath提取，同时匹配textGreen和textRed
            return_on_investment_xpath = '''//div[contains(text(), "投资回报率")]/ancestor::div/following-sibling::span[@class="info_val"]/span[contains(@class, "text")]/text()'''
            return_on_investment_value = tree.xpath(return_on_investment_xpath)
            # logger.info(f"XPath提取到的投资回报率: {return_on_investment_value}")

            # 如果上面的xpath没有提取到，尝试备用xpath
            if not return_on_investment_value:
                backup_xpath = '''//div[contains(text(), "投资回报率")]/following::span[contains(@class, "text")][1]/text()'''
                return_on_investment_value = tree.xpath(backup_xpath)
                # logger.info(f"备用XPath提取到的投资回报率: {return_on_investment_value}")

            scripts = tree.xpath('//script[contains(text(), "online_time")]/text()')
            if scripts:
                script_content = scripts[0]

                # 先处理流通量数据，因为它可能被用作maximum_supply的备选
                circulation_value_processed = "null"
                if circulation_value:
                    circulation = circulation_value[0].strip()
                    if circulation == '--' or re.match(r'^[a-zA-Z]$', circulation):
                        circulation_value_processed = "null"
                    else:
                        circulation_value_processed = format_decimal(circulation)
                    # logger.info(f"处理后的circulation值: {circulation_value_processed}")

                # 定义常规的patterns
                patterns = {
                    'online_time': r'online_time["\s:]+([^,"]+)',
                    'highest_price': r'his_highest_usd["\s:]+([^,]+)',
                    'highest_date': r'his_highprice_time["\s:]+\"([^"]+)\"',
                    'lowest_price': r'his_lowest_usd["\s:]+([^,]+)',
                    'lowest_date': r'his_lowprice_time["\s:]+\"([^"]+)\"',
                    'issue_method': r'issuemode["\s:]+([^,]+)',
                    'public_chain': r'publicchain:"([^"]+)"',
                    'current_supply_quantity': r'totalSupply:(\d+)',
                    'market_cap_total': r'marketcap_total_usd["\s:]+([^,]+)',
                    'market_cap_percent': r'marketcappercent["\s:]+([^,]+)',
                    'exchange_listcount': r'exchange_listcount:(\d+)',
                    'holders': r'holders["\s:]+([^,]+)',
                    'first_opening_price': r'first_price["\s:]+([^,\s]+)'
                }

                # 需要进行数值格式化的字段
                numeric_fields = [
                    'highest_price',
                    'lowest_price',
                    'first_opening_price',
                    'market_cap_total',
                    'crowdfunding_price',
                    'current_supply_quantity'
                ]

                result = {}

                for key, pattern in patterns.items():
                    match = re.search(pattern, script_content)
                    if match:
                        value = match.group(1).strip('"')
                        if re.match(r'^[a-zA-Z]$', value):
                            result[key] = "null"
                        else:
                            if key in numeric_fields:
                                original_value = value
                                value = format_decimal(value)
                                # if value != original_value:
                                    # logger.info(f"格式化数值 {key}: {original_value} -> {value}")
                            elif key == 'market_cap_percent':
                                value = format_percentage(value)
                            elif key == 'public_chain':
                                # logger.info(f"提取到的原始public_chain值: {value}")
                                if value:
                                    value = value.strip()
                                    # logger.info(f"保存的public_chain值: {value}")
                            result[key] = value
                    else:
                        result[key] = "null"
                        if key == 'public_chain':
                            logger.warning(f"未找到{key}数据")

                if result.get('exchange_listcount') == "null":
                    backup_pattern1 = r'\d+,true,"--","24H涨幅","\\u002F",[^,]+,[^,]+,[^,]+,[^,]+,[^,]+,(\d+),"currencies"'
                    backup_pattern2 = r'"24H涨幅","\\u002F",[^,]+,[^,]+,[^,]+,[^,]+,[^,]+,"[^"]+","[^"]+","[^"]+","currencies"'

                    # 尝试所有可能的备用模式
                    backup_patterns = [
                        r'\(0,"",false,1,(?:true|"[^"]+"),(?:"[^"]+"|true),[^,]*,[^,]*,"24H涨幅","\\u002F",[^,]+,[^,]+,[^,]+,[^,]+(?:,[^,]+)*?,(\d+)(?:,"(?:currencies|ethereum)")',
                        r'\(0,"",false,1,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,(\d+),',
                        r'\(0,"",false,1,(?:true|"[^"]+"),(?:"[^"]+"|true),[^,]+,[^,]+,"24H涨幅","\\u002F",[^,]+,[^,]+,[^,]+,[^,]+,[^,]+,(\d+),',
                        # 新增的模式，匹配您提供的格式
                        r'"",\d+,false,"[^"]+",\d+,"[^"]+",true,[^,]+,"[^"]+",\d+,"24H涨幅","\\u002F",[^,]+,[^,]+,[^,]+,\d+,"[^"]+",[\d.]+,\d+,\d+,\d+,(\d+),',
                        # 更通用的模式
                        r'"24H涨幅","\\u002F",[^,]+,[^,]+,[^,]+,\d+,"[^"]+",[\d.]+,\d+,[^,]+,\d+,(\d+),',
                        backup_pattern1,
                        backup_pattern2
                    ]
                    
                    for i, pattern in enumerate(backup_patterns):
                        try:
                            match = re.search(pattern, script_content)
                            if match:
                                value = match.group(1)
                                if value and value.isdigit():
                                    result['exchange_listcount'] = value
                                    logger.info(f"使用备用模式{i+1}成功提取到exchange_listcount: {value}")
                                    break
                        except Exception as e:
                            logger.warning(f"使用备用模式{i+1}提取exchange_listcount时出错: {e}")
                            continue
                    
                    if result['exchange_listcount'] == "null":
                        logger.warning("所有exchange_listcount提取模式都失败")

                # 定义前三种maximum_supply的提取模式
                maximum_supply_patterns = [
                    r'maxsupply:(\d+)',
                    r',(\d+),"24H涨幅"',
                ]

                if code:
                    third_pattern = fr'[^"]*"{code}"[^"]*(?:true|false)[^,]*,[^,]*,[^,]*,[^,]*,(\d{{8,}})'
                    maximum_supply_patterns.append(third_pattern)

                    fourth_pattern = r'"24H涨幅","\\u002F",[^"]*?(\d{9,})[^"]*?"(?:ethereum|currencies|--)"'
                    maximum_supply_patterns.append(fourth_pattern)

                    fifth_pattern = r'"24H涨幅","\\u002F",[^,]+,[^,]+,[^,]+,[^,]+,(\d{9,})'
                    maximum_supply_patterns.append(fifth_pattern)
                    
                    # logger.info(f"添加新的maximum_supply提取模式，针对code: {code}")

                maximum_supply_value = "null"
                is_maximum_supply_backup = False
                
                for i, pattern in enumerate(maximum_supply_patterns):
                    try:
                        match = re.search(pattern, script_content)
                        if match:
                            value = match.group(1).strip('"')
                            if re.match(r'^[a-zA-Z]$', value):
                                continue

                            original_value = value
                            value = format_decimal(value)
                            if value != original_value:
                                logger.info(f"格式化数值 maximum_supply: {original_value} -> {value}")
                            
                            maximum_supply_value = value
                            # logger.info(f"使用模式{i+1}提取到maximum_supply: {value}")
                            break
                    except Exception as e:
                        logger.warning(f"使用模式{i+1}提取maximum_supply时出错: {e}")
                        continue
                
                # 如果所有模式都提取失败，使用circulation的值
                if maximum_supply_value == "null" and circulation_value_processed != "null":
                    maximum_supply_value = circulation_value_processed
                    is_maximum_supply_backup = True
                    # logger.info(f"所有模式都未能提取到maximum_supply数据，使用circulation值代替: {maximum_supply_value}")

                # 将maximum_supply最终结果存入result
                result['maximum_supply'] = maximum_supply_value

                # 如果从script中没有提取到首日开盘价，尝试使用xpath或正则提取的结果
                if result['first_opening_price'] == "null" and first_price_value:
                    result['first_opening_price'] = format_decimal(first_price_value[0])
                    # logger.info(f"使用备用方式提取的首日开盘价: {result['first_opening_price']}")
                
                # 使用xpath提取的众筹价格
                if crowdfunding_price_value:
                    result['crowdfunding_price'] = format_decimal(crowdfunding_price_value[0])
                    # logger.info(f"使用XPath提取的众筹价格: {result['crowdfunding_price']}")
                else:
                    result['crowdfunding_price'] = "null"
                    # logger.warning("未找到众筹价格数据")

                # 如果first_opening_price仍然为null，使用crowdfunding_price作为备选
                if result['first_opening_price'] == "null" and result['crowdfunding_price'] != "null":
                    result['first_opening_price'] = result['crowdfunding_price']
                    # logger.info(f"首日开盘价使用众筹价格作为备选值: {result['first_opening_price']}")

                # 存储circulation字段到result
                result['circulation'] = circulation_value_processed

                # 在处理完其他字段后，添加投资回报率的处理
                if return_on_investment_value:
                    value = return_on_investment_value[0].strip()
                    # 处理特殊情况
                    if value == "ExRank":
                        result['return_on_investment'] = "0"
                        logger.info("投资回报率值为ExRank，已转换为0")
                    else:
                        result['return_on_investment'] = value
                        # logger.info(f"最终的投资回报率值: {result['return_on_investment']}")
                else:
                    result['return_on_investment'] = "null"
                    logger.warning("未找到投资回报率数据")

                # 在result字典处理部分，添加famc的计算
                # 计算famc值（在处理完circulation后）
                if circulation_value_processed != "null" and famc_info_processed != "null":
                    try:
                        # 移除字符串中的逗号后再转换为浮点数
                        circulation_str = circulation_value_processed.replace(',', '')
                        famc_info_str = famc_info_processed.replace(',', '')
                        
                        # 转换为浮点数进行计算
                        circulation_float = float(circulation_str)
                        famc_info_float = float(famc_info_str)
                        
                        # 计算famc
                        famc_value = circulation_float * famc_info_float
                        
                        # 格式化结果
                        result['famc'] = format_decimal(str(famc_value))
                        # logger.info(f"计算得到的famc值: {result['famc']}")
                    except Exception as e:
                        logger.error(f"计算famc时出错: {e}")
                        logger.error(f"circulation值: {circulation_value_processed}, famc_info值: {famc_info_processed}")
                        result['famc'] = "null"
                else:
                    result['famc'] = "null"
                    logger.warning("无法计算famc值，因为circulation或famc_info为null")

                # 在计算famc的代码块后面添加
                # 计算maximum_supply_market_value
                if maximum_supply_value != "null" and famc_info_processed != "null":
                    try:
                        # 移除字符串中的逗号后再转换为浮点数
                        maximum_supply_str = maximum_supply_value.replace(',', '')
                        famc_info_str = famc_info_processed.replace(',', '')
                        
                        # 转换为浮点数进行计算
                        maximum_supply_float = float(maximum_supply_str)
                        famc_info_float = float(famc_info_str)
                        
                        # 计算maximum_supply_market_value
                        market_value = maximum_supply_float * famc_info_float
                        
                        # 格式化结果
                        result['maximum_supply_market_value'] = format_decimal(str(market_value))
                        # logger.info(f"计算得到的maximum_supply_market_value值: {result['maximum_supply_market_value']}")
                    except Exception as e:
                        logger.error(f"计算maximum_supply_market_value时出错: {e}")
                        logger.error(f"maximum_supply值: {maximum_supply_value}, famc_info值: {famc_info_processed}")
                        result['maximum_supply_market_value'] = "null"
                else:
                    result['maximum_supply_market_value'] = "null"
                    logger.warning("无法计算maximum_supply_market_value值，因为maximum_supply或famc_info为null")

                # 处理current_supply_quantity字段
                # 先尝试从patterns中提取
                if result['current_supply_quantity'] == "null" and result['maximum_supply'] != "null":
                    # 如果current_supply_quantity为null且maximum_supply有值，则使用maximum_supply的值
                    result['current_supply_quantity'] = result['maximum_supply']
                    # logger.info(f"current_supply_quantity使用maximum_supply的值代替: {result['current_supply_quantity']}")

                # 计算circulation_rate
                if circulation_value_processed != "null" and result['maximum_supply'] != "null":
                    try:
                        # 移除字符串中的逗号
                        circulation_str = circulation_value_processed.replace(',', '')
                        maximum_supply_str = result['maximum_supply'].replace(',', '')
                        
                        # 转换为浮点数进行计算
                        circulation_float = float(circulation_str)
                        maximum_supply_float = float(maximum_supply_str)
                        
                        if maximum_supply_float > 0:  # 防止除以0
                            # 计算比率
                            rate = circulation_float / maximum_supply_float
                            # 转换为百分比并保留两位小数
                            percentage = f"{rate * 100:.2f}%"
                            
                            # 检查是否为100.00%
                            if percentage == "100.00%" and is_maximum_supply_backup:
                                # 只有当maximum_supply是备用值且比率为100%时，才设置为null
                                result['circulation_rate'] = "null"
                                result['maximum_supply'] = "null"
                                # logger.info("检测到circulation_rate为100.00%且maximum_supply为备用值，将两者设置为null")
                            else:
                                result['circulation_rate'] = percentage
                                # logger.info(f"计算得到的circulation_rate值: {result['circulation_rate']}")
                        else:
                            result['circulation_rate'] = "null"
                            logger.warning("maximum_supply为0，无法计算circulation_rate")
                    except Exception as e:
                        logger.error(f"计算circulation_rate时出错: {e}")
                        logger.error(f"circulation值: {circulation_value_processed}, maximum_supply值: {result['maximum_supply']}")
                        result['circulation_rate'] = "null"
                else:
                    result['circulation_rate'] = "null"
                    logger.warning("无法计算circulation_rate值，因为circulation或maximum_supply为null")
                
                return result
                    
            logger.warning("未找到包含数据的script标签")
            return None
                
        except Exception as e:
            logger.error(f'解析数据时出错：{e}')
            logger.error(f"错误详情：{str(e)}")
            return None


    def insert_data(self, parsed_data, code):
        """将数据插入到数据库"""
        if not parsed_data:
            return

        conn = self.get_db_connection()
        try:
            with conn.cursor() as cursor:
                sql = """
                    INSERT INTO fxh_basic_data (
                        code,
                        online_time,
                        first_opening_price,
                        crowdfunding_price,
                        return_on_investment,
                        highest_price,
                        highest_date,
                        lowest_price,
                        lowest_date,
                        issue_method,
                        public_chain,
                        maximum_supply,
                        maximum_supply_market_value,
                        current_supply_quantity,
                        market_cap_total,
                        circulation,
                        famc,
                        circulation_rate,
                        market_cap_percent,
                        exchange_listcount,
                        holders
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    ) ON DUPLICATE KEY UPDATE
                        online_time = VALUES(online_time),
                        first_opening_price = VALUES(first_opening_price),
                        crowdfunding_price = VALUES(crowdfunding_price),
                        return_on_investment = VALUES(return_on_investment),
                        highest_price = VALUES(highest_price),
                        highest_date = VALUES(highest_date),
                        lowest_price = VALUES(lowest_price),
                        lowest_date = VALUES(lowest_date),
                        issue_method = VALUES(issue_method),
                        public_chain = VALUES(public_chain),
                        maximum_supply = VALUES(maximum_supply),
                        maximum_supply_market_value = VALUES(maximum_supply_market_value),
                        current_supply_quantity = VALUES(current_supply_quantity),
                        market_cap_total = VALUES(market_cap_total),
                        circulation = VALUES(circulation),
                        famc = VALUES(famc),
                        circulation_rate = VALUES(circulation_rate),
                        market_cap_percent = VALUES(market_cap_percent),
                        exchange_listcount = VALUES(exchange_listcount),
                        holders = VALUES(holders)
                """
                cursor.execute(sql, (
                    code,
                    parsed_data['online_time'],
                    parsed_data['first_opening_price'],
                    parsed_data['crowdfunding_price'],
                    parsed_data['return_on_investment'],
                    parsed_data['highest_price'],
                    parsed_data['highest_date'],
                    parsed_data['lowest_price'],
                    parsed_data['lowest_date'],
                    parsed_data['issue_method'],
                    parsed_data['public_chain'],
                    parsed_data['maximum_supply'],
                    parsed_data['maximum_supply_market_value'],
                    parsed_data['current_supply_quantity'],
                    parsed_data['market_cap_total'],
                    parsed_data['circulation'],
                    parsed_data['famc'],
                    parsed_data['circulation_rate'],
                    parsed_data['market_cap_percent'],
                    parsed_data['exchange_listcount'],
                    parsed_data['holders']
                ))
                conn.commit()
                logger.success(f'成功插入/更新代码 {code} 的数据')
        except Exception as e:
            logger.error(f'插入数据时出错: {e}')
            conn.rollback()

    def process_codes(self, thread_id):
        """处理单个线程的代码获取和数据抓取"""
        try:
            request_count = 0

            while True:
                code = self.get_code_from_redis()
                if not code:
                    logger.info(f'线程 {thread_id} 没有更多的code可处理')
                    break

                try:
                    # 修改请求限制为5次
                    if request_count >= 5:
                        wait_time = random.uniform(3, 4)  # 等待3-4秒
                        # logger.info(f'线程 {thread_id} 已发送 {request_count} 次请求，休息 {wait_time:.2f} 秒')
                        time.sleep(wait_time)
                        request_count = 0

                    data = self.fetch_data(code)
                    request_count += 1
                    # logger.info(f'线程 {thread_id} 处理 code: {code}，当前请求次数: {request_count}')

                    if data:
                        parsed_data = self.parse_data(data, code)
                        if parsed_data:
                            self.insert_data(parsed_data, code)
                        else:
                            logger.warning(f'线程 {thread_id} 解析 {code} 的数据为空')
                    else:
                        logger.warning(f'线程 {thread_id} 获取 {code} 的数据失败')

                    # 每次请求后添加短暂延迟，避免请求过于频繁
                    time.sleep(random.uniform(1, 2))

                except Exception as e:
                    logger.error(f'线程 {thread_id} 处理 code: {code} 时出错: {e}')
                    time.sleep(1)  # 发生错误时等待1秒再继续

        finally:
            if hasattr(self.thread_local, 'connection'):
                self.thread_local.connection.close()
                logger.info(f'线程 {thread_id} 关闭数据库连接')


    def run(self):
        # 先验证Redis连接和数据
        try:
            # 检查Redis连接
            self.redis_client.ping()
            logger.info("Redis连接正常")
            
            # 获取集合大小
            total_codes = self.redis_client.scard('feixiaohao:coin_codes')
            logger.info(f'Redis中共有 {total_codes} 个code待处理')
            
            # 验证是否能获取数据
            test_code = self.get_code_from_redis()
            if test_code:
                logger.info(f"测试获取code成功: {test_code}")
                # 将测试获取的code放回Redis
                self.redis_client.sadd('feixiaohao:coin_codes', test_code)
            else:
                logger.error("测试获取code失败")
                return

            if total_codes == 0:
                logger.warning('Redis中没有数据需要处理')
                return

            thread_count = 2  # 设置为3个线程
            threads = []

            # 创建并启动3个线程
            for i in range(thread_count):
                thread = threading.Thread(
                    target=self.process_codes,
                    args=(i+1,),
                    name=f'Spider-Thread-{i+1}'
                )
                threads.append(thread)
                thread.start()
                logger.info(f'启动线程 {i+1}')
                time.sleep(0.5)

            # 等待所有线程完成
            for thread in threads:
                thread.join()

            logger.info('所有线程处理完成')
            
        except Exception as e:
            logger.error(f'程序运行出错: {str(e)}')
            import traceback
            logger.error(f'错误详情: {traceback.format_exc()}')
        finally:
            logger.info('程序执行结束')


if __name__ == '__main__':
    try:
        spider = BASE_INFOMATION_SPIDER()
        spider.run()
    except KeyboardInterrupt:
        logger.warning('程序被手动中断')
    except Exception as e:
        logger.error(f'程序运行出错: {e}')
    finally:
        logger.info('程序结束')
