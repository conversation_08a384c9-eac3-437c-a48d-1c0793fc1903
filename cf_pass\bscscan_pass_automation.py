import redis
import requests
import time
import json
from datetime import date
from loguru import logger
import re
crawl_date = date.today()
from DrissionPage import ChromiumPage

redis_client = redis.Redis(
    host='**************',
    port=6379,
    db=14,
    password='123456',
    decode_responses=True
)

def setup_browser():
    """设置并启动浏览器"""
    page = ChromiumPage()
    return page

def get_and_save_cookies():
    """获取并保存cookies"""
    try:
        page = setup_browser()
        
        # 访问目标页面
        logger.info('正在访问目标页面...')
        page.get('https://bscscan.com/tokens')
        
        # 等待15秒通过CloudFlare检测
        logger.info('等待15秒通过CloudFlare检测...')
        time.sleep(15)
        
        # 获取所有cookies - 使用正确的cookies()方法
        all_cookies = page.cookies(all_info=False)  # 只获取基本cookie信息
        
        # 提取需要的cookies
        required_cookies = {}
        for cookie in all_cookies:
            if cookie['name'] in ['cf_clearance', 'ASP.NET_SessionId', '__cflb']:
                required_cookies[cookie['name']] = cookie['value']
        
        # 获取User-Agent
        user_agent = page.user_agent
        
        # 打印cookie信息用于调试
        logger.info(f'获取到的cookies: {required_cookies}')
        
        # 保存cookies到Redis
        redis_client.set('cf_cookies:bscscan', json.dumps(required_cookies))
        redis_client.set('cf_headers:bscscan', user_agent)
        
        logger.info('Cookie和User-Agent已保存到Redis')
        
        # 关闭浏览器
        page.quit()
        
        return True
        
    except Exception as e:
        logger.error(f'获取Cookie过程出错: {str(e)}')
        if 'page' in locals():
            page.quit()
        return False

def main():
    """主函数"""
    success = get_and_save_cookies()
    if success:
        logger.info('Cookie获取成功！')
    else:
        logger.error('Cookie获取失败！')

if __name__ == '__main__':
    main()
