import requests
import json
from datetime import datetime, timezone, timedelta
import sqlite3
from loguru import logger

proxy = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

headers = {
    "authority": "web3.okx.com",
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "app-type": "web",
    "devid": "79566de0-ec54-4c4d-8559-9b4568d80d28",
    "referer": "https://web3.okx.com/zh-hans/token/solana/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "x-cdn": "https://web3.okx.com",
    "x-fptoken": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "x-fptoken-signature": "{P1363}CaEkLM5Rd3Z2/Zcm5i59hh6qC8ygP5P72NiGxHmvo6l0GWqA3p8jiE8jZ3+wJHOaLpO7+u58QEiC7q7jykZwLw==",
    "x-id-group": "2130770399288510003-c-48",
    "x-locale": "zh_CN",
    "x-request-timestamp": "1747039937285",
    "x-simulated-trading": "undefined",
    "x-site-info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIySIJiOi42bpdWZyJye",
    "x-utc": "8",
    "x-zkdex-env": "0"
}

cookies = {
    "_gcl_gs": "2.1.k1$i1746767445$u3801134",
    "_ym_uid": "1746767455756016886",
    "_ym_d": "1746767455",
    "ok_login_type": "OKX_GLOBAL",
    "intercom-device-id-ny9cf50h": "38ee8fb1-c78a-4875-bcd7-51596b054278",
    "devId": "79566de0-ec54-4c4d-8559-9b4568d80d28",
    "locale": "zh_CN",
    "ok_prefer_udColor": "0",
    "ok_prefer_udTimeZone": "0",
    "fingerprint_id": "79566de0-ec54-4c4d-8559-9b4568d80d28",
    "first_ref": "https%3A%2F%2Fwww.okx.com%2F",
    "_gid": "GA1.2.1626718822.1747017981",
    "_ym_isad": "2",
    "ok-exp-time": "1747018311581",
    "tmx_session_id": "9kd5hblychl_1747018312753",
    "fp_s": "0",
    "intercom-session-ny9cf50h": "c3MrVFY0TFpuK252a0EyS3EzV2tKb0NKc2VsWUI4eDZ0VmlkTjI2SkpkR1FEakVNcjVjckhReWRuM29RUlJBaS9aTlZtWlI2WndVSmN1RjRyUlYxMUI4VS9sSkl0Snpnb2RHUDB0cHNmTXc9LS04VDIxNTVvd200dFEwTU9BclM3WmVnPT0=--9b53cf5a8a85c43175cc55f31ec464caf7a3f874",
    "ok_site_info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIySIJiOi42bpdWZyJye",
    "__cf_bm": "O8Wh5JSnnz_NdfOxPF1czwXkAqSIZU__mXYBjC9sz5M-1747039173-1.0.1.1-Gmpu7GbZzl3kJ25HCKI24hqW1gqvC4W7.ANN_8uclRa_cgExrEEYAmYqxkeEoVl6XdNdO7ZAMaw3PTZNXO_ThCjBNdnlIDwb09EvEk4pW9Q",
    "ok_prefer_currency": "%7B%22currencyId%22%3A50%2C%22isDefault%22%3A0%2C%22isPremium%22%3Afalse%2C%22isoCode%22%3A%22HKD%22%2C%22precision%22%3A2%2C%22symbol%22%3A%22HK%24%22%2C%22usdToThisRate%22%3A7.794535%2C%22usdToThisRatePremium%22%3A7.794535%2C%22displayName%22%3A%22%E6%B8%AF%E5%85%83%22%7D",
    "ok-ses-id": "z/MBUWEJ3VpKpksGw0NK7Huz+sccT+bu6s3K2gToAJ+x7BR1glcdmA5O1/HQRKc5HxyBc1PIK/zNP507J6SiQAVFIcHqga475nit4+ofjnAWm1SRtIAAEoV82xoFsTyY",
    "_ga_G0EKWWQGTZ": "GS2.1.s1747038428$o7$g1$t1747039929$j1$l0$h0",
    "_ga": "GA1.2.**********.**********",
    "okg.currentMedia": "lg",
    "traceId": "2130270399339320003",
    "ok_prefer_exp": "1",
    "_monitor_extras": "{\"deviceId\":\"G1EGctpUBMsdRGanofOKot\",\"eventId\":284,\"sequenceNumber\":284}"
}

class OKXSpider:
    def __init__(self):
        self.url = "https://web3.okx.com/priapi/v5/dex/token/market/history-dex-token-hlc-candles"
        self.db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        self.time_tolerance = 0.1 # 设置时间误差为2秒

    def parse_data(self):
        params = {
            "chainId": "501",
            "address": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
            "after": "1747031040000",  # 这里修改时间戳
            "bar": "1s",
            "limit": "257",
            "t": "1747029900000"
        }
        
        response = requests.get(
            self.url, 
            headers=headers, 
            cookies=cookies, 
            params=params,
            # proxies=proxy
        )
        # print(response.text)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == "0":
                logger.info(f'访问成功,状态码:{response.status_code},正在获取数据')
                self.save_to_sqlite(data["data"])
            else:
                logger.error(f'请求失败,错误信息:{data.get("msg")}')
        else:
            logger.error(f'请求失败,状态码:{response.status_code}')

    def find_closest_timestamp(self, cursor, target_time, target_datetime):
        """查找最接近的时间戳记录（误差2秒内）"""
        time_range = timedelta(seconds=self.time_tolerance)
        start_time = target_datetime - time_range
        end_time = target_datetime + time_range
        
        query = """
            SELECT id, timestamp 
            FROM gmgn_data_2 
            WHERE token_symbol = 'SOL'
            AND timestamp BETWEEN ? AND ?
            ORDER BY ABS(strftime('%s', timestamp) - ?)
            LIMIT 1
        """
        
        # 转换为本地时间字符串
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.execute(query, (
            start_time_str,
            end_time_str,
            int(target_datetime.timestamp())
        ))
        
        return cursor.fetchone()

    def save_to_sqlite(self, data_list):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 记录匹配和未匹配的数量
        matched_count = 0
        unmatched_count = 0
        
        for item in data_list:
            # 转换为北京时间
            timestamp = int(item[0]) / 1000
            beijing_tz = timezone(timedelta(hours=8))
            dt = datetime.fromtimestamp(timestamp, timezone.utc).astimezone(beijing_tz)
            time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # 查找最接近的时间戳记录
            closest_record = self.find_closest_timestamp(cursor, timestamp, dt)
            
            if closest_record:
                record_id, record_time = closest_record
                # 更新找到的记录
                cursor.execute("""
                    UPDATE gmgn_data_2 
                    SET okx_open_price = ?,
                        okx_high_price = ?,
                        okx_low_price = ?,
                        okx_close_price = ?
                    WHERE id = ?
                """, (
                    float(item[1]),
                    float(item[2]),
                    float(item[3]),
                    float(item[4]),
                    record_id
                ))
                logger.info(f'更新记录: OKX时间={time_str}, 匹配SOL时间={record_time}, 开盘价={item[1]}')
                matched_count += 1
            else:
                logger.warning(f'未找到匹配记录: {time_str}')
                unmatched_count += 1
        
        conn.commit()
        
        # 打印匹配统计
        logger.info(f'\n数据匹配统计:')
        logger.info(f'成功匹配并更新的记录数: {matched_count}')
        logger.info(f'未找到匹配的记录数: {unmatched_count}')
        
        # 验证数据更新情况
        cursor.execute("""
            SELECT COUNT(*) FROM gmgn_data_2 
            WHERE okx_open_price IS NOT NULL 
            AND token_symbol = 'SOL'
        """)
        total_records_with_okx = cursor.fetchone()[0]
        logger.info(f'数据库中包含OKX价格的SOL记录总数: {total_records_with_okx}')
        
        conn.close()

if __name__ == '__main__':
    spider = OKXSpider()
    spider.parse_data()


# import requests


# headers = {
#     "authority": "web3.okx.com",
#     "accept": "application/json",
#     "accept-language": "zh-CN,zh;q=0.9",
#     "app-type": "web",
#     "devid": "79566de0-ec54-4c4d-8559-9b4568d80d28",
#     "referer": "https://web3.okx.com/zh-hans/token/solana/3T721bpRc5FNY84W36vWffxoKs4FLXhBpSaqwUCRpump",
#     "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
#     "sec-ch-ua-mobile": "?0",
#     "sec-ch-ua-platform": "\"Windows\"",
#     "sec-fetch-dest": "empty",
#     "sec-fetch-mode": "cors",
#     "sec-fetch-site": "same-origin",
#     "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
#     "x-cdn": "https://web3.okx.com",
#     "x-fptoken": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
#     "x-fptoken-signature": "{P1363}T7b2jMu3LFSeJFMwgdDtkisAuUJpJXEuP5FTE0i2jtRhhIFOZHAT/zL77yEaL924YHCC6fGOBSPvV+D/GI2JWQ==",
#     "x-id-group": "2140470328190100003-c-47",
#     "x-locale": "zh_CN",
#     "x-request-timestamp": "1747032828806",
#     "x-simulated-trading": "undefined",
#     "x-site-info": "==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIyVUJiOi42bpdWZyJye",
#     "x-utc": "8",
#     "x-zkdex-env": "0"
# }
# cookies = {
#     "_gcl_gs": "2.1.k1$i1746767445$u3801134",
#     "_ym_uid": "1746767455756016886",
#     "_ym_d": "1746767455",
#     "ok_login_type": "OKX_GLOBAL",
#     "intercom-device-id-ny9cf50h": "38ee8fb1-c78a-4875-bcd7-51596b054278",
#     "devId": "79566de0-ec54-4c4d-8559-9b4568d80d28",
#     "locale": "zh_CN",
#     "ok_prefer_udColor": "0",
#     "ok_prefer_udTimeZone": "0",
#     "fingerprint_id": "79566de0-ec54-4c4d-8559-9b4568d80d28",
#     "first_ref": "https%3A%2F%2Fwww.okx.com%2F",
#     "_gid": "GA1.2.1626718822.1747017981",
#     "_ym_isad": "2",
#     "ok-exp-time": "1747018311581",
#     "tmx_session_id": "9kd5hblychl_1747018312753",
#     "fp_s": "0",
#     "intercom-session-ny9cf50h": "c3MrVFY0TFpuK252a0EyS3EzV2tKb0NKc2VsWUI4eDZ0VmlkTjI2SkpkR1FEakVNcjVjckhReWRuM29RUlJBaS9aTlZtWlI2WndVSmN1RjRyUlYxMUI4VS9sSkl0Snpnb2RHUDB0cHNmTXc9LS04VDIxNTVvd200dFEwTU9BclM3WmVnPT0=--9b53cf5a8a85c43175cc55f31ec464caf7a3f874",
#     "ok_prefer_currency": "%7B%22currencyId%22%3A50%2C%22isDefault%22%3A0%2C%22isPremium%22%3Afalse%2C%22isoCode%22%3A%22HKD%22%2C%22precision%22%3A2%2C%22symbol%22%3A%22HK%24%22%2C%22usdToThisRate%22%3A7.792195%2C%22usdToThisRatePremium%22%3A7.792195%2C%22displayName%22%3A%22%E6%B8%AF%E5%85%83%22%7D",
#     "__cf_bm": "O89tavozXG0ABv5zdChyfFbIq_VRiAiLaxMxwreTrUw-**********-1.0.1.1-NH3CkuZqeMjYqVP8nYq12uvtgD9HO0_pIDvRYqc0lQCgxSeNoRPZGnfvFTh4ohjnUGdY5gffTtGoSnunGuWnxLbAQ3uGWNlDHo1_YUjUAFI",
#     "ok_site_info": "==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIyVUJiOi42bpdWZyJye",
#     "_ga_G0EKWWQGTZ": "GS2.1.s1747032814$o6$g1$t1747032817$j57$l0$h0",
#     "_ga": "GA1.2.**********.**********",
#     "_gat_UA-35324627-3": "1",
#     "ok-ses-id": "o0knPiZB3zNA0n+hNqDUv/wcacQlso/roFnrA4bSHf0zAA4yq4YbXoXChWJGBdJ8Lwvc1Ttr1/B6yrX75B7tvCnsTvOPdjUxiYAahvVbPHH46bfyz5FZIkTRDVs0fXKH",
#     "_monitor_extras": "{\"deviceId\":\"G1EGctpUBMsdRGanofOKot\",\"eventId\":136,\"sequenceNumber\":136}",
#     "okg.currentMedia": "lg",
#     "traceId": "2131170328250520003"
# }
# url = "https://web3.okx.com/priapi/v5/dex/token/market/history-dex-token-hlc-candles"
# params = {
#     "chainId": "501",
#     "address": "3T721bpRc5FNY84W36vWffxoKs4FLXhBpSaqwUCRpump",
#     "after": "1747031361000",
#     "bar": "1s",
#     "limit": "57",
#     "t": "1747032828806"
# }
# response = requests.get(url, headers=headers, cookies=cookies, params=params)

# print(response.text)
# print(response)