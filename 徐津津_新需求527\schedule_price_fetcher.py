#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
定时执行代币价格爬虫的脚本
"""

import time
import logging
import os
import sys
from datetime import datetime
import schedule
from coin_current_price import CoinPriceSpider

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("price_fetcher.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("PriceFetcher")

# 设置数据库路径
script_dir = os.path.dirname(os.path.abspath(__file__))
db_path = os.path.join(script_dir, 'coin_prices.db')

# 创建爬虫实例
spider = CoinPriceSpider(db_path)

# 定义要监控的代币列表
TOKENS = [
    # 链名称, 代币合约地址, 描述
    ("SOL", "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "USDC on Solana"),
    ("ETH", "******************************************", "USDT on Ethereum"),
    # 添加更多代币
]

def fetch_all_prices():
    """获取所有代币的价格"""
    logger.info(f"开始获取价格数据 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    for chain_name, token_address, desc in TOKENS:
        try:
            logger.info(f"获取 {desc} ({chain_name}/{token_address}) 价格...")
            success, price_data = spider.fetch_and_save(chain_name, token_address)
            
            if success:
                # 获取最新价格
                latest = spider.get_latest_price(chain_name, token_address)
                if latest:
                    logger.info(f"{desc} 价格: {latest['pricechange_volume']}, 时间: {latest['time']}")
            else:
                logger.error(f"获取 {desc} 价格失败")
                
        except Exception as e:
            logger.exception(f"处理 {desc} 时出错: {e}")
    
    logger.info(f"价格数据获取完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    logger.info("价格定时获取服务启动")
    
    # 立即执行一次
    fetch_all_prices()
    
    # 设置定时任务
    # 每分钟执行一次
    schedule.every(1).minutes.do(fetch_all_prices)
    
    # 每小时整点执行
    # schedule.every().hour.at(":00").do(fetch_all_prices)
    
    # 每天特定时间执行
    # schedule.every().day.at("10:30").do(fetch_all_prices)
    
    logger.info("已设置定时任务，每分钟执行一次")
    
    # 运行定时任务
    while True:
        try:
            schedule.run_pending()
            time.sleep(1)
        except KeyboardInterrupt:
            logger.info("用户中断，服务停止")
            break
        except Exception as e:
            logger.exception(f"运行时错误: {e}")
            # 等待10秒后继续
            time.sleep(10)

if __name__ == "__main__":
    main() 