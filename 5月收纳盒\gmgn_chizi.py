from curl_cffi import requests

proxy = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

headers = {
    "authority": "gmgn.ai",
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=20250510-918-0737323,sentry-public_key=93c25bab7246077dc3eb85b59d6e7d40,sentry-trace_id=1737239acd134a0e87507d94747cbc94,sentry-sample_rate=0.01,sentry-sampled=false",
    "if-none-match": "W/\"55b3-znXcSGJZE7VzfWyTNkyTjkh2FeM\"",
    "referer": "https://gmgn.ai/sol/token/3T721bpRc5FNY84W36vWffxoKs4FLXhBpSaqwUCRpump",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "1737239acd134a0e87507d94747cbc94-b5393c7478df7bdc-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}
cookies = {
    "_ga": "GA1.1.1784117668.1746768932",
    "GMGN_LOCALE": "zh-CN",
    "GMGN_CHAIN": "sol",
    "GMGN_THEME": "dark",
    "cf_clearance": "dptNgiURru.hjCsumsd8uwwc8vhjiD1rjfslUrsePrU-1747038030-1.2.1.1-3HPsUAV2sbB5rHhJhqSKLMsDi3PFuuuIQBmR_ZRCJ95l89BMKdgXtNMtTl43x02G.n8XNBaQejJ608xCzdqg6im3pDf4v6533pVZcZ7WkfbsYsxlxH5R5LF9__s.nJCkh69Q0DtGy2ej7hdbKgcYsaYcgjKMUfuIodID.wpq4bqPEWdkfI_7mE2oQuaVej3Ajd7d2B7SbURdjyhou6Vpx01PIl_PJ_5UL2p8f0U5Uh0LUCJoFsIKyxvvZsDKJMFzoCUiDKZYt8iqaArpk8kqcoMz1PNgNpMVCuH5m2Z7cRpgEWNylwwmmrElQws5.Yx3vSoXKMrgKqR_db7sMKisCcIF4VoVfze81GN29ODz3vE",
    "__cf_bm": "VA_dXgP0Hxc2qerwtPx64SqEiZQ_l_oqW2VycL_NMa0-1747038273-1.0.1.1-4hWSQCJS75oUwPQ3aFAJZbMUNTSbmdOM0fueBBbsbshVPr5iRn5MUU1BTGS3h2uwKKffzUMqKL9Rlzs_doj.wIHhNh0g2yvEs2Bs9CkBPXc",
    "_ga_0XM0LYXGC8": "GS2.1.s1747034871$o4$g1$t1747038599$j0$l0$h0"
}
url = "https://gmgn.ai/defi/quotation/v1/rank/sol/swaps/1h"
params = {
    "device_id": "e17ccaad-5aa6-43a9-a219-ae220bc8a6e4",
    "client_id": "gmgn_web_20250510-918-0737323",
    "from_app": "gmgn",
    "app_ver": "20250510-918-0737323",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a799e4cfe3ca887eb253de175a8cc8bf",
    "os": "web",
    "orderby": "swaps",
    "direction": "desc",
    "limit": "15",
    "filters\\[\\]": "not_risk"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params, proxies=proxy)

print(response.text)
print(response)