import os
from Spider<PERSON> import utils

# utils.HOST_U = os.getenv('MYSQL_SERVER_IP')
# utils.PORT_U = int(os.getenv('MYSQL_SERVER_PORT'))
# utils.POSS_WORD_U = 'ctkejEmpODYYefHm'
# utils.USER_U = 'root'
# utils.MYSQL_BASE_U = 'spider'
#
# utils.REDIS_HOST = os.getenv('REDIS_SERVER_IP')
# utils.REDIS_PORT = int(os.getenv('REDIS_SERVER_PORT'))
# utils.REDIS_PASSWORD = 'VxEmnQaexUcs6kzO'
# utils.REDIS_DB_U = 11

SEED_LIST = "eth_abi_address_list"
CACHE_LIST = "eth_abi_cache_list"
IP_LIST = "eth_abi_ip_list"
IP_KEY = "ip_key_"
LAST_ADDRESS_LIST = "eth_abi_list_address_list"
LIST_ADDRESS_KEY = "hash_key"