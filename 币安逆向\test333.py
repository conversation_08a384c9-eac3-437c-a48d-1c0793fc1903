# from curl_cffi import requests
#
#
# proxies = {
#             "http": "http://127.0.0.1:7897",
#             "https": "http://127.0.0.1:7897"
#         }
#
# timeout = 10
#
# headers = {
#     "referer": "https://www.binance.com/",
# }
# cookies = {
#     "aws-waf-token": "08b61375-0f4f-4058-825c-18800e349a06:AAABmBybxf0=:FAoFAoHAYDAYDIbDYbBYrFYrlUolEgkEgsHg8Pj8/v8UCgUCgcBgMBgMhsNhsFisViuVSiUSCQSCweDw+Pz+/xQKBQKBwGAwGAyGw2GwWKxWK5VKJRIJBILB4PD4/P7/FAoFAoHAYDAYDIbDYbBYrFYrlUolEgkEgsHg8Pj8/v8="
# }
# url = "https://www.binance.com/zh-CN"
# response = requests.get(url, headers=headers, cookies=cookies, proxies=proxies, timeout=timeout, impersonate="chrome116")
#
# print(response.text)
# print(response)

from curl_cffi import requests


headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "max-age=0",
    "priority": "u=0, i",
    "referer": "https://www.binance.com/zh-CN",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
}
cookies = {
    "bnc-uuid": "e3c623c6-c9f3-4127-8d59-8302832e481b",
    "_ga_3WP50LGEEC": "GS2.1.s1752822830$o1$g1$t1752822900$j60$l0$h0",
    "aws-waf-token": "a732e0f3-e600-4815-9318-5cf59ee1ed89:AAABmByfvbc=:FIrFYrHY7HY7nc7n83m8Xq/X6/V6PZ7PZzMZDAYDAQAUisVisdjsdjudzufzebxer9fr9Xo9ns9nMxkMBgMBABSKxWKx2Ox2O53O5/N5vF6v1+v1ej2ez2czGQwGAwEAFIrFYrHY7HY7nc7n83m8Xq/X6/V6PZ7PZzMZDAYDAQA="
}
url = "https://www.binance.com/zh-CN"
response = requests.get(url, headers=headers, cookies=cookies, impersonate="chrome131")

print(response.text)
print(response)