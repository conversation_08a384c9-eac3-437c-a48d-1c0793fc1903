## 使用轻量级的官方Python 3.12镜像作为基础
##FROM python:3.12-slim
#FROM python:3.11-slim
#RUN pip install phoenixdb
#
## 设置工作目录
#WORKDIR /app
#
## 复制依赖文件并安装
#COPY requirements.txt .
#RUN pip install --no-cache-dir -r requirements.txt
#
## 复制项目中的所有文件
#COPY . .
#
#
## 设置容器启动时要执行的命令
## 默认以生产模式、3个线程运行脚本
#CMD ["python", "coin_info.spider.py", "production", "9"]
## CMD  ["bash"]

#FROM python:3.11-slim
#
## 安装依赖，特别是 gcc 和 libpq
#RUN apt update && apt install -y gcc libpq-dev
#
## 安装 phoenixdb
#RUN pip install phoenixdb
#
#WORKDIR /app
#
#COPY requirements.txt .
#RUN pip install --no-cache-dir -r requirements.txt
#
#COPY . .
#
#CMD ["python", "coin_info.spider.py", "production", "9"]

FROM python:3.11

RUN pip install phoenixdb


WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["python", "coin_info.spider.py", "production", "9"]
