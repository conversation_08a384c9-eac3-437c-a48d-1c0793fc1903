#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX平台历史K线数据爬虫
功能：从OKX平台API获取历史K线数据并更新到MySQL数据库（三平台对比表）
"""

import requests
import sqlite3
import pymysql
import time
import json
import hmac
import base64
import hashlib
from datetime import datetime
from typing import List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OKXSpider:
    def __init__(self):
        # OKX平台API配置
        self.api_url = "https://web3.okx.com/api/v5/dex/market/historical-candles"
        
        # OKX API认证参数
        self.api_key = "d87d0a5f-a4df-4209-a3b7-573dad862d25"
        self.api_secret = "8807594F0F5B6A15F2B223638B8537D0"
        self.api_passphrase = "Dh112211!"
        
        # SQLite数据库配置（读取Token Address）
        self.sqlite_db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        
        # MySQL数据库配置
        self.mysql_config = {
            'host': '**************',
            'port': 33060,
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8',
            'database': 'kline_data'
        }
        
        # 请求参数配置
        self.request_params = {
            'chainIndex': '501',  # Solana链ID
            'bar': '1m',
            'limit': '299'  # OKX限制较小，分批获取
        }
        
        self.request_interval = 1.5  # 请求间隔（秒）
        
    def generate_signature(self, timestamp: str, method: str, request_path: str, params: Dict = None) -> str:
        """生成OKX API签名"""
        try:
            # 构建签名字符串
            if params:
                query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
                request_path = request_path + '?' + query_string
            
            message = timestamp + method + request_path
            
            mac = hmac.new(
                bytes(self.api_secret, encoding='utf8'),
                bytes(message, encoding='utf-8'),
                digestmod=hashlib.sha256
            )
            
            signature = base64.b64encode(mac.digest()).decode('utf-8')
            return signature
        except Exception as e:
            logger.error(f"生成OKX签名失败: {e}")
            return ""
    
    def get_token_addresses(self) -> List[Dict[str, str]]:
        """从SQLite数据库获取Token Address列表"""
        try:
            conn = sqlite3.connect(self.sqlite_db_path)
            cursor = conn.cursor()
            
            # 查询Token Address和Symbol
            cursor.execute("""
                SELECT "Token Address", "Token symbol" 
                FROM high_low_sol_new_new 
                WHERE "Token Address" IS NOT NULL 
                AND "Token Address" != ''
            """)
            
            results = cursor.fetchall()
            token_list = []
            
            for row in results:
                token_list.append({
                    'address': row[0],
                    'symbol': row[1] if row[1] else 'UNKNOWN'
                })
            
            conn.close()
            logger.info(f"从SQLite数据库获取到 {len(token_list)} 个Token地址")
            return token_list
            
        except Exception as e:
            logger.error(f"获取Token Address失败: {e}")
            return []
    
    def timestamp_to_datetime(self, timestamp: str) -> str:
        """将时间戳转换为标准时间格式"""
        try:
            # OKX返回的是毫秒时间戳
            timestamp_sec = int(timestamp) / 1000
            dt = datetime.fromtimestamp(timestamp_sec)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.error(f"时间戳转换失败 {timestamp}: {e}")
            return ""
    
    def get_existing_timestamps(self, token_address: str) -> List[int]:
        """获取数据库中已存在该Token的所有时间戳（用于时间对齐）"""
        try:
            connection = pymysql.connect(**self.mysql_config)
            cursor = connection.cursor()
            
            # 查询该Token已存在的时间戳
            query_sql = """
            SELECT timestamp FROM kline_comparison_sol_new_new
            WHERE token_address = %s AND ha_open IS NOT NULL
            ORDER BY timestamp DESC
            """
            cursor.execute(query_sql, (token_address,))
            results = cursor.fetchall()
            
            timestamps = [row[0] for row in results]
            cursor.close()
            connection.close()
            
            logger.info(f"Token {token_address} 在数据库中有 {len(timestamps)} 个时间点")
            return timestamps
            
        except Exception as e:
            logger.error(f"获取已存在时间戳失败: {e}")
            return []
    
    def fetch_kline_data(self, token_address: str, after_timestamp: str = None) -> Optional[List[List]]:
        """获取指定Token的K线数据"""
        try:
            # 构建请求参数
            params = self.request_params.copy()
            params['tokenContractAddress'] = token_address
            
            # 如果有after参数，添加到请求中
            if after_timestamp:
                params['after'] = after_timestamp
            
            # 生成时间戳和签名
            timestamp = datetime.utcnow().isoformat("T", "milliseconds") + "Z"
            request_path = "/api/v5/dex/market/historical-candles"
            signature = self.generate_signature(timestamp, "GET", request_path, params)
            
            if not signature:
                logger.error(f"Token {token_address} 签名生成失败")
                return None
            
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "OK-ACCESS-KEY": self.api_key,
                "OK-ACCESS-SIGN": signature,
                "OK-ACCESS-TIMESTAMP": timestamp,
                "OK-ACCESS-PASSPHRASE": self.api_passphrase
            }
            
            # 发送请求
            response = requests.get(
                self.api_url,
                headers=headers,
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('code') == '0' and 'data' in data:
                    kline_data = data['data']
                    logger.info(f"Token {token_address} 获取到 {len(kline_data)} 条K线数据")
                    return kline_data
                else:
                    logger.warning(f"Token {token_address} API返回错误: {data}")
                    return None
            else:
                logger.error(f"Token {token_address} 请求失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Token {token_address} 请求异常: {e}")
            return None
    
    def save_okx_kline_data(self, token_address: str, token_symbol: str, kline_data: List[List]):
        """保存OKX平台K线数据到三平台对比表（仅更新已存在的时间点）"""
        try:
            connection = pymysql.connect(**self.mysql_config)
            cursor = connection.cursor()
            
            successful_updates = 0
            skipped_count = 0
            
            for kline in kline_data:
                if len(kline) >= 5:
                    timestamp = kline[0]  # 时间戳 ts
                    open_price = float(kline[1])   # 开盘价 o
                    high_price = float(kline[2])   # 最高价 h
                    low_price = float(kline[3])    # 最低价 l
                    close_price = float(kline[4])  # 收盘价 c
                    
                    # 转换时间戳
                    datetime_str = self.timestamp_to_datetime(timestamp)
                    
                    if not datetime_str:
                        continue
                    
                    # 检查是否已存在该时间戳的记录（且有HA数据）
                    check_sql = """
                    SELECT id FROM kline_comparison_sol_new_new 
                    WHERE token_address = %s AND timestamp = %s AND ha_open IS NOT NULL
                    """
                    cursor.execute(check_sql, (token_address, int(timestamp)))
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                        # 更新现有记录的OKX数据
                        update_sql = """
                        UPDATE kline_comparison_sol_new_new SET 
                            okx_open = %s, okx_high = %s, okx_low = %s, okx_close = %s, okx_time = %s
                        WHERE token_address = %s AND timestamp = %s
                        """
                        cursor.execute(update_sql, (
                            open_price, high_price, low_price, close_price, datetime_str,
                            token_address, int(timestamp)
                        ))
                        successful_updates += 1
                    else:
                        # 跳过没有HA数据的时间点
                        skipped_count += 1
            
            connection.commit()
            logger.info(f"Token {token_address} OKX数据 - 更新: {successful_updates}条, 跳过: {skipped_count}条")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"Token {token_address} 保存OKX数据失败: {e}")
    
    def process_token_with_pagination(self, token_address: str, token_symbol: str):
        """分页处理单个Token的数据获取"""
        total_updates = 0
        after_timestamp = None
        page_count = 0
        max_pages = 20  # 限制最大页数，避免无限循环
        
        try:
            while page_count < max_pages:
                page_count += 1
                logger.info(f"Token {token_address} 获取第 {page_count} 页数据...")
                
                # 获取K线数据
                kline_data = self.fetch_kline_data(token_address, after_timestamp)
                
                if not kline_data:
                    logger.warning(f"Token {token_address} 第 {page_count} 页未获取到数据，结束")
                    break
                
                # 保存数据
                before_updates = total_updates
                self.save_okx_kline_data(token_address, token_symbol, kline_data)
                
                # 检查是否还有更多数据（OKX通常按时间倒序返回）
                if len(kline_data) < int(self.request_params['limit']):
                    logger.info(f"Token {token_address} 数据已获取完毕（第 {page_count} 页）")
                    break
                
                # 设置下一页的after参数（最后一条记录的时间戳）
                after_timestamp = kline_data[-1][0]
                
                # 请求间隔
                time.sleep(0.5)  # 分页请求间隔较短
                
        except Exception as e:
            logger.error(f"Token {token_address} 分页处理异常: {e}")
    
    def run(self):
        """运行OKX平台爬虫主程序"""
        logger.info("开始运行OKX平台K线数据爬虫（三平台对比模式）")
        
        try:
            # 1. 获取Token Address列表
            logger.info("获取Token Address列表...")
            token_list = self.get_token_addresses()
            
            if not token_list:
                logger.error("未获取到Token Address列表，程序结束")
                return
            
            # 2. 依次处理每个Token
            total_tokens = len(token_list)
            for i, token_info in enumerate(token_list, 1):
                token_address = token_info['address']
                token_symbol = token_info['symbol']
                
                logger.info(f"[{i}/{total_tokens}] 处理Token: {token_symbol} ({token_address})")
                
                # 分页获取并处理数据
                self.process_token_with_pagination(token_address, token_symbol)
                
                # 请求间隔
                if i < total_tokens:  # 最后一个请求不需要等待
                    logger.info(f"等待 {self.request_interval} 秒...")
                    time.sleep(self.request_interval)
            
            logger.info("OKX平台K线数据爬虫运行完成")
            
        except Exception as e:
            logger.error(f"爬虫运行失败: {e}")
            raise

def main():
    """主函数"""
    spider = OKXSpider()
    spider.run()

if __name__ == "__main__":
    main()
