import requests
import re
import time
import redis
import random
from loguru import logger
from pymysql.cursors import DictCursor
from html import unescape
import pymysql
import os
import threading
from concurrent.futures import ThreadPoolExecutor
from threading import Lock


class INVESTMENTTEAM_SPIDER():
    def __init__(self):
        self.url = "https://dncapi.flink1.com/api/v3/coin/team"
        self.headers = {
            "authority": "dncapi.flink1.com",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "origin": "https://www.feixiaohao.com",
            "referer": "https://www.feixiaohao.com/",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.params = {
            "webp": "1"
        }
        # self.mysql_config = {
        #     'host': '**************',
        #     'port': 33060,
        #     'user': 'root',
        #     'password': '12345678',
        #     'db': 'fxh_data',
        #     'charset': 'utf8',
        #     'cursorclass': DictCursor
        # }
        self.mysql_config = {
            'host': '***************',
            'port': 3306,
            'user': 'spider',
            'password': 'ha13579.',
            'db': 'spider',
            'charset': 'utf8',
            'cursorclass': DictCursor
        }
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            password='123456',
            db=2,
            decode_responses=True
        )
        self.proxies = {
            'http': 'socks5://**************:30889',
            'https': 'socks5://**************:30889'
        }

        self.team_logo_dir = '/data/spider_icons/TOKEN_ICON/token_basic_info/team_agency_logo/team_logo'
        self.agency_logo_dir = '/data/spider_icons/TOKEN_ICON/token_basic_info/team_agency_logo/agency_logo'

        for directory in [self.team_logo_dir, self.agency_logo_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"创建目录: {directory}")

        self.conn = pymysql.connect(**self.mysql_config)
        self.request_count = 0

        # 添加线程安全相关的属性
        self.request_lock = Lock()
        self.last_request_time = 0
        self.min_request_interval = 1  # 最小请求间隔（秒）
        self.thread_local = threading.local()  # 线程本地存储

    def clean_text(self, text):
        if not text:
            return ''
        text = unescape(text)
        text = re.sub(r'<[^>]+>', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        text = re.sub(r'[\s\u3000]+', ' ', text)
        text = re.sub(r'\(\s*\)', '', text)
        return text.strip()

    def clean_filename(self, filename):
        if not filename:
            return ''
        filename = re.sub(r'[\\/*?:"<>|]', '', filename)
        filename = re.sub(r'\（.*?\）', '', filename)
        filename = re.sub(r'\(.*?\)', '', filename)
        filename = ' '.join(filename.split())
        return filename.strip()

    def download_logo(self, url, code, name, is_team=True):
        try:
            if not url:
                return None

            clean_name = self.clean_filename(name)
            if not clean_name:
                logger.warning(f"清理后的名称为空，跳过下载: {name}")
                return None

            filename = f"{code}_{clean_name}.png"
            save_dir = self.team_logo_dir if is_team else self.agency_logo_dir
            save_path = os.path.join(save_dir, filename)

            relative_path = f"/icon/token_basic_info/team_agency_logo/{'team_logo' if is_team else 'agency_logo'}/{filename}"

            if os.path.exists(save_path):
                logger.info(f"文件已存在，跳过下载: {filename}")
                return relative_path

            response = requests.get(
                url,
                headers=self.headers,
                proxies=self.proxies,
                timeout=10
            )

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                logger.success(f"成功下载: {filename}")
                return relative_path
            else:
                logger.error(f"下载失败 {name}, 状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"下载出错 {name}: {str(e)}")
            return None

    def get_code_from_redis(self):
        try:
            code = self.redis_client.spop('feixiaohao:coin_codes')
            if code:
                logger.info(f'从Redis获取到code: {code}')
                return code
            return None
        except Exception as e:
            logger.error(f'从Redis获取code失败: {e}')
            return None

    def get_db_connection(self):
        """为每个线程获取独立的数据库连接"""
        if not hasattr(self.thread_local, 'conn'):
            self.thread_local.conn = pymysql.connect(**self.mysql_config)
        return self.thread_local.conn

    def close_db_connection(self):
        """关闭当前线程的数据库连接"""
        if hasattr(self.thread_local, 'conn'):
            self.thread_local.conn.close()
            del self.thread_local.conn

    def wait_for_request(self):
        """控制请求频率"""
        with self.request_lock:
            current_time = time.time()
            time_since_last_request = current_time - self.last_request_time
            if time_since_last_request < self.min_request_interval:
                sleep_time = self.min_request_interval - time_since_last_request
                time.sleep(sleep_time)
            self.last_request_time = time.time()

    def fetch_data(self):
        """添加请求频率控制"""
        try:
            self.wait_for_request()  # 控制请求频率
            response = requests.get(
                self.url, 
                headers=self.headers, 
                params=self.params, 
                proxies=self.proxies,
                timeout=10
            )
            logger.info(f'线程 {threading.current_thread().name} 请求URL: {self.url}')
            return response.json()
        except Exception as e:
            logger.error(f'获取数据时出错: {e}')
            return None

    def process_data(self, data, code):
        if not data:
            return False  # 添加返回值表示处理状态

        team_data = data.get('data', {}).get('team', [])
        agency_data = data.get('data', {}).get('agency', [])
        
        logger.info(f"开始处理code: {code} 的数据，团队数量: {len(team_data)}，机构数量: {len(agency_data)}")

        # 先下载所有图片
        all_downloads_success = True
        team_info_list = []
        agency_info_list = []

        # 处理团队数据
        for team in team_data:
            team_name = self.clean_text(team.get('name', ''))
            team_logo_url = team.get('logo', '')
            
            if team_logo_url and team_name:
                logger.info(f"开始下载团队 {team_name} 的logo")
                team_route = self.download_logo(team_logo_url, code, team_name, is_team=True)
                if team_route:
                    team_info_list.append({
                        'team_name': team_name,
                        'team_logo': team_logo_url,
                        'team_route': team_route
                    })
                else:
                    all_downloads_success = False
                    logger.error(f"团队 {team_name} 的logo下载失败")

        # 处理机构数据
        for agency in agency_data:
            agency_name = self.clean_text(agency.get('name', ''))
            agency_logo_url = agency.get('logo', '')
            
            if agency_logo_url and agency_name:
                logger.info(f"开始下载机构 {agency_name} 的logo")
                agency_route = self.download_logo(agency_logo_url, code, agency_name, is_team=False)
                if agency_route:
                    agency_info_list.append({
                        'agency_name': agency_name,
                        'agency_logo': agency_logo_url,
                        'agency_route': agency_route
                    })
                else:
                    all_downloads_success = False
                    logger.error(f"机构 {agency_name} 的logo下载失败")

        # 只有当所有下载都成功时，才进行数据库操作
        if all_downloads_success:
            try:
                # 数据库操作放在一个事务中
                if team_info_list:
                    self.insert_team_data_to_mysql(team_info_list, code)
                if agency_info_list:
                    self.insert_agency_data_to_mysql(agency_info_list, code)
                logger.success(f"成功完成code: {code} 的所有数据处理")
                return True
            except Exception as e:
                logger.error(f"数据库操作失败: {e}")
                return False
        else:
            logger.error(f"code: {code} 的某些图片下载失败，跳过数据库操作")
            return False

    def insert_team_data_to_mysql(self, parsed_team_data, code):
        if not parsed_team_data:
            return

        try:
            with self.conn.cursor() as cursor:
                # 先检查是否存在记录
                check_sql = "SELECT id FROM fxh_team_agency_data WHERE code = %s AND team_name = %s"
                insert_sql = """
                    INSERT INTO fxh_team_agency_data 
                    (code, team_name, team_logo, team_route)
                    VALUES (%s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    team_logo = VALUES(team_logo),
                    team_route = VALUES(team_route)
                """
                
                for team in parsed_team_data:
                    team_name = team['team_name']
                    team_logo = team['team_logo']
                    team_route = team['team_route']
                    
                    # 记录要插入的数据
                    logger.info(f"准备插入团队数据 - code: {code}, name: {team_name}, route: {team_route}")
                    
                    cursor.execute(check_sql, (code, team_name))
                    result = cursor.fetchone()
                    
                    if result:
                        # 如果记录存在，更新
                        update_sql = """
                            UPDATE fxh_team_agency_data 
                            SET team_logo = %s, team_route = %s
                            WHERE code = %s AND team_name = %s
                        """
                        cursor.execute(update_sql, (team_logo, team_route, code, team_name))
                        logger.info(f"更新团队数据 - code: {code}, name: {team_name}")
                    else:
                        # 如果记录不存在，插入
                        cursor.execute(insert_sql, (code, team_name, team_logo, team_route))
                        logger.info(f"插入新团队数据 - code: {code}, name: {team_name}")
                
            self.conn.commit()
            logger.success(f"成功处理团队数据，code: {code}")
        except Exception as e:
            logger.error(f"处理团队数据时出错: {e}")
            self.conn.rollback()

    def insert_agency_data_to_mysql(self, parsed_agency_data, code):
        if not parsed_agency_data:
            return

        try:
            with self.conn.cursor() as cursor:
                # 先检查是否存在记录
                check_sql = "SELECT id FROM fxh_team_agency_data WHERE code = %s AND agency_name = %s"
                insert_sql = """
                    INSERT INTO fxh_team_agency_data 
                    (code, agency_name, agency_logo, agency_route)
                    VALUES (%s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    agency_logo = VALUES(agency_logo),
                    agency_route = VALUES(agency_route)
                """
                
                for agency in parsed_agency_data:
                    agency_name = agency['agency_name']
                    agency_logo = agency['agency_logo']
                    agency_route = agency['agency_route']
                    
                    # 记录要插入的数据
                    logger.info(f"准备插入机构数据 - code: {code}, name: {agency_name}, route: {agency_route}")
                    
                    cursor.execute(check_sql, (code, agency_name))
                    result = cursor.fetchone()
                    
                    if result:
                        # 如果记录存在，更新
                        update_sql = """
                            UPDATE fxh_team_agency_data 
                            SET agency_logo = %s, agency_route = %s
                            WHERE code = %s AND agency_name = %s
                        """
                        cursor.execute(update_sql, (agency_logo, agency_route, code, agency_name))
                        logger.info(f"更新机构数据 - code: {code}, name: {agency_name}")
                    else:
                        # 如果记录不存在，插入
                        cursor.execute(insert_sql, (code, agency_name, agency_logo, agency_route))
                        logger.info(f"插入新机构数据 - code: {code}, name: {agency_name}")
                
            self.conn.commit()
            logger.success(f"成功处理机构数据，code: {code}")
        except Exception as e:
            logger.error(f"处理机构数据时出错: {e}")
            self.conn.rollback()

    def process_single_code(self, code):
        """处理单个code的完整流程"""
        try:
            logger.info(f"线程 {threading.current_thread().name} 开始处理新的code: {code}")
            self.params['code'] = code
            
            data = self.fetch_data()
            if not data:
                logger.error(f"获取code: {code}的数据失败")
                return False
            
            success = self.process_data(data, code)
            
            if success:
                logger.success(f"线程 {threading.current_thread().name} 成功完成code: {code}的所有处理")
            else:
                logger.error(f"线程 {threading.current_thread().name} code: {code}的处理过程中出现错误")
            
            return success
            
        except Exception as e:
            logger.error(f"处理code: {code}时发生错误: {e}")
            return False
        finally:
            # 确保关闭数据库连接
            self.close_db_connection()

    def worker(self):
        """工作线程函数"""
        while True:
            try:
                # 获取一个code
                code = self.get_code_from_redis()
                if not code:
                    logger.info(f"线程 {threading.current_thread().name} 完成所有任务")
                    break
                
                # 处理code
                success = self.process_single_code(code)
                
                # 根据处理结果决定休眠时间
                if success:
                    sleep_time = random.uniform(1, 2)
                else:
                    sleep_time = random.uniform(3, 5)
                
                logger.info(f"线程 {threading.current_thread().name} 休息 {sleep_time:.2f} 秒")
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"线程 {threading.current_thread().name} 发生错误: {e}")
                time.sleep(5)  # 发生错误时多等待一会

    def run_with_threads(self, num_threads=4):
        """使用线程池运行爬虫"""
        try:
            logger.info(f"启动 {num_threads} 个线程开始运行")
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                # 提交多个工作线程
                futures = [executor.submit(self.worker) for _ in range(num_threads)]
                # 等待所有线程完成
                for future in futures:
                    future.result()
                    
        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
        finally:
            logger.info("所有线程运行完成")


if __name__ == '__main__':
    spider = INVESTMENTTEAM_SPIDER()
    spider.run_with_threads(4)  # 使用4个线程运行