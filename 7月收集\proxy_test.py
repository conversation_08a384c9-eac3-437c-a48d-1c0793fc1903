import requests
import time
import sys

def test_proxy(proxy_url):
    """测试代理IP是否有效"""
    proxy = {
        "http": proxy_url,
        "https": proxy_url
    }
    
    # 测试网站列表
    test_urls = [
        "http://www.google.com",
        "https://www.youtube.com",
        "https://www.facebook.com"
    ]
    
    print(f"正在测试代理: {proxy_url}")
    
    for url in test_urls:
        try:
            start_time = time.time()
            response = requests.get(url, proxies=proxy, timeout=10)
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                print(f"成功访问 {url}")
                print(f"  响应时间: {elapsed_time:.2f}秒")
                print(f"  状态码: {response.status_code}")
            else:
                print(f"✗ 访问 {url} 失败")
                print(f"  状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ 访问 {url} 出错: {str(e)}")
    
    try:
        ip_response = requests.get("https://api.ipify.org?format=json", proxies=proxy, timeout=10)
        if ip_response.status_code == 200:
            ip_info = ip_response.json()
            print(f"\n当前代理IP: {ip_info['ip']}")
    except Exception as e:
        print(f"无法获取IP信息: {str(e)}")

if __name__ == "__main__":
    # 默认使用文件中定义的代理
    proxy_api = "http://dgjm6ipcchif:hidzbgr325hn@*************:2333"
    
    # 如果命令行提供了代理URL，则使用命令行参数
    if len(sys.argv) > 1:
        proxy_api = sys.argv[1]
    
    test_proxy(proxy_api)
    print("\n测试完成！")