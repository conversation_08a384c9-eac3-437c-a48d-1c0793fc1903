# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2025/2/19 14:39
@Python  :  Python3.7
@Desc    :  None
"""
import json
import random
import time

import redis
from loguru import logger

from settings import *


class RedisClient:
    def __init__(
            self,
            redis_name,
            host=utils.REDIS_HOST,
            password=utils.REDIS_PASSWORD,
            port=utils.REDIS_PORT,
            db=utils.REDIS_DB_U
    ):
        self.db = redis.StrictRedis(host=host, port=port, password=password, db=db, decode_responses=True)
        self.redis_name = redis_name
        self.cookies_is_using_db = redis.StrictRedis(
            host=host, port=port, password=password, db=db, decode_responses=True)

    def set(self, username, value):
        """
        设置键值对
        :param username: 用户名
        :param value:
        :return:
        """
        return self.db.hset(self.redis_name, username, value)

    def get(self, username):
        """
        根据键名获取键值
        :param username: 用户名
        :return:
        """
        return self.db.hget(self.redis_name, username)

    def get_crawlable_user(self):
        """
        获取一个待爬取用户的user_name
        有3种状态：
            'null': 没爬过
            'crawling': 正在爬
            '1231455': 有userID，则已经爬完此用户
        【注】：若程序中断，重启程序需要先把状态为'crawling'的改为'null'，
               因为每次只会取状态为'null'的用户，然后再去进度记录里去看此用户上次爬到了哪个位置
        :return:
        """
        user_name = None
        for _user_name, user_id in self.all().items():
            if user_id == 'null':
                user_name = _user_name
                break

        if user_name:
            self.set(user_name, 'crawling')

        return user_name

    def reset_username_status(self):
        """
        重置username的状态（用于程序中断）
        若程序中断，重启程序需要先把状态为'crawling'的改为'null'
        :return:
        """
        for _user_name, user_id in self.all().items():
            if user_id == 'crawling':
                self.set(_user_name, 'null')

    def get_usable_cookies(self):
        """
        获取一个可用的cookies
        :return:
        """
        account = None
        login_info = None
        for _account, _login_info in self.all().items():
            # 防止多进程拿到同一个cookies
            if _account in self.cookies_is_using_db.hvals(REDIS_COOKIES_IS_USING):
                continue

            _login_info = json.loads(_login_info)
            last_429_time = _login_info.get('UserTweets_last_429_time')
            if last_429_time is None or last_429_time is True or ((int(time.time()) - int(last_429_time)) > 60*60):  # 每过x分钟拿出来请求
                account = _account
                login_info = _login_info
                break
        return account, login_info

    def random(self):
        """
        随机Cookies获取
        :return:
        """
        return random.choice(self.db.hvals(self.redis_name))

    def all(self):
        """
        获取所有键值对
        :return:
        """
        return self.db.hgetall(self.redis_name)

    def delete(self, key):
        self.db.hdel(self.redis_name, key)
