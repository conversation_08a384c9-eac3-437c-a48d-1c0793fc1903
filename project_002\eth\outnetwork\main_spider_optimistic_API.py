# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2023/11/14 13:19
@Python  :  Python3.7
@Desc    :  https://etherscan.io/labelcloud  手动登录复制cookie
            [注意]user-agent的版本要一致（生成cookies的 与 参数headers的）
"""

from settings import *
import re
import random
import time
import json
import phoenixdb
import requests
# from curl_cffi import requests
from datetime import date
from login_optimistic import Login, LoginException
from loguru import logger
from lxml import etree
import redis

from Spiders.Spider import Spider_Crawler_Request


class MainSpider(Spider_Crawler_Request):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.login = Login()
        self.chain_name = 'ETH'
        self.cookies = self.get_cookies_from_redis()
        print('Cookie information retrieved:', self.cookies)
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        self.user_agent = 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        self.crawl_date = date.today()

    def get_cookies_from_redis(self):
        redis_client = redis.Redis(
            host='**************',
            port=6379,
            db=14,
            password='123456',
            decode_responses=True
        )
        cookies_json = redis_client.get('API_COOKIES')
        logger.info(f'已经从redis中获取到cookie:{cookies_json}')
        if cookies_json:
            return json.loads(cookies_json)
        else:
            raise Exception("Failed to retrieve cookies from Redis.")

    def req_page(self, url, method='GET', headers=None, params=None, data=None):
        count = 3
        while count > 0:
            time.sleep(2.2 + random.random())
            logger.info(f'Requesting URL: {url}')
            try:
                response = requests.request(method=method, url=url, headers=headers, cookies=self.cookies, proxies=proxies_api,
                                            params=params, data=data, timeout=20)
            except requests.exceptions.RequestException as e:
                logger.error(f"Request error: {e}")
            else:
                if response.status_code == 200:
                    return response
                else:
                    logger.error(f"HTTP error {response.status_code}")

            count -= 1
        return False

    def conn_phoenix(self):
        conn = phoenixdb.connect("http://**************:8765", authentication=False)
        cursor = conn.cursor()
        return conn, cursor
        pass

    def close_phoenix(self, conn, cursor):
        conn.commit()
        cursor.close()
        conn.close()
        pass

    def judge_and_upsert(self, address, name_tag, cursor):
        """
        判断是否可以插入：
        1、有本链，则判断本链的TAG_EXCHANGE；无本链，但有ALL，则判断ALL的TAG_EXCHANGE, 判断是否满足以下2条：
            a、"不是交易所（字段TAG_EXCHANGE为空 或者 开头不是1）" 则插入；
            b、"是交易所并且是User（字段TAG_EXCHANGE开头是1末尾是3）" 则插入；
        2、表里不存在这个地址，则插入;
        3、查出来有这个地址，但是链既不是本链，也不是ALL，则插入
        """


        logger.info(f'address: {address}, name_tage: {name_tag}')
        can_upsert = False
        judge_chain = None
        need_judge_tag_exchange = False

        sql = f"""
            SELECT CHAIN_NAME, TAG_EXCHANGE
            FROM COMMON.PUBLIC_NAME_TAG
            WHERE ADDRESS='{address}'
        """
        ret = self.select_of_phoenix(sql=sql, phoenixdb_conn='http://**************:8765')

        if not ret:
            # 情况2
            can_upsert = True
        else:
            chain_list = []
            for item in ret:
                chain_name = item[0]
                chain_list.append(chain_name)

            # 情况1
            if self.chain_name in chain_list:
                judge_chain = self.chain_name
                need_judge_tag_exchange = True
            elif 'ALL' in chain_list:
                judge_chain = 'ALL'
                need_judge_tag_exchange = True
            # 情况3
            else:
                can_upsert = True
                need_judge_tag_exchange = False

            # 情况1
            if need_judge_tag_exchange:
                for item in ret:
                    chain_name = item[0]
                    tag_exchange = item[1]
                    if chain_name == judge_chain:
                        # 不是交易所
                        if tag_exchange is None or not str(tag_exchange).startswith('1'):
                            can_upsert = True
                        else:  # 是交易所 并且是user
                            if str(tag_exchange).endswith('3'):
                                can_upsert = True

                    if can_upsert:
                        break

        if can_upsert:
            sql = f"""
                UPSERT INTO COMMON.PUBLIC_NAME_TAG(ADDRESS,CHAIN_NAME,SHOW_NAME)
                VALUES('{address}', '{self.chain_name}', '{name_tag}')
            """
            cursor.execute(sql)

            sql = f"""
                INSERT INTO label_cloud(CHAIN,WALLET_ADDRESS,SHOW_NAME,CRAWL_DATE)
                VALUES('{self.chain_name}', '{address}', '{name_tag}', '{self.crawl_date}');
            """
            try:
                self.insert_mysql(sql)
                print(f"已存入一条数据, address:{address}")
            except Exception as e:
                logger.error(repr(e))

    def get_subCategoryId(self, href) -> list:
        # url = 'https://etherscan.io' + href
        url = 'https://optimistic.etherscan.io' + href

        headers = {
            "User-Agent": 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        response = self.req_page(url=url, headers=self.headers)

        if response is False:
            self.set_seed(seed_key, {'detail_href': href})
            return []

        class LoginException(Exception):
            pass

        login_status, error_msg = self.login.check_login_status(response.text)
        if not login_status:
            self.set_seed(seed_key, {'detail_href': href})
            self.send_to_fs({'Program': 'eth_label_cloud', 'Error': error_msg})
            raise LoginException(error_msg)

        subCategoryIds = []
        html = etree.HTML(response.text)
        # lis = html.xpath('//*[@id="content"]/section[3]/ul/li') # //*[@id="content"]//li
        lis = html.xpath('*[@id="content"]//li')
        if len(lis) == 0:
            subcatid = 'undefined'
            subCategoryIds.append(subcatid)
        else:
            for li in lis:
                subcatid = li.xpath('./a/@val')[0]
                subCategoryIds.append(subcatid)

        return subCategoryIds

    def detail_page(self, label, subCategoryId, start=0):
        logger.info(f'label:{label}, subCategoryId:{subCategoryId}, start:{start}')
        url = 'https://optimistic.etherscan.io/accounts.aspx/GetTableEntriesBySubLabel'

        headers = {
            "User-Agent": 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        data = {"dataTableModel": {"draw": 1, "columns": [
            {"data": "address", "name": "", "searchable": "true", "orderable": "false",
             "search": {"value": "", "regex": "false"}},
            {"data": "nameTag", "name": "", "searchable": "true", "orderable": "false",
             "search": {"value": "", "regex": "false"}},
            {"data": "balance", "name": "", "searchable": "true", "orderable": "true",
             "search": {"value": "", "regex": "false"}},
            {"data": "txnCount", "name": "", "searchable": "true", "orderable": "true",
             "search": {"value": "", "regex": "false"}}],
                                   "order": [{"column": 1, "dir": "asc"}], "start": start, "length": 100,
                                   "search": {"value": "", "regex": "false"}},
                "labelModel": {"label": label}}

        if subCategoryId != 'undefined':
            data['labelModel']['subCategoryId'] = subCategoryId

        response = self.req_page(url=url, method='POST', headers=headers, data=json.dumps(data))
        if response is False:
            return

        data_json = response.json()['d']
        currentPage = data_json['currentPage']  # str
        totalPage = data_json['totalPage']  # str

        conn, cursor = self.conn_phoenix()
        for item in data_json['data']:
            address_html_text = item['address']
            logger.info(address_html_text)
            address = re.compile(r"data-bs-title='(.*?)'>").search(address_html_text).group(1)
            logger.info(address)
            name_tag = item['nameTag'].replace("'", "\\'")
            logger.info(name_tag)
            logger.info(f'address:{address}, name_tag:{name_tag}')
            if not name_tag:
                continue

            if not address.startswith('0x'):
                logger.error(f'address not startswith 0x, address: {address}')
                continue
            self.judge_and_upsert(address, name_tag, cursor)
            logger.info(f'already insert, address: {address}, name:{name_tag}')

        self.close_phoenix(conn, cursor)

        if currentPage == totalPage:
            return
        else:
            self.detail_page(label=label, subCategoryId=subCategoryId, start=start + 100)

    def run(self):
        while self.get_scard(seed_key):
            detail_href = self.get_seed(seed_key)['detail_href']
            logger.info(f"目前正在访问的detail page: {detail_href}")
            label = detail_href.split('/')[-1].split('?')[0]
            subCategoryIds = self.get_subCategoryId(detail_href)  # ['undefined'] or ['0', '1']
            for subCategoryId in subCategoryIds:
                self.detail_page(label, subCategoryId=subCategoryId)
            # break
        print("all data has saved")

    def test(self):
        conn, cursor = self.conn_phoenix()
        self.judge_and_upsert('0x000000000000093dfd365e70a598de99c41ec840', '3333', cursor)
        self.close_phoenix(conn, cursor)


if __name__ == '__main__':
    """
    数量过大，请求超时的：
        ['/accounts/label/beacon-depositor', '/accounts/label/uniswap']
    """
    # conn, cursor = MainSpider().conn_phoenix()
    MainSpider().run()
    # MainSpider().test()

