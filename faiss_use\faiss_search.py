"""
pip install sentence-transformers faiss-cpu numpy jieba gensim
"""

"""
测试，按照关键词去匹配向量库中的数据
"""

import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
import os
import pymysql
from prettytable import PrettyTable
import jieba
import re
import json
from collections import Counter

# 添加更全面的中文停用词表
STOPWORDS_FILE = "stopwords.txt"

def load_stopwords(file_path=STOPWORDS_FILE):
    """加载停用词表"""
    if os.path.exists(file_path):
        with open(file_path, "r", encoding="utf-8") as f:
            return set([line.strip() for line in f])
    else:
        # 默认停用词，如果文件不存在
        return {'的', '了', '和', '是', '在', '我', '有', '个', '这', '那', '你', '我们', '他们', '它',
                '就', '都', '而', '及', '与', '着', '或', '一个', '没有', '因为', '但是', '为了', 
                '如果', '因此', '所以', '那么', '只是', '于是', '故而', '虽然', '尽管', '除了', 
                '一些', '很多', '一样', '一直', '只要', '一般', '一种', '一定', '一天', '一次',
                '一边', '不会', '不要', '这个', '那个', '这些', '那些', '自己', '什么', '这样', 
                '那样', '怎么', '怎样', '如何', '如此', '只有', '大家', '以及', '可以', '不是', 
                '不能', '这种', '那种', '其他', '之前', '之后', '几乎', '差不多', '这里', '那里',
                '不过', '因而', '所有', '无论', '然后', '还是', '还有', '其实', '甚至', '不仅',
                '不但', '并且', '也许', '可能', '比较', '应该', '由于', '正在', '已经', '曾经',
                '将来', '之所以', '如今', '虽说', '除此之外', '换句话说', '总的来说', '总而言之'}

def load_id_map(file_path="id_map.txt"):
    """加载ID映射文件"""
    id_map = []
    if not os.path.exists(file_path):
        print(f"错误: ID映射文件 {file_path} 不存在")
        return []
        
    with open(file_path, "r", encoding="utf-8") as f:
        for line in f:
            id_map.append(line.strip())
    return id_map

def load_faiss_index(file_path="news_index.faiss"):
    """加载FAISS索引"""
    if not os.path.exists(file_path):
        print(f"错误: FAISS索引文件 {file_path} 不存在")
        return None

    try:
        index = faiss.read_index(file_path)
        return index
    except Exception as e:
        print(f"加载FAISS索引时出错: {e}")
        return None

def preprocess_query(query_text, stopwords=None):
    """预处理查询文本"""
    if stopwords is None:
        stopwords = load_stopwords()

    # 去除特殊字符
    query_text = re.sub(r'[^\w\s]', ' ', query_text)
    
    # 使用jieba进行更精确的分词
    jieba.initialize()  # 确保jieba已初始化
    words = jieba.cut(query_text)
    
    # 去除停用词和空白词
    filtered_words = [word for word in words if word not in stopwords and len(word.strip()) > 0]
    
    # 重新组合为查询文本
    processed_query = ' '.join(filtered_words)
    return processed_query

def expand_query(query, top_n=3):
    """
    扩展查询，增加同义词或相关词
    这里使用简单的方法，实际应用中可以使用更复杂的同义词库或词向量模型
    """
    # 这里是一个简单的示例同义词映射
    # 实际应用中可以使用更全面的同义词库或词向量模型
    synonym_map = {
        "经济": ["金融", "财政", "贸易", "商业"],
        "科技": ["技术", "创新", "数字", "互联网"],
        "医疗": ["健康", "医院", "药品", "疾病"],
        "教育": ["学习", "学校", "培训", "教学"],
        "政治": ["政府", "政策", "国家", "法律"],
        "环保": ["环境", "生态", "绿色", "可持续"],
        "体育": ["运动", "比赛", "健身", "奥运"],
        "娱乐": ["明星", "电影", "音乐", "游戏"],
        "军事": ["国防", "武器", "战争", "安全"],
        "文化": ["艺术", "传统", "历史", "文学"]
    }
    
    # 分词
    words = list(jieba.cut(query))
    expanded_words = words.copy()
    
    # 对每个词尝试扩展
    for word in words:
        if word in synonym_map:
            # 添加top_n个同义词
            expanded_words.extend(synonym_map[word][:top_n])
    
    # 返回扩展后的查询
    return ' '.join(expanded_words)

def search_similar(query_text, model, index, id_map, top_k=5, use_preprocessing=True, 
                   use_query_expansion=False, similarity_threshold=0.6, use_weighted_search=False):
    """搜索相似内容"""
    # 加载停用词
    stopwords = load_stopwords()
    
    # 预处理查询文本
    if use_preprocessing:
        processed_query = preprocess_query(query_text, stopwords)
        print(f"原始查询: {query_text}")
        print(f"处理后查询: {processed_query}")
        # 如果处理后的查询为空，则使用原始查询
        if not processed_query:
            processed_query = query_text
    else:
        processed_query = query_text
    
    # 查询扩展
    if use_query_expansion and processed_query:
        expanded_query = expand_query(processed_query)
        print(f"扩展后查询: {expanded_query}")
        search_query = expanded_query
    else:
        search_query = processed_query
    
    # 将查询文本转换为向量
    query_vector = model.encode([search_query], normalize_embeddings=True)
    
    # 搜索最相似的向量 - 多检索一些结果，以便后续过滤
    search_k = min(top_k * 3, 100)  # 多检索一些，但不超过100
    distances, indices = index.search(np.array(query_vector).astype("float32"), search_k)
    
    results = []
    for i, (dist, idx) in enumerate(zip(distances[0], indices[0])):
        if idx < len(id_map) and idx >= 0:  # 确保索引有效
            # 使用余弦相似度（FAISS使用L2距离，需要转换）
            # 对于归一化向量，余弦相似度 = 1 - L2^2/2
            similarity = 1 - float(dist) / 2
            
            # 应用相似度阈值过滤
            if similarity >= similarity_threshold:
                results.append({
                    "rank": i + 1,
                    "id": id_map[idx],
                    "distance": float(dist),
                    "similarity": similarity
                })
    
    # 如果使用加权搜索，计算关键词权重
    if use_weighted_search and processed_query:
        # 获取查询中的关键词
        query_keywords = processed_query.split()
        
        # 为每个结果获取内容并计算关键词匹配度
        conn = connect_to_database()
        if conn:
            for result in results:
                news = get_news_by_id(conn, result["id"])
                if news:
                    # 计算标题和内容中关键词出现的频率
                    title_content = f"{news['title']} {news['content'][:1000]}"
                    # 分词
                    content_words = list(jieba.cut(title_content.lower()))
                    # 计算关键词匹配度
                    keyword_matches = sum(1 for word in query_keywords if word.lower() in content_words)
                    # 计算加权分数
                    weight = 0.3 + (0.7 * (keyword_matches / len(query_keywords)) if query_keywords else 0)
                    # 调整相似度分数
                    result["similarity"] = result["similarity"] * weight
            conn.close()
    
    # 按相似度降序排序
    results.sort(key=lambda x: x["similarity"], reverse=True)
    
    # 只保留top_k个结果
    results = results[:top_k]
    
    # 更新排名
    for i, result in enumerate(results):
        result["rank"] = i + 1
    
    return results

def print_index_stats(index):
    """打印索引统计信息"""
    print(f"=== FAISS索引统计信息 ===")
    print(f"向量维度: {index.d}")
    print(f"向量数量: {index.ntotal}")
    print(f"索引类型: {type(index).__name__}")
    
    # 尝试获取更详细的索引信息
    try:
        if hasattr(index, 'nlist'):
            print(f"聚类数量(nlist): {index.nlist}")
        if hasattr(index, 'nprobe'):
            print(f"搜索探测数(nprobe): {index.nprobe}")
    except:
        pass

def connect_to_database():
    """连接到MySQL数据库"""
    try:
        conn = pymysql.connect(
            host="**************",
            port=33060,
            user="root",
            password="12345678",
            database="spider",
            charset="utf8mb4"
        )
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def get_news_by_id(conn, news_id):
    """根据ID获取新闻内容"""
    try:
        cursor = conn.cursor()
        # 查询新闻的标题、发布日期、内容和URL
        cursor.execute("SELECT id, title, publish_date, content, url FROM news WHERE id = %s", (news_id,))
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            return {
                "id": result[0],
                "title": result[1],
                "publish_date": result[2],
                "content": result[3],
                "url": result[4] if len(result) > 4 else ""  # 添加URL字段，如果不存在则为空字符串
            }
        else:
            print(f"未找到ID为 {news_id} 的新闻")
            return None
    except Exception as e:
        print(f"查询数据库时出错: {e}")
        return None

def display_news(news):
    """格式化显示新闻内容"""
    if not news:
        return
        
    print("\n" + "="*80)
    print(f"新闻ID: {news['id']}")
    print(f"标题: {news['title']}")
    print(f"发布日期: {news['publish_date']}")
    print("-"*80)
    
    # 显示内容，可能很长，所以做一些格式化处理
    content = news['content']
    if len(content) > 1000:
        print(f"{content[:1000]}...\n(内容过长，已截断显示)")
    else:
        print(content)
    print("="*80)

def analyze_query_results(query, results, conn):
    """分析查询结果，找出匹配度高的关键词"""
    if not results or not conn:
        return
    
    # 获取查询词
    query_words = list(jieba.cut(query))
    query_words = [w for w in query_words if len(w.strip()) > 1]  # 只保留有意义的词
    
    # 获取所有结果的内容
    all_content = ""
    for result in results[:3]:  # 只分析前3个结果
        news = get_news_by_id(conn, result["id"])
        if news:
            all_content += news["title"] + " " + news["content"][:500] + " "
    
    # 分词
    content_words = list(jieba.cut(all_content))
    word_freq = Counter(content_words)
    
    # 分析哪些查询词在结果中出现频率高
    query_word_freq = {word: word_freq.get(word, 0) for word in query_words}
    
    print("\n查询词在结果中的匹配情况:")
    for word, freq in query_word_freq.items():
        print(f"- '{word}': 出现 {freq} 次")
    
    # 提供改进建议
    if query_words:
        best_words = sorted(query_word_freq.items(), key=lambda x: x[1], reverse=True)
        if best_words:
            print(f"\n建议: 查询中最有效的词是 '{best_words[0][0]}'")
            low_freq_words = [w for w, f in query_word_freq.items() if f == 0]
            if low_freq_words:
                print(f"这些词在结果中没有出现: {', '.join(low_freq_words)}")

def save_search_config(config, file_path="search_config.json"):
    """保存搜索配置"""
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    print(f"配置已保存到 {file_path}")

def load_search_config(file_path="search_config.json"):
    """加载搜索配置"""
    if os.path.exists(file_path):
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except:
            pass
    # 默认配置
    return {
        "use_preprocessing": True,
        "use_query_expansion": False,
        "similarity_threshold": 0.6,
        "use_weighted_search": False,
        "top_k": 5
    }

def main():
    # 加载配置
    config = load_search_config()
    use_preprocessing = config.get("use_preprocessing", True)
    use_query_expansion = config.get("use_query_expansion", False)
    similarity_threshold = config.get("similarity_threshold", 0.6)
    use_weighted_search = config.get("use_weighted_search", False)
    top_k = config.get("top_k", 5)
    
    # 加载模型
    print("正在加载模型...")
    model = SentenceTransformer('BAAI/bge-small-zh')
    
    # 加载索引和ID映射
    print("正在加载FAISS索引...")
    index = load_faiss_index()
    if index is None:
        return
        
    id_map = load_id_map()
    if not id_map:
        return
    
    # 打印索引统计信息
    print_index_stats(index)
    print(f"ID映射数量: {len(id_map)}")
    
    # 连接数据库
    conn = connect_to_database()
    if conn is None:
        return
    
    # 交互式查询
    print("\n=== 向量搜索测试 ===")
    print("输入查询文本，或输入'exit'退出，输入'settings'修改设置")
    
    while True:
        query = input("\n请输入查询内容: ")
        if query.lower() == 'exit':
            break
        elif query.lower() == 'settings':
            print("\n=== 设置 ===")
            print(f"1. 查询预处理: {'开启' if use_preprocessing else '关闭'}")
            print(f"2. 查询扩展: {'开启' if use_query_expansion else '关闭'}")
            print(f"3. 相似度阈值: {similarity_threshold}")
            print(f"4. 加权搜索: {'开启' if use_weighted_search else '关闭'}")
            print(f"5. 返回结果数量: {top_k}")
            print("6. 返回搜索")
            print("7. 保存当前配置")
            
            setting_choice = input("请选择要修改的设置 (1-7): ")
            if setting_choice == '1':
                use_preprocessing = not use_preprocessing
                print(f"查询预处理已{'开启' if use_preprocessing else '关闭'}")
            elif setting_choice == '2':
                use_query_expansion = not use_query_expansion
                print(f"查询扩展已{'开启' if use_query_expansion else '关闭'}")
            elif setting_choice == '3':
                try:
                    new_threshold = float(input("请输入相似度阈值 (0.0-1.0): "))
                    if 0.0 <= new_threshold <= 1.0:
                        similarity_threshold = new_threshold
                        print(f"相似度阈值已设置为 {similarity_threshold}")
                    else:
                        print("无效的阈值，请输入0.0-1.0之间的数字")
                except ValueError:
                    print("请输入有效的数字")
            elif setting_choice == '4':
                use_weighted_search = not use_weighted_search
                print(f"加权搜索已{'开启' if use_weighted_search else '关闭'}")
            elif setting_choice == '5':
                try:
                    new_top_k = int(input("请输入返回结果数量 (1-20): "))
                    if 1 <= new_top_k <= 20:
                        top_k = new_top_k
                        print(f"返回结果数量已设置为 {top_k}")
                    else:
                        print("无效的数量，请输入1-20之间的数字")
                except ValueError:
                    print("请输入有效的数字")
            elif setting_choice == '7':
                # 保存当前配置
                config = {
                    "use_preprocessing": use_preprocessing,
                    "use_query_expansion": use_query_expansion,
                    "similarity_threshold": similarity_threshold,
                    "use_weighted_search": use_weighted_search,
                    "top_k": top_k
                }
                save_search_config(config)
            continue
            
        results = search_similar(
            query, model, index, id_map, top_k, 
            use_preprocessing=use_preprocessing,
            use_query_expansion=use_query_expansion,
            similarity_threshold=similarity_threshold,
            use_weighted_search=use_weighted_search
        )
        
        if not results:
            print("未找到相关结果")
            continue
            
        # 使用PrettyTable美化输出
        table = PrettyTable()
        table.field_names = ["排名", "新闻ID", "相似度"]
        for result in results:
            table.add_row([result['rank'], result['id'], f"{result['similarity']:.4f}"])
        
        print("\n搜索结果:")
        print(table)
        
        # 分析查询结果
        analyze_query_results(query, results, conn)
        
        while True:
            print("\n选项:")
            print("1. 查看某条新闻详情")
            print("2. 返回搜索")
            print("3. 退出程序")
            print("4. 修改设置")
            
            choice = input("请选择操作 (1/2/3/4): ")
            
            if choice == '1':
                news_idx = input(f"请输入要查看的排名 (1-{len(results)}): ")
                try:
                    idx = int(news_idx) - 1
                    if 0 <= idx < len(results):
                        news_id = results[idx]['id']
                        news = get_news_by_id(conn, news_id)
                        if news:
                            display_news(news)
                    else:
                        print(f"无效的排名，请输入1-{len(results)}之间的数字")
                except ValueError:
                    print("请输入有效的数字")
            elif choice == '2':
                break
            elif choice == '3':
                conn.close()
                return
            elif choice == '4':
                print("\n=== 设置 ===")
                print(f"1. 查询预处理: {'开启' if use_preprocessing else '关闭'}")
                print(f"2. 查询扩展: {'开启' if use_query_expansion else '关闭'}")
                print(f"3. 相似度阈值: {similarity_threshold}")
                print(f"4. 加权搜索: {'开启' if use_weighted_search else '关闭'}")
                print(f"5. 返回结果数量: {top_k}")
                
                setting_choice = input("请选择要修改的设置 (1-5): ")
                if setting_choice == '1':
                    use_preprocessing = not use_preprocessing
                    print(f"查询预处理已{'开启' if use_preprocessing else '关闭'}")
                elif setting_choice == '2':
                    use_query_expansion = not use_query_expansion
                    print(f"查询扩展已{'开启' if use_query_expansion else '关闭'}")
                elif setting_choice == '3':
                    try:
                        new_threshold = float(input("请输入相似度阈值 (0.0-1.0): "))
                        if 0.0 <= new_threshold <= 1.0:
                            similarity_threshold = new_threshold
                            print(f"相似度阈值已设置为 {similarity_threshold}")
                        else:
                            print("无效的阈值，请输入0.0-1.0之间的数字")
                    except ValueError:
                        print("请输入有效的数字")
                elif setting_choice == '4':
                    use_weighted_search = not use_weighted_search
                    print(f"加权搜索已{'开启' if use_weighted_search else '关闭'}")
                elif setting_choice == '5':
                    try:
                        new_top_k = int(input("请输入返回结果数量 (1-20): "))
                        if 1 <= new_top_k <= 20:
                            top_k = new_top_k
                            print(f"返回结果数量已设置为 {top_k}")
                        else:
                            print("无效的数量，请输入1-20之间的数字")
                    except ValueError:
                        print("请输入有效的数字")
                break
            else:
                print("无效选择，请重试")
    
    # 关闭数据库连接
    conn.close()

if __name__ == "__main__":
    main()