import redis
import requests
# import curl_cffi
import time
import json
from datetime import date
from loguru import logger
import re

proxy_yj = {
    "http": "*************************************************"
}

crawl_date = date.today()

redis_client = redis.Redis(
                host='**************',
                port=6379,
                db=14, # 14为测试库，原库为6
                password='123456',
                decode_responses=True
            )

def get_cookies_ua_from_redis():
    
    cookies_json = redis_client.get('API_optimistic_cookie_for_5s')
    logger.info(f'已经拿到通过5s盾的cookie: {cookies_json}')

    ua = redis_client.get('API_user_agent_for_5s')
    logger.info(f'已经拿到通过5s盾的user-agent: {ua}')

    token_for_5s = redis_client.get('token_for_yzm')
    logger.info(f'已经拿到token: {token_for_5s}')

    cookies = json.loads(cookies_json) if cookies_json else None
    ua = ua if ua else None
    token = token_for_5s if token_for_5s else None

    headers = {
        'User-Agent': ua if ua else 'default_user_agent'
    }

    if cookies is None or ua is None or token is None:
        logger.info("未能获取到必要的cookie、user-agent或token,退出程序。")
        return None, None, None

    return cookies, headers, token


def login_for_5s():
    cookies, headers, token = get_cookies_ua_from_redis()
    headers = {
        'User-Agent':'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
        'Referer':'https://optimistic.etherscan.io/login'
    }
    logger.info('正在拿取载荷参数')
    login_url = "https://optimistic.etherscan.io/login"
    resp1 = requests.post(login_url, headers=headers, cookies=cookies, proxies=proxy_yj)
    time.sleep(1)
    if resp1.status_code != 200:
        logger.info(f'获取登录页面失败，状态码：{resp1.status_code}')
        logger.info(f'{resp1.text}')
        return

    viewstate = re.search(r'__VIEWSTATE" value="(.*?)"', resp1.text)
    viewstate_generator = re.search(r'__VIEWSTATEGENERATOR" value="(.*?)"', resp1.text)
    event_validation = re.search(r'__EVENTVALIDATION" value="(.*?)"', resp1.text)
    logger.info(f'{viewstate},{viewstate_generator},{event_validation}')

if __name__ =='__main__':

    get_cookies_ua_from_redis()
    login_for_5s()