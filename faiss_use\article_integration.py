"""
文章整合工具 - 基于关键词自动生成微信公众号文章

功能：
1. 从关键词列表文件中读取关键词
2. 使用FAISS向量数据库检索相关文章
3. 使用DeepSeek模型整合文章内容
4. 直接将整合后的内容推送到微信公众号草稿箱
"""

import os
import sys
import argparse
import csv
import json
import time
from datetime import datetime, timedelta
import faiss
import numpy as np
import pymysql
from sentence_transformers import SentenceTransformer
from tqdm import tqdm
import requests
import jieba
import re
from PIL import Image
import io
from loguru import logger

# 导入自定义模块中的函数
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from faiss_use.faiss_search import (
    load_id_map, 
    load_faiss_index, 
    preprocess_query, 
    connect_to_database,
    get_news_by_id
)

# DeepSeek API
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"

# 微信公众号API
WECHAT_APPID = "wx63d68fc6d0bb2656"
WECHAT_APPSECRET = "8658fa717de61968a95f3d0029390bbc"

ARTICLE_TEMPLATE = """
# {title}

{content}

{conclusion}

> 本文由AI助手基于多篇相关文章整合生成,仅供参考。
> 发布时间：{publish_date}
"""

def load_keywords(file_path):
    """从文件中加载关键词列表"""
    if not os.path.exists(file_path):
        logger.info(f"错误: 关键词文件 {file_path} 不存在")
        return []

    keywords = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            keyword = line.strip()
            if keyword:
                keywords.append(keyword)

    logger.info(f"已加载 {len(keywords)} 个关键词")
    return keywords

def search_articles_for_keyword(keyword, model, index, id_map, conn, top_k=5, similarity_threshold=0.8):
    """为单个关键词搜索相关文章，只返回24小时内的新闻，提高相似度阈值，确保匹配精度"""
    logger.info(f"\n正在为关键词 '{keyword}' 搜索相关文章...")
    
    processed_keyword = preprocess_query(keyword)
    if not processed_keyword:
        processed_keyword = keyword

    # 将关键词转换为向量
    query_vector = model.encode([processed_keyword], normalize_embeddings=True)

    # 搜索最相似的向量
    distances, indices = index.search(np.array(query_vector).astype("float32"), top_k * 3)  # 多检索一些，因为后面会过滤时间、去重
    
    # 获取24小时前的时间点
    current_time = datetime.now()
    time_threshold = current_time - timedelta(hours=120)
    
    # 获取相关文章
    articles = []
    for i, (dist, idx) in enumerate(zip(distances[0], indices[0])):
        if idx < len(id_map) and idx >= 0:
            news_id = id_map[idx]
            similarity = 1 - float(dist) / 2  # 转换为相似度
            
            # 提高相似度阈值，确保匹配精度
            if similarity >= similarity_threshold:
                news = get_news_by_id(conn, news_id)
                if news:
                    if 'publish_date' in news and news['publish_date']:
                        try:
                            if isinstance(news['publish_date'], str):
                                publish_date = datetime.strptime(news['publish_date'], '%Y-%m-%d %H:%M:%S')
                            else:
                                publish_date = news['publish_date']
                                
                            if publish_date >= time_threshold:
                                news['similarity'] = similarity
                                # 确保URL字段存在
                                if 'url' not in news or not news['url']:
                                    news['url'] = f"https://news.source.com/article/{news['id']}"  # 生成一个默认URL
                                articles.append(news)
                        except Exception as e:
                            logger.info(f"解析发布时间出错: {e}, 使用默认处理")
                            # 如果无法解析时间，默认保留该新闻
                            news['similarity'] = similarity
                            if 'url' not in news or not news['url']:
                                news['url'] = f"https://news.source.com/article/{news['id']}"
                            articles.append(news)
                    else:
                        # 如果没有发布时间字段，默认保留
                        news['similarity'] = similarity
                        if 'url' not in news or not news['url']:
                            news['url'] = f"https://news.source.com/article/{news['id']}"
                        articles.append(news)
                        
        # 如果已经找到足够数量的文章，就停止
        if len(articles) >= top_k:
            break

    # 按相似度降序排序
    articles.sort(key=lambda x: x['similarity'], reverse=True)
    
    logger.info(f"找到 {len(articles)} 篇24小时内的相关文章，相似度阈值: {similarity_threshold}")
    
    # 如果没有找到足够相似的文章，返回空列表
    if len(articles) == 0:
        logger.info(f"警告: 关键词 '{keyword}' 没有找到足够相似的文章（阈值: {similarity_threshold}）")
    return articles

def generate_article_with_deepseek(keyword, articles, max_tokens=4000):
    """使用DeepSeek模型整合文章"""
    if not articles:
        logger.info(f"警告: 关键词 '{keyword}' 没有找到相关文章，无法生成内容")
        return None
    
    # 准备文章内容
    article_texts = []
    for i, article in enumerate(articles):
        # 添加URL信息
        url_info = f"\n原文链接: {article.get('url', '未提供')}" if article.get('url') else ""
        article_texts.append(f"文章{i+1}标题: {article['title']}\n文章{i+1}内容: {article['content'][:2000]}...{url_info}")
    
    combined_text = "\n\n".join(article_texts)
    if len(combined_text) > 12000:
        combined_text = combined_text[:12000] + "..."
    
    prompt = f"""
你是一位专业的微信公众号内容编辑，请根据以下关键词和相关文章内容，撰写一篇微信公众号文章。
关键词: {keyword}

相关文章内容:
{combined_text}

要求:
1. 生成一篇有吸引力的微信公众号文章，包含标题和正文
2. 标题要吸引人，能够引起读者兴趣
3. 正文内容要基于提供的文章，但不要直接复制，要有自己的组织和表达
4. 文章风格要通俗易懂，适合大众阅读
5. 文章结构要清晰，可以包含小标题、要点等
6. 文章末尾要有简短的总结或结论
7. 文章字数在300-500字之间
8. 在引用文章内容时，请在相关段落后添加原文链接，格式为: [原文链接](URL)

请按以下JSON格式返回:
```json
{{
  "title": "文章标题",
  "content": "文章正文（包含原文链接引用）",
  "conclusion": "文章结论"
}}
```
只返回JSON格式内容，不要有其他解释。
"""

    try:
        logger.info(f"正在使用DeepSeek整合关于 '{keyword}' 的文章...")
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
        }
        payload = {
            "model": "deepseek-chat",  # 使用DeepSeek的r1模型
            "messages": [
                {"role": "system", "content": "你是一位专业的内容编辑，擅长整合多篇文章成一篇连贯的微信公众号文章，你要做的不只是复制粘贴我给你的新闻标题，是要你自己根据这些新闻标题，自己组织语言，写出一篇有逻辑，有条理，有深度的文章,加长内容"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": max_tokens
        }
        
        response = requests.post(DEEPSEEK_API_URL, headers=headers, json=payload)
        response.raise_for_status()
        result = response.json()
        content = result["choices"][0]["message"]["content"].strip()
        
        # 解析JSON
        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = content

            # 清理可能的非JSON字符
            json_str = re.sub(r'^[^{]*', '', json_str)
            json_str = re.sub(r'[^}]*$', '', json_str)

            article_data = json.loads(json_str)
            return article_data
        except Exception as e:
            logger.info(f"解析DeepSeek响应时出错: {e}")
            logger.info(f"原始响应: {content}")
            # 尝试手动解析
            title_match = re.search(r'"title":\s*"([^"]*)"', content)
            content_match = re.search(r'"content":\s*"([^"]*)"', content)
            conclusion_match = re.search(r'"conclusion":\s*"([^"]*)"', content)

            if title_match and content_match:
                return {
                    "title": title_match.group(1).replace('\\n', '\n').replace('\\"', '"'),
                    "content": content_match.group(1).replace('\\n', '\n').replace('\\"', '"'),
                    "conclusion": conclusion_match.group(1).replace('\\n', '\n').replace('\\"', '"') if conclusion_match else "感谢阅读！"
                }
            else:
                return {
                    "title": f"关于{keyword}的深度解析",
                    "content": content,
                    "conclusion": "感谢阅读！"
                }
    except Exception as e:
        logger.info(f"调用DeepSeek API时出错: {e}")
        return None

def save_to_csv(articles, output_file):
    """将生成的文章保存到CSV文件"""
    fieldnames = ['keyword', 'title', 'content', 'publish_date', 'status']
    
    # 检查文件是否存在，决定是否写入表头
    file_exists = os.path.isfile(output_file)
    
    with open(output_file, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        if not file_exists:
            writer.writeheader()
        
        for article in articles:
            writer.writerow(article)

    logger.info(f"已将 {len(articles)} 篇文章保存到 {output_file}")

def generate_single_news_with_deepseek(keyword, article, max_tokens=2000):
    """对单条新闻进行AI润色优化，扩充内容但保持真实性"""
    if not article:
        logger.info(f"警告: 关键词 '{keyword}' 没有找到相关文章，无法生成内容")
        return None
    
    # 构建AI提示词
    prompt = f"""
请对以下新闻内容进行润色优化和适当扩充，使其风格通顺、吸引人，适合微信公众号快讯发布。

新闻标题：{article['title']}
新闻正文：{article['content']}

要求：
1. 保持新闻的真实性和准确性，不得改变原新闻的事实和核心信息
2. 在原新闻基础上适当扩充内容，可以：
   - 补充相关背景信息
   - 解释专业术语
   - 增加行业相关数据或趋势
   - 提供更详细的事件描述
3. 扩充的内容必须基于原文已有信息进行合理推断，不得添加虚构内容
4. 最终文章字数控制在500-800字之间

请严格按以下格式返回润色后的内容：

[润色后的简短标题，不超过20个字符]

[润色和扩充后的正文内容]

[原文链接]({article.get('url', '未提供')})

关键词：{keyword}

严格注意：
1. 第一行直接是标题，不要有任何前缀
2. 标题必须简短，不超过20个字符
3. 关键词信息放在最后一行
"""
    try:
        logger.info(f"正在润色关键词 '{keyword}' 的新闻ID {article['id']} ...")
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
        }
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {"role": "system", "content": "你是一位专业的新闻编辑，擅长对新闻进行润色优化和内容扩充。你需要确保扩充的内容真实可靠，基于原文信息进行合理推断，同时确保标题简洁有力，不超过20个字符。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,  # 提高温度，让AI更有创造性
            "max_tokens": max_tokens
        }
        response = requests.post(DEEPSEEK_API_URL, headers=headers, json=payload)
        response.raise_for_status()
        result = response.json()
        content = result["choices"][0]["message"]["content"].strip()
        return content
    except Exception as e:
        print(f"调用DeepSeek API时出错: {e}")
        return None

def save_single_news_to_txt(keyword, article, ai_content, output_dir="articles_single"):
    """保存单篇新闻到txt文件"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    # 文件名：关键词_新闻ID.txt
    safe_keyword = str(keyword).replace("/", "_").replace("\\", "_")
    file_name = f"{safe_keyword}_{article['id']}.txt"
    file_path = os.path.join(output_dir, file_name)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(ai_content)
    logger.info(f"已保存：{file_path}")

# 微信公众号API相关
def get_wechat_access_token():
    """获取微信公众号接口调用凭证access_token"""
    url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={WECHAT_APPID}&secret={WECHAT_APPSECRET}"
    try:
        response = requests.get(url)
        response.raise_for_status()
        result = response.json()
        if 'access_token' in result:
            return result['access_token']
        else:
            logger.info(f"获取access_token失败: {result.get('errmsg', '未知错误')}")
            return None
    except Exception as e:
        logger.info(f"获取access_token时出错: {e}")
        return None

def upload_default_image(access_token):
    """上传默认封面图片并获取media_id
    
    由于微信草稿箱接口强制要求封面图，我们上传一个默认图片作为封面
    """
    url = f"https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={access_token}&type=image"
    
    # 生成一个简单的白色图片
    default_img = np.ones((400, 400, 3), dtype=np.uint8) * 255
    img = Image.fromarray(default_img.astype('uint8'))
    
    # 转换为二进制
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='JPEG')
    img_byte_arr = img_byte_arr.getvalue()
    
    # 准备上传
    files = {'media': ('default_cover.jpg', img_byte_arr, 'image/jpeg')}
    
    try:
        response = requests.post(url, files=files)
        result = response.json()
        
        if 'media_id' in result:
            logger.info(f"成功上传默认封面图，media_id: {result['media_id']}")
            return result['media_id']
        else:
            logger.info(f"上传默认封面图失败: {result.get('errmsg', '未知错误')}, 错误码: {result.get('errcode', '未知')}")
            return None
    except Exception as e:
        logger.info(f"上传封面图时出错: {e}")
        return None

def add_to_wechat_draft(access_token, title, content, source_url="", thumb_media_id=None):
    """将内容添加到微信公众号草稿箱"""
    if not thumb_media_id:
        logger.info("错误：缺少封面图media_id，无法添加到草稿箱")
        return False

    url = f"https://api.weixin.qq.com/cgi-bin/draft/add?access_token={access_token}"

    # 处理HTML标签
    html_content = content.replace('\n', '<br/>')

    # 处理原文链接
    html_content = re.sub(r'\[原文链接\]\((.*?)\)', r'<a href="\1">原文链接</a>', html_content)

    # 准备请求数据
    data = {
        "articles": [
            {
                "title": title,
                "content": html_content,
                "content_source_url": source_url,
                "author": "",
                "digest": "",
                "thumb_media_id": thumb_media_id,
                "need_open_comment": 0,
                "only_fans_can_comment": 0
            }
        ]
    }

    try:
        # 使用json参数，让requests自动处理编码
        headers = {'Content-Type': 'application/json'}
        response = requests.post(url, json=data, headers=headers)
        result = response.json()
        
        if 'media_id' in result:
            print(f"成功添加到草稿箱，media_id: {result['media_id']}")
            return True
        else:
            print(f"添加到草稿箱失败: {result.get('errmsg', '未知错误')}, 错误码: {result.get('errcode', '未知')}")
            return False
    except Exception as e:
        print(f"添加草稿时出错: {e}")
        return False

def extract_title_from_content(content, original_title=""):
    """从AI生成的内容中提取标题，如果提取失败则使用原标题"""
    # 尝试从内容中提取标题（假设标题可能在内容开始处）
    title_match = re.search(r'^#\s+(.*?)$', content, re.MULTILINE)
    if title_match:
        title = title_match.group(1).strip()
    else:
        # 尝试查找"标题："开头的行
        title_match = re.search(r'标题[:：]\s*(.*?)$', content, re.MULTILINE)
        if title_match:
            title = title_match.group(1).strip()
        # 如果没有找到，返回原标题或使用内容前几个字
        elif original_title:
            title = original_title
        else:
            # 使用内容前20个字作为标题
            first_line = content.split('\n')[0].strip()
            if len(first_line) > 20:
                title = first_line[:20] + "..."
            else:
                title = first_line

    # 确保标题长度不超过微信公众号限制（64个字符）
    if len(title) > 64:
        # 截断标题并添加省略号
        title = title[:60] + "..."

    return title

def main():
    keywords_file = "faiss_use/keywords.txt"  # 关键词文件路径
    top_k = 8  # 每个关键词检索的文章数量
    output_dir = "articles_single"  # 输出目录
    similarity_threshold = 0.8  # 相似度阈值，提高匹配精度

    # 获取微信access_token
    access_token = get_wechat_access_token()
    if not access_token:
        logger.error("获取微信access_token失败，程序退出")
        return
    logger.info(f"成功获取微信access_token: {access_token[:10]}...")
    
    # 上传默认封面图，获取media_id
    default_thumb_media_id = upload_default_image(access_token)
    if not default_thumb_media_id:
        logger.error("上传默认封面图失败，无法继续添加草稿，程序退出")
        return

    keywords = load_keywords(keywords_file)
    if not keywords:
        logger.error("未找到有效关键词，程序退出")
        return
    
    logger.info("正在加载Sentence Transformer模型...")
    model = SentenceTransformer('BAAI/bge-small-zh')
    
    logger.info("正在加载FAISS索引...")
    index = load_faiss_index()
    if index is None:
        return
        
    id_map = load_id_map()
    if not id_map:
        return
        
    conn = connect_to_database()
    if conn is None:
        return
    
    # 用于去重的集合，记录已处理的文章ID和标题
    processed_ids = set()
    processed_titles = set()
    
    for keyword in tqdm(keywords, desc="处理关键词"):
        articles = search_articles_for_keyword(keyword, model, index, id_map, conn, top_k, similarity_threshold)
        if not articles:
            # logger.info(f"警告: 关键词 '{keyword}' 没有找到相关文章，跳过")
            continue

        for article in articles:
            # 检查是否已经处理过这篇文章
            article_id = article['id']
            article_title = article['title']
            
            # 如果文章ID或标题已经处理过，则跳过
            if article_id in processed_ids or article_title in processed_titles:
                logger.info(f"跳过重复文章: ID={article_id}, 标题={article_title}")
                continue
                
            # 记录已处理的文章ID和标题
            processed_ids.add(article_id)
            processed_titles.add(article_title)
            
            # 使用AI润色文章
            ai_content = generate_single_news_with_deepseek(keyword, article)
            if not ai_content:
                continue
                
            # 保存到本地txt文件
            save_single_news_to_txt(keyword, article, ai_content, output_dir)
            
            # 提取标题和正文
            content_lines = ai_content.split('\n')
            
            # 第一行应该是标题
            if content_lines and content_lines[0].strip():
                raw_title = content_lines[0].strip()
                # 确保标题不超过20个字符
                if len(raw_title) > 20:
                    raw_title = raw_title[:17] + "..."
            else:
                # 如果第一行为空，使用原始标题
                raw_title = article['title'][:17] + "..." if len(article['title']) > 20 else article['title']
            
            # 正文内容（跳过第一行标题）
            if len(content_lines) > 1:
                # 直接使用原始内容
                raw_content = '\n'.join(content_lines[1:]).strip()
            else:
                raw_content = "内容生成失败，请检查AI响应格式。"

            # 在Windows系统中，可能需要特殊处理编码
            import platform
            if platform.system() == 'Windows':
                # 尝试将可能的Unicode编码转换为中文
                try:
                    # 先尝试将内容编码为bytes，然后解码为unicode_escape
                    # 这可以将\uXXXX转换为实际字符
                    content = raw_content.encode().decode('unicode_escape')
                    title = raw_title.encode().decode('unicode_escape')
                except:
                    content = raw_content
                    title = raw_title
            else:
                content = raw_content
                title = raw_title
            
            # 检查生成的标题是否已经存在于processed_titles中
            # 这里使用title而不是raw_title，因为title是经过处理后的最终标题
            if title in processed_titles and title != raw_title:  # 避免刚刚添加的标题被认为是重复的
                logger.info(f"跳过重复标题: {title}")
                continue

            # logger.info(f"准备添加到草稿箱的标题: '{title}'")
            # logger.info(f"内容预览: {content[:50]}...")
            
            # 推送到微信草稿箱
            result = add_to_wechat_draft(access_token, title, content, article.get('url', ''), default_thumb_media_id)
            if result:
                logger.info(f"已成功将文章《{title}》推送到微信草稿箱")
            else:
                # 如果失败，尝试使用简化的内容再次提交
                logger.info("尝试使用简化内容重新提交...")
                simplified_content = "本文内容暂时无法显示，请稍后查看。\n\n[原文链接](" + article.get('url', '') + ")"
                retry_result = add_to_wechat_draft(access_token, title, simplified_content, article.get('url', ''), default_thumb_media_id)
                if retry_result:
                    logger.info(f"使用简化内容成功添加文章《{title}》到草稿箱")
            # 每条新闻处理间隔，避免API限流
            time.sleep(2)

    conn.close()
    print(f"\n处理完成! 所有单篇新闻已保存到 {output_dir} 目录下，并尝试推送到微信草稿箱")
    print(f"共处理了 {len(processed_ids)} 篇不重复的新闻")

if __name__ == "__main__":
    main()