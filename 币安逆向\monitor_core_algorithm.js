// 核心HashcashScrypt算法监控脚本

console.log("=== 核心算法深度监控设置 ===");

// 🔥 最重要：监控_0x363923 (真正的HashcashScrypt算法)
if (typeof _0x363923 !== 'undefined') {
    const original_0x363923 = _0x363923;
    _0x363923 = function(_0x4b9879, _0x2fece3) {
        console.log("🎯🎯🎯 核心算法_0x363923调用！");
        console.log("参数1 (challenge数据):", _0x4b9879);
        console.log("参数2 (checksum):", _0x2fece3);
        
        // 详细分析challenge结构
        if (_0x4b9879 && _0x4b9879.challenge) {
            console.log("Challenge详情:");
            console.log("- input:", _0x4b9879.challenge.input);
            console.log("- hmac:", _0x4b9879.challenge.hmac);
            console.log("- region:", _0x4b9879.challenge.region);
        }
        
        debugger; // 🔥 最重要的断点！
        
        const startTime = performance.now();
        const result = original_0x363923.call(this, _0x4b9879, _0x2fece3);
        const endTime = performance.now();
        
        console.log("🏆 算法执行结果:", result);
        console.log("⏱️ 执行耗时:", endTime - startTime, "ms");
        
        // 如果是Promise，监控异步结果
        if (result && typeof result.then === 'function') {
            result.then(solution => {
                console.log("🏆 异步算法结果:", solution);
                console.log("解决方案类型:", typeof solution);
                if (solution && solution.length) {
                    console.log("解决方案长度:", solution.length);
                }
            }).catch(err => {
                console.log("❌ 算法执行错误:", err);
            });
        }
        
        return result;
    };
    console.log("✅ _0x363923 监控已设置");
} else {
    console.log("❌ _0x363923 函数未找到");
}

// 监控 _0x4fa2fd (配置初始化)
if (typeof _0x4fa2fd !== 'undefined') {
    const original_0x4fa2fd = _0x4fa2fd;
    _0x4fa2fd = function(_0xc1825) {
        console.log("🔧 _0x4fa2fd (配置初始化) 调用:");
        console.log("参数:", _0xc1825);
        
        const result = original_0x4fa2fd.call(this, _0xc1825);
        console.log("配置结果:", result);
        
        if (result && typeof result.then === 'function') {
            result.then(config => {
                console.log("📝 配置详情:", config);
                if (config.signals) console.log("- signals:", config.signals);
                if (config.checksum) console.log("- checksum:", config.checksum);
                if (config.metrics) console.log("- metrics:", config.metrics);
            });
        }
        
        return result;
    };
    console.log("✅ _0x4fa2fd 监控已设置");
}

// 监控 _0x259345 (信号处理)
if (typeof _0x259345 !== 'undefined') {
    const original_0x259345 = _0x259345;
    _0x259345 = function(_0xc4b5ad) {
        console.log("📡 _0x259345 (信号处理) 调用:");
        console.log("参数:", _0xc4b5ad);
        
        const result = original_0x259345.call(this, _0xc4b5ad);
        console.log("信号结果:", result);
        return result;
    };
    console.log("✅ _0x259345 监控已设置");
}

// 重写完整的 _0x43fc64 进行全流程监控
if (typeof _0x43fc64 !== 'undefined') {
    const original_0x43fc64 = _0x43fc64;
    _0x43fc64 = function(_0x4b9879, _0xc1825) {
        console.log("🚀 完整算法流程 _0x43fc64 开始:");
        console.log("Challenge数据:", _0x4b9879);
        console.log("配置对象:", _0xc1825);
        
        const result = original_0x43fc64.call(this, _0x4b9879, _0xc1825);
        
        if (result && typeof result.then === 'function') {
            result.then(finalResult => {
                console.log("🎊 最终token数据:", finalResult);
                if (finalResult.req) {
                    console.log("📋 请求详情:");
                    console.log("- challenge:", finalResult.req.challenge);
                    console.log("- solution:", finalResult.req.solution);
                    console.log("- signals:", finalResult.req.signals);
                    console.log("- checksum:", finalResult.req.checksum);
                    console.log("- client:", finalResult.req.client);
                    console.log("- domain:", finalResult.req.domain);
                }
            });
        }
        
        return result;
    };
    console.log("✅ _0x43fc64 完整流程监控已设置");
}

console.log("🎯 所有监控设置完成！现在触发token生成来查看完整流程。"); 