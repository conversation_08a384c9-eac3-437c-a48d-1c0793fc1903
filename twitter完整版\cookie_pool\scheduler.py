import time
from multiprocessing import Process

from loguru import logger

from settings import *
from flask_api import app
from tester import TwitterValidTester
from login import Login


class Scheduler:
    @staticmethod
    def valid_cookie(cycle=60*10):
        while True:
            logger.info('Cookies检测进程开始运行')
            try:
                tester = TwitterValidTester(
                    redis_cookies_name=REDIS_COOKIES_NAME, redis_accounts_name=REDIS_ACCOUNTS_NAME)
                tester.run()
                logger.info('Cookies检测完成')
                del tester

            except Exception as e:
                logger.error(repr(e))

            time.sleep(cycle)

    @staticmethod
    def generate_cookie(cycle=60):
        while True:
            logger.info('Cookies生成进程开始运行')
            try:
                generator = Login()
                generator.run()
                logger.info('Cookies生成完成')
                del generator
                time.sleep(cycle)
            except Exception as e:
                logger.error(repr(e))

    @staticmethod
    def api():
        logger.info('API接口开始运行')
        app.run(host='0.0.0.0', port=5003)

    def run(self):
        api_process = Process(target=Scheduler.api)
        api_process.start()

        # login_process = Process(target=Scheduler.generate_cookie)
        # login_process.start()

        valid_process = Process(target=Scheduler.valid_cookie)
        valid_process.start()


if __name__ == '__main__':
    sch = Scheduler()
    sch.run()
