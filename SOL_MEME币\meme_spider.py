import requests
import json
import time
import threading
import queue
import os
import datetime
import random
import signal
import sys
from loguru import logger
import traceback
import pymysql
import redis
import psycopg2
from psycopg2.extras import execute_batch
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Tuple, Optional, Any
from utils import DB_BASE

# 配置日志
logger.add("meme_spider.log", rotation="10 MB", level="INFO", compression="zip")

# MySQL配置（用于读取地址）
DB_CONFIG = {
    'host': '**************',
    'port': 33060,
    'user': 'root',
    'password': '12345678',
    'charset': 'utf8',
    'database': 'spider'
}

# PostgreSQL配置（用于写入数据）
POSTGRESQL_CONFIG = {
    'host': '*********',
    'port': 5432,
    'database': 'postgres',
    'user': 'postgres',
    'password': '12345678',
    'schema': 'solana_history'
}

# Redis配置
REDIS_CONFIG = {
    'host': '**************',
    'port': 6379,
    'password': '123456',
    'db': 1  # 代理IP所在的Redis库
}

# Redis断点续传配置
REDIS_CHECKPOINT_CONFIG = {
    'host': '**************',
    'port': 6379,
    'password': '123456',
    'db': 4  # 断点续传信息所在的Redis库
}

# Redis键名前缀
REDIS_KEY_PROCESSING = "meme_spider:processing_H"  # 正在处理的地址
REDIS_KEY_COMPLETED = "meme_spider:completed_H"  # 已完成的地址
REDIS_KEY_NUMBER = "meme_spider:number_H"  # 已完成的地址数量
REDIS_KEY_FAILED = "meme_spider:failed_address_H"  # 失败的地址

# 爬取时间范围配置（半年）
START_TIMESTAMP = 1732032000000  # 2024-11-20 00:00:00
END_TIMESTAMP = 1716134400000  # 2024-05-20 00:00:00

# 重试配置
MAX_RETRIES = 50  # 最大重试次数


class MemeSpider:
    def __init__(self, thread_num: int = 20, interval: str = "1H"):
        """
        初始化爬虫

        Args:
            thread_num: 线程数
            interval: 时间间隔，如 1m, 5m, 15m, 1h, 4h, 1d
        """
        # 添加停止标志
        self.running = True
        self.shutdown_event = threading.Event()

        # 注册信号处理
        signal.signal(signal.SIGINT, self.handle_shutdown)
        signal.signal(signal.SIGTERM, self.handle_shutdown)

        # 获取代理列表
        self.proxy_list = self.get_proxies_from_redis()

        # 确保线程数不超过可用代理数
        available_proxies = len(self.proxy_list)
        if available_proxies == 0:
            logger.error("无法从Redis获取代理IP，请检查Redis连接和代理配置")
            raise ValueError("无法获取代理IP，程序无法继续运行")

        if available_proxies < thread_num:
            logger.warning(
                f"可用代理数量({available_proxies})小于指定线程数({thread_num})，将使用可用代理数量作为线程数")
            self.thread_num = available_proxies
        else:
            self.thread_num = thread_num

        logger.info(f"初始化爬虫，线程数: {self.thread_num}, 可用代理数: {available_proxies}")

        # 默认代理（用于初始化，实际执行时每个线程会分配独立代理）
        proxy_url = f"http://{self.proxy_list[0]}"
        self.proxies = {
            "http": proxy_url,
            "https": proxy_url
        }

        logger.info(f"默认代理设置为: {proxy_url}")

        self.headers = {
            "accept": "application/json",
            "accept-language": "zh-CN,zh;q=0.9",
            "app-type": "web",
            "referer": "https://web3.okx.com/zh-hans/token/solana/2hKSxkRZHkWJ3Mr5Fp9gRgLr4pbgP5UcYciCjvdppump",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "x-cdn": "https://web3.okx.com",
            "x-utc": "8",
            "x-zkdex-env": "0"
        }

        self.cookies = {
            "_gcl_gs": "2.1.k1$i1747121212$u3801134",
            "ok_global": "{%22g_t%22:2}",
            "fp_s": "0",
            "_ga_G0EKWWQGTZ": "GS2.1.s1752903913$o153$g0$t1752903913$j60$l0$h0",
            "__cf_bm": "DITCT32q3aoYh2j8leor_UW4Te7Hc_Nux831MD_WBEU-1752905362-1.0.1.1-Y0yOPbwj08tGv4Kc.dIrriG5Te28bj.oR8K00hJKq3ruN4QDhQY.qYnfuaTy1.BvhMP_UG1NgdEIAaci2wc17s5QucvWIyRwoEtdAuH_EA8"
        }

        self.url = "https://web3.okx.com/priapi/v5/dex/token/market/history-dex-token-hlc-candles"

        # 基础参数
        self.base_params = {
            "chainId": "501",  # SOL链
            "bar": interval,  # 时间间隔
            "limit": "300",  # 每次请求数量
        }

        # 线程相关
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.lock = threading.Lock()

        # 地址列表
        self.token_addresses = []

        # 初始化Redis连接
        self.redis_checkpoint = redis.Redis(
            host=REDIS_CHECKPOINT_CONFIG['host'],
            port=REDIS_CHECKPOINT_CONFIG['port'],
            password=REDIS_CHECKPOINT_CONFIG['password'],
            db=REDIS_CHECKPOINT_CONFIG['db']
        )

        # 初始化飞书通知
        self.notifier = DB_BASE()

        # 初始化PostgreSQL连接
        self.init_postgresql()

    def init_postgresql(self):
        """初始化PostgreSQL连接"""
        try:
            # 连接PostgreSQL数据库
            self.pg_conn = psycopg2.connect(
                host=POSTGRESQL_CONFIG['host'],
                port=POSTGRESQL_CONFIG['port'],
                database=POSTGRESQL_CONFIG['database'],
                user=POSTGRESQL_CONFIG['user'],
                password=POSTGRESQL_CONFIG['password']
            )

            # 设置模式
            with self.pg_conn.cursor() as cursor:
                cursor.execute(f"SET search_path TO {POSTGRESQL_CONFIG['schema']}")

            self.pg_conn.commit()
            logger.info("PostgreSQL数据库连接初始化完成")
        except Exception as e:
            logger.error(f"初始化PostgreSQL连接失败: {e}")
            logger.error(traceback.format_exc())
            raise e

    def get_proxies_from_redis(self) -> List[str]:
        """
        从Redis中获取代理IP列表

        Returns:
            List[str]: 代理IP列表
        """
        try:
            # 连接Redis
            r = redis.Redis(
                host=REDIS_CONFIG['host'],
                port=REDIS_CONFIG['port'],
                password=REDIS_CONFIG['password'],
                db=REDIS_CONFIG['db']
            )

            # 检查键是否存在
            if not r.exists('proxy_ip:twitter'):
                logger.error("Redis中未找到代理IP键 'proxy_ip:twitter'")
                return []

            # 获取键类型
            key_type = r.type('proxy_ip:twitter').decode('utf-8')
            logger.info(f"Redis键 'proxy_ip:twitter' 的类型是: {key_type}")

            proxy_list = []

            # 根据键类型获取代理
            if key_type == 'string':
                # 字符串类型
                try:
                    proxy_str = r.get('proxy_ip:twitter')
                    proxy_items = proxy_str.decode('utf-8').split(',')
                    proxy_list.extend(proxy_items)
                    logger.info(f"从Redis字符串获取到 {len(proxy_items)} 个代理IP")
                except Exception as e:
                    logger.error(f"从字符串获取代理失败: {e}")

            elif key_type == 'hash':
                # 哈希表类型
                try:
                    # 获取所有哈希字段和值
                    all_proxies = r.hgetall('proxy_ip:twitter')
                    for field, value in all_proxies.items():
                        try:
                            # 使用字段（键）作为代理，而不是值
                            proxy = field.decode('utf-8')
                            proxy_list.append(proxy)
                            logger.debug(f"字段 {proxy} 的代理值: {value.decode('utf-8')}")
                        except:
                            logger.warning(f"无法解码字段")

                    logger.info(f"从Redis哈希表获取到 {len(proxy_list)} 个代理IP")
                except Exception as e:
                    logger.error(f"从哈希表获取代理失败: {e}")

            else:
                logger.error(f"不支持的Redis键类型: {key_type}")

            if not proxy_list:
                logger.error("无法从Redis中获取代理IP，尝试所有方法均失败")
                return []

            # 记录获取到的代理示例
            if proxy_list:
                logger.info(f"代理示例: {proxy_list[0]}")

            return proxy_list

        except Exception as e:
            logger.error(f"从Redis获取代理失败: {e}")
            logger.error(traceback.format_exc())
            return []

    def get_token_addresses(self) -> List[str]:
        """
        从MySQL数据库中获取代币地址列表

        Returns:
            List[str]: 代币地址列表
        """
        addresses = []
        conn = None
        try:
            # 连接MySQL数据库
            conn = pymysql.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # 执行查询
            sql = "SELECT address FROM first_trans_is_not_create_pool_address"
            cursor.execute(sql)

            # 获取所有结果
            results = cursor.fetchall()

            # 提取地址
            for row in results:
                addresses.append(row[0])

            logger.info(f"从数据库中获取到 {len(addresses)} 个代币地址")
            return addresses

        except Exception as e:
            logger.error(f"获取代币地址失败: {e}")
            logger.error(traceback.format_exc())
            return []
        finally:
            if conn:
                conn.close()

    def get_address_progress(self, address: str) -> int:
        """
        从Redis获取地址的爬取进度

        Args:
            address: 代币地址

        Returns:
            int: 上次爬取的时间戳，如果没有记录则返回起始时间戳
        """
        try:
            # 检查该地址是否已完成
            if self.redis_checkpoint.sismember(REDIS_KEY_COMPLETED, address):
                return END_TIMESTAMP  # 返回结束时间戳，使其被跳过

            # 获取正在处理的地址进度
            timestamp = self.redis_checkpoint.hget(REDIS_KEY_PROCESSING, address)
            if timestamp:
                timestamp = int(timestamp)
                return timestamp
            else:
                return START_TIMESTAMP

        except Exception as e:
            logger.error(f"获取地址进度失败: {address}, 错误: {e}")
            logger.error(traceback.format_exc())
            return START_TIMESTAMP

    def update_address_progress(self, address: str, timestamp: int, status: str = "processing"):
        """
        更新地址的爬取进度

        Args:
            address: 代币地址
            timestamp: 最新爬取的时间戳
            status: 爬取状态 (processing/completed)
        """
        try:
            if status == "processing":
                # 更新处理中的地址进度
                self.redis_checkpoint.hset(REDIS_KEY_PROCESSING, address, timestamp)
                logger.debug(f"更新地址 {address} 进度: {timestamp}")
            else:
                # 移除处理中的地址
                self.redis_checkpoint.hdel(REDIS_KEY_PROCESSING, address)
                # 添加到已完成集合
                self.redis_checkpoint.sadd(REDIS_KEY_COMPLETED, address)
                # 更新已完成计数
                self.redis_checkpoint.incr(REDIS_KEY_NUMBER)
                logger.info(f"地址 {address} 已完成爬取，当前已完成: {int(self.redis_checkpoint.get(REDIS_KEY_NUMBER))}")

        except Exception as e:
            logger.error(f"更新地址进度失败: {address}, 错误: {e}")
            logger.error(traceback.format_exc())

    def save_kline_data(self, address: str, data_list: List[List]):
        """
        保存K线数据到数据库

        Args:
            address: 代币地址
            data_list: K线数据列表
        """
        if not data_list:
            return

        try:
            # 检查连接是否断开，如果断开则重新连接
            if self.pg_conn.closed:
                self.init_postgresql()

            cursor = self.pg_conn.cursor()

            # 批量插入数据
            sql = """
            INSERT INTO meme_kline_data 
            (address, timestamp, open_price, high_price, low_price, close_price, volume, amount, count) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
            open_price = VALUES(open_price),
            high_price = VALUES(high_price),
            low_price = VALUES(low_price),
            close_price = VALUES(close_price),
            volume = VALUES(volume),
            amount = VALUES(amount),
            count = VALUES(count)
            """

            # 准备批量插入的数据
            batch_data = []
            for item in data_list:
                timestamp = int(item[0])
                open_price = float(item[1])
                high_price = float(item[2])
                low_price = float(item[3])
                close_price = float(item[4])
                volume = float(item[5])
                amount = float(item[6])
                count = int(item[7])

                batch_data.append(
                    (address, timestamp, open_price, high_price, low_price, close_price, volume, amount, count))

            # 执行批量插入
            cursor.executemany(sql, batch_data)
            self.pg_conn.commit()

            logger.info(f"成功保存 {len(data_list)} 条K线数据，地址: {address}")

        except Exception as e:
            self.pg_conn.rollback()
            logger.error(f"保存K线数据失败: {address}, 错误: {e}")
            logger.error(traceback.format_exc())

            # 如果是连接错误，尝试重新连接
            if "connection" in str(e).lower():
                try:
                    self.init_postgresql()
                except:
                    pass

    def handle_request_failure(self, address: str, error_msg: str):
        """
        处理请求失败的情况

        Args:
            address: 代币地址
            error_msg: 错误信息
        """
        try:
            # 将失败的地址写入Redis
            self.redis_checkpoint.sadd(REDIS_KEY_FAILED, address)

            # 发送飞书通知
            error_message = f"\naddress处理失败，已写入redis中，等待手动处理\n地址: {address}\n错误信息: {error_msg}"
            self.notifier.send_to_fs(error_message)

            logger.error(f"地址 {address} 处理失败，已写入Redis并发送通知")
        except Exception as e:
            logger.error(f"处理失败地址时发生错误: {e}")
            logger.error(traceback.format_exc())

    def fetch_data(self, address: str, after_time: int, thread_id: int) -> Tuple[List, int]:
        """获取指定地址和时间的K线数据"""
        retry_count = 0
        last_error = None

        while retry_count < MAX_RETRIES:
            try:
                # 为当前线程分配代理
                proxy_index = thread_id % len(self.proxy_list)
                proxy = self.proxy_list[proxy_index]

                # 构造代理URL - 确保格式正确
                proxy_url = f"http://{proxy}"

                # 设置代理
                proxies = {
                    "http": proxy_url,
                    "https": proxy_url
                }

                logger.debug(f"使用代理: {proxy_url} 请求地址: {address}")

                # 动态修改referer，使其与当前请求的address匹配
                headers = self.headers.copy()
                headers["referer"] = f"https://web3.okx.com/zh-hans/token/solana/{address}"

                # 构造请求参数
                params = self.base_params.copy()
                params["address"] = address
                params["after"] = str(after_time)
                params["t"] = str(int(time.time() * 1000))  # 当前时间戳

                # 发送请求
                response = requests.get(
                    self.url,
                    headers=headers,
                    cookies=self.cookies,
                    params=params,
                    proxies=proxies,
                    timeout=10
                )

                # 检查响应状态
                if response.status_code != 200:
                    error_msg = f"请求失败，状态码: {response.status_code}, 响应: {response.text}"
                    logger.warning(f"第 {retry_count + 1} 次重试: {error_msg}")
                    last_error = error_msg

                    # 根据重试次数设置等待时间
                    if retry_count < 2:
                        wait_time = random.uniform(3, 5)  # 3-5秒
                    else:
                        wait_time = random.uniform(5, 7)  # 5-7秒

                    time.sleep(wait_time)
                    retry_count += 1
                    continue

                # 解析响应数据
                result = response.json()

                # 检查是否返回特定错误码51001（表示没有数据，不是真正的错误）
                if result.get("code") == "51001" and "Instrument ID or Spread ID doesn't exist" in result.get("msg",
                                                                                                              ""):
                    logger.info(f"地址 {address} 没有历史数据（API返回51001），这是正常情况")
                    return [], after_time

                if result.get("code") != "0":
                    error_msg = f"API返回错误: {result}"
                    logger.warning(f"第 {retry_count + 1} 次重试: {error_msg}")
                    last_error = error_msg

                    # 根据重试次数设置等待时间
                    if retry_count < 2:
                        wait_time = random.uniform(3, 5)  # 3-5秒
                    else:
                        wait_time = random.uniform(5, 7)  # 5-7秒

                    time.sleep(wait_time)
                    retry_count += 1
                    continue

                data_list = result.get("data", [])

                # 如果没有数据，直接返回
                if not data_list:
                    logger.warning(
                        f"线程ID: {thread_id}, 地址: {address}, 没有获取到数据，时间: {self.timestamp_to_str(after_time)}")
                    return [], after_time

                logger.info(
                    f"线程ID: {thread_id}, 代理: {proxy}, 地址: {address}, 成功获取 {len(data_list)} 条数据，时间范围: {self.timestamp_to_str(int(data_list[-1][0]))} - {self.timestamp_to_str(int(data_list[0][0]))}")

                # 返回数据列表和最后一条数据的时间戳（用于下一次请求）
                next_timestamp = int(data_list[-1][0])
                return data_list, next_timestamp

            except Exception as e:
                error_msg = f"请求异常: {str(e)}"
                logger.warning(f"第 {retry_count + 1} 次重试: {error_msg}")
                last_error = error_msg

                # 根据重试次数设置等待时间
                if retry_count < 2:
                    wait_time = random.uniform(3, 5)  # 3-5秒
                else:
                    wait_time = random.uniform(5, 7)  # 5-7秒

                time.sleep(wait_time)
                retry_count += 1

        # 如果达到最大重试次数仍然失败
        logger.error(f"地址 {address} 达到最大重试次数 {MAX_RETRIES}，放弃处理")
        self.handle_request_failure(address, last_error or "未知错误")
        return [], after_time

    def timestamp_to_str(self, timestamp: int) -> str:
        """将时间戳转换为可读字符串"""
        return datetime.datetime.fromtimestamp(timestamp / 1000).strftime("%Y-%m-%d %H:%M:%S")

    def process_kline_data(self, address: str, data_list: List[List]) -> List[Tuple]:
        """
        处理K线数据

        Args:
            address: 代币地址
            data_list: 原始K线数据列表

        Returns:
            List[Tuple]: 处理后的数据列表，每个元素为(time, base_address, open, low, high, close, volume, value)
        """
        # 首先验证数据
        if not self.validate_data(data_list, address):
            return []

        try:
            processed_data = []
            for item in data_list:
                # 解析数据
                timestamp = int(item[0])  # time
                
                # 过滤早于END_TIMESTAMP的数据
                if timestamp < END_TIMESTAMP:
                    logger.debug(f"跳过早于{self.timestamp_to_str(END_TIMESTAMP)}的数据: {self.timestamp_to_str(timestamp)}")
                    continue
                    
                open_price = float(item[1])  # open
                high_price = float(item[2])  # high
                low_price = float(item[3])  # low
                close_price = float(item[4])  # close
                volume = float(item[5])  # volume
                value = float(item[6])  # value

                # 构造数据元组，按照表字段顺序
                data_tuple = (
                    timestamp,  # time
                    address,  # base_address
                    open_price,  # open
                    low_price,  # low
                    high_price,  # high
                    close_price,  # close
                    volume,  # volume
                    value  # value
                )

                processed_data.append(data_tuple)

            logger.debug(f"处理 {len(processed_data)} 条K线数据，地址: {address}")
            return processed_data

        except Exception as e:
            logger.error(f"处理K线数据失败, 地址: {address}, 错误: {e}")
            logger.error(traceback.format_exc())
            return []

    def save_to_postgresql(self, data_list: List[Tuple]) -> bool:
        """
        将数据保存到PostgreSQL数据库

        Args:
            data_list: 处理后的数据列表

        Returns:
            bool: 是否保存成功
        """
        if not data_list:
            logger.warning("没有数据需要保存到PostgreSQL")
            return True

        try:
            # 连接到PostgreSQL数据库
            conn = psycopg2.connect(
                host=POSTGRESQL_CONFIG['host'],
                port=POSTGRESQL_CONFIG['port'],
                database=POSTGRESQL_CONFIG['database'],
                user=POSTGRESQL_CONFIG['user'],
                password=POSTGRESQL_CONFIG['password']
            )

            # 创建游标
            cursor = conn.cursor()

            # 构建批量插入语句 - 使用正确的参数格式
            insert_sql = """
            INSERT INTO solana_history.crypto_1s_kline (time, base_address, open, low, high, close, volume, value)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (time, base_address)
            DO UPDATE SET
                open = EXCLUDED.open,
                low = EXCLUDED.low,
                high = EXCLUDED.high,
                close = EXCLUDED.close,
                volume = EXCLUDED.volume,
                value = EXCLUDED.value
            """

            # 转换时间戳格式
            converted_data_list = []
            for item in data_list:
                # 将毫秒时间戳转换为PostgreSQL timestamp格式
                # 原始格式: (timestamp_ms, address, open, low, high, close, volume, value)
                timestamp_ms = item[0]
                # 将毫秒时间戳转换为秒
                timestamp_sec = timestamp_ms / 1000.0
                # 创建datetime对象
                dt = datetime.datetime.fromtimestamp(timestamp_sec, tz=datetime.timezone.utc)
                # 替换第一个元素为datetime对象
                converted_item = (dt,) + item[1:]
                converted_data_list.append(converted_item)

            # 使用execute_batch执行批量插入
            execute_batch(cursor, insert_sql, converted_data_list, page_size=1000)

            # 提交事务
            conn.commit()
            logger.info(f"成功保存 {len(data_list)} 条数据到PostgreSQL")

            # 关闭游标和连接
            cursor.close()
            conn.close()

            return True

        except Exception as e:
            logger.error(f"保存数据到PostgreSQL失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def worker(self, thread_id: int):
        """工作线程"""
        logger.info(f"线程 {thread_id} 启动")

        while self.running and not self.task_queue.empty():
            try:
                # 获取任务
                task = self.task_queue.get(block=False)
                address = task["address"]
                after_time = task["after_time"]
                is_first_request = task.get("is_first_request", False)

                # 如果时间戳已经早于结束时间，则跳过
                if after_time <= END_TIMESTAMP:
                    logger.info(f"线程 {thread_id}, 地址 {address} 已达到目标时间范围，跳过")
                    self.update_address_progress(address, after_time, "completed")
                    self.task_queue.task_done()
                    continue

                # 获取数据
                data_list, next_timestamp = self.fetch_data(address, after_time, thread_id)

                if data_list:
                    # 处理数据
                    processed_data = self.process_kline_data(address, data_list)

                    # 保存到PostgreSQL
                    if processed_data:
                        self.save_to_postgresql(processed_data)

                    # 更新爬取进度
                    self.update_address_progress(address, next_timestamp)

                    # 如果还没有达到目标时间范围，继续添加任务
                    if next_timestamp > END_TIMESTAMP:
                        # 创建下一个任务
                        next_task = {
                            "address": address,
                            "after_time": next_timestamp,
                            "is_first_request": False
                        }

                        # 等待3.5秒再继续
                        time.sleep(3.5)

                        # 将下一个任务加入队列
                        self.task_queue.put(next_task)
                        logger.info(f"线程 {thread_id}, 地址 {address} 添加下一个任务，时间戳: {next_timestamp}")
                    else:
                        # 已达到目标时间范围，标记为完成
                        self.update_address_progress(address, next_timestamp, "completed")
                        logger.info(f"线程 {thread_id}, 地址 {address} 已完成爬取，达到目标时间范围")
                else:
                    # 如果没有获取到数据
                    if not is_first_request:
                        # 不是首次请求，可能是已经没有更早的数据了，标记为完成
                        self.update_address_progress(address, after_time, "completed")
                        logger.info(f"线程 {thread_id}, 地址 {address} 没有更多数据，标记为完成")
                    else:
                        # 首次请求就没有数据，可能是这个代币没有历史数据，标记为完成
                        self.update_address_progress(address, END_TIMESTAMP, "completed")
                        logger.info(
                            f"线程 {thread_id}, 地址 {address} 首次请求没有数据，可能是新代币或没有历史，标记为完成")

                # 标记任务完成
                self.task_queue.task_done()

            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"线程 {thread_id} 异常: {e}")
                logger.error(traceback.format_exc())
                # 标记任务完成
                self.task_queue.task_done()

        logger.info(f"线程 {thread_id} 结束")

    def handle_shutdown(self, signum, frame):
        """处理退出信号"""
        logger.warning(f"收到退出信号 {signum}，开始优雅退出...")
        self.running = False
        self.shutdown_event.set()

    def cleanup(self):
        """清理资源"""
        try:
            # 关闭PostgreSQL连接
            if hasattr(self, 'pg_conn') and not self.pg_conn.closed:
                self.pg_conn.close()
                logger.info("PostgreSQL连接已关闭")

            # 关闭Redis连接
            if hasattr(self, 'redis_checkpoint'):
                self.redis_checkpoint.close()
                logger.info("Redis连接已关闭")

            # 等待任务队列处理完成
            if hasattr(self, 'task_queue'):
                self.task_queue.join()
                logger.info("任务队列已清空")

            # 等待结果队列处理完成
            if hasattr(self, 'result_queue'):
                self.result_queue.join()
                logger.info("结果队列已清空")

            logger.info("所有资源已清理完成")
        except Exception as e:
            logger.error(f"清理资源时发生错误: {e}")
            logger.error(traceback.format_exc())

    def __del__(self):
        """析构函数"""
        self.cleanup()

    def get_statistics(self):
        """获取统计信息"""
        try:
            # 从Redis获取统计信息
            processing_count = len(self.redis_checkpoint.hgetall(REDIS_KEY_PROCESSING))
            completed_count = int(self.redis_checkpoint.get(REDIS_KEY_NUMBER) or 0)

            # 从MySQL获取K线数据统计
            conn = pymysql.connect(**DB_CONFIG)
            cursor = conn.cursor()

            # 获取总记录数
            cursor.execute("SELECT COUNT(*) FROM meme_kline_data")
            total_records = cursor.fetchone()[0]

            # 获取最早和最晚的数据时间
            cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM meme_kline_data")
            min_time, max_time = cursor.fetchone()

            conn.close()

            return {
                "total_records": total_records,
                "completed_addresses": completed_count,
                "processing_addresses": processing_count,
                "time_range": f"{self.timestamp_to_str(min_time) if min_time else 'N/A'} - {self.timestamp_to_str(max_time) if max_time else 'N/A'}"
            }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            logger.error(traceback.format_exc())
            return {}

    def run(self):
        """运行爬虫"""
        try:
            logger.info("开始运行爬虫")
            logger.info(
                f"目标时间范围: {self.timestamp_to_str(START_TIMESTAMP)} - {self.timestamp_to_str(END_TIMESTAMP)}")

            # 获取代币地址列表
            self.token_addresses = self.get_token_addresses()

            if not self.token_addresses:
                logger.error("没有获取到代币地址，无法生成任务")
                return

            total_addresses = len(self.token_addresses)
            logger.info(f"获取到 {total_addresses} 个代币地址")

            # 批量检查已完成的地址
            completed_addresses = set()
            try:
                # 获取所有已完成的地址
                if self.redis_checkpoint.exists(REDIS_KEY_COMPLETED):
                    completed_addresses = set(
                        addr.decode('utf-8') for addr in self.redis_checkpoint.smembers(REDIS_KEY_COMPLETED))
                    logger.info(f"从Redis获取到 {len(completed_addresses)} 个已完成的地址")
            except Exception as e:
                logger.error(f"获取已完成地址失败: {e}")

            # 批量获取处理中的地址进度
            processing_addresses = {}
            try:
                # 获取所有处理中的地址
                if self.redis_checkpoint.exists(REDIS_KEY_PROCESSING):
                    all_processing = self.redis_checkpoint.hgetall(REDIS_KEY_PROCESSING)
                    for addr, ts in all_processing.items():
                        try:
                            addr_str = addr.decode('utf-8')
                            ts_int = int(ts.decode('utf-8'))
                            processing_addresses[addr_str] = ts_int
                        except:
                            pass
                    logger.info(f"从Redis获取到 {len(processing_addresses)} 个处理中的地址")
            except Exception as e:
                logger.error(f"获取处理中地址失败: {e}")

            # 创建线程池
            logger.info("创建线程池...")
            with ThreadPoolExecutor(max_workers=self.thread_num) as executor:
                # 提交任务监控线程
                monitor_future = executor.submit(self.monitor_progress)

                # 分批处理地址
                batch_size = 5000  # 每批处理的地址数
                total_batches = (total_addresses + batch_size - 1) // batch_size

                task_count = 0
                completed_count = 0
                processing_count = 0
                start_time = time.time()

                for batch_idx in range(total_batches):
                    batch_start = batch_idx * batch_size
                    batch_end = min(batch_start + batch_size, total_addresses)
                    batch_addresses = self.token_addresses[batch_start:batch_end]

                    batch_task_count = 0
                    batch_completed_count = 0
                    batch_processing_count = 0

                    # 为这批地址生成任务
                    for address in batch_addresses:
                        # 检查是否已完成
                        if address in completed_addresses:
                            batch_completed_count += 1
                            continue

                        # 获取处理进度
                        if address in processing_addresses:
                            start_time_stamp = processing_addresses[address]
                            batch_processing_count += 1
                        else:
                            start_time_stamp = START_TIMESTAMP

                        # 如果时间戳已经早于结束时间，则跳过
                        if start_time_stamp <= END_TIMESTAMP:
                            batch_completed_count += 1
                            continue

                        # 构造任务
                        task = {
                            "address": address,
                            "after_time": start_time_stamp,
                            "is_first_request": True  # 标记是否为首次请求
                        }

                        # 将任务加入队列
                        self.task_queue.put(task)
                        batch_task_count += 1

                    # 更新计数
                    task_count += batch_task_count
                    completed_count += batch_completed_count
                    processing_count += batch_processing_count

                    # 报告批次进度
                    elapsed = time.time() - start_time
                    progress = (batch_idx + 1) / total_batches * 100
                    logger.info(f"批次 {batch_idx + 1}/{total_batches} ({progress:.1f}%) 完成: "
                                f"处理 {batch_end - batch_start} 个地址，"
                                f"生成 {batch_task_count} 个任务，"
                                f"跳过 {batch_completed_count} 个已完成地址，"
                                f"继续 {batch_processing_count} 个处理中地址，"
                                f"总耗时 {elapsed:.2f}秒")

                    # 每生成一批任务后，启动工作线程处理
                    if batch_idx == 0:
                        # 第一批任务生成后，启动工作线程
                        logger.info(f"第一批任务生成完成，启动 {self.thread_num} 个工作线程...")
                        worker_futures = [executor.submit(self.worker, i) for i in range(self.thread_num)]

                # 等待所有工作线程完成
                logger.info("所有任务已生成，等待工作线程完成...")
                for future in worker_futures:
                    future.result()

                # 取消监控线程
                monitor_future.cancel()

            # 获取统计信息
            stats = self.get_statistics()
            logger.info(f"爬虫运行完成，统计信息: {stats}")

        except Exception as e:
            logger.error(f"爬虫运行异常: {e}")
            logger.error(traceback.format_exc())
        finally:
            # 清理资源
            self.cleanup()

    def monitor_progress(self):
        """监控任务进度"""
        try:
            start_time = time.time()
            last_task_size = self.task_queue.qsize()
            last_time = start_time

            while not self.shutdown_event.is_set():
                time.sleep(30)  # 每30秒检查一次

                current_time = time.time()
                current_task_size = self.task_queue.qsize()

                # 计算处理速度
                time_diff = current_time - last_time
                tasks_processed = last_task_size - current_task_size

                if time_diff > 0 and tasks_processed > 0:
                    tasks_per_second = tasks_processed / time_diff
                    estimated_time = current_task_size / tasks_per_second if tasks_per_second > 0 else 0

                    # 格式化预估时间
                    hours, remainder = divmod(estimated_time, 3600)
                    minutes, seconds = divmod(remainder, 60)
                    time_str = f"{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒"

                    logger.info(f"进度监控: 队列中还有 {current_task_size} 个任务，"
                                f"处理速度 {tasks_per_second:.2f} 任务/秒，"
                                f"预计剩余时间 {time_str}")

                last_task_size = current_task_size
                last_time = current_time

        except Exception as e:
            logger.error(f"监控线程异常: {e}")

    def validate_data(self, data_list: List[List], address: str) -> bool:
        """
        验证数据完整性

        Args:
            data_list: 原始数据列表
            address: 代币地址

        Returns:
            bool: 数据是否有效
        """
        try:
            if not data_list:
                logger.warning(f"数据列表为空，地址: {address}")
                return False

            for item in data_list:
                # 检查数据项长度
                if len(item) != 8:
                    logger.error(f"数据项长度错误，期望8个字段，实际{len(item)}个字段，地址: {address}")
                    return False

                # 检查时间戳
                timestamp = int(item[0])
                if timestamp <= 0 or timestamp > int(time.time() * 1000):
                    logger.error(f"时间戳无效: {timestamp}，地址: {address}")
                    return False

                # 检查价格数据
                for i in range(1, 5):  # open, high, low, close
                    try:
                        price = float(item[i])
                        if price < 0:
                            logger.error(f"价格数据无效: {price}，地址: {address}")
                            return False
                    except ValueError:
                        logger.error(f"价格数据格式错误: {item[i]}，地址: {address}")
                        return False

                # 检查成交量和成交额
                try:
                    volume = float(item[5])
                    value = float(item[6])
                    if volume < 0 or value < 0:
                        logger.error(f"成交量或成交额无效: volume={volume}, value={value}，地址: {address}")
                        return False
                except ValueError:
                    logger.error(f"成交量或成交额格式错误: volume={item[5]}, value={item[6]}，地址: {address}")
                    return False

                # 检查计数
                try:
                    count = int(item[7])
                    if count < 0:
                        logger.error(f"计数无效: {count}，地址: {address}")
                        return False
                except ValueError:
                    logger.error(f"计数格式错误: {item[7]}，地址: {address}")
                    return False

            # 检查时间戳顺序（应该是降序）
            timestamps = [int(item[0]) for item in data_list]
            if not all(timestamps[i] >= timestamps[i + 1] for i in range(len(timestamps) - 1)):
                logger.error(f"时间戳顺序错误，地址: {address}")
                return False

            logger.debug(f"数据验证通过，地址: {address}")
            return True

        except Exception as e:
            logger.error(f"数据验证过程发生错误: {e}，地址: {address}")
            logger.error(traceback.format_exc())
            return False

    def generate_tasks(self):
        """生成任务列表 - 此方法在新模式下不再使用"""
        logger.warning("generate_tasks方法已不再使用，任务生成已集成到run方法中")
        pass


if __name__ == "__main__":
    try:
        # 创建爬虫实例
        spider = MemeSpider(
            thread_num=20,  # 使用20个线程，每个线程对应一个代理
            interval="1H"
        )

        # 运行爬虫
        spider.run()
    except KeyboardInterrupt:
        logger.warning("收到键盘中断信号")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        sys.exit(0)