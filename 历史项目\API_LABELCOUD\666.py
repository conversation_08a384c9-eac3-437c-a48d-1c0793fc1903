import requests
from bs4 import BeautifulSoup

# 目标网址
url = "https://optimistic.etherscan.io/accounts/label/aave"

# 发送请求获取页面内容
response = requests.get(url)
if response.status_code == 200:
    page_content = response.text
    print(response.text)
    # 使用 BeautifulSoup 解析页面
    soup = BeautifulSoup(page_content, 'html.parser')
    
    # 假设 website_key 被嵌入在某个 <script> 标签中，或者在某个特定的 HTML 元素中
    # 你需要根据页面结构找到合适的位置。通常，website_key 可能会被嵌入在 JavaScript 中，如：
    # "website_key": "xyz123"

    # 查找页面中的 <script> 标签并提取 JavaScript 变量（例如: website_key）
    script_tags = soup.find_all('script')

    for script in script_tags:
        # 搜索 script 标签中的内容，找到 website_key
        if 'websiteKey' in script.text:
            print("Found website_key in script tag:", script.text)
            # 进一步解析脚本中的具体值，使用正则或字符串查找方法提取 website_key
            import re
            match = re.search(r'websiteKey\s*=\s*"([^"]+)"', script.text)
            if match:
                website_key = match.group(1)
                print("Extracted website_key:", website_key)
                break
else:
    print("Error: Failed to retrieve the page.")
