#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级AWS WAF Challenge.js解混淆器
专门处理函数调用模式和token生成逻辑
"""
import re
import json
from collections import defaultdict

class AdvancedJSDeobfuscator:
    def __init__(self, js_content):
        self.js_content = js_content
        self.function_mapping = {}  # 存储函数调用映射
        self.string_arrays = {}     # 存储字符串数组
        self.key_variables = {}     # 存储关键变量
        self.token_flow = []        # Token生成流程
        
    def extract_function_call_mappings(self):
        """提取函数调用映射，如 _0x401049(0x192) 对应的实际字符串"""
        print("🔍 提取函数调用映射...")
        
        # 查找模式: function _0x401049(_0x25dce3, _0x4528cb) { return _0x175530[_0x25dce3 -= 0x1c3]; }
        mapping_pattern = r"function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\([^)]*\)\s*\{\s*return\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\[([^[\]]*)\];\s*\}"
        matches = re.findall(mapping_pattern, self.js_content, re.MULTILINE | re.DOTALL)
        
        for func_name, array_name, index_expr in matches:
            print(f"   找到映射函数: {func_name} -> {array_name}[{index_expr}]")
            self.function_mapping[func_name] = {
                'array': array_name,
                'index_expr': index_expr
            }
            
        # 查找数组定义，如 _0x175530 = [...]
        for func_name, mapping in self.function_mapping.items():
            array_name = mapping['array']
            array_pattern = rf"var\s+{re.escape(array_name)}\s*=\s*\[(.*?)\]"
            array_match = re.search(array_pattern, self.js_content, re.DOTALL)
            
            if array_match:
                array_content = array_match.group(1)
                # 提取字符串数组
                strings = re.findall(r"'([^']*)'", array_content)
                self.string_arrays[array_name] = strings
                print(f"   数组 {array_name} 包含 {len(strings)} 个字符串")
                
        return self.function_mapping
    
    def resolve_function_call(self, func_name, hex_index):
        """解析函数调用，返回实际字符串"""
        if func_name not in self.function_mapping:
            return f"UNKNOWN_FUNC[{func_name}({hex_index})]"
            
        mapping = self.function_mapping[func_name]
        array_name = mapping['array']
        index_expr = mapping['index_expr']
        
        if array_name not in self.string_arrays:
            return f"UNKNOWN_ARRAY[{array_name}]"
            
        try:
            # 解析十六进制索引
            if hex_index.startswith('0x'):
                index = int(hex_index, 16)
            else:
                index = int(hex_index)
                
            # 处理索引表达式中的偏移量
            # 如: _0x25dce3 -= 0x1c3
            offset_match = re.search(r'-=?\s*(0x[0-9a-fA-F]+|\d+)', index_expr)
            if offset_match:
                offset = int(offset_match.group(1), 16 if offset_match.group(1).startswith('0x') else 10)
                adjusted_index = index - offset
            else:
                adjusted_index = index
                
            array = self.string_arrays[array_name]
            if 0 <= adjusted_index < len(array):
                return array[adjusted_index]
            else:
                return f"OUT_OF_BOUNDS[{index}->{adjusted_index}]"
                
        except (ValueError, IndexError) as e:
            return f"ERROR[{e}]"
    
    def find_token_generation_flow(self):
        """查找token生成流程"""
        print("🔍 分析Token生成流程...")
        
        # 查找aws-waf-token相关的关键代码段
        patterns = {
            'token_assignment': r"(\w+)\s*=\s*([^;]*aws-waf-token[^;]*)",
            'goku_props': r"(gokuProps[^;]*)",
            'fetch_calls': r"(fetch\([^)]*\))",
            'token_generation': r"(\w+)\[\w+\]\(\)\s*",  # 如 _0x4f1c88[_0x401049(0x192)]()
        }
        
        for pattern_name, pattern in patterns.items():
            matches = re.findall(pattern, self.js_content, re.IGNORECASE)
            if matches:
                print(f"   {pattern_name}: 找到 {len(matches)} 个匹配")
                self.token_flow.append({
                    'type': pattern_name,
                    'matches': matches[:5]  # 只保留前5个
                })
                
        return self.token_flow
    
    def analyze_key_objects(self):
        """分析关键对象和变量"""
        print("🔍 分析关键对象...")
        
        # 查找重要的对象定义
        key_patterns = {
            'challenge_object': r"var\s+(\w+)\s*=\s*\{[^}]*challenge[^}]*\}",
            'goku_object': r"var\s+(\w+)\s*=\s*\{[^}]*goku[^}]*\}",
            'token_object': r"(\w+)\s*=\s*\{[^}]*fetch[^}]*\}",
            'window_assignment': r"window\[([^\]]+)\]\s*=\s*(\w+)",
        }
        
        for pattern_name, pattern in key_patterns.items():
            matches = re.findall(pattern, self.js_content, re.IGNORECASE)
            if matches:
                print(f"   {pattern_name}: {matches}")
                self.key_variables[pattern_name] = matches
                
        return self.key_variables
    
    def find_encryption_logic(self):
        """查找加密相关的逻辑"""
        print("🔍 查找加密逻辑...")
        
        encryption_keywords = [
            'encrypt', 'decrypt', 'AES', 'crypto', 'cipher',
            'key', 'iv', 'context', 'algorithm', 'hash'
        ]
        
        encryption_blocks = []
        for keyword in encryption_keywords:
            # 查找包含加密关键词的函数或代码块
            pattern = rf"(function[^{{]*\{{[^}}]*{keyword}[^}}]*\}})"
            matches = re.findall(pattern, self.js_content, re.IGNORECASE | re.DOTALL)
            
            for match in matches[:3]:  # 限制每个关键词最多3个匹配
                if len(match) < 500:  # 只保留较短的代码块
                    encryption_blocks.append({
                        'keyword': keyword,
                        'code': match[:200] + '...' if len(match) > 200 else match
                    })
                    
        print(f"   找到 {len(encryption_blocks)} 个加密相关代码块")
        return encryption_blocks
    
    def extract_challenge_metadata(self):
        """提取挑战元数据"""
        print("🔍 提取挑战元数据...")
        
        metadata = {}
        
        # 查找难度设置
        difficulty_match = re.search(r"'difficulty'\s*:\s*parseInt\('(\d+)'\)", self.js_content)
        if difficulty_match:
            metadata['difficulty'] = int(difficulty_match.group(1))
            
        # 查找内存设置
        memory_match = re.search(r"'memory'\s*:\s*parseInt\([^)]*'(\d+)'", self.js_content)
        if memory_match:
            metadata['memory'] = int(memory_match.group(1))
            
        # 查找挑战类型
        challenge_type_match = re.search(r"'challenge_type'\s*:\s*[^,]*'([^']+)'", self.js_content)
        if challenge_type_match:
            metadata['challenge_type'] = challenge_type_match.group(1)
            
        # 查找区域信息
        region_match = re.search(r"'region'\s*:\s*[^,]*'([^']+)'", self.js_content)
        if region_match:
            metadata['region'] = region_match.group(1)
            
        # 查找URL信息
        url_pattern = r"https://[a-zA-Z0-9.-]+\.token\.awswaf\.com/[a-zA-Z0-9/.-]+"
        url_matches = re.findall(url_pattern, self.js_content)
        if url_matches:
            metadata['challenge_urls'] = list(set(url_matches))
            
        print(f"   提取的元数据: {metadata}")
        return metadata
    
    def replace_resolved_calls(self):
        """替换已解析的函数调用"""
        print("🔄 替换已解析的函数调用...")
        
        cleaned_code = self.js_content
        replacement_count = 0
        
        for func_name in self.function_mapping:
            # 匹配形如 _0x401049(0x192) 的调用
            call_pattern = rf"{re.escape(func_name)}\s*\(\s*(0x[0-9a-fA-F]+|\d+)\s*\)"
            
            def replace_call(match):
                nonlocal replacement_count
                hex_index = match.group(1)
                resolved = self.resolve_function_call(func_name, hex_index)
                replacement_count += 1
                return f"'{resolved}'"
                
            cleaned_code = re.sub(call_pattern, replace_call, cleaned_code)
            
        print(f"   替换了 {replacement_count} 个函数调用")
        return cleaned_code
    
    def generate_analysis_report(self):
        """生成完整的分析报告"""
        print("📊 生成分析报告...")
        
        report = {
            'metadata': self.extract_challenge_metadata(),
            'function_mappings': {
                name: {
                    'array': mapping['array'],
                    'array_size': len(self.string_arrays.get(mapping['array'], []))
                }
                for name, mapping in self.function_mapping.items()
            },
            'key_variables': self.key_variables,
            'token_flow': self.token_flow,
            'encryption_blocks_count': len(self.find_encryption_logic()),
            'string_arrays_stats': {
                name: len(strings) 
                for name, strings in self.string_arrays.items()
            }
        }
        
        return report
    
    def full_analysis(self):
        """执行完整分析"""
        print("🚀 开始高级解混淆分析...")
        print("=" * 60)
        
        # 步骤1: 提取函数调用映射
        self.extract_function_call_mappings()
        print()
        
        # 步骤2: 分析关键对象
        self.analyze_key_objects()
        print()
        
        # 步骤3: 查找token生成流程
        self.find_token_generation_flow()
        print()
        
        # 步骤4: 查找加密逻辑
        encryption_blocks = self.find_encryption_logic()
        print()
        
        # 步骤5: 替换函数调用
        cleaned_code = self.replace_resolved_calls()
        print()
        
        # 步骤6: 生成报告
        report = self.generate_analysis_report()
        
        print("=" * 60)
        print("✅ 高级分析完成！")
        
        return {
            'cleaned_code': cleaned_code,
            'report': report,
            'encryption_blocks': encryption_blocks
        }

def main():
    print("🎯 AWS WAF高级解混淆器")
    print("=" * 60)
    
    # 读取challenge.js文件
    try:
        with open('challenge.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        print(f"📖 文件大小: {len(js_content):,} 字符")
    except FileNotFoundError:
        print("❌ 未找到challenge.js文件")
        return
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 创建高级解混淆器
    deobfuscator = AdvancedJSDeobfuscator(js_content)
    
    # 执行分析
    result = deobfuscator.full_analysis()
    
    # 保存清理后的代码
    with open('challenge_advanced_cleaned.js', 'w', encoding='utf-8') as f:
        f.write(result['cleaned_code'])
    print("💾 清理后代码已保存到 challenge_advanced_cleaned.js")
    
    # 保存详细报告
    with open('advanced_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(result['report'], f, indent=2, ensure_ascii=False)
    print("📊 详细报告已保存到 advanced_analysis_report.json")
    
    # 保存加密代码块
    with open('encryption_blocks.json', 'w', encoding='utf-8') as f:
        json.dump(result['encryption_blocks'], f, indent=2, ensure_ascii=False)
    print("🔐 加密代码块已保存到 encryption_blocks.json")
    
    # 显示关键发现
    print("\n🎯 关键发现:")
    metadata = result['report']['metadata']
    if 'difficulty' in metadata:
        print(f"   难度级别: {metadata['difficulty']}")
    if 'challenge_type' in metadata:
        print(f"   挑战类型: {metadata['challenge_type']}")
    if 'challenge_urls' in metadata:
        print(f"   挑战URL: {metadata['challenge_urls'][0]}")

if __name__ == "__main__":
    main() 