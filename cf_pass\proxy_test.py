import requests

proxy = {
    "http": "***************************************************",
    "https": "***************************************************"
}

# proxy = {
#     "http": "socks5://**************:30889",
#     "https": "socks5://**************:30889"
# }

# proxy = {
#     "http": "http://127.0.0.1:33210",
#     "https": "http://127.0.0.1:33210"
# }

headers = {
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

url = "https://etherscan.io/"

response = requests.get(url, headers=headers, proxies=proxy)

print(response)