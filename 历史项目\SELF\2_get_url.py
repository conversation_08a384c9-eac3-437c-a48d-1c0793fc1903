import requests
import time
import csv
from bs4 import BeautifulSoup

base_url = 'https://optimistic.etherscan.io'
start_url = 'https://optimistic.etherscan.io/labelcloud'

headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Pragma": "no-cache",
    "Referer": "https://optimistic.etherscan.io/",
    "Sec-Ch-Ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "Sec-Ch-Ua-Arch": "x86",
    "Sec-Ch-Ua-Bitness": "64",
    "Sec-Ch-Ua-Full-Version": "114.0.5735.199",
    "Sec-Ch-Ua-Full-Version-List": "\"Not.A/Brand\";v=\"*******\", \"Chromium\";v=\"114.0.5735.199\", \"Google Chrome\";v=\"114.0.5735.199\"",
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Ch-Ua-Model": "",
    "Sec-Ch-Ua-Platform": "Windows",
    "Sec-Ch-Ua-Platform-Version": "15.0.0",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

# cookies = {}
# with open('1_login_cookies.csv', 'r', newline='', encoding='utf-8') as csvfile:
#     reader = csv.reader(csvfile)
#     for row in reader:
#         if row:
#             cookies[row[0]] = row[1]

cookies = {}
with open('1_login_cookies.csv', 'r', newline='', encoding='utf-8') as cookiefile:
    reader = csv.reader(cookiefile)
    for row in reader:
        if row:
            cookies[row[0]] = row[1]

response = requests.get(start_url, headers=headers, cookies=cookies)
time.sleep(2)
soup = BeautifulSoup(response.text, 'html.parser')
categories = soup.find_all('div', class_='col-md-4 col-lg-3 mb-3 secondary-container')

csv_file_path = '2_url_data.csv'
with open(csv_file_path, 'w', newline='', encoding='utf-8') as file:
    writer = csv.writer(file)
    writer.writerow(['Category', 'Count', 'Account URL', 'Token URL'])

    for category in categories:
        button = category.find('button')
        if button:
            parts = button.text.strip().rsplit(' ', 1)
            if len(parts) == 2:
                name, count = parts
                formatted_name = name.replace(' ', '-')
                account_url = None
                token_url = None
                links = category.find_all('a', class_='dropdown-item')
                for link in links:
                    if 'accounts/label' in link['href']:
                        account_url = f'{base_url}{link["href"]}'
                    if 'tokens/label' in link['href']:
                        token_url = f'{base_url}{link["href"]}'
                
                writer.writerow([name, count, account_url, token_url])

print("数据已保存到CSV文件。")