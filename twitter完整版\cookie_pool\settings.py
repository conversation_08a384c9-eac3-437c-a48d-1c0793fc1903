from Spiders import utils
utils.RETRY_NUM_U = 3

utils.REDIS_DB_U = 1


PROXY = {
    'http': 'socks5://127.0.0.1:10808',
    'https': 'socks5://127.0.0.1:10808'
}

REDIS_ACCOUNTS_NAME = 'accounts:twitter'
REDIS_COOKIES_NAME = 'cookies:twitter'

USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36"

AUTHORIZATION = "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA"

# 会变，但是经测试，都能用
X_CLIENT_TRANSACTION_ID = "czWgmtMpQI5ASaeKoJK8HuGHgKaDEaY/s1uFtEt4hWMsQLcpVyv8OcTkWyoGjxSu3AKtFnA2hoSEm7ctlU47RdtPX/DNcA"
