from curl_cffi import requests


headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "If-Modified-Since": "Mon, 26 May 2025 00:59:23 GMT",
    "If-None-Match": "W/\"6833bceb-8880\"",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    # "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
cookies = {
    # "__jsluid_s": "990074d68d46cc54860d96772d5753ce",
    # "d09ceb7d-92a2-4212-9eb8-1a3989d9b2eb": "WyIyMzc3Mjc2MDMyIl0",
    # "zh_choose_29": "s"
}
url = "https://gat.jiangsu.gov.cn/"
response = requests.get(url, cookies=cookies,impersonate= "chrome110")

print(response.text)
print(response)