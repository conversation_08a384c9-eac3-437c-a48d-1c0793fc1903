"""检查URL请求状态，失败的写入redis"""
#!/usr/bin/env python312
"""
SOL链检查coin_info URL请求状态
将失败的URL写入Redis
支持断点续传
"""

import pymysql
import time
import json
import redis
import signal
import sys
import threading
from threading import Lock
from queue import Queue
from loguru import logger
from datetime import datetime
from curl_cffi import requests as cf_requests

class SolUrlChecker():

    def __init__(self):
        """初始化"""
        self.db_config = {
            'host': '**********',
            'port': 6000,
            'user': 'root',
            'password': 'iAn7*+154-j9r3_dcm',
            'database': 'solana'
        }
        self.redis_config = {
            'host': '**************',
            'password': 123456,
            'port': 6379,
            'db': 4,
            'decode_responses': True
        }
        self.redis_client = redis.Redis(**self.redis_config)
        self.failed_url_key = 'sol_url:failed'
        self.max_retry = 3
        self.timeout = 10
        
        # 断点续传相关配置
        self.progress_keys = {
            'last_address': 'sol_checker:last_address',
            'processed_count': 'sol_checker:processed_count',
            'total_count': 'sol_checker:total_count'
        }
        
        # 设置信号处理器
        self.setup_signal_handlers()
        self.is_interrupted = False
        
        # 多线程配置
        self.thread_count = 24
        self.progress_lock = Lock()
        
        # 已分发但未完成的任务集合（用于防重复）
        self.dispatched_addresses = set()
        self.dispatched_lock = Lock()
        
        # 代理池配置 - 三个代理，每8个线程分配一个
        self.proxy_pool = [
            {  # 代理1 (线程0-7)
                "http": "http://9bc99966eb7353:<EMAIL>:5001",
                "https": "http://9bc99966eb7353:<EMAIL>:5001"
            },
            {  # 代理2 (线程8-15)
                "http": "http://e045b6d42d7e5a:<EMAIL>:5001",
                "https": "http://e045b6d42d7e5a:<EMAIL>:5001"
            },
            {  # 代理3 (线程16-23)
                "http": "http://29be0ced785e61:<EMAIL>:5001",
                "https": "http://29be0ced785e61:<EMAIL>:5001"
            }
        ]

    def setup_signal_handlers(self):
        """设置信号处理器，处理Ctrl+C和程序终止"""
        def signal_handler(signum, frame):
            logger.warning(f"接收到信号 {signum}，正在安全退出...")
            self.is_interrupted = True
            self.save_interrupt_state()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # 程序终止

    def save_interrupt_state(self):
        """保存中断状态到Redis"""
        try:
            logger.info("程序被中断，进度已自动保存")
        except Exception as e:
            logger.error(f"保存中断状态失败: {e}")

    def initialize_progress(self):
        """初始化或恢复进度统计"""
        try:
            # 清理已分发集合
            self.dispatched_addresses.clear()
            
            # 检查是否有之前的进度
            last_address = self.redis_client.get(self.progress_keys['last_address'])
            processed_count = self.redis_client.get(self.progress_keys['processed_count'])
            
            if last_address and processed_count:
                logger.info(f"检测到之前的进度，继续从地址 {last_address} 开始处理")
                logger.info(f"已处理数量: {processed_count}")
            else:
                logger.info("开始新的处理任务")
                self.redis_client.set(self.progress_keys['processed_count'], 0)
                self.redis_client.set(self.progress_keys['last_address'], '')
            
            # 设置或获取总数量
            if not self.redis_client.exists(self.progress_keys['total_count']):
                total = self.get_total_count()
                self.redis_client.set(self.progress_keys['total_count'], total)
                logger.info(f"总共需要处理 {total} 条数据")
            
        except Exception as e:
            logger.error(f"初始化进度失败: {e}")
            raise

    def get_total_count(self):
        """获取总数量"""
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = "SELECT COUNT(DISTINCT address) FROM coin_info WHERE uri != '' AND uri IS NOT NULL"
                cursor.execute(sql)
                return cursor.fetchone()[0]
        finally:
            connection.close()

    def get_next_batch_by_address(self, batch_size=1000):
        """基于address主键获取下一批数据"""
        last_address = self.redis_client.get(self.progress_keys['last_address']) or ''
        
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = """SELECT DISTINCT address, uri FROM coin_info 
                         WHERE address > %s AND uri != '' AND uri IS NOT NULL
                         ORDER BY address LIMIT %s"""
                cursor.execute(sql, (last_address, batch_size))
                result = cursor.fetchall()
                return result
        finally:
            connection.close()

    def update_progress(self, address):
        """更新处理进度（线程安全）"""
        try:
            with self.progress_lock:
                # 更新正式的处理进度
                self.redis_client.set(self.progress_keys['last_address'], address)
                self.redis_client.incr(self.progress_keys['processed_count'])
                
            # 从已分发集合中移除已完成的任务
            with self.dispatched_lock:
                self.dispatched_addresses.discard(address)
                    
        except Exception as e:
            logger.error(f"更新进度失败: {e}")

    def get_progress_info(self):
        """获取进度信息"""
        try:
            total = int(self.redis_client.get(self.progress_keys['total_count']) or 0)
            processed = int(self.redis_client.get(self.progress_keys['processed_count']) or 0)
            last_address = self.redis_client.get(self.progress_keys['last_address']) or '未开始'
            
            if total > 0:
                progress = (processed / total) * 100
                remaining = total - processed
                
                return {
                    'total': total,
                    'processed': processed,
                    'remaining': remaining,
                    'progress': f"{progress:.2f}%",
                    'last_address': last_address
                }
            return None
        except Exception as e:
            logger.error(f"获取进度信息失败: {e}")
            return None

    def is_processing_completed(self):
        """检查是否已完成全部处理"""
        try:
            total = int(self.redis_client.get(self.progress_keys['total_count']) or 0)
            processed = int(self.redis_client.get(self.progress_keys['processed_count']) or 0)
            
            if total > 0 and processed >= total:
                logger.info(f"处理完成检查：已处理 {processed}/{total} 条数据")
                return True
            return False
        except Exception as e:
            logger.error(f"检查处理完成状态失败: {e}")
            return False

    def worker_thread(self, thread_id, task_queue):
        """工作线程函数"""
        processed_count = 0
        try:
            # 每8个线程分配一个代理：0-7用代理1，8-15用代理2，16-23用代理3
            proxy_index = thread_id // 8
            if proxy_index >= len(self.proxy_pool):
                proxy_index = len(self.proxy_pool) - 1
            proxy = self.proxy_pool[proxy_index]
            logger.info(f"[线程{thread_id}] 启动，使用代理{proxy_index + 1}: {proxy}")
            
            while not self.is_interrupted:
                try:
                    # 检查是否已完成全部处理
                    if self.is_processing_completed():
                        logger.info(f"[线程{thread_id}] 检测到全部处理完成，准备退出")
                        break
                    
                    # 从任务队列获取任务
                    task = task_queue.get(timeout=5)  # 5秒超时
                    if task is None:  # 结束信号
                        break
                        
                    address, uri = task
                    success = self.check_url_status(address, uri, thread_id, proxy)
                    
                    # 无论成功还是失败，都更新进度
                    self.update_progress(address)
                    processed_count += 1
                    
                    # 定期显示线程统计
                    if processed_count % 50 == 0:
                        logger.info(f"[线程{thread_id}] 已处理 {processed_count} 条记录")
                    
                    # 添加延时避免请求过快
                    time.sleep(0.5)
                    
                except Exception as e:
                    error_type = type(e).__name__
                    error_msg = str(e)
                    
                    # 处理队列超时异常（正常情况，不是错误）
                    if "Empty" in error_type or "timeout" in error_msg.lower():
                        continue
                    
                    # 其他异常则记录并退出
                    logger.error(f"[线程{thread_id}] 工作线程异常 - 类型: {error_type}, 消息: {error_msg}")
                    break
                    
        except Exception as e:
            logger.error(f"[线程{thread_id}] 线程启动失败: {type(e).__name__} - {str(e)}")
        
        logger.info(f"[线程{thread_id}] 退出，共处理 {processed_count} 条记录")

    def check_url_status(self, address, uri, thread_id, proxy):
        """检查URL请求状态，失败则写入Redis"""
        try:
            if self.is_interrupted:
                return False
                
            logger.info(f"[线程{thread_id}] 检查地址: {address}")
            
            # 尝试请求URL
            success = self.make_request(uri, proxy)
            
            if not success:
                # 请求失败，写入Redis
                self.write_failed_to_redis(address, uri)
                logger.warning(f"[线程{thread_id}] URL请求失败，已写入Redis: {address}")
                return False
            else:
                logger.info(f"[线程{thread_id}] URL请求成功: {address}")
                return True
            
        except Exception as e:
            logger.error(f"[线程{thread_id}] 检查失败 {address}: {str(e)}")
            # 异常也算失败，写入Redis
            self.write_failed_to_redis(address, uri)
            return False

    def make_request(self, url, proxy):
        """发送请求检查URL状态，支持重试"""
        retry_count = 0
        
        while retry_count < self.max_retry:
            try:
                retry_count += 1
                
                response = cf_requests.get(url, proxies=proxy, impersonate="chrome116", timeout=self.timeout)
                response.raise_for_status()
                
                # 请求成功
                return True
                    
            except Exception as e:
                if retry_count < self.max_retry:
                    logger.debug(f"请求失败，2秒后重试 (第{retry_count}/{self.max_retry}次): {url}")
                    time.sleep(2)  # 等待2秒后重试
                else:
                    logger.debug(f"请求失败，已重试{self.max_retry}次: {url}")
                    return False

        return False

    def write_failed_to_redis(self, address, uri):
        """将失败的address和uri写入Redis"""
        try:
            # 使用address作为key，uri作为value
            self.redis_client.hset(self.failed_url_key, address, uri)
            logger.debug(f"已写入Redis失败记录: {address} -> {uri}")
        except Exception as e:
            logger.error(f"写入Redis失败: {e}")

    def run(self, batch_size=1000):
        """主执行方法 - 多线程处理，支持断点续传"""
        try:
            # 0. 预检查：测试基础连接
            logger.info("正在检查基础服务连接...")
            self._check_connections()
            
            # 1. 初始化进度
            self.initialize_progress()
            
            # 2. 显示当前状态
            progress_info = self.get_progress_info()
            if progress_info and progress_info['processed'] > 0:
                logger.info(f"继续执行：已处理 {progress_info['processed']}/{progress_info['total']} "
                           f"({progress_info['progress']})，最后处理地址：{progress_info['last_address']}")
            else:
                logger.info("开始新的URL检查任务")
            
            # 3. 创建任务队列
            task_queue = Queue(maxsize=batch_size * 2)
            
            # 4. 启动工作线程
            threads = []
            for i in range(self.thread_count):
                thread = threading.Thread(target=self.worker_thread, args=(i, task_queue))
                thread.daemon = True
                thread.start()
                threads.append(thread)
                
                if i < self.thread_count - 1:
                    time.sleep(0.1)
            
            logger.info(f"已启动 {self.thread_count} 个工作线程（每8个线程使用一个代理）")
            
            batch_count = 0
            
            # 5. 主循环：获取数据并分发任务
            while not self.is_interrupted:
                # 检查是否已完成全部处理
                if self.is_processing_completed():
                    logger.info("全部数据处理完成，准备退出...")
                    break
                
                # 获取下一批数据
                batch_data = self.get_next_batch_by_address(batch_size)
                
                if not batch_data:
                    logger.info("所有数据已分发完成，等待线程处理完毕...")
                    break
                
                # 过滤掉已经处理过的和已经分发的数据
                current_last_address = self.redis_client.get(self.progress_keys['last_address']) or ''
                filtered_data = []
                
                with self.dispatched_lock:
                    for address, uri in batch_data:
                        if address > current_last_address and address not in self.dispatched_addresses:
                            filtered_data.append((address, uri))
                            self.dispatched_addresses.add(address)
                
                if not filtered_data:
                    time.sleep(2)
                    continue
                
                batch_count += 1
                logger.info(f"开始分发第 {batch_count} 批，共 {len(filtered_data)} 条数据")
                
                # 将任务添加到队列
                for address, uri in filtered_data:
                    if self.is_interrupted:
                        break
                    task_queue.put((address, uri))
                
                # 定期显示整体进度
                if batch_count % 5 == 0:
                    progress_info = self.get_progress_info()
                    if progress_info:
                        logger.info(f"整体进度：{progress_info['progress']} "
                                   f"({progress_info['processed']}/{progress_info['total']})")
                
                time.sleep(2)
            
            # 6. 发送结束信号给所有线程
            for _ in range(self.thread_count):
                task_queue.put(None)
            
            # 7. 等待所有线程完成
            logger.info("等待所有工作线程完成...")
            for thread in threads:
                thread.join(timeout=30)
            
            # 8. 显示最终统计
            progress_info = self.get_progress_info()
            failed_count = self.redis_client.hlen(self.failed_url_key)
            
            if progress_info:
                logger.info(f"URL检查完成！最终统计：")
                logger.info(f"  总数量: {progress_info['total']}")
                logger.info(f"  已处理: {progress_info['processed']}")
                logger.info(f"  完成度: {progress_info['progress']}")
                logger.info(f"  失败URL数量: {failed_count}")
            else:
                logger.info(f"URL检查完成，失败URL数量: {failed_count}")
                
        except Exception as e:
            logger.error(f"执行过程中发生错误: {e}")
            raise

    def get_database_connection(self):
        """连接数据库"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def _check_connections(self):
        """检查数据库和Redis连接"""
        try:
            # 测试数据库连接
            connection = self.get_database_connection()
            connection.close()
            logger.info("数据库连接成功")
            
            # 测试Redis连接
            self.redis_client.ping()
            logger.info("Redis连接成功")
            
        except Exception as e:
            logger.error(f"连接检查失败: {e}")
            raise

    def show_status(self):
        """显示当前状态"""
        progress_info = self.get_progress_info()
        failed_count = self.redis_client.hlen(self.failed_url_key)
        
        if progress_info:
            print(f"""
        === SOL URL Checker 状态 ===
        总数量: {progress_info['total']}
        已处理: {progress_info['processed']}
        剩余量: {progress_info['remaining']}
        进度: {progress_info['progress']}
        最后地址: {progress_info['last_address']}
        失败URL数: {failed_count}
        ========================
            """)
        else:
            print(f"没有找到进度信息，失败URL数: {failed_count}")

    def get_failed_urls_sample(self, limit=10):
        """获取失败URL样例"""
        try:
            failed_data = self.redis_client.hscan(self.failed_url_key, count=limit)
            return failed_data[1]  # 返回字典格式 {address: uri}
        except Exception as e:
            logger.error(f"获取失败URL样例失败: {e}")
            return {}

    def cleanup_progress(self):
        """清理进度信息（可选）"""
        try:
            for key in self.progress_keys.values():
                self.redis_client.delete(key)
            logger.info("进度信息已清理")
        except Exception as e:
            logger.error(f"清理进度信息失败: {e}")


if __name__ == '__main__':
    checker = SolUrlChecker()
    
    # 显示当前状态
    checker.show_status()
    
    try:
        # 执行URL检查
        checker.run(batch_size=1000)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
    finally:
        # 显示最终状态和失败URL样例
        checker.show_status()
        failed_samples = checker.get_failed_urls_sample(5)
        if failed_samples:
            logger.info("失败URL样例:")
            for address, uri in list(failed_samples.items())[:5]:
                logger.info(f"  {address} -> {uri}")