import sqlite3
import pandas as pd
import os
from loguru import logger

def import_and_compare_data():
    try:
        # 数据库路径
        db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 读取CSV文件
        logger.info("开始读取KPAH CSV文件...")
        kpah_df = pd.read_csv("dex_swap_sol_202503251424.csv")
        
        # 连接到SQLite数据库
        logger.info("连接到SQLite数据库...")
        conn = sqlite3.connect(db_path)
        
        # 创建kpah_parse表
        create_table_sql = '''
        CREATE TABLE IF NOT EXISTS kpah_parse (
            sol_price REAL,
            token_amount_base REAL,
            token_amount_quote REAL,
            tx_hash TEXT,
            swap_type INTEGER
        )
        '''
        
        logger.info("创建KPAH表结构...")
        conn.execute(create_table_sql)
        
        # 将DataFrame写入SQLite数据库
        logger.info("开始导入KPAH数据...")
        kpah_df.to_sql('kpah_parse', conn, if_exists='replace', index=False)
        
        # 获取导入的记录数
        kpah_count = conn.execute("SELECT COUNT(*) FROM kpah_parse").fetchone()[0]
        logger.success(f"KPAH数据导入完成，共导入 {kpah_count} 条记录")

        # 创建关联查询
        compare_sql = '''
        SELECT 
            k.tx_hash,
            k.sol_price as kpah_sol_price,
            k.token_amount_base as kpah_amount_base,
            k.token_amount_quote as kpah_amount_quote,
            k.swap_type as kpah_swap_type,
            g.gmgn_price_usd as gmgn_price,
            g.gmgn_amount_base,
            g.gmgn_amount_quote,
            g.gmgn_swap_type,
            g.gmgn_time,
            CASE 
                WHEN ABS(k.sol_price - g.gmgn_price_usd)/g.gmgn_price_usd > 0.01 THEN 'Price Difference > 1%'
                ELSE 'Price Match'
            END as price_comparison,
            CASE 
                WHEN k.swap_type != g.gmgn_swap_type THEN 'Swap Type Mismatch'
                ELSE 'Swap Type Match'
            END as swap_type_comparison
        FROM kpah_parse k
        INNER JOIN gmgn_price g ON k.tx_hash = g.gmgn_tx_hash
        '''

        # 执行比对查询
        logger.info("开始数据比对...")
        comparison_df = pd.read_sql_query(compare_sql, conn)
        
        # 保存比对结果到CSV
        result_file = "comparison_results.csv"
        comparison_df.to_csv(result_file, index=False)
        
        # 输出统计信息
        match_count = len(comparison_df)
        logger.info(f"找到 {match_count} 条匹配的交易记录")
        
        # 计算价格差异统计
        price_diff_stats = comparison_df['price_comparison'].value_counts()
        logger.info("\n价格比对统计:")
        logger.info(price_diff_stats)
        
        # 计算swap类型匹配统计
        swap_type_stats = comparison_df['swap_type_comparison'].value_counts()
        logger.info("\nSwap类型比对统计:")
        logger.info(swap_type_stats)
        
        # 关闭连接
        conn.close()
        logger.info("数据库连接已关闭")
        logger.success(f"比对结果已保存到文件: {result_file}")
        
    except Exception as e:
        logger.error(f"处理过程中出错: {str(e)}")
        if 'conn' in locals():
            conn.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    # 设置日志
    logger.add("data_comparison.log", rotation="500 MB")
    
    # 执行导入和比对
    import_and_compare_data()