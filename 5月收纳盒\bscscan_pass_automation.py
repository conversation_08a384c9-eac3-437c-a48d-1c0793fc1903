import redis
import requests
import time
import json
from datetime import date
from loguru import logger
import re
crawl_date = date.today()
from DrissionPage import ChromiumPage

redis_client = redis.Redis(
    host='**************',
    port=6379,
    db=9,
    password='123456',
    decode_responses=True
)

def setup_browser():
    """设置并启动浏览器"""
    page = ChromiumPage()
    return page

def get_and_save_cookies():
    """获取并保存cookies"""
    try:
        page = setup_browser()
        
        logger.info('正在访问目标页面...')
        page.get('https://bscscan.com/tokens')
        
        logger.info('等待15秒通过CloudFlare检测...')
        time.sleep(15)
        
        all_cookies = page.cookies(all_info=False)
        
        required_cookies = {}
        for cookie in all_cookies:
            if cookie['name'] in ['cf_clearance', 'ASP.NET_SessionId', '__cflb']:
                required_cookies[cookie['name']] = cookie['value']
        
        user_agent = page.user_agent
        
        required_cookies['user_agent'] = user_agent
        
        logger.info(f'获取到的cookies和user-agent: {required_cookies}')
        
        redis_client.set('cf_cookies:bscscan', json.dumps(required_cookies))
        
        logger.info('Cookie和User-Agent已一起保存到Redis')
        
        page.quit()
        
        return True
        
    except Exception as e:
        logger.error(f'获取Cookie过程出错: {str(e)}')
        if 'page' in locals():
            page.quit()
        return False

def main():
    """主函数"""
    success = get_and_save_cookies()
    if success:
        logger.info('Cookie获取成功！')
    else:
        logger.error('Cookie获取失败！')

if __name__ == '__main__':
    main()
