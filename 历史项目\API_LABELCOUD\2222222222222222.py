import requests
from loguru import logger

proxy = "*************************************************"
proxies = {
    'http': proxy, 
    'https': proxy,
}

def check_login_status(text):
    """ 检查登录状态 """

    login_status: bool
    error_msg: str = ''
    if 'Sign In for Continued Access' in text:
        login_status = False
        error_msg = '登录失败, 已过掉cloudflare检测, 但未登录成功!'
    elif 'Top Accounts by ETH Balance' in text:
        login_status = True
        error_msg = '检测登录-成功'
    else:
        login_status = False
        error_msg = '登录失败, 没有过掉cloudflare检测'
    return login_status, error_msg

headers = {
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
}
cookies = {
    "cf_clearance": "ByfiQRH76AFp_yRUHsS4aQ0hUBNUvficRhplZXUsJr4-**********-*******-N7XIPIW9OXtbCCrU2td8D2PiBG64ImCN1mE4RhVseyMOUdKhJnv5u8i1lOBqf0YcE5__xYfkfFS1VA0MBjBeok3bTl20Mx1QQyF5p1Y_kac.uJCQJq5hfVhCccKMepObwfR59SuJLyGEUw8ZPrAdmR5BTsIlwipo.GA31w8iSJ7vcZrAIhuoxvqv9rXUwFa2kR_G8rBUCXQoDX_D2J3hNf2ZTeieCcP8KclN7LUnWVifWgEV8tltI84MHT4CrtMOpenTGhmGNmQqw9tsBHuz3f4ACveUp1DFzkxzPsw1_o5W1Cby0oPusLpbUkMZbwtCQbNMgeZvTkh_c7V8XZ6BV5CMhDCfJ7wRHRXdEE.qCuirwfjCeGmLNjXugGKgPf.fIvlRjUpvH.GtELuJXLHI7yCdEKRi.BW3uJP_R9Y_4KQ",
    "ASP.NET_SessionId": "gtftfepho2hjle05hgs3ymge",
    '__cflb': '02DiuJ7NLBYKjsZtxjRR4QggQcq1CaL9Qe54nwD4hq9PA'
}
url = "https://optimistic.etherscan.io/login"
response = requests.get(url, headers=headers, cookies=cookies, proxies=proxies)

print(response.text)
print(response)

ogin_status, error_msg = check_login_status(text=response.text)
print(error_msg)