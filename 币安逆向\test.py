import requests

proxies = {
            "http": "http://127.0.0.1:7897",
            "https": "http://127.0.0.1:7897"
        }

headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "max-age=0",
    "priority": "u=0, i",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "none",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
}
cookies = {
    "_ga_3WP50LGEEC": "GS2.1.s1752659743$o15$g1$t1752659856$j60$l0$h0",
    "aws-waf-token": "9b1796da-3077-48c0-9e91-5592887a02c3:AAABmBw013Y=:4/F4vF6vVyuVSiWSyeRyuVwul8tlstns9vv9/v/////j8Xi8Xq9XK5VKJZLJ5HK5XC6Xy2Wy2ez2+/3+/////+PxeLxer1crlUolksnkcrlcLpfLZbLZ7Pb7/f7/////4/F4vF6vVyuVSiWSyeRyuVwul8tlstns9vv9/v////8="
}
url = "https://www.binance.com/zh-CN/support/announcement"
response = requests.get(url, headers=headers, cookies=cookies, proxies=proxies)

print(response.text)
print(response)