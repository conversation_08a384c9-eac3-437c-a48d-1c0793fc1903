import subprocess
import time
import os
import sys
from loguru import logger
from verify_cookies import verify_all_cookies, verify_single_site, SITES_CONFIG
from utils import DB_BASE

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

db_base = DB_BASE()

GENERATOR_SCRIPTS = {
    'optimistic': 'op_pass.py',
    'polygon': 'polygonscan_pass.py',
    'scroll': 'scrollscan_pass.py',
    'base': 'basescan_pass.py',
    'eth': 'etherscan_pass.py',
    'linea': 'lineascan_pass.py',
    'arbitrum': 'arbiscan_pass.py',
    'blast': 'blastscan_pass.py',
    'bsc': 'bscscan_pass.py'
}

site_failure_counts = {}

def run_generator(site_name):
    """运行指定站点的cookie生成器"""
    try:
        script_name = GENERATOR_SCRIPTS.get(site_name)
        if not script_name:
            logger.error(f"未找到 {site_name} 的生成器脚本")
            return False

        script_path = os.path.join(SCRIPT_DIR, script_name)
        
        if not os.path.exists(script_path):
            logger.error(f"生成器脚本不存在: {script_path}")
            return False

        logger.info(f"开始为 {site_name} 生成新的cookie...")
        logger.info(f"执行脚本: {script_path}")
        
        # 使用sys.executable获取当前python解释器的完整路径
        python_executable = sys.executable
        logger.debug(f"使用python解释器: {python_executable}")
        
        process = subprocess.Popen([python_executable, script_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 cwd=SCRIPT_DIR)
        
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            logger.success(f"{site_name} 的cookie生成成功")
            if stdout:
                logger.info(f"脚本输出: {stdout.decode('utf-8')}")
            return True
        else:
            logger.error(f"{site_name} 的cookie生成失败")
            logger.error(f"错误信息: {stderr.decode('utf-8')}")
            return False
            
    except Exception as e:
        logger.error(f"运行 {site_name} 生成器时发生错误: {str(e)}")
        return False

def manage_cookies():
    """管理cookie的主函数"""
    while True:
        try:
            logger.info("开始验证所有站点的cookie...")
            results = verify_all_cookies()
            invalid_sites = [site for site, is_valid in results.items() if not is_valid]
            
            if not invalid_sites:
                logger.success("所有站点的cookie都有效！")
                site_failure_counts.clear()
                # logger.info("等待半小时后重新检查...")
                time.sleep(300)
                continue
                
            for site in invalid_sites:
                logger.warning(f"开始处理 {site} 的无效cookie...")

                if site not in site_failure_counts:
                    site_failure_counts[site] = 1
                else:
                    site_failure_counts[site] += 1
                
                if run_generator(site):
                    time.sleep(20)
                    
                    if verify_single_site(site, SITES_CONFIG[site]):
                        logger.success(f"{site} 的cookie已更新并验证成功")
                        # 重置失败计数
                        site_failure_counts[site] = 0
                    else:
                        logger.error(f"{site} 的cookie更新后验证仍然失败")
                        
                        # 如果连续三次验证失败，发送飞书预警
                        if site_failure_counts[site] >= 3:
                            logger.critical(f"{site} 平台的cookie已连续三次验证失败！")
                            db_base.send_to_fs(f"\n{site}平台的cookie已连续三次验证失败，请手动检查！")
                else:
                    logger.error(f"{site} 的cookie生成失败")
                    
                    # 如果连续三次生成失败，也发送飞书预警
                    if site_failure_counts[site] >= 3:
                        logger.critical(f"{site} 平台的cookie已连续三次生成失败！")
                        db_base.send_to_fs(f"\n{site}平台的cookie已连续三次生成失败，请手动检查！")
            
            time.sleep(300)
            
        except Exception as e:
            logger.error(f"cookie管理过程中发生错误: {str(e)}")
            logger.info("等待5分钟后重试...")
            time.sleep(300)

if __name__ == "__main__":
    # 验证脚本目录是否正确
    logger.info(f"脚本目录: {SCRIPT_DIR}")
    
    # 验证所有生成器脚本是否存在
    for site, script in GENERATOR_SCRIPTS.items():
        script_path = os.path.join(SCRIPT_DIR, script)
        if not os.path.exists(script_path):
            logger.warning(f"警告: {site} 的生成器脚本不存在: {script_path}")
    
    logger.add("cookie_manager.log", rotation="1 day", retention="7 days")
    logger.info("Cookie管理器启动...")
    manage_cookies() 