import requests

ClientKey = '86529fa1d01339f34ff2b5f77f5ff0d1622065ed56034'
proxy_eth = {
    'http': "socks5://192.168.224.75:30889",
    'https': "socks5://192.168.224.75:30889"
}

websiteURL = "https://optimistic.etherscan.io/accounts/label/aave"
website_key = '6Le1YycTAAAAAJXqwosyiATvJ6Gs2NLn8VEzTVlS'

def create_task():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": ClientKey,
        "task":
        {
            "type": "TurnstileTaskProxyless",
            "websiteURL": websiteURL,
            "websiteKey": "6Le1YycTAAAAAJXqwosyiATvJ6Gs2NLn8VEzTVlS",
            "proxy": proxy_eth['http'],
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"
        }
    }
    response = requests.post(url, headers=headers, json=data, proxies=proxy_eth)
    print(response.json())
    return response.json()

if __name__ == '__main__':
    create_task()
    # get_task_result()