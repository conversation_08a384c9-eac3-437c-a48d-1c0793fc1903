#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HA代币数据获取工具
获取SOL链上代币的涨跌幅差异和交易量数据
接口: /api/v1/dex/market/stats
"""

import requests
import json
import sqlite3
import time
import sys
import random
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库配置
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "Price_Difference"

# HA API配置
API_URL = "https://www.valuescan.ai/api/v1/dex/market/stats"

# 代理配置
PRIMARY_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

BACKUP_PROXY = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

# 请求配置
MAX_RETRIES = 5
RETRY_INTERVAL = 3
REQUEST_TIMEOUT = 10
REQUEST_INTERVAL_MIN = 1
REQUEST_INTERVAL_MAX = 2

# 批处理配置
BATCH_SIZE = 1  # 每批处理的代币数量

# 多线程配置
NUM_THREADS = 1

# 线程锁
PRINT_LOCK = threading.Lock()
DB_LOCK = threading.Lock()
PROXY_LOCK = threading.Lock()

# 当前代理
CURRENT_PROXY = PRIMARY_PROXY.copy()


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with PRINT_LOCK:
        print(*args, **kwargs)


def get_headers():
    """生成HA请求头"""
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    return headers


def get_token_addresses_from_db():
    """从数据库获取Token地址列表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        cursor.execute(f'SELECT rowid, "Token Address" FROM {DB_TABLE}')
        rows = cursor.fetchall()
        
        token_data = []
        for row_id, address in rows:
            if address and address.strip():
                token_data.append({
                    "row_id": row_id,
                    "token_address": address.strip()
                })
        
        thread_safe_print(f"从数据库获取到 {len(token_data)} 个Token地址")
        return token_data
        
    except sqlite3.Error as e:
        thread_safe_print(f"从数据库获取Token地址时出错: {e}")
        return []
    finally:
        conn.close()


def update_ha_data_in_db(row_id, token_address, price_change, vol_usd, prev_price, curr_price):
    """更新数据库中的HA涨跌幅和交易量数据"""
    with DB_LOCK:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        try:
            # 获取当前时间
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 更新数据库
            cursor.execute(f'''
                UPDATE {DB_TABLE}
                SET "data HA" = ?,
                    "volume HA" = ?,
                    "prevPrice" = ?,
                    "currPrice" = ?
                WHERE rowid = ?
            ''', (price_change, vol_usd, prev_price, curr_price, row_id))
            
            if cursor.rowcount > 0:
                conn.commit()
                thread_safe_print(f"成功更新 {token_address} - 涨跌幅: {price_change}, 交易量: {vol_usd}, 前价格: {prev_price}, 当前价格: {curr_price}")
                return True
            else:
                thread_safe_print(f"未找到记录 rowid: {row_id}")
                return False
                
        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库时出错: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()


def get_batch_ha_data(token_batch, thread_id):
    """
    批量获取HA数据并立即写入数据库
    """
    global CURRENT_PROXY
    
    # 构造请求数据
    coin_keys = []
    for token_data in token_batch:
        coin_keys.append({
            "chainName": "SOL",
            "tokenContractAddress": token_data["token_address"]
        })

    request_data = {
        "bar": "24h",  # 时间周期，24小时
        "coinKeys": coin_keys
    }
    
    thread_safe_print(f"[线程-{thread_id}] 请求 {len(coin_keys)} 个代币的HA数据...")
    
    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            # 获取当前代理
            with PROXY_LOCK:
                current_proxy = CURRENT_PROXY.copy()
            
            thread_safe_print(f"[线程-{thread_id}] 尝试 {retry_count + 1}/{MAX_RETRIES}")
            
            # 生成请求头
            headers = get_headers()
            
            # 发送POST请求
            response = requests.post(
                API_URL,
                headers=headers,
                json=request_data,
                proxies=current_proxy,
                timeout=REQUEST_TIMEOUT
            )
            
            # 处理响应
            if response.status_code == 200:
                try:
                    result = response.json()
                    
                    # 检查API返回状态
                    if result.get("code") == 200 and "data" in result:
                        stats_data = result["data"]
                        
                        # 创建地址到rowid的映射
                        address_to_rowid = {item["token_address"]: item["row_id"] for item in token_batch}
                        
                        success_count = 0
                        
                        # 处理每个代币的数据并立即写入数据库
                        for stats in stats_data:
                            token_address = stats.get("tokenContractAddress")
                            price_change = stats.get("priceChange")
                            vol_usd = stats.get("volUsd")
                            prev_price = stats.get("prevPrice")
                            curr_price = stats.get("currPrice")
                            
                            if token_address and price_change is not None:
                                # 通过地址找到对应的rowid
                                row_id = address_to_rowid.get(token_address)
                                
                                if row_id:
                                    # 确保数值字段有值，如果没有则设为"0"
                                    vol_usd = vol_usd if vol_usd is not None else "0"
                                    prev_price = prev_price if prev_price is not None else "0"
                                    curr_price = curr_price if curr_price is not None else "0"
                                    
                                    # 立即写入数据库
                                    if update_ha_data_in_db(row_id, token_address, price_change, vol_usd, prev_price, curr_price):
                                        success_count += 1
                                        thread_safe_print(f"[线程-{thread_id}] {token_address} 数据已成功写入数据库")
                                else:
                                    thread_safe_print(f"[线程-{thread_id}] 未找到代币 {token_address} 对应的rowid")
                        
                        thread_safe_print(f"[线程-{thread_id}] 本批次成功处理 {success_count}/{len(token_batch)} 个代币")
                        return success_count
                        
                    else:
                        error_msg = result.get("msg", "未知错误")
                        thread_safe_print(f"[线程-{thread_id}] API错误: {error_msg}")
                        
                except json.JSONDecodeError:
                    thread_safe_print(f"[线程-{thread_id}] JSON解析失败: {response.text[:200]}...")
            else:
                thread_safe_print(f"[线程-{thread_id}] HTTP错误 {response.status_code}: {response.text[:200]}...")
            
            # 重试逻辑
            retry_count += 1
            if retry_count < MAX_RETRIES:
                thread_safe_print(f"[线程-{thread_id}] 等待 {RETRY_INTERVAL} 秒后重试...")
                time.sleep(RETRY_INTERVAL)
            
        except requests.exceptions.RequestException as e:
            thread_safe_print(f"[线程-{thread_id}] 请求异常: {e}")
            retry_count += 1
            if retry_count < MAX_RETRIES:
                time.sleep(RETRY_INTERVAL)
        
        except Exception as e:
            thread_safe_print(f"[线程-{thread_id}] 未知错误: {e}")
            retry_count += 1
            if retry_count < MAX_RETRIES:
                time.sleep(RETRY_INTERVAL)
    
    thread_safe_print(f"[线程-{thread_id}] 已达到最大重试次数，本批次获取失败")
    return 0  # 返回成功处理的数量


def switch_to_backup_proxy():
    """切换到备用代理"""
    global CURRENT_PROXY
    with PROXY_LOCK:
        CURRENT_PROXY = BACKUP_PROXY.copy()
        thread_safe_print("已切换到备用代理")


def worker_thread(token_batches, thread_id):
    """工作线程函数，处理多个批次"""
    thread_safe_print(f"[线程-{thread_id}] 开始处理 {len(token_batches)} 个批次")
    
    total_success = 0
    
    for i, token_batch in enumerate(token_batches):
        thread_safe_print(f"[线程-{thread_id}] 处理第 {i + 1}/{len(token_batches)} 个批次，包含 {len(token_batch)} 个代币")
        
        # 获取HA数据并立即写入数据库
        success_count = get_batch_ha_data(token_batch, thread_id)
        total_success += success_count
        
        # 批次间隔
        if i < len(token_batches) - 1:
            delay = random.uniform(REQUEST_INTERVAL_MIN, REQUEST_INTERVAL_MAX)
            thread_safe_print(f"[线程-{thread_id}] 等待 {delay:.2f} 秒...")
            time.sleep(delay)
    
    thread_safe_print(f"[线程-{thread_id}] 完成处理，总成功: {total_success}")
    return total_success


def process_all_tokens():
    """处理所有代币数据"""
    # 获取所有token地址
    all_tokens = get_token_addresses_from_db()
    
    if not all_tokens:
        thread_safe_print("未找到任何Token地址")
        return 0, 0
    
    total_count = len(all_tokens)
    thread_safe_print(f"总共需要处理 {total_count} 个代币，使用 {NUM_THREADS} 个线程，每批 {BATCH_SIZE} 个")
    
    # 先将所有token分成批次
    all_batches = []
    for i in range(0, total_count, BATCH_SIZE):
        batch = all_tokens[i:i + BATCH_SIZE]
        all_batches.append(batch)
    
    thread_safe_print(f"总共分成 {len(all_batches)} 个批次")
    
    # 将批次分配给各个线程
    batches_per_thread = len(all_batches) // NUM_THREADS
    remainder = len(all_batches) % NUM_THREADS
    
    thread_batches = []
    start_idx = 0
    
    for i in range(NUM_THREADS):
        batch_count = batches_per_thread + (1 if i < remainder else 0)
        end_idx = start_idx + batch_count
        thread_batches.append(all_batches[start_idx:end_idx])
        thread_safe_print(f"线程-{i + 1} 分配到 {len(thread_batches[-1])} 个批次")
        start_idx = end_idx
    
    # 使用线程池执行
    success_counts = []
    with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
        futures = [executor.submit(worker_thread, thread_batches[i], i + 1) for i in range(NUM_THREADS)]
        
        for future in futures:
            success_counts.append(future.result())
    
    total_success = sum(success_counts)
    return total_count, total_success


def main():
    """主函数"""
    thread_safe_print("=" * 80)
    thread_safe_print("HA代币数据获取工具")
    thread_safe_print("获取SOL链上代币的涨跌幅差异和交易量数据")
    thread_safe_print("=" * 80)
    thread_safe_print(f"API URL: {API_URL}")
    thread_safe_print(f"数据库: {DB_PATH}")
    thread_safe_print(f"数据表: {DB_TABLE}")
    thread_safe_print(f"线程数: {NUM_THREADS}")
    thread_safe_print(f"批次大小: {BATCH_SIZE}")
    thread_safe_print("=" * 80)
    
    start_time = time.time()
    
    try:
        total_count, success_count = process_all_tokens()
        
        thread_safe_print("\n" + "=" * 80)
        thread_safe_print("处理完成！")
        thread_safe_print(f"总计处理: {total_count} 个代币")
        thread_safe_print(f"成功更新: {success_count} 个代币")
        thread_safe_print(f"成功率: {success_count / total_count * 100:.2f}%" if total_count > 0 else "成功率: 0%")
        
    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        thread_safe_print(f"程序运行错误: {e}")
        import traceback
        thread_safe_print(traceback.format_exc())
    
    end_time = time.time()
    thread_safe_print(f"总耗时: {end_time - start_time:.2f} 秒")
    thread_safe_print("=" * 80)


if __name__ == "__main__":
    main()
