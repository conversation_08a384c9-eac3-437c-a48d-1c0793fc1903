import requests
import re
import time
import redis
import random
from loguru import logger
from pymysql.cursors import DictCursor
from html import unescape
import pymysql
import os
import threading


class INVESTMENTTEAM_SPIDER():
    def __init__(self):
        self.url = "https://dncapi.flink1.com/api/v3/coin/team"
        self.headers = {
            "authority": "dncapi.flink1.com",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "origin": "https://www.feixiaohao.com",
            "referer": "https://www.feixiaohao.com/",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.params = {
            "webp": "1"
        }
        
        self.mysql_config = {
            'host': '***************',
            'port': 3306,
            'user': 'spider',
            'password': 'ha13579.',
            'db': 'spider',
            'charset': 'utf8',
            'cursorclass': DictCursor
        }
        
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            password='123456',
            db=2,
            decode_responses=True
        )
        
        self.proxies = {
            'http': 'socks5://**************:30889',
            'https': 'socks5://**************:30889'
        }

        self.team_logo_dir = '/data/spider_icons/TOKEN_ICON/token_basic_info/team_agency_logo/team_logo'
        self.agency_logo_dir = '/data/spider_icons/TOKEN_ICON/token_basic_info/team_agency_logo/agency_logo'

        for directory in [self.team_logo_dir, self.agency_logo_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"创建目录: {directory}")

        # 添加一个程序终止标志
        self.should_stop = False
        self.conn = pymysql.connect(**self.mysql_config)
        self.request_count = 0

    def clean_text(self, text):
        if not text:
            return ''
        text = unescape(text)
        text = re.sub(r'<[^>]+>', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        text = re.sub(r'[\s\u3000]+', ' ', text)
        text = re.sub(r'\(\s*\)', '', text)
        return text.strip()

    def clean_filename(self, filename):
        if not filename:
            return ''
        filename = re.sub(r'[\\/*?:"<>|]', '', filename)
        filename = re.sub(r'\（.*?\）', '', filename)
        filename = re.sub(r'\(.*?\)', '', filename)
        filename = ' '.join(filename.split())
        return filename.strip()

    def download_logo(self, url, code, name, is_team=True):
        try:
            if not url:
                return None

            clean_name = self.clean_filename(name)
            if not clean_name:
                logger.warning(f"清理后的名称为空，跳过下载: {name}")
                return None

            filename = f"{code}_{clean_name}.png"
            save_dir = self.team_logo_dir if is_team else self.agency_logo_dir
            save_path = os.path.join(save_dir, filename)

            relative_path = f"/icon/token_basic_info/team_agency_logo/{'team_logo' if is_team else 'agency_logo'}/{filename}"

            if os.path.exists(save_path):
                logger.info(f"文件已存在，跳过下载: {filename}")
                return relative_path

            response = requests.get(
                url,
                headers=self.headers,
                proxies=self.proxies,
                timeout=10
            )

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                logger.success(f"成功下载: {filename}")
                return relative_path
            else:
                logger.error(f"下载失败 {name}, 状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"下载出错 {name}: {str(e)}")
            return None

    def get_code_from_redis(self):
        try:
            code = self.redis_client.spop('feixiaohao:coin_codes')
            if code:
                logger.info(f'从Redis获取到code: {code}')
                return code
            return None
        except Exception as e:
            logger.error(f'从Redis获取code失败: {e}')
            return None

    def fetch_data(self):
        try:
            response = requests.get(self.url, headers=self.headers, params=self.params, proxies=self.proxies)
            logger.info(f'请求URL: {self.url}')
            return response.json()
        except Exception as e:
            logger.error(f'获取数据时出错: {e}')
            return None

    def process_data(self, data, code):
        if not data:
            return

        team_data = data.get('data', {}).get('team', [])
        agency_data = data.get('data', {}).get('agency', [])

        parsed_team_data = []
        for team in team_data:
            team_name = self.clean_text(team.get('name', ''))
            team_logo_url = team.get('logo', '')
            team_route = None

            if team_logo_url and team_name:
                clean_name = self.clean_filename(team_name)
                if clean_name:
                    filename = f"{code}_{clean_name}.png"
                    team_route = f"/icon/token_basic_info/team_agency_logo/team_logo/{filename}"

            team_info = {
                'team_name': team_name,
                'team_logo': team_logo_url,
                'team_route': team_route
            }
            parsed_team_data.append(team_info)

        parsed_agency_data = []
        for agency in agency_data:
            agency_name = self.clean_text(agency.get('name', ''))
            agency_logo_url = agency.get('logo', '')
            agency_route = None

            if agency_logo_url and agency_name:
                clean_name = self.clean_filename(agency_name)
                if clean_name:
                    filename = f"{code}_{clean_name}.png"
                    agency_route = f"/icon/token_basic_info/team_agency_logo/agency_logo/{filename}"

            agency_info = {
                'agency_name': agency_name,
                'agency_logo': agency_logo_url,
                'agency_route': agency_route
            }
            parsed_agency_data.append(agency_info)

        if parsed_team_data:
            self.insert_team_data_to_mysql(parsed_team_data, code)
        if parsed_agency_data:
            self.insert_agency_data_to_mysql(parsed_agency_data, code)

    def insert_team_data_to_mysql(self, parsed_team_data, code):
        if not parsed_team_data:
            return

        try:
            with self.conn.cursor() as cursor:
                check_sql = "SELECT id FROM fxh_team_agency_data WHERE code = %s AND team_name = %s"
                insert_sql = """
                    INSERT INTO fxh_team_agency_data 
                    (code, team_name, team_logo, team_route)
                    VALUES (%s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    team_logo = VALUES(team_logo),
                    team_route = VALUES(team_route)
                """
                
                for team in parsed_team_data:
                    team_name = team['team_name']
                    team_logo = team['team_logo']
                    team_route = team['team_route']
                    
                    logger.info(f"准备插入团队数据 - code: {code}, name: {team_name}, route: {team_route}")
                    
                    try:
                        cursor.execute(check_sql, (code, team_name))
                        result = cursor.fetchone()
                        
                        if result:
                            update_sql = """
                                UPDATE fxh_team_agency_data 
                                SET team_logo = %s, team_route = %s
                                WHERE code = %s AND team_name = %s
                            """
                            cursor.execute(update_sql, (team_logo, team_route, code, team_name))
                            logger.info(f"更新团队数据 - code: {code}, name: {team_name}")
                        else:
                            cursor.execute(insert_sql, (code, team_name, team_logo, team_route))
                            logger.info(f"插入新团队数据 - code: {code}, name: {team_name}")
                    except pymysql.err.IntegrityError as e:
                        if e.args[0] == 1452:  # 外键约束错误
                            logger.warning(f"外键约束错误，跳过该记录 - code: {code}, name: {team_name}")
                            continue
                        else:
                            logger.error(f"数据库操作失败: {e}")
                            self.should_stop = True
                            raise Exception("数据库操作失败，程序将终止")
                    except Exception as e:
                        logger.error(f"数据库操作失败: {e}")
                        self.should_stop = True
                        raise Exception("数据库操作失败，程序将终止")
                
            self.conn.commit()
            logger.success(f"成功处理团队数据，code: {code}")
        except Exception as e:
            if isinstance(e, pymysql.err.IntegrityError) and e.args[0] == 1452:
                logger.warning(f"外键约束错误，跳过该记录 - code: {code}")
                self.conn.rollback()
            else:
                logger.error(f"处理团队数据时出错: {e}")
                self.conn.rollback()
                self.should_stop = True
                raise Exception("处理团队数据失败，程序将终止")

    def insert_agency_data_to_mysql(self, parsed_agency_data, code):
        if not parsed_agency_data:
            return

        try:
            with self.conn.cursor() as cursor:
                check_sql = "SELECT id FROM fxh_team_agency_data WHERE code = %s AND agency_name = %s"
                insert_sql = """
                    INSERT INTO fxh_team_agency_data 
                    (code, agency_name, agency_logo, agency_route)
                    VALUES (%s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    agency_logo = VALUES(agency_logo),
                    agency_route = VALUES(agency_route)
                """
                
                for agency in parsed_agency_data:
                    agency_name = agency['agency_name']
                    agency_logo = agency['agency_logo']
                    agency_route = agency['agency_route']
                    
                    logger.info(f"准备插入机构数据 - code: {code}, name: {agency_name}, route: {agency_route}")
                    
                    try:
                        cursor.execute(check_sql, (code, agency_name))
                        result = cursor.fetchone()
                        
                        if result:
                            update_sql = """
                                UPDATE fxh_team_agency_data 
                                SET agency_logo = %s, agency_route = %s
                                WHERE code = %s AND agency_name = %s
                            """
                            cursor.execute(update_sql, (agency_logo, agency_route, code, agency_name))
                            logger.info(f"更新机构数据 - code: {code}, name: {agency_name}")
                        else:
                            cursor.execute(insert_sql, (code, agency_name, agency_logo, agency_route))
                            logger.info(f"插入新机构数据 - code: {code}, name: {agency_name}")
                    except pymysql.err.IntegrityError as e:
                        if e.args[0] == 1452:  # 外键约束错误
                            logger.warning(f"外键约束错误，跳过该记录 - code: {code}, name: {agency_name}")
                            continue
                        else:
                            logger.error(f"数据库操作失败: {e}")
                            self.should_stop = True
                            raise Exception("数据库操作失败，程序将终止")
                    except Exception as e:
                        logger.error(f"数据库操作失败: {e}")
                        self.should_stop = True
                        raise Exception("数据库操作失败，程序将终止")
                
            self.conn.commit()
            logger.success(f"成功处理机构数据，code: {code}")
        except Exception as e:
            if isinstance(e, pymysql.err.IntegrityError) and e.args[0] == 1452:
                logger.warning(f"外键约束错误，跳过该记录 - code: {code}")
                self.conn.rollback()
            else:
                logger.error(f"处理机构数据时出错: {e}")
                self.conn.rollback()
                self.should_stop = True
                raise Exception("处理机构数据失败，程序将终止")

    def process_single_thread(self, thread_id):
        """单个线程的处理逻辑"""
        logger.info(f'线程 {thread_id} 开始运行')
        
        while not self.should_stop:
            with self.thread_lock:
                code = self.get_code_from_redis()
                if not code:
                    logger.info(f"线程 {thread_id} 完成所有code处理")
                    break
                if code in self.processed_codes:
                    continue
                self.processed_codes.add(code)

            try:
                self.params['code'] = code
                data = self.fetch_data()
                if data:
                    self.process_data(data, code)
                else:
                    logger.error(f"线程 {thread_id} 未获取到数据，程序将终止")
                    self.should_stop = True
                    break

                # 检查是否应该停止
                if self.should_stop:
                    logger.warning(f"线程 {thread_id} 检测到停止信号，正在退出")
                    break

                self.request_count += 1
                if self.request_count >= 2:
                    sleep_time = random.uniform(10, 15)
                    logger.info(f"线程 {thread_id} 已完成8次请求，休息 {sleep_time:.2f} 秒")
                    time.sleep(sleep_time)
                    self.request_count = 0
                else:
                    wait_time = random.uniform(4, 7)
                    logger.info(f"线程 {thread_id} 单次请求后休息 {wait_time:.2f} 秒")
                    time.sleep(wait_time)

            except Exception as e:
                logger.error(f"线程 {thread_id} 处理code {code}时发生错误: {e}")
                self.should_stop = True
                break

    def run(self):
        """使用多线程运行爬虫"""
        try:
            self.thread_lock = threading.Lock()
            self.processed_codes = set()
            self.should_stop = False

            threads = []
            for i in range(3):
                thread = threading.Thread(
                    target=self.process_single_thread,
                    args=(i+1,),
                    name=f"Thread-{i+1}"
                )
                threads.append(thread)
                thread.start()
                time.sleep(0.5)
                logger.info(f"线程 {i+1} 已启动")

            for thread in threads:
                thread.join()

            if self.should_stop:
                logger.warning("程序因错误终止")
            else:
                logger.success("所有线程正常完成")

        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
        finally:
            self.conn.close()


if __name__ == '__main__':
    spider = INVESTMENTTEAM_SPIDER()
    spider.run()