import requests


headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Connection": "keep-alive",
    "Origin": "https://fanyi.youdao.com",
    "Referer": "https://fanyi.youdao.com/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
cookies = {
    "OUTFOX_SEARCH_USER_ID_NCOO": "1373042093.4709904",
    "OUTFOX_SEARCH_USER_ID": "-964980022@171.223.207.50",
    "DICT_DOCTRANS_SESSION_ID": "MTMxZTQ1NDctMmZhYS00M2UxLTg5ZjUtODc2NjNhZGQ2MzJi",
    "_uetsid": "949ff9d05e3311f0b4e2ed6988db27da",
    "_uetvid": "94a03d305e3311f09147b373d57a9bed"
}
url = "https://dict.youdao.com/webtranslate/key"
params = {
    "keyid": "webfanyi-key-getter-2025",
    "sign": "0678491699224eb15f2a558e5b502c0c",
    "client": "fanyideskweb",
    "product": "webfanyi",
    "appVersion": "1.0.0",
    "vendor": "web",
    "pointParam": "client,mysticTime,product",
    "mysticTime": "1752310290422",
    "keyfrom": "fanyi.web",
    "mid": "1",
    "screen": "1",
    "model": "1",
    "network": "wifi",
    "abtest": "0",
    "yduuid": "abcdefg"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params)

print(response.text)
print(response)