import requests
from lxml import etree
from loguru import logger
from datetime import date
import time
import random
from project_002.Spiders.utils import DB_BASE

class TokenScheduler(DB_BASE):
    def __init__(self):
        self.base_url = 'https://optimistic.etherscan.io'
        self.tokens_url = f'{self.base_url}/tokens'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
        }
        self.seed_key = 'op_token_urls'
        self.crawl_date = date.today()

    def fetch_tokens(self, page_number=1):
        params = {
        "ps": "100",
        "p": str(page_number)
        }
        # logger.info(params)
        response = requests.get(self.tokens_url, headers=self.headers, params=params)
        if response.status_code == 200:
            return response.text
        else:
            raise Exception(f"获取地址失败, status code: {response.status_code}")

    def parse_tokens(self, html_content):
        tree = etree.HTML(html_content)
        token_links = tree.xpath('//*[@id="ContentPlaceHolder1_tblErc20Tokens"]/table/tbody/tr/td[2]/a/@href')
        token_names = tree.xpath('//table/tbody/tr/td/a/div/div/text()')
        if len(token_links) != len(token_names):
            raise ValueError("令牌链接的数量与名称不匹配")
        
        full_urls = [f"{name} ------- {self.base_url}{link}" for link, name in zip(token_links, token_names)]
        return full_urls
    
    def clear_seed_list(self):
        from project_002.Spiders.settings import REDIS_CONN
        REDIS_CONN.delete(self.seed_key)
        logger.info(f"清除种子列表: {self.seed_key}")

    def run(self):
        self.clear_seed_list()
        logger.info("开始调度第一层地址")
        for page_number in range(1, 2):
            logger.info(f"开始调度第:{page_number}页")
            html_content = self.fetch_tokens(page_number)
            token_urls = self.parse_tokens(html_content)
            for url in token_urls:
                logger.info(url)
                self.set_seed(self.seed_key, {'url': url})
            logger.info(f"第{page_number}页调度完成!")
            time.sleep(random.uniform(3, 7))
        logger.info("第一层地址调度完成")

if __name__ == '__main__':
    scheduler = TokenScheduler()
    scheduler.run()