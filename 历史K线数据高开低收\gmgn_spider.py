#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GMGN平台历史K线数据爬虫
功能：通过爬取GMGN网站获取历史K线数据并更新到MySQL数据库（三平台对比表）
"""

import sqlite3
import pymysql
import time
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from curl_cffi import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GMGNSpider:
    def __init__(self):
        # 代理配置
        self.primary_proxy = {
            "http": "http://127.0.0.1:33210",
            "https": "http://127.0.0.1:33210"
        }
        
        self.backup_proxy = {
            "http": "socks5://192.168.224.75:30889",
            "https": "socks5://192.168.224.75:30889"
        }
        
        self.current_proxy = self.primary_proxy
        
        # 请求配置
        self.base_headers = {
            "referer": "https://gmgn.ai/sol/token/"  # 会根据token动态更新
        }
        
        self.cookies = {
            "_ga": "GA1.1.1787124478.1747114971",
            "cf_clearance": "0nqiVkDBoNqyd9JnhZQk5A2dYKplrya_tu3cDOQ8Ys4-1750058521-1.2.1.1-GUVF3TR_MyIH6TllFfLv1ljAkzHT58Xy78zRMGy.SMzJeH2l26spDjnCJvxlzhiR5Q5g1Tc.KojyaabHbjJ5_jJ_P5PZyWqeriXC4Egf.o8gVJVkBckfq31JgSwqAgUyCwicoOeG_BrZ__DPXMNvoGLNMdGqhkMG9oFjBdjibFtwsOU2b_0oFAVhawXwfESOzJFdw_1DkohWEwzlU5supKTdzEmH.kRoDS2UHzApVTEoB_BjSx2BE6mYSDYrHyHI3rsgbdAhkZifFbRGp4ilVVr7vzAbJCCf5uAMg8rjuAFYFGk0FPXn76IW7oi2WoTwCMVQVpYCDbWsmQn6h9jDIkmFsEodZH4DzcgGynofpyI",
            "_ga_0XM0LYXGC8": "GS2.1.s1750058522$o51$g1$t1750058526$j56$l0$h0"
        }
        
        # SQLite数据库配置（读取Token Address）
        self.sqlite_db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        
        # MySQL数据库配置
        self.mysql_config = {
            'host': '**************',
            'port': 33060,
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8',
            'database': 'kline_data'
        }
        
        # 重试配置
        self.max_retries = 5
        self.retry_delay = 3
        self.request_interval = 1.5
        
        # 翻页配置（每次提前4小时）
        self.page_interval_hours = 4
        
    def switch_proxy(self):
        """切换代理"""
        if self.current_proxy == self.primary_proxy:
            self.current_proxy = self.backup_proxy
            logger.info("切换到备用代理")
        else:
            self.current_proxy = self.primary_proxy
            logger.info("切换到主代理")
    
    def get_token_addresses(self) -> List[Dict[str, str]]:
        """从SQLite数据库获取Token Address列表"""
        try:
            conn = sqlite3.connect(self.sqlite_db_path)
            cursor = conn.cursor()
            
            # 查询Token Address和Symbol
            cursor.execute("""
                SELECT "Token Address", "Token symbol" 
                FROM high_low_sol_new_new 
                WHERE "Token Address" IS NOT NULL 
                AND "Token Address" != ''
            """)
            
            results = cursor.fetchall()
            token_list = []
            
            for row in results:
                token_list.append({
                    'address': row[0],
                    'symbol': row[1] if row[1] else 'UNKNOWN'
                })
            
            conn.close()
            logger.info(f"从SQLite数据库获取到 {len(token_list)} 个Token地址")
            return token_list
            
        except Exception as e:
            logger.error(f"获取Token Address失败: {e}")
            return []
    
    def timestamp_to_datetime(self, timestamp: int) -> str:
        """将时间戳转换为标准时间格式"""
        try:
            # GMGN返回的是毫秒时间戳
            timestamp_sec = timestamp / 1000
            dt = datetime.fromtimestamp(timestamp_sec)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.error(f"时间戳转换失败 {timestamp}: {e}")
            return ""
    
    def fetch_kline_data_with_retry(self, token_address: str, from_timestamp: int, to_timestamp: int) -> Optional[List]:
        """带重试机制的获取K线数据"""
        url = f"https://gmgn.ai/api/v1/token_candles/sol/{token_address}"
        
        # 构建请求头（动态更新referer）
        headers = self.base_headers.copy()
        headers["referer"] = f"https://gmgn.ai/sol/token/{token_address}"
        
        # 构建请求参数
        params = {
            "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
            "client_id": "gmgn_web_20250616-2264-9ced500",
            "from_app": "gmgn",
            "app_ver": "20250616-2264-9ced500",
            "tz_name": "Asia/Shanghai",
            "tz_offset": "28800",
            "app_lang": "zh-CN",
            "fp_did": "a0adc6758070914b5d3cd4679349eed1",
            "os": "web",
            "resolution": "1m",
            "from": str(from_timestamp),
            "to": str(to_timestamp),
            "limit": "322",
            "pool_type": "unified"
        }
        
        # 尝试使用当前代理
        for attempt in range(self.max_retries):
            try:
                logger.info(f"Token {token_address} 尝试第 {attempt + 1} 次请求...")
                
                response = requests.get(
                    url, 
                    headers=headers, 
                    cookies=self.cookies, 
                    params=params, 
                    proxies=self.current_proxy, 
                    impersonate="chrome110",
                    timeout=30
                )
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        # 修正：GMGN返回的数据在data.list中，不是data.candles
                        if 'data' in data and 'list' in data['data'] and data.get('code') == 0:
                            raw_list = data['data']['list']
                            
                            # 转换为标准K线格式 [timestamp, open, high, low, close, volume]
                            kline_data = []
                            for item in raw_list:
                                try:
                                    kline_record = [
                                        int(item['time']),        # 时间戳
                                        float(item['open']),      # 开盘价
                                        float(item['high']),      # 最高价  
                                        float(item['low']),       # 最低价
                                        float(item['close']),     # 收盘价
                                        float(item.get('volume', 0))  # 成交量（可选）
                                    ]
                                    kline_data.append(kline_record)
                                except (KeyError, ValueError, TypeError) as e:
                                    logger.warning(f"Token {token_address} 数据项解析失败: {item}, 错误: {e}")
                                    continue
                            
                            logger.info(f"Token {token_address} 获取到 {len(kline_data)} 条K线数据")
                            return kline_data
                        else:
                            logger.warning(f"Token {token_address} 响应格式异常或无数据: code={data.get('code')}, message={data.get('message')}")
                            
                    except json.JSONDecodeError:
                        logger.error(f"Token {token_address} JSON解析失败: {response.text[:200]}")
                        
                else:
                    logger.error(f"Token {token_address} 请求失败，状态码: {response.status_code}")
                
            except Exception as e:
                logger.error(f"Token {token_address} 请求异常 (尝试 {attempt + 1}): {e}")
            
            # 失败后等待重试
            if attempt < self.max_retries - 1:
                logger.info(f"等待 {self.retry_delay} 秒后重试...")
                time.sleep(self.retry_delay)
        
        # 5次重试都失败，切换代理再试一轮
        logger.warning(f"Token {token_address} 使用当前代理5次重试都失败，切换代理重试...")
        self.switch_proxy()
        
        for attempt in range(self.max_retries):
            try:
                logger.info(f"Token {token_address} 使用新代理尝试第 {attempt + 1} 次请求...")
                
                response = requests.get(
                    url, 
                    headers=headers, 
                    cookies=self.cookies, 
                    params=params, 
                    proxies=self.current_proxy, 
                    impersonate="chrome110",
                    timeout=30
                )
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        # 修正：GMGN返回的数据在data.list中，不是data.candles
                        if 'data' in data and 'list' in data['data'] and data.get('code') == 0:
                            raw_list = data['data']['list']
                            
                            # 转换为标准K线格式 [timestamp, open, high, low, close, volume]
                            kline_data = []
                            for item in raw_list:
                                try:
                                    kline_record = [
                                        int(item['time']),        # 时间戳
                                        float(item['open']),      # 开盘价
                                        float(item['high']),      # 最高价  
                                        float(item['low']),       # 最低价
                                        float(item['close']),     # 收盘价
                                        float(item.get('volume', 0))  # 成交量（可选）
                                    ]
                                    kline_data.append(kline_record)
                                except (KeyError, ValueError, TypeError) as e:
                                    logger.warning(f"Token {token_address} 数据项解析失败: {item}, 错误: {e}")
                                    continue
                            
                            logger.info(f"Token {token_address} 使用新代理获取到 {len(kline_data)} 条K线数据")
                            return kline_data
                        else:
                            logger.warning(f"Token {token_address} 使用新代理响应格式异常或无数据: code={data.get('code')}, message={data.get('message')}")
                            
                    except json.JSONDecodeError:
                        logger.error(f"Token {token_address} 使用新代理JSON解析失败: {response.text[:200]}")
                        
                else:
                    logger.error(f"Token {token_address} 使用新代理请求失败，状态码: {response.status_code}")
                
            except Exception as e:
                logger.error(f"Token {token_address} 使用新代理请求异常 (尝试 {attempt + 1}): {e}")
            
            # 失败后等待重试
            if attempt < self.max_retries - 1:
                logger.info(f"等待 {self.retry_delay} 秒后重试...")
                time.sleep(self.retry_delay)
        
        logger.error(f"Token {token_address} 所有重试都失败，跳过该Token")
        return None
    
    def save_gmgn_kline_data(self, token_address: str, token_symbol: str, kline_data: List):
        """保存GMGN平台K线数据到三平台对比表（仅更新已存在的时间点）"""
        try:
            connection = pymysql.connect(**self.mysql_config)
            cursor = connection.cursor()
            
            successful_updates = 0
            skipped_count = 0
            
            for candle in kline_data:
                if len(candle) >= 5:
                    timestamp = candle[0]  # 时间戳
                    open_price = float(candle[1])   # 开盘价
                    high_price = float(candle[2])   # 最高价
                    low_price = float(candle[3])    # 最低价
                    close_price = float(candle[4])  # 收盘价
                    
                    # 转换时间戳
                    datetime_str = self.timestamp_to_datetime(timestamp)
                    
                    if not datetime_str:
                        continue
                    
                    # 检查是否已存在该时间戳的记录（且有HA数据）
                    check_sql = """
                    SELECT id FROM kline_comparison_sol_new_new 
                    WHERE token_address = %s AND timestamp = %s AND ha_open IS NOT NULL
                    """
                    cursor.execute(check_sql, (token_address, timestamp))
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                        # 更新现有记录的GMGN数据
                        update_sql = """
                        UPDATE kline_comparison_sol_new_new SET 
                            gmgn_open = %s, gmgn_high = %s, gmgn_low = %s, gmgn_close = %s, gmgn_time = %s
                        WHERE token_address = %s AND timestamp = %s
                        """
                        cursor.execute(update_sql, (
                            open_price, high_price, low_price, close_price, datetime_str,
                            token_address, timestamp
                        ))
                        successful_updates += 1
                    else:
                        # 跳过没有HA数据的时间点
                        skipped_count += 1
            
            connection.commit()
            logger.info(f"Token {token_address} GMGN数据 - 更新: {successful_updates}条, 跳过: {skipped_count}条")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"Token {token_address} 保存GMGN数据失败: {e}")
    
    def process_token_with_time_pagination(self, token_address: str, token_symbol: str):
        """通过时间戳分页处理单个Token的数据获取"""
        try:
            # 获取当前时间戳（毫秒）
            current_time = datetime.now()
            to_timestamp = int(current_time.timestamp() * 1000)
            
            # 获取历史数据（从当前时间往前推24小时开始）
            total_hours = 24  # 获取24小时的历史数据
            page_count = 0
            max_pages = total_hours // self.page_interval_hours + 1
            
            logger.info(f"Token {token_address} 开始获取历史数据，预计需要 {max_pages} 页")
            
            for page in range(max_pages):
                page_count += 1
                
                # 计算本页的时间范围
                from_time = current_time - timedelta(hours=(page + 1) * self.page_interval_hours)
                to_time = current_time - timedelta(hours=page * self.page_interval_hours)
                
                from_timestamp = int(from_time.timestamp() * 1000)
                to_timestamp = int(to_time.timestamp() * 1000)
                
                logger.info(f"Token {token_address} 获取第 {page_count} 页数据 ({from_time.strftime('%Y-%m-%d %H:%M')} ~ {to_time.strftime('%Y-%m-%d %H:%M')})")
                
                # 获取K线数据
                kline_data = self.fetch_kline_data_with_retry(token_address, from_timestamp, to_timestamp)
                
                if kline_data:
                    # 保存数据
                    self.save_gmgn_kline_data(token_address, token_symbol, kline_data)
                else:
                    logger.warning(f"Token {token_address} 第 {page_count} 页未获取到数据")
                
                # 页面请求间隔
                if page < max_pages - 1:
                    time.sleep(1)  # 页面间较短间隔
                
        except Exception as e:
            logger.error(f"Token {token_address} 时间分页处理异常: {e}")
    
    def run(self):
        """运行GMGN平台爬虫主程序"""
        logger.info("开始运行GMGN平台K线数据爬虫（三平台对比模式）")
        
        try:
            # 1. 获取Token Address列表
            logger.info("获取Token Address列表...")
            token_list = self.get_token_addresses()
            
            if not token_list:
                logger.error("未获取到Token Address列表，程序结束")
                return
            
            # 2. 依次处理每个Token
            total_tokens = len(token_list)
            for i, token_info in enumerate(token_list, 1):
                token_address = token_info['address']
                token_symbol = token_info['symbol']
                
                logger.info(f"[{i}/{total_tokens}] 处理Token: {token_symbol} ({token_address})")
                
                # 通过时间分页获取并处理数据
                self.process_token_with_time_pagination(token_address, token_symbol)
                
                # 请求间隔
                if i < total_tokens:  # 最后一个请求不需要等待
                    logger.info(f"等待 {self.request_interval} 秒...")
                    time.sleep(self.request_interval)
            
            logger.info("GMGN平台K线数据爬虫运行完成")
            
        except Exception as e:
            logger.error(f"爬虫运行失败: {e}")
            raise

def main():
    """主函数"""
    spider = GMGNSpider()
    spider.run()

if __name__ == "__main__":
    main()