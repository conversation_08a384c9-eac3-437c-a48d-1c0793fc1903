import curl_cffi.requests

from settings import *
from Spiders.Spider import Spider_Crawler_Request
from loguru import logger
from curl_cffi import requests
import time
from datetime import datetime

class Currency_proportion(Spider_Crawler_Request):

    def __init__(self):
        super().__init__()
        self.crawl_date = datetime.now()

    def parse(self):
        proxy_vpn = {
            'http': "socks5://192.168.224.75:30889",
            'https': "socks5://192.168.224.75:30889"
        }

        headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        }

        params = {
            "duration": "1",
            "locale": "zh"
        }
        url = "https://www.coingecko.com/global_charts/market_dominance_data"

        response = requests.get(url, params=params, proxies=proxy_vpn, impersonate='chrome110')
        if response.status_code == 200:
            logger.info(f'请求成功，状态码{response.status_code}')
            data = response.json()
            for item in data:
                name = item['name']
                last_data = item['data'][-1]
                timestamp = last_data[0] / 1000
                proportion = last_data[1]
                the_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
                formatted_crawl_date = self.crawl_date.strftime('%Y-%m-%d %H:%M:%S')

                sql_create_table = """
                CREATE TABLE IF NOT EXISTS currency_proportion (
                    name VARCHAR(255) PRIMARY KEY,
                    the_time DATETIME,
                    proportion FLOAT,
                    crawl_date DATETIME
                )
                """
                self.insert_mysql(sql_create_table)

                sql_replace = """
                REPLACE INTO currency_proportion (name, the_time, proportion, crawl_date)
                VALUES (%s, %s, %s, %s)
                """
                v_list = [name, the_time, proportion, formatted_crawl_date]
                self.insert_mysql(sql=sql_replace, v_list=v_list)
                logger.info(f'数据库更新: 货币 {name}, 时间 {the_time}, 占比 {proportion}%, 爬取日期 {formatted_crawl_date}')
        else:
            logger.error(f'请求失败，状态码:{response.status_code}')

if __name__ == '__main__':
    Currency_proportion().parse()