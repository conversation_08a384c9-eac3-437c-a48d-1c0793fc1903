import sys
import time
import random
import requests
# from lxml import etree
from common import redis_sentry_connection, CommonFun
from loguru import logger
from datetime import date
import redis
import json


def conn_redis(host="**************", port=6379, password='123456', db=9):
    while True:
        try:
            r = redis.Redis(host=host, port=port, password=password, db=db, decode_responses=True,
                            socket_timeout=120, socket_connect_timeout=10, retry_on_timeout=True)
            return r
        except Exception as e:
            logger.error(repr(e))
            logger.info('等待重新连接数据库....')
            time.sleep(5)


def del_expire_hash(address: str, chain_name):
    """ 删除过期记录"""
    ABI_EXPIRE_TIME_FORMAT = 'abi:%s_expire_time'  # 过期时间
    expire_hash_name = ABI_EXPIRE_TIME_FORMAT % chain_name
    r9 = conn_redis()
    r9.hdel(expire_hash_name, address)
    r9.close()


class CoreSpider:
    def __init__(self):
        self.chain_name = 'Core'
        self.redis_master = redis_sentry_connection()
        self.abi_addr = f'spider:abi:{self.chain_name}'
        self.headers = {
            "content-type": "application/json;charset=UTF-8",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.common_fun = CommonFun()
        self.crawl_date = date.today()

    def run(self):
        self.get_address()

    def get_address(self):
        redis_master, _ = redis_sentry_connection()
        chain_name = self.chain_name
        abi_addr = f'spider:abi:{chain_name}'
        addresses = []

        while True:
            address = redis_master.spop(abi_addr)
            if address:
                is_expire = self.common_fun.judge_expire(address, self.chain_name)
                if is_expire:
                    logger.info(f"获取到地址: {address}")
                    self.parse_data(address)
            else:
                logger.info("没有从数据库中找到address")
                break

    
    def pass_5s_cookie(self):
        # 返回一个cf_clearance
        url_get_cf = 'https://scan.coredao.org/tokens-nft'
        response = requests.get()
        pass

    def parse_data(self, address):
        url = "https://scan.coredao.org/api/chain/abi"
        cookies = {}
        data_addr = {
            "contractAddress": f"{address}"
        }
        data = json.dumps(data_addr, separators=(',', ':'))
        time.sleep(1.2 + random.random())
        try:
            response = requests.post(url, headers=self.headers, data=data)

            if response is None or response.status_code != 200:
                logger.error(f'获取失败： {address}, status code: {response.status_code}')
                return

            response_data = response.json()
            contract_abi = response_data['data']['abi']
            contract_name = response_data['data']['contractName']
            contract_address = response_data['data']['contractAddress']
            logger.info(f"ABI: {contract_abi}")
            logger.info(f'contractName: {contract_name}')
            logger.info(f"Contract Address: 0x{contract_address}")

            proxy_contract = response_data['data']['logicAddress']
            if proxy_contract:
                logger.info(f'存在代理合约: {proxy_contract}')
            else:
                logger.info('没有代理合约')

        except requests.exceptions.RequestException as e:
            logger.error(f'请求异常：{e}')
        # proxy_contract = proxy_contract.strip().lower()  # 是代理合约

        if contract_abi:
            logger.info(f'获取到数据:{address}: {self.chain_name}: {contract_name}: {contract_abi}')
            item = {
                'ADDRESS': address,
                'CHAIN_NAME': self.chain_name,
                'CONTRACT_NAME': contract_name,
                'CONTRACT_ABI': contract_abi
            }

            # 如果是代理合约，则把代理合约的abi也拿到
            if proxy_contract:
                item['IMP_CONTRACT'] = proxy_contract
                self.common_fun.save_to_phoenix(item)

                existed = self.common_fun.check_exist(proxy_contract, self.chain_name)
                if not existed:  # 没有的话则爬
                    self.parse_data(proxy_contract)
            else:
                self.common_fun.save_to_phoenix(item)

            del_expire_hash(address, self.chain_name)

        else:
            logger.info('no abi')
            self.common_fun.set_expire_hash(address, self.chain_name)

    def judge_expire(self, address: str, chain_name):
        ABI_EXPIRE_TIME_FORMAT = 'abi:%s_expire_time'  # 过期时间
        """ 同一个地址一个月内不再重复爬取 """
        expire_hash_name = ABI_EXPIRE_TIME_FORMAT % chain_name
        expire_time = self.common_fun.get_hash(hash_name=expire_hash_name, hash_key=address)
        logger.info(f'chain_name: {chain_name}, address: {address}, expire_time: {expire_time}')

        if expire_time is None:
            return True
        else:
            time_differ = int(time.time()) - int(expire_time)

            if time_differ < 2592000:  # 一个月之内不再重复爬
                return False
            else:
                return True

if __name__ == '__main__':
    spider = CoreSpider()
    spider.run()
