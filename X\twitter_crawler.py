
# import requests
# import pandas as pd
# import json
# from loguru import logger
# import time
# import random
# from datetime import datetime
# import os

# def fetch_data(keyword, cursor=None):
#     url = "https://x.com/i/api/graphql/QGMTWxm841rbDndB-yQhIw/SearchTimeline"

#     headers = {
#         "authority": "x.com",
#         "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
#         "referer": f"https://x.com/search?q={keyword}&src=typed_query",
#         "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
#         "x-client-transaction-id": "IOV9MB+knAGGcPhTt2KvbXLbwgyPUQUcQIYMFmdDwM7w7+POsGGqT3Drg4/4/+9jdmxJFiPPabhKNvXmaFQy5fh/2Fa4Iw",
#         "x-client-uuid": "809db5e0-ea17-4d61-8eb3-b162d3a607da",
#         "x-csrf-token": "a2238fc4a13508a2b50852d4d19f159985aa48a5bca4b14eaaa7c4d5ab67fd2c511b4276f8fd400e4c7a0da317d7157dd7a1530b3b13f9f8091be5c04d3c6fa3bf042a63f04b0c1d8e815dc38b29d93e",
#     }

#     cookies = {
#         "guest_id": "v1%3A173674625403880007",
#         "auth_token": "a2eb1f5247dbaa8880c5d713328f8935755a30a7",
#         "ct0": "a2238fc4a13508a2b50852d4d19f159985aa48a5bca4b14eaaa7c4d5ab67fd2c511b4276f8fd400e4c7a0da317d7157dd7a1530b3b13f9f8091be5c04d3c6fa3bf042a63f04b0c1d8e815dc38b29d93e",
#     }

#     variables = {
#         "rawQuery": keyword,
#         "count": 200,
#         "querySource": "typed_query",
#         "product": "Top"
#     }
#     if cursor:
#         variables["cursor"] = cursor

#     params = {
#         "variables": json.dumps(variables),
#         "features": json.dumps({
#             "profile_label_improvements_pcf_label_in_post_enabled": True,
#             "rweb_tipjar_consumption_enabled": True,
#             "responsive_web_graphql_exclude_directive_enabled": True,
#             "verified_phone_label_enabled": False,
#             "creator_subscriptions_tweet_preview_api_enabled": True,
#             "responsive_web_graphql_timeline_navigation_enabled": True,
#             "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
#             "premium_content_api_read_enabled": False,
#             "communities_web_enable_tweet_community_results_fetch": True,
#             "c9s_tweet_anatomy_moderator_badge_enabled": True,
#             "responsive_web_grok_analyze_button_fetch_trends_enabled": False,
#             "responsive_web_grok_analyze_post_followups_enabled": True,
#             "responsive_web_grok_share_attachment_enabled": True,
#             "articles_preview_enabled": True,
#             "responsive_web_edit_tweet_api_enabled": True,
#             "graphql_is_translatable_rweb_tweet_is_translatable_enabled": True,
#             "view_counts_everywhere_api_enabled": True,
#             "longform_notetweets_consumption_enabled": True,
#             "responsive_web_twitter_article_tweet_consumption_enabled": True,
#             "tweet_awards_web_tipping_enabled": False,
#             "creator_subscriptions_quote_tweet_preview_enabled": False,
#             "freedom_of_speech_not_reach_fetch_enabled": True,
#             "standardized_nudges_misinfo": True,
#             "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": True,
#             "rweb_video_timestamps_enabled": True,
#             "longform_notetweets_rich_text_read_enabled": True,
#             "longform_notetweets_inline_media_enabled": True,
#             "responsive_web_enhance_cards_enabled": False
#         })
#     }

#     try:
#         response = requests.get(url, headers=headers, cookies=cookies, params=params)
#         response.raise_for_status()
#         logger.success(f'请求成功，状态码: {response.status_code}')
#         return response.json()
#     except requests.exceptions.RequestException as e:
#         logger.error(f"请求失败: {str(e)}")
#         return None

# def convert_twitter_time(twitter_time):
#     try:
#         dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
#         return dt.strftime('%Y-%m-%d %H:%M:%S')
#     except Exception as e:
#         logger.warning(f"时间格式转换失败: {str(e)}")
#         return twitter_time

# def extract_entries(data):
#     try:
#         entries = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [{}])[0].get('entries', [])
#         extracted_data = []

#         for entry in entries[:22]:
#             content = entry.get('content', {}).get('itemContent', {}).get('tweet_results', {}).get('result', {})
#             user_result = content.get('core', {}).get('user_results', {}).get('result', {})
#             legacy = user_result.get('legacy', {})
            
#             # 转换时间格式
#             raw_time = legacy.get('created_at', '')
#             created_at = convert_twitter_time(raw_time) if raw_time else ''

#             extracted_data.append({
#                 'is_blue_verified': user_result.get('is_blue_verified', False),
#                 'created_at': created_at,
#                 'description': legacy.get('description', ''),
#                 'default_profile_image': legacy.get('default_profile_image', False),
#                 'followers_count': legacy.get('followers_count', 0),
#                 'location': legacy.get('location', ''),
#                 'name': legacy.get('name', ''),
#                 'media_url_https': content.get('legacy', {}).get('entities', {}).get('media', [{}])[0].get('media_url_https', ''),
#                 'thumb_url': content.get('legacy', {}).get('entities', {}).get('media', [{}])[0].get('sizes', {}).get('thumb', {}).get('url', ''),
#                 'full_text': content.get('legacy', {}).get('full_text', '')
#             })
#         return extracted_data
#     except Exception as e:
#         logger.error(f"数据解析失败: {str(e)}")
#         return []

# def get_next_cursor(data):
#     try:
#         instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [])
        
#         for instruction in instructions:
#             # 处理两种指令类型
#             if instruction.get('type') in ['TimelineAddEntries', 'TimelineReplaceEntry']:
#                 entries = instruction.get('entries', [])
#                 for entry in entries:
#                     # 优先检查cursor-bottom类型
#                     if entry.get('entryId') == 'cursor-bottom-0':
#                         return entry.get('content', {}).get('value')
#                     # 兼容其他可能的游标格式
#                     elif entry.get('content', {}).get('cursorType') == 'Bottom':
#                         return entry.get('content', {}).get('value')
#         return None
#     except Exception as e:
#         logger.error(f"获取下一页游标失败: {str(e)}")
#         return None
    
# def save_to_csv(data, file_name):
#     if not data:
#         logger.warning("没有数据需要保存")
#         return
#     try:
#         df = pd.DataFrame(data)
#         df.drop_duplicates(inplace=True)
        
#         # 添加header参数控制列名写入
#         header = not os.path.exists(file_name)
#         df.to_csv(file_name, mode='a', index=False, 
#                  encoding='utf-8-sig', header=header)
#         logger.success(f"成功保存到: {file_name} (追加模式)")
#     except Exception as e:
#         logger.error(f"保存文件失败: {str(e)}")

# def main():
#     with open('X/x_keyword/keywords.txt', 'r', encoding='utf-8') as file: 
#         keywords = [k.strip() for k in file.read().splitlines() if k.strip()]

#     for keyword in keywords:
#         next_cursor = None
#         page = 1
#         max_retry = 3  # 添加重试机制
        
#         while True:
#             logger.info(f"正在爬取 [{keyword}] 第 {page} 页...")
            
#             # 带重试的请求逻辑
#             data = None
#             for attempt in range(max_retry):
#                 data = fetch_data(keyword, next_cursor)
#                 if data:
#                     break
#                 logger.warning(f"第{attempt+1}次请求失败，5秒后重试...")
#                 time.sleep(5)
                
#             if not data:
#                 logger.error("连续请求失败，停止翻页")
#                 break
                
#             extracted_data = extract_entries(data)
#             if not extracted_data:
#                 logger.info("没有提取到有效数据，停止翻页")
#                 break
                
#             # 保存数据（自动追加）
#             save_to_csv(extracted_data, f'X/x_output/{keyword}.csv')
            
#             # 获取下一页游标（增强解析逻辑）
#             new_cursor = get_next_cursor(data)
#             if not new_cursor:
#                 logger.info("已到达最后一页")
#                 break
                
#             # 更新游标前检查是否相同
#             if new_cursor == next_cursor:
#                 logger.warning("检测到相同游标，可能到达页尾")
#                 break
#             next_cursor = new_cursor
            
#             # 动态等待时间（5-7秒）
#             wait_time = 5 + random.uniform(0, 2)
#             logger.info(f"等待 {wait_time:.2f} 秒后翻页")
#             time.sleep(wait_time)
#             page += 1

#         # 关键词间隔等待（5.6-6.6秒）
#         wait_time = 5.6 + random.random()
#         logger.info(f"关键词 [{keyword}] 完成，等待 {wait_time:.2f} 秒")
#         time.sleep(wait_time)

# if __name__ == "__main__":
#     main()


import requests
import pandas as pd
import json
import os
import time
import random
from loguru import logger
from datetime import datetime

class XSpider:
    def __init__(self):
        self.headers = {
            "authority": "x.com",
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "referer": "",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-client-transaction-id": "QzuxyNdmWG4Ecp8MrbB1F8ChCQpRhRVfpUOGgTf5f1ob9+4ZzHKFbFPiEz3RiP9xSyEzR0f5DxAhZ6CzYKJmLY4uRrXUQA",
            "x-client-uuid": "809db5e0-ea17-4d61-8eb3-b162d3a607da",
            "x-csrf-token": "a2238fc4a13508a2b50852d4d19f159985aa48a5bca4b14eaaa7c4d5ab67fd2c511b4276f8fd400e4c7a0da317d7157dd7a1530b3b13f9f8091be5c04d3c6fa3bf042a63f04b0c1d8e815dc38b29d93e",
        }
        self.cookies = {
            "guest_id": "v1%3A173674625403880007",
            "auth_token": "a2eb1f5247dbaa8880c5d713328f8935755a30a7",
            "ct0": "a2238fc4a13508a2b50852d4d19f159985aa48a5bca4b14eaaa7c4d5ab67fd2c511b4276f8fd400e4c7a0da317d7157dd7a1530b3b13f9f8091be5c04d3c6fa3bf042a63f04b0c1d8e815dc38b29d93e",
        }
        self.base_params = {
            "features": json.dumps({
                "profile_label_improvements_pcf_label_in_post_enabled": True,
                "rweb_tipjar_consumption_enabled": True,
                "responsive_web_graphql_exclude_directive_enabled": True,
                "verified_phone_label_enabled": False,
                "creator_subscriptions_tweet_preview_api_enabled": True,
                "responsive_web_graphql_timeline_navigation_enabled": True,
                "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
                "premium_content_api_read_enabled": False,
                "communities_web_enable_tweet_community_results_fetch": True,
                "c9s_tweet_anatomy_moderator_badge_enabled": True,
                "responsive_web_grok_analyze_button_fetch_trends_enabled": False,
                "responsive_web_grok_analyze_post_followups_enabled": True,
                "responsive_web_grok_share_attachment_enabled": True,
                "articles_preview_enabled": True,
                "responsive_web_edit_tweet_api_enabled": True,
                "graphql_is_translatable_rweb_tweet_is_translatable_enabled": True,
                "view_counts_everywhere_api_enabled": True,
                "longform_notetweets_consumption_enabled": True,
                "responsive_web_twitter_article_tweet_consumption_enabled": True,
                "tweet_awards_web_tipping_enabled": False,
                "creator_subscriptions_quote_tweet_preview_enabled": False,
                "freedom_of_speech_not_reach_fetch_enabled": True,
                "standardized_nudges_misinfo": True,
                "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": True,
                "rweb_video_timestamps_enabled": True,
                "longform_notetweets_rich_text_read_enabled": True,
                "longform_notetweets_inline_media_enabled": True,
                "responsive_web_enhance_cards_enabled": False
            })
        }

    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间格式到标准格式"""
        try:
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}")
            return None

    def fetch_data(self, keyword, cursor=None):
        """发送请求获取数据"""
        url = "https://x.com/i/api/graphql/QGMTWxm841rbDndB-yQhIw/SearchTimeline"
        variables = {
            "rawQuery": keyword,
            "count": 200,
            "querySource": "typed_query",
            "product": "Top"
        }
        if cursor:
            variables["cursor"] = cursor

        params = self.base_params.copy()
        params["variables"] = json.dumps(variables)
        self.headers["referer"] = f"https://x.com/search?q={keyword}&src=typed_query"

        try:
            response = requests.get(
                url,
                headers=self.headers,
                cookies=self.cookies,
                params=params
            )
            response.raise_for_status()
            logger.success(f"请求成功 [{response.status_code}]")
            return response.json()
        except Exception as e:
            logger.error(f"请求失败: {e}")
            return None

    def extract_entries(self, data):
        """从响应数据中提取目标字段"""
        extracted = []
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [])
            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    for entry in instruction.get('entries', []):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineItem':
                            item_content = content.get('itemContent', {})
                            tweet_result = item_content.get('tweet_results', {}).get('result', {})
                            user_result = tweet_result.get('core', {}).get('user_results', {}).get('result', {})
                            legacy_user = user_result.get('legacy', {})
                            legacy_tweet = tweet_result.get('legacy', {})

                            # 处理媒体数据
                            media = legacy_tweet.get('entities', {}).get('media', [{}])
                            media_info = media[0] if media else {}

                            # 时间转换
                            created_at = self.convert_twitter_time(legacy_user.get('created_at'))

                            extracted.append({
                                'name': legacy_user.get('name'),
                                'rest_id': user_result.get('rest_id'),
                                'is_blue_verified': user_result.get('is_blue_verified', False),
                                'verified_type': user_result.get('verified_type'),
                                'normal_followers_count': legacy_user.get('normal_followers_count', 0),
                                'created_at': created_at,
                                'description': legacy_user.get('description'),
                                'default_profile_image': legacy_user.get('default_profile_image', False),
                                'followers_count': legacy_user.get('followers_count', 0),
                                'media_url_https': media_info.get('media_url_https')
                            })
            return extracted
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return []

    def get_next_cursor(self, data):
        """获取下一页游标"""
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [])
            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    for entry in instruction.get('entries', []):
                        if entry.get('entryId') == 'cursor-bottom-0':
                            return entry.get('content', {}).get('value')
            return None
        except Exception as e:
            logger.error(f"游标解析失败: {e}")
            return None

    def save_to_csv(self, data, filename):
        """保存数据到CSV文件"""
        if not data:
            return

        try:
            df = pd.DataFrame(data)
            df.replace({pd.NA: None}, inplace=True)
            
            # 处理文件存在性
            file_exists = os.path.exists(filename)
            df.to_csv(
                filename,
                mode='a' if file_exists else 'w',
                header=not file_exists,
                index=False,
                encoding='utf-8-sig'
            )
            logger.success(f"成功保存 {len(data)} 条数据到 {filename}")
        except Exception as e:
            logger.error(f"保存失败: {e}")

    def run(self):
        """主运行方法"""
        with open('X/x_keyword/keywords.txt', 'r', encoding='utf-8') as f:
            keywords = [k.strip() for k in f if k.strip()]

        for keyword in keywords:
            logger.info(f"开始处理关键词: {keyword}")
            next_cursor = None
            page = 1
            max_retry = 3

            while True:
                logger.info(f"正在爬取第 {page} 页...")
                data = None
                
                # 带重试的请求
                for attempt in range(max_retry):
                    data = self.fetch_data(keyword, next_cursor)
                    if data:
                        break
                    logger.warning(f"第 {attempt+1} 次重试...")
                    time.sleep(5)

                if not data:
                    logger.error("请求失败，停止翻页")
                    break

                # 数据提取和保存
                extracted = self.extract_entries(data)
                if extracted:
                    self.save_to_csv(extracted, f'X/x_output/{keyword}.csv')
                else:
                    logger.warning("未提取到有效数据")
                
                # 获取下一页游标
                new_cursor = self.get_next_cursor(data)
                if not new_cursor or new_cursor == next_cursor:
                    logger.info("已到达最后一页")
                    break
                
                next_cursor = new_cursor
                page += 1
                
                # 随机等待
                wait_time = random.uniform(5, 7)
                logger.info(f"等待 {wait_time:.1f} 秒后继续")
                time.sleep(wait_time)

            logger.success(f"完成关键词 {keyword} 的采集")

if __name__ == "__main__":
    spider = XSpider()
    spider.run()

'''
拿取推特评论请求
1、先发送一个大请求之后
        提取这个请求内的所有文章详细链接地址
            开始发送详情页的请求，拿到评论数据

   缺点：大请求等待时间长，请求过多会被封
'''

'''
思路二 
    大请求的时候把详情页的链接保存下来
        另外写一个请求,依次读取详情页的链接 发送请求后获取评论再进行翻页操作
        重写的代码大体和主爬虫的结构一致
        分开爬取减少整个代码的复杂度 也减少了大请求的等待时间

    优点： 减少了大请求的等待时间
          两个请求模块分开 便于管理
          数据库中设置主键外键关联即可
'''