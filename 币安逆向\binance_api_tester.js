/**
 * 币安API测试器 - 实战版本
 * 使用生成的AWS WAF Token进行真实API测试
 * 
 * 使用方法：
 * 1. 在币安网站（https://www.binance.com）打开控制台
 * 2. 复制粘贴本脚本
 * 3. 运行：await startBinanceAPITest()
 */

class BinanceAPITester {
    constructor() {
        this.baseUrl = 'https://www.binance.com';
        this.apiUrl = 'https://api.binance.com';
        this.generator = null;
    }
    
    // 🎯 初始化Token生成器
    async initGenerator() {
        if (typeof AWSWAFTokenGenerator === 'undefined') {
            console.log('❌ 未找到AWSWAFTokenGenerator，请先运行Token生成器代码');
            return false;
        }
        
        this.generator = new AWSWAFTokenGenerator();
        console.log('✅ Token生成器初始化成功');
        return true;
    }
    
    // 🔧 构造标准请求头
    buildHeaders(awsWafToken, cookies, deviceInfo = null) {
        const headers = {
            'x-aws-waf-token': awsWafToken,
            'Cookie': cookies,
            'User-Agent': navigator.userAgent,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': navigator.language + ',en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.binance.com/',
            'Origin': 'https://www.binance.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };
        
        // 添加设备信息
        if (deviceInfo) {
            headers['device-info'] = deviceInfo;
        }
        
        // 添加BNC-UUID（从Cookie中提取）
        const bncUuid = this.extractCookie(cookies, 'bnc-uuid');
        if (bncUuid) {
            headers['BNC-UUID'] = bncUuid;
        }
        
        return headers;
    }
    
    // 🍪 提取特定Cookie值
    extractCookie(cookieString, name) {
        const value = `; ${cookieString}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }
    
    // 📱 获取设备信息
    getDeviceInfo() {
        if (this.generator) {
            const fingerprint = this.generator.generateDeviceFingerprint();
            return fingerprint.encoded;
        }
        return null;
    }
    
    // 🧪 测试单个API端点
    async testAPI(endpoint, token, cookies, method = 'GET', body = null) {
        try {
            console.log(`\n🧪 测试端点: ${endpoint}`);
            
            const deviceInfo = this.getDeviceInfo();
            const headers = this.buildHeaders(token, cookies, deviceInfo);
            
            // 显示关键请求头
            console.log(`🔑 Token: ${token.substring(0, 30)}...`);
            console.log(`🍪 Cookie数量: ${cookies.split(';').length} 个`);
            if (deviceInfo) {
                console.log(`📱 设备信息: ${deviceInfo.substring(0, 50)}...`);
            }
            
            const options = {
                method: method,
                headers: headers,
                mode: 'cors',
                credentials: 'omit' // 不包含Cookie，使用header中的
            };
            
            if (body && method !== 'GET') {
                options.body = JSON.stringify(body);
            }
            
            const startTime = performance.now();
            const response = await fetch(endpoint, options);
            const endTime = performance.now();
            
            console.log(`📡 响应状态: ${response.status} ${response.statusText}`);
            console.log(`⏱️ 响应时间: ${(endTime - startTime).toFixed(2)}ms`);
            
            // 检查响应头
            const responseHeaders = {};
            for (let [key, value] of response.headers.entries()) {
                responseHeaders[key] = value;
            }
            console.log(`📋 响应头数量: ${Object.keys(responseHeaders).length} 个`);
            
            if (response.ok) {
                let data;
                try {
                    data = await response.json();
                    console.log(`✅ 请求成功!`);
                    
                    // 显示响应数据摘要
                    if (Array.isArray(data)) {
                        console.log(`📄 响应数据: 数组 (${data.length} 项)`);
                    } else if (typeof data === 'object') {
                        const keys = Object.keys(data);
                        console.log(`📄 响应数据: 对象 (${keys.length} 个字段: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''})`);
                    } else {
                        console.log(`📄 响应数据:`, data);
                    }
                    
                    return { success: true, data: data, status: response.status, timeMs: endTime - startTime };
                } catch (parseError) {
                    const text = await response.text();
                    console.log(`⚠️ JSON解析失败，返回文本:`, text.substring(0, 200));
                    return { success: true, data: text, status: response.status, timeMs: endTime - startTime };
                }
            } else {
                let errorText;
                try {
                    errorText = await response.text();
                } catch (e) {
                    errorText = '无法读取错误信息';
                }
                
                console.log(`❌ 请求失败: ${response.status}`);
                console.log(`📄 错误信息:`, errorText.substring(0, 300));
                
                // 特殊错误处理
                if (response.status === 405) {
                    console.log(`💡 提示: 405错误可能意味着需要重新生成Token`);
                } else if (response.status === 403) {
                    console.log(`💡 提示: 403错误可能是权限问题或Token无效`);
                } else if (response.status === 429) {
                    console.log(`💡 提示: 429错误表示请求过于频繁，请降低频率`);
                }
                
                return { success: false, error: errorText, status: response.status, timeMs: endTime - startTime };
            }
            
        } catch (error) {
            console.log(`❌ 网络错误:`, error.message);
            return { success: false, error: error.message, status: 0, timeMs: 0 };
        }
    }
    
    // 🎯 运行完整测试套件
    async runTestSuite(token, cookies) {
        console.log('🚀 开始币安API测试套件...');
        console.log('='.repeat(60));
        console.log(`🔑 Token: ${token.substring(0, 50)}...`);
        console.log(`🍪 Cookie: ${cookies.substring(0, 100)}...`);
        console.log('='.repeat(60));
        
        const testEndpoints = [
            {
                name: '基础Ping',
                url: 'https://api.binance.com/api/v3/ping',
                description: '测试API基础连通性',
                priority: 'high'
            },
            {
                name: '服务器时间',
                url: 'https://api.binance.com/api/v3/time',
                description: '获取服务器时间',
                priority: 'high'
            },
            {
                name: 'BTC价格',
                url: 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT',
                description: '获取BTC当前价格',
                priority: 'medium'
            },
            {
                name: 'BTC 24h统计',
                url: 'https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT',
                description: '获取BTC 24小时统计',
                priority: 'medium'
            },
            {
                name: '订单簿',
                url: 'https://api.binance.com/api/v3/depth?symbol=BTCUSDT&limit=5',
                description: '获取BTC/USDT订单簿（5档）',
                priority: 'low'
            },
            {
                name: '交易对信息',
                url: 'https://api.binance.com/api/v3/exchangeInfo?symbol=BTCUSDT',
                description: '获取BTC/USDT交易对信息',
                priority: 'low'
            }
        ];
        
        const results = [];
        let successCount = 0;
        
        for (const [index, endpoint] of testEndpoints.entries()) {
            console.log(`\n📍 [${index + 1}/${testEndpoints.length}] ${endpoint.name}`);
            console.log(`📝 ${endpoint.description}`);
            
            const result = await this.testAPI(endpoint.url, token, cookies);
            
            results.push({
                name: endpoint.name,
                url: endpoint.url,
                priority: endpoint.priority,
                ...result
            });
            
            if (result.success) {
                successCount++;
            }
            
            // 添加延迟避免频率限制
            if (index < testEndpoints.length - 1) {
                console.log(`⏳ 等待1秒避免频率限制...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // 📊 输出详细测试结果
        console.log('\n' + '='.repeat(60));
        console.log('📊 测试结果详细报告');
        console.log('='.repeat(60));
        
        results.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            const priority = result.priority === 'high' ? '🔥' : result.priority === 'medium' ? '🟡' : '🔵';
            console.log(`${index + 1}. ${status} ${priority} ${result.name}`);
            console.log(`   状态: ${result.status} | 耗时: ${result.timeMs.toFixed(2)}ms`);
            console.log(`   URL: ${result.url}`);
            if (!result.success && result.error) {
                console.log(`   错误: ${result.error.substring(0, 100)}...`);
            }
            console.log('');
        });
        
        // 📈 统计信息
        const highPriorityTests = results.filter(r => r.priority === 'high');
        const highPrioritySuccess = highPriorityTests.filter(r => r.success).length;
        
        console.log('='.repeat(60));
        console.log('📈 统计摘要:');
        console.log(`总体成功率: ${successCount}/${results.length} (${(successCount/results.length*100).toFixed(1)}%)`);
        console.log(`核心API成功率: ${highPrioritySuccess}/${highPriorityTests.length} (${(highPrioritySuccess/highPriorityTests.length*100).toFixed(1)}%)`);
        console.log(`平均响应时间: ${(results.filter(r => r.success).reduce((sum, r) => sum + r.timeMs, 0) / successCount || 0).toFixed(2)}ms`);
        console.log('='.repeat(60));
        
        // 🎯 结论和建议
        if (highPrioritySuccess === highPriorityTests.length) {
            console.log('🎉 所有核心API测试通过！Token工作正常！');
        } else if (successCount > 0) {
            console.log('⚠️ 部分API可用，Token可能需要优化或某些端点有限制');
        } else {
            console.log('❌ 所有测试失败，Token可能无效或存在其他问题');
            console.log('💡 建议：重新生成Token或检查网络连接');
        }
        
        return results;
    }
}

// 🎪 主要测试函数
async function startBinanceAPITest() {
    console.log('🎯 启动币安API测试...');
    
    try {
        // 创建测试器
        const tester = new BinanceAPITester();
        
        // 初始化Token生成器
        const initSuccess = await tester.initGenerator();
        if (!initSuccess) {
            console.log('❌ 初始化失败，请确保先加载Token生成器代码');
            return;
        }
        
        // 生成新的Token
        console.log('🔄 生成新的AWS WAF Token...');
        const tokenResult = await tester.generator.generateToken("https://api.binance.com");
        
        if (!tokenResult) {
            console.log('❌ Token生成失败');
            return;
        }
        
        const awsWafToken = tokenResult.token;
        console.log('✅ Token生成成功');
        
        // 获取当前页面Cookie
        const cookies = document.cookie;
        if (!cookies) {
            console.log('⚠️ 未检测到Cookie，请确保在币安网站上运行此脚本');
        }
        
        console.log(`📋 检测到 ${cookies.split(';').length} 个Cookie`);
        
        // 运行测试套件
        const results = await tester.runTestSuite(awsWafToken, cookies);
        
        // 返回结果供进一步分析
        return {
            token: awsWafToken,
            cookies: cookies,
            results: results,
            tester: tester
        };
        
    } catch (error) {
        console.log('❌ 测试过程出现错误:', error.message);
        console.error(error);
    }
}

// 🔧 快速测试单个API
async function quickAPITest(endpoint, token = null, cookies = null) {
    const tester = new BinanceAPITester();
    await tester.initGenerator();
    
    // 如果没有提供Token，生成新的
    if (!token) {
        console.log('🔄 生成Token...');
        const tokenResult = await tester.generator.generateToken("https://api.binance.com");
        token = tokenResult.token;
    }
    
    // 如果没有提供Cookie，使用当前页面的
    if (!cookies) {
        cookies = document.cookie;
    }
    
    return await tester.testAPI(endpoint, token, cookies);
}

// 🎉 使用说明
console.log(`
🎯 币安API测试器已加载！

📋 使用方法：

1. 完整测试套件：
   await startBinanceAPITest()

2. 快速测试单个API：
   await quickAPITest('https://api.binance.com/api/v3/ping')

3. 使用现有Token测试：
   await quickAPITest('API端点', 'your-token', 'your-cookies')

⚠️ 注意：请在币安网站（https://www.binance.com）上运行以获取正确的Cookie
`);

// 将函数添加到全局作用域
if (typeof window !== 'undefined') {
    window.startBinanceAPITest = startBinanceAPITest;
    window.quickAPITest = quickAPITest;
    window.BinanceAPITester = BinanceAPITester;
} 