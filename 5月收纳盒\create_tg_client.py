from telethon import TelegramClient

# Use your own values from my.telegram.org
api_info_list = [
    # {'phone_number': '+8618781696204', 'api_id': 11943814, 'api_hash': '04e4fcdecbce07f3aa1a5001a30c56e9'},
    # # LJY
    # {'phone_number': '+8618793190889', 'api_id': 25374031, 'api_hash': '3ab07591858b17b8122c270307de9ede'},
    # 购买的
    {'phone_number': '+855318645802', 'api_id': 26372580, 'api_hash': '8b6dbbb0b450ef133fc5f2dcd5bf7c62'}
]


def get_tg_client(index: int) -> dict:
    api_info = api_info_list[index]
    session_file_name = f"session_files/full_msg_session{api_info['phone_number'].replace('+', '')}"
    client = TelegramClient(
        session_file_name,
        api_info['api_id'],
        api_info['api_hash'],
        proxy=("http", '127.0.0.1', 33210)
    ).start(phone=api_info['phone_number'])

    return {'client': client, 'phone_number': api_info['phone_number']}

if __name__ == '__main__':
    # 先登录，会提示输入验证码
    get_tg_client(0)
