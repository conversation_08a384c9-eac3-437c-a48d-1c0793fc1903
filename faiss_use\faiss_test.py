"""把当前写入数据库中的新闻标题和内容，生成向量，并保存到faiss向量数据库中"""
"""提取新闻的时候，都捕获最近24小时的新闻存入向量数据库中"""
import pymysql
import faiss
import numpy as np
from tqdm import tqdm
from sentence_transformers import SentenceTransformer
from datetime import datetime, timedelta
import os

# 加载模型（会自动从 Hugging Face 下载）
model = SentenceTransformer('BAAI/bge-small-zh')

# === Step 1：连接 MySQL 数据库 ===
conn = pymysql.connect(
    host="**************",
    port=33060,
    user="root",
    password="12345678",
    database="spider",
    charset="utf8mb4"
)
cursor = conn.cursor()

# === Step 2：提取字段 ===
# 获取最近24小时内的新闻
current_time = datetime.now()
time_threshold = current_time - timedelta(hours=240)

# 字段顺序是：id, url, title, publish_date, content
cursor.execute("SELECT id, title, content, publish_date FROM news WHERE publish_date >= %s", (time_threshold,))
rows = cursor.fetchall()

texts = []
id_map = []

for row in rows:
    id_, title, content, publish_date = row
    if content and title:
        full_text = f"{title.strip()}\n\n{content.strip()}"
        texts.append(full_text)
        id_map.append(id_)

print(f"共找到最近24小时内的新闻：{len(texts)}条")
print(f"时间范围：{time_threshold} 至 {current_time}")

if not texts:
    print("没有新数据需要处理，程序退出")
    cursor.close()
    conn.close()
    exit(0)

# === Step 3：使用 Hugging Face 模型向量化 ===
print("开始生成向量...")
embeddings = []
batch_size = 32 

# 批量处理文本以提高效率
for i in tqdm(range(0, len(texts), batch_size), desc="正在生成向量"):
    batch_texts = texts[i:i+batch_size]
    try:
        batch_embeddings = model.encode(batch_texts, normalize_embeddings=True)
        embeddings.extend(batch_embeddings)
    except Exception as e:
        print(f"批次 {i} 向量生成失败: {e}")
        # 如果批处理失败，尝试逐个处理
        for j, text in enumerate(batch_texts):
            try:
                vector = model.encode(text, normalize_embeddings=True)
                embeddings.append(vector)
            except Exception as e:
                print(f"文本 {i+j} 向量生成失败，跳过: {e}")
                # 添加一个空向量占位，保持ID映射一致性
                if len(embeddings) > 0:
                    embeddings.append(np.zeros_like(embeddings[0]))
                else:
                    print("无法生成任何向量，请检查模型或输入")
                    exit(1)

# === Step 4：存入 FAISS ===
if embeddings:
    dimension = len(embeddings[0])
    
    # 检查是否存在现有索引
    if os.path.exists("news_index.faiss"):
        print("加载现有索引...")
        index = faiss.read_index("news_index.faiss")
        # 读取现有的ID映射
        with open("id_map.txt", "r", encoding="utf-8") as f:
            existing_id_map = [line.strip() for line in f]
        # 更新ID映射
        existing_id_map.extend(id_map)
        id_map = existing_id_map
    else:
        print("创建新索引...")
        index = faiss.IndexFlatL2(dimension)  # 使用欧氏距离
    
    # 添加新向量
    # index.reset()
    index.add(np.array(embeddings).astype("float32"))

    # === Step 5：保存向量和映射 ===
    faiss.write_index(index, "news_index.faiss")
    with open("id_map.txt", "w", encoding="utf-8") as f:
        for id_ in id_map:
            f.write(str(id_) + "\n")

    print("✅ 向量索引构建完毕，保存 news_index.faiss 和 id_map.txt")
    print(f"✅ 本次新增 {len(texts)} 条新闻的向量")
else:
    print("❌ 没有生成任何向量，无法构建索引")

# 关闭数据库连接
cursor.close()
conn.close()
