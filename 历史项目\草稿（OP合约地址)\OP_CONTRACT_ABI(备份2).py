import os
import sys
import time
import random
import requests
from lxml import etree
import sqlite3
from common import redis_sentry_connection
from loguru import logger
from datetime import date
# from settings import ABI_EXPIRE_TIME_FORMAT, PROXY
from .common import CommonFun

class OptimisticSpider:
    def __init__(self):
        self.chain_name = 'Optimism'
        self.redis_master = redis_sentry_connection()
        self.abi_addr = f'spider:abi:{self.chain_name}'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
        }
        self.database_path = r'C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db'
        self.create_table()
        self.common_fun = CommonFun()
        self.crawl_date = date.today()

    def create_table(self):
        conn = sqlite3.connect(self.database_path)
        with conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS OP_TEST (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ADDRESS TEXT,
                    CHAIN_NAME TEXT,
                    CONTRACT_NAME TEXT,
                    CONTRACT_ABI TEXT
                )
            ''')

    def get_address(self):
        redis_master, _ = redis_sentry_connection()
        chain_name = self.chain_name
        abi_addr = f'spider:abi:{chain_name}'
        addresses = []

        while True:
            address = redis_master.spop(abi_addr)
            if address:
                addresses.append(address)
                logger.info(f"获取到地址: {address}")
                self.parse_data(address)
            else:
                logger.info("没有从数据库中找到address")
                break

    def parse_data(self, address):
        url = f"https://optimistic.etherscan.io/address/{address}"
        time.sleep(1.2 + random.random())
        response = requests.get(url, headers=self.headers)

        if response is None or response.status_code != 200:
            logger.error(f'获取失败： {address}, status code: {response.status_code}')
            return

        html = etree.HTML(response.text)
        contractAbi = ''.join(html.xpath('//pre[@id="js-copytextarea2"]/text()'))
        contract_name = ''.join(html.xpath('//div[2]/div[1]/div[1]/div[2]/span/text()'))

        if contractAbi:
            logger.info(f'获取到数据:{address}:{contract_name}: {contractAbi}')
            # self.save_to_sqlite3(address, contract_name, contractAbi)
            # logger.info('已经存入此条数据')
        # else:
        #     logger.info('No ABI found')

    # def save_to_phoneix(self):
    #     if contractAbi:
    #         logger.info(f'获取到数据:{address}:{contract_name}: {contractAbi}')
            item = {
                'ADDRESS': address,
                'CHAIN_NAME': self.chain_name,
                'CONTRACT_NAME': contract_name,
                'CONTRACT_ABI': contractAbi
            }
            self.common_fun.save_to_phoenix(item)
            logger.info('已经存入此条数据')
        else:
            logger.info('No ABI found')

    def save_to_sqlite3(self, address, contract_name, contractAbi):
        conn = sqlite3.connect(self.database_path)
        with conn:
            conn.execute('''
                INSERT INTO OP_TEST (ADDRESS, CHAIN_NAME, CONTRACT_NAME, CONTRACT_ABI)
                VALUES (?, ?, ?, ?)
            ''', (address, self.chain_name, contract_name, contractAbi))
        logger.info(f"Data for {address} saved to database.")

    # def judge_expire(self, address: str, chain_name):
    #     """ 同一个地址一个月内不再重复爬取 """
    #     expire_hash_name = ABI_EXPIRE_TIME_FORMAT % chain_name
    #     expire_time = self.common_fun.get_hash(hash_name=expire_hash_name, hash_key=address)
    #     logger.info(f'chain_name: {chain_name}, address: {address}, expire_time: {expire_time}')
    #
    #     if expire_time is None:
    #         return True
    #     else:
    #         time_differ = int(time.time()) - int(expire_time)
    #
    #         if time_differ < 2592000:  # 一个月之内不再重复爬
    #             return False
    #         else:
    #             return True

#
# if __name__ == '__main__':
#     OP_SPIDER = OptimisticSpider()
#     OP_SPIDER.get_address()