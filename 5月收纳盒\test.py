import requests


headers = {
    "Content-Type": "application/json"
}
url = "https://valuescan.ai/api/v1/dex/market/kline-history"
data = {
        "tokenContractAddress": "5z3EqYQo9HiCEs3R84RCDMu2n7anpDMxRhdK8PSWmrRC",
        "bar": "1s",
        "limit": 400,
        "chainName": "SOL",
        "useNativePricing": False
        }
response = requests.post(url, headers=headers, data=data)

print(response.text)
print(response)