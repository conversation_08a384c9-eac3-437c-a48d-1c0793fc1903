"""
该文件配置该文件夹下程序运行所需的一些公共变量，包括数据库的一些配置
"""
import pymysql
from dbutils.pooled_db import PooledDB
import redis
from Spiders.utils import REDIS_DB_U, REDIS_POLL_SIZE_U, RETRY_NUM_U, HOST_U, POSS_WORD_U, PORT_U, USER_U, MYSQL_BASE_U, PHOENIX_PORT, PHOENIX_HOST, CHAR_U, REDIS_HOST, REDIS_PASSWORD, REDIS_PORT


CONFIG = {
    'host': HOST_U,
    'port': PORT_U,
    'database': MYSQL_BASE_U,
    'user': USER_U,
    'password': POSS_WORD_U,
    'charset': CHAR_U
}
# MySQL库名必须是字符串类型
POLL_DB = PooledDB(
    # 指定数据库连接驱动
    creator=pymysql,
    # 连接池允许的最大连接数,0和None表示没有限制
    maxconnections=5,
    # 初始化时,连接池至少创建的空闲连接,0表示不创建
    mincached=2,
    # 连接池中空闲的最多连接数,0和None表示没有限制
    maxcached=5,
    # 连接池中最多共享的连接数量,0和None表示全部共享(其实没什么卵用)
    maxshared=0,
    # 连接池中如果没有可用共享连接后,是否阻塞等待,True表示等等,
    # False表示不等待然后报错
    blocking=True,
    # 开始会话前执行的命令列表
    setsession=[],
    # ping Mysql服务器检查服务是否可用
    ping=0,
    **CONFIG
)
# phoenixdb的配置
PHOENIX_CONN = f"http://{PHOENIX_HOST}:{PHOENIX_PORT}"

# redis库的编号必须是整数，并且在0-15之间
REDIS_DB = REDIS_DB_U
# 配置连接redis, 使用堵塞式的redis连接池，默认最大连接数为10，开发人员可以根据实际开发需求做相应修改
REDIS_POLL_SIZE = REDIS_POLL_SIZE_U
REDIS_CONFIG = {
    "host": REDIS_HOST,
    "port": REDIS_PORT,
    "db": REDIS_DB,
    "password": REDIS_PASSWORD,
    "max_connections": REDIS_POLL_SIZE
}
REDIS_CONN = redis.Redis(connection_pool=redis.BlockingConnectionPool(**REDIS_CONFIG))

RETRY_NUM = RETRY_NUM_U  # 请求重试次数，默认是10次

