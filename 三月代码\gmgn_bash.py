import csv
import json
from datetime import datetime
from curl_cffi import requests
from loguru import logger
import time
from datetime import datetime, timezone


proxy = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

class Trump():
    def __init__(self):
        self.headers = {
        "referer": "https://gmgn.ai/sol/token/4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R",
        }
        self.params = {
            "device_id": "1713e771-6640-405d-8c71-fe483feeb742",
            "client_id": "gmgn_web_2025.0319.104420",
            "from_app": "gmgn",
            "tz_name": "Asia/Shanghai",
            "tz_offset": "28800",
            "app_lang": "zh-CN",
            "resolution": "1m",
            "from": "0",
            "to": "1704960021000",
        }
        self.cookies = {
            "_ga": "GA1.1.566489715.1737427304",
            "cf_clearance": "0myUabVjqD0rBx7ERZklhhs0wwDCfyt3LaCrsTN1zWM-1742362458-1.2.1.1-fhJ2Tzt8r0MIJIFPLB_KJ854YZ5_oLkhfad9.SoQW9yZ7Qd5Gduh6AQz7hjo6WXHfcktxQyPcmtDSD_k3_JI1RQyk5oojfIJvxZctpRxpOFFs_9R1C7COWvtetyCTTjh8H.sedOAkBnz2zPeWNoeVhB4TYS_5QAJoEjTxC5W1cjW9_wVoVz5FWQMEBI0csabMV126NftmdpmbNVseJhLH9aaXRZ55mF7YuKCQbw5GpRPoXf9gvxHCqxRINw541am.gAinlyFVm4j6QMa5heG8mjK3obWFXRPooOX01geL9nIRQ2LmiycEsG2aAHVeC71FX2UoDmtsdr1vNoEE389W0YLTkZObKfxLAm64rTULhM",
            "_ga_0XM0LYXGC8": "GS1.1.1742361212.11.1.1742363148.0.0.0",
        }

    def parse_data(self):
        url = "https://gmgn.ai/api/v1/token_candles/sol/4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R"
        response = requests.get(url, headers=self.headers, cookies=self.cookies, params=self.params, proxies=proxy, impersonate='chrome110')


        if response.status_code == 200:
            print(response.text)
            data = response.json()
            if data.get("message") == "success":
                logger.info(f'访问成功,状态码:{response.status_code},正在获取数据')
                self.save_to_csv(data["data"]["list"])
        elif response.status_code == 403 and "Just a moment..." in response.text:
            logger.info(f'未通过校验,状态码:{response.status_code},请检查cookie')


    def save_to_csv(self, data_list):
        with open('RAY_s级.csv', mode='w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(["open", "high", "low", "close", "time"])
            for item in data_list:
                open_price = item["open"]
                high = item["high"]
                low = item["low"]
                close = item["close"]
                time_stamp = int(item["time"]) / 1000
                time_str = datetime.fromtimestamp(time_stamp, timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                writer.writerow([open_price, high, low, close, time_str])

            logger.info('数据保存成功')


if __name__ == '__main__':
    trump = Trump()
    trump.parse_data()