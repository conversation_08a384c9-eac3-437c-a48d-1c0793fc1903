from curl_cffi import requests


headers = {
    "authority": "gmgn.ai",
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "referer": "https://gmgn.ai/eth/token/******************************************",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-arch": "\"x86\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"114.0.5735.199\"",
    "sec-ch-ua-full-version-list": "\"Not.A/Brand\";v=\"8.0.0.0\", \"Chromium\";v=\"114.0.5735.199\", \"Google Chrome\";v=\"114.0.5735.199\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-ch-ua-platform-version": "\"15.0.0\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/11******* Safari/537.36"
}
cookies = {
    "_ga": "GA1.1.566489715.1737427304",
    "__cf_bm": "DC8TbNGEbNJcrnin1vTK9_HuBjjCPsowSmVHkzH9kjY-1737699235-*******-eR1yca794J60WZlO0eLiXMuQqqMrdPol4J9Ise4SU_rFdVr2d5pWhgtXdPI4fng9xSFcVrJSwYEwFjqKG0joSg",
    "cf_clearance": "UPhmsuZWmbsEU4O2jUrLl6hnnb8ps2QE8hF9fgs6JZo-1737699396-*******-WLINzbSAC6MHVKEOiMrlm4pJlC3wunumiwykk9o3_Lu0ze5VfDlJkcHTaqcikz2pyMwKEfms9HZ0Upqq_WXatOL8SQpLBlCCOfyYcfnavP1WGW2A0mGW5cPNy2W.fZSfN5vT0Nvuw8vR6Zemgw7He.zp5NktCfjmcLsB3m7vY.NMPjdU0.OMWAipLAiF3UKQDBugzF.Cj.QKeS3mAHrsg3miTl55kx3LL7swF0VyZtcyqZBpFXRO5ES_h608PfXZTZgYHUQf3BC.Gh1cvr9oZYN7sRuXxp3xDwz8AR_86KU",
    "_ga_0XM0LYXGC8": "GS1.1.1737697298.4.1.1737699900.0.0.0"
}
url = "https://gmgn.ai/api/v1/token_kline/eth/******************************************"
params = {
    "device_id": "1713e771-6640-405d-8c71-fe483feeb742",
    "client_id": "gmgn_web_2025.0124.113931",
    "from_app": "gmgn",
    "app_ver": "2025.0124.113931",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "en",
    "resolution": "1s",
    "from": "1737664139",
    "to": "1737700139"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params)

# print(response.text)
print(response)