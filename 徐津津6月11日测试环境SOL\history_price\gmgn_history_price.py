#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取Solana链上代币的最新价格数据并更新到数据库的"data GMGN"字段
使用GMGN API获取Solana代币价格
"""

import json
import sqlite3
import time
import datetime
import random
import sys
import threading
from concurrent.futures import ThreadPoolExecutor
from curl_cffi import requests

# 设置控制台输出编码
if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库路径
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "test_data_sol"  # 表名

# 代理配置
PRIMARY_PROXY = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

# PRIMARY_PROXY = {
#     "http": "http://127.0.0.1:33210",
#     "https": "http://127.0.0.1:33210"
# }

# 备用代理
BACKUP_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

# 当前使用的代理
CURRENT_PROXY = PRIMARY_PROXY.copy()

# 线程锁，用于多线程环境下保护代理切换
PROXY_LOCK = threading.Lock()

# GMGN API请求配置
COOKIES = {
    "_ga": "GA1.1.1787124478.1747114971",
    "cf_clearance": "zFVwnICqkSenUiBfVwb2ttr2jWXQgp7brd2XB2bw8Ck-1749791891-*******-lAY_rtO1tpYPj7ry1gzMZBHL8sKbUPn.HN7h1LUVPQ6ibDfVs3JvKJuHD2qJ0LCGdAWk4Dxg0Aa5xWolSAIXsSH_COrj3V67J1Zkhb8RYn6ieRVprv3ywXztBFnsqYItmjoZ371LrdQBgFJZHqhhL1MPdn.DNTrq15EqbFuvq7N.DRlNJs6o5cNoeGPqBAqLDCaZ5hM3ZVGv_1pRCfJzco29R2VN14RknJVNTqYEMhnSVNTGrgNw1HSkMWuPMmdafdXKrEypeeT3cRy9SIELIkM6rBuRV76SdCg6YZeLJFw3JyHgdXyXwrnbTWerV3r4O7moIp6rul_2fCIJGHABUy7Xh1dL0ANLz8HIFV8p0fE",
    "_ga_0XM0LYXGC8": "GS2.1.s1749791891$o45$g1$t1749791898$j53$l0$h0"
}

# 基础参数，to参数会根据HA Time动态设置
BASE_PARAMS = {
    "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
    "client_id": "gmgn_web_20250613-2194-6838f94",
    "from_app": "gmgn",
    "app_ver": "20250613-2194-6838f94",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a0adc6758070914b5d3cd4679349eed1",
    "os": "web",
    "resolution": "1s",
    "from": "0",
    "limit": "365"
}

# 默认时间戳 (2025-05-28 17:25:27 的时间戳)
DEFAULT_TIMESTAMP = int(datetime.datetime(2025, 6, 13, 13, 17, 27).timestamp() * 1000)

# 重试配置
MAX_RETRIES = 5  # 增加重试次数
RETRY_DELAY = 3  # 秒

# 线程数量
NUM_THREADS = 1  # 修改为10个线程

# 线程锁，用于打印日志
PRINT_LOCK = threading.Lock()


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with PRINT_LOCK:
        print(*args, **kwargs)


def get_token_count():
    """获取需要处理的Token总数"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # 计算非空Token Address的总数
        cursor.execute(f'SELECT COUNT(*) FROM {DB_TABLE} WHERE "code_address" IS NOT NULL')
        count = cursor.fetchone()[0]
    except sqlite3.Error as e:
        thread_safe_print(f"获取Token总数时出错: {e}")
        count = 0
    finally:
        conn.close()

    return count


def get_all_tokens():
    """获取所有需要处理的token数据"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # 获取所有非空Token Address记录
        cursor.execute(f'SELECT rowid, "code_address", "HA Time" FROM {DB_TABLE} WHERE "code_address" IS NOT NULL')
        rows = cursor.fetchall()

        token_data_list = []
        for row in rows:
            token_data_list.append({
                "row_id": row[0],
                "token_address": row[1],
                "ha_time": row[2]
            })

        return token_data_list
    except sqlite3.Error as e:
        thread_safe_print(f"获取所有Token数据时出错: {e}")
        return []
    finally:
        conn.close()


def convert_time_to_timestamp(time_str):
    """将标准时间字符串转换为毫秒时间戳"""
    if not time_str:
        # 如果时间为空，返回默认时间戳
        return DEFAULT_TIMESTAMP

    try:
        # 尝试解析时间字符串
        dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        # 转换为毫秒时间戳
        return int(dt.timestamp() * 1000)
    except (ValueError, TypeError) as e:
        thread_safe_print(f"时间转换错误: {e}, 使用默认时间戳")
        return DEFAULT_TIMESTAMP


def update_price_in_db(row_id, price, timestamp_str):
    """更新代币价格和时间到数据库"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # 将时间戳转换为标准时间格式
        if timestamp_str is not None:
            try:
                # API返回的时间戳是秒级时间戳，并且已经是整数类型
                timestamp = int(timestamp_str)  # 确保是整数
                standard_time = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                thread_safe_print(f"时间戳 {timestamp_str} 转换为标准时间: {standard_time}")
            except (ValueError, TypeError) as e:
                # 如果转换失败，直接使用原始值
                standard_time = str(timestamp_str)
                thread_safe_print(f"时间戳转换失败: {e}, 使用原始时间字符串: {standard_time}")
        else:
            standard_time = None
            thread_safe_print("没有时间戳数据")

        # 更新数据库，更新data GMGN和GMGN Time字段
        cursor.execute(
            f'''
            UPDATE {DB_TABLE}
            SET "gmgn_price" = ?, "GMGN Time" = ?
            WHERE rowid = ?
            ''',
            (price, standard_time, row_id)
        )

        if cursor.rowcount > 0:
            thread_safe_print(f"成功更新 rowid {row_id} 的GMGN价格: {price}, 时间: {standard_time}")
            conn.commit()
            return True
        else:
            thread_safe_print(f"未找到rowid {row_id}")
            return False

    except sqlite3.Error as e:
        thread_safe_print(f"更新数据库时出错: {e}")
        conn.rollback()
        return False

    finally:
        conn.close()


def parse_gmgn_response(response_text, token_address):
    """解析GMGN API响应，提取价格和时间数据"""
    try:
        data = json.loads(response_text)

        # 检查响应是否成功
        if data.get("code") == 0 and "data" in data and "history" in data["data"]:
            history = data["data"]["history"]

            # 如果有历史记录
            if history and len(history) > 0:
                # 获取第一条记录的价格和时间
                first_record = history[0]
                price_usd = first_record.get("price_usd")
                timestamp = first_record.get("timestamp")

                if price_usd:
                    thread_safe_print(f"解析到代币 {token_address} 的价格: {price_usd}, 时间戳: {timestamp}")
                    return {
                        "price": price_usd,
                        "timestamp": timestamp
                    }
                else:
                    thread_safe_print(f"代币 {token_address} 的价格数据为空")
            else:
                thread_safe_print(f"代币 {token_address} 没有交易历史")
        else:
            error_msg = data.get("message", "未知错误")
            thread_safe_print(f"API请求失败: {error_msg}")

    except json.JSONDecodeError:
        thread_safe_print(f"JSON解析错误，响应内容: {response_text[:200]}...")
    except Exception as e:
        thread_safe_print(f"解析响应时出错: {e}")

    return None


def switch_to_backup_proxy():
    """切换到备用代理"""
    global CURRENT_PROXY
    with PROXY_LOCK:
        CURRENT_PROXY = BACKUP_PROXY.copy()
        thread_safe_print("切换到备用代理:", CURRENT_PROXY)


def fetch_token_price(token_data, thread_id):
    """获取单个代币的价格"""
    global CURRENT_PROXY

    row_id = token_data["row_id"]
    token_address = token_data["token_address"]
    ha_time = token_data["ha_time"]

    # 将HA Time转换为时间戳
    to_timestamp = convert_time_to_timestamp(ha_time)

    # 设置参数，使用转换后的时间戳
    params = BASE_PARAMS.copy()
    # params["to"] = str(to_timestamp)

    # url = f"https://gmgn.ai/vas/api/v1/token_trades/bsc/{token_address}"
    # url = f"https://gmgn.ai/api/v1/token_candles/sol/{token_address}"
    # url = f"https://gmgn.ai/api/v1/token_stat/sol/{token_address}"
    url = f"https://gmgn.ai/vas/api/v1/token_trades/sol/{token_address}"
    headers = {
        "referer": f"https://gmgn.ai/sol/token/{token_address}"
    }

    thread_safe_print(f"[线程-{thread_id}] 使用时间戳: {to_timestamp} (来自HA Time: {ha_time})")

    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            thread_safe_print(
                f"[线程-{thread_id}] 请求代币 {token_address} 的价格... (尝试 {retry_count + 1}/{MAX_RETRIES})")
            thread_safe_print(f"[线程-{thread_id}] URL: {url}")

            current_proxy = None
            with PROXY_LOCK:
                current_proxy = CURRENT_PROXY.copy()
            thread_safe_print(f"[线程-{thread_id}] 当前使用代理: {current_proxy}")

            # 发送请求
            response = requests.get(
                url,
                headers=headers,
                cookies=COOKIES,
                params=params,
                impersonate="chrome110",
                proxies=current_proxy,
                timeout=30
            )

            thread_safe_print(f"[线程-{thread_id}] 响应状态码: {response.status_code}")

            if response.status_code == 200:
                # 解析响应
                result = parse_gmgn_response(response.text, token_address)

                if result and "price" in result:
                    # 更新数据库
                    if update_price_in_db(row_id, result["price"], result.get("timestamp")):
                        return True
                # 即使没有价格，也算是成功请求，不需要重试
                break
            elif response.status_code == 403:
                retry_count += 1
                thread_safe_print(f"[线程-{thread_id}] 收到403状态码，等待{RETRY_DELAY}秒后重试...")

                # 如果连续重试3次都是403，切换到备用代理
                if retry_count >= MAX_RETRIES // 2:
                    thread_safe_print(f"[线程-{thread_id}] 连续多次收到403状态码，切换到备用代理...")
                    switch_to_backup_proxy()

                time.sleep(RETRY_DELAY)
            else:
                thread_safe_print(f"[线程-{thread_id}] 请求失败: {response.status_code}")
                retry_count += 1
                time.sleep(RETRY_DELAY)

        except Exception as e:
            thread_safe_print(f"[线程-{thread_id}] 请求代币 {token_address} 出错: {e}")
            import traceback
            thread_safe_print(traceback.format_exc())
            retry_count += 1
            if retry_count < MAX_RETRIES:
                thread_safe_print(f"[线程-{thread_id}] 等待{RETRY_DELAY}秒后重试...")
                time.sleep(RETRY_DELAY)

    return False


def worker_thread(token_data_batch, thread_id):
    """工作线程函数，处理一批token数据"""
    thread_safe_print(f"[线程-{thread_id}] 启动，负责处理 {len(token_data_batch)} 个代币")

    success_count = 0
    total_count = len(token_data_batch)

    for i, token_data in enumerate(token_data_batch):
        thread_safe_print(f"\n[线程-{thread_id}] 处理第 {i + 1}/{total_count} 个代币: {token_data['token_address']}")

        # 获取价格并立即更新数据库
        if fetch_token_price(token_data, thread_id):
            success_count += 1

        # 随机延迟1-3秒，并发环境下降低延迟时间
        if i < total_count - 1:  # 最后一个不需要延迟
            delay = random.uniform(1.0, 3.0)
            thread_safe_print(f"[线程-{thread_id}] 等待 {delay:.2f} 秒...")
            time.sleep(delay)

    thread_safe_print(f"[线程-{thread_id}] 完成! 成功处理 {success_count}/{total_count} 个代币")
    return success_count


def process_tokens_with_threads():
    """使用多线程处理所有token数据"""
    # 获取所有token数据
    all_tokens = get_all_tokens()
    total_count = len(all_tokens)

    if not all_tokens:
        thread_safe_print("未找到任何Token数据，请检查数据库")
        return 0, 0

    thread_safe_print(f"总共有 {total_count} 个代币需要处理，将使用 {NUM_THREADS} 个线程并行处理")

    # 将token数据平均分配给各个线程
    tokens_per_thread = total_count // NUM_THREADS
    remainder = total_count % NUM_THREADS

    token_batches = []
    start_idx = 0

    for i in range(NUM_THREADS):
        # 计算每个线程分配的token数量，考虑余数分配
        batch_size = tokens_per_thread + (1 if i < remainder else 0)
        end_idx = start_idx + batch_size

        # 分配token批次
        token_batches.append(all_tokens[start_idx:end_idx])
        thread_safe_print(f"线程-{i + 1} 将处理 {len(token_batches[-1])} 个代币，索引范围: {start_idx}-{end_idx - 1}")

        start_idx = end_idx

    # 使用线程池执行任务
    success_counts = []
    with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
        # 提交任务并收集Future对象
        futures = [executor.submit(worker_thread, token_batches[i], i + 1) for i in range(NUM_THREADS)]

        # 等待所有任务完成并收集结果
        for future in futures:
            success_counts.append(future.result())

    # 计算总成功数
    total_success = sum(success_counts)

    return total_count, total_success


def main():
    """主函数"""
    thread_safe_print("=" * 60)
    thread_safe_print("GMGN Token价格获取工具 (多线程版)")
    thread_safe_print("=" * 60)
    thread_safe_print("数据库路径:", DB_PATH)
    thread_safe_print("数据库表名:", DB_TABLE)
    thread_safe_print("主要代理:", PRIMARY_PROXY)
    thread_safe_print("备用代理:", BACKUP_PROXY)
    thread_safe_print("默认时间戳:", DEFAULT_TIMESTAMP,
                      f"({datetime.datetime.fromtimestamp(DEFAULT_TIMESTAMP / 1000).strftime('%Y-%m-%d %H:%M:%S')})")
    thread_safe_print("线程数量:", NUM_THREADS)
    thread_safe_print("最大重试次数:", MAX_RETRIES)
    thread_safe_print("=" * 60)

    # 获取价格并实时更新数据库
    thread_safe_print("\n开始多线程处理代币数据...")
    total_count, success_count = process_tokens_with_threads()

    # 输出结果
    thread_safe_print("\n更新完成!")
    thread_safe_print(f"总计: {total_count} 个Token")
    thread_safe_print(f"成功更新数据库: {success_count} 个")


if __name__ == "__main__":
    thread_safe_print("开始获取Solana链上代币的GMGN价格数据...")
    start_time = time.time()

    try:
        main()
    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        import traceback

        thread_safe_print(f"程序运行出错: {e}")
        thread_safe_print(traceback.format_exc())

    end_time = time.time()
    duration = end_time - start_time
    thread_safe_print(f"程序运行耗时: {duration:.2f} 秒")
    thread_safe_print("程序运行结束")