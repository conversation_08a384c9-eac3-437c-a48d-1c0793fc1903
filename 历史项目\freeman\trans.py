# import pandas as pd
# from google_trans_new import google_translator

# def translate_text(text, lang_tgt='zh-cn'):
#     translator = google_translator()
#     try:
#         return translator.translate(text, lang_tgt=lang_tgt)
#     except Exception as e:
#         print(f"Error during translation: {e}")
#         return text

# def translate_columns(file_path, columns):
#     df = pd.read_excel(file_path)
#     translator = google_translator()
    
#     for column in columns:
#         print(f"Translating column: {column}")
#         df[column] = df[column].apply(lambda text: translate_text(text, lang_tgt='zh-cn'))
    
#     return df

# if __name__ == "__main__":
#     file_path = 'cryptocurrency_need_ts.xlsx'  # Update this to your Excel file path
#     columns_to_translate = ['Country', 'Content']  # Columns you want to translate
#     translated_df = translate_columns(file_path, columns_to_translate)
#     translated_df.to_excel('translated_cryptocurrency_regulations.xlsx', index=False)
#     print("Translation completed and saved to 'translated_cryptocurrency_regulations.xlsx'")


# from deep_translator import GoogleTranslator
# import pandas as pd

# def translate_text(text, target_lang='zh-cn'):
#     try:
#         return GoogleTranslator(source='en', target=target_lang).translate(text)
#     except Exception as e:
#         print(f"Error during translation: {e}")
#         return text

# def translate_columns(file_path, columns):
#     df = pd.read_excel(file_path)
    
#     for column in columns:
#         print(f"Translating column: {column}")
#         df[column] = df[column].apply(lambda text: translate_text(text, target_lang='zh-CN'))
    
#     return df

# if __name__ == "__main__":
#     file_path = 'cryptocurrency_need_ts.xlsx'
#     columns_to_translate = ['Country', 'Content']
#     translated_df = translate_columns(file_path, columns_to_translate)
#     translated_df.to_excel('translated_cryptocurrency_regulations.xlsx', index=False)
#     print("Translation completed and saved to 'translated_cryptocurrency_regulations.xlsx'")



# '''
# from deep_translator import GoogleTranslator

# try:
#     sample_translation = GoogleTranslator(source='auto', target='zh-cn').translate("Hello, how are you?")
#     print(sample_translation)
# except Exception as e:
#     print(f"Translation error: {e}")
# '''



from deep_translator import GoogleTranslator
import pandas as pd
from loguru import logger


def translate_text(text, target_lang='zh-CN'):
    try:
        return GoogleTranslator(source='auto', target=target_lang).translate(text)
    except Exception as e:
        logger.info(f"Error during translation: {e}")
        return text


def translate_columns(file_path, columns, max_rows=150):
    df = pd.read_excel(file_path)

    for column in columns:
        logger.info(f"Translating column: {column}")
        # 只选择每列的前150行进行翻译
        df[column] = df[column].head(max_rows).apply(lambda text: translate_text(text, target_lang='zh-CN'))

    return df


if __name__ == "__main__":
    file_path = 'cryptocurrency_regulations.csv'
    columns_to_translate = ['Country', 'Content']
    translated_df = translate_columns(file_path, columns_to_translate)
    translated_df.to_excel('32323_cryptocurrency_regulations.csv', index=False)
    logger.info("Translation completed and saved to 'translated_cryptocurrency_regulations.xlsx'")