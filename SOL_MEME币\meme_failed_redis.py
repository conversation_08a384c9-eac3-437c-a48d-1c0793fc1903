import requests
import json
import time
import threading
import queue
import os
import datetime
import random
import signal
import sys
from loguru import logger
import traceback
import pymysql
import redis
import psycopg2
from psycopg2.extras import execute_batch
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Tuple, Optional, Any
from utils import DB_BASE

# 配置日志
logger.add("meme_failed_redis.log", rotation="10 MB", level="INFO", compression="zip")

# Redis配置
REDIS_CONFIG = {
    'host': '**************',
    'port': 6379,
    'password': '123456',
    'db': 1  # 代理IP所在的Redis库
}

# Redis断点续传配置
REDIS_CHECKPOINT_CONFIG = {
    'host': '**************',
    'port': 6379,
    'password': '123456',
    'db': 4  # 断点续传信息所在的Redis库
}

# PostgreSQL配置（用于写入数据）
POSTGRESQL_CONFIG = {
    'host': '*********',
    'port': 5432,
    'database': 'postgres',
    'user': 'postgres',
    'password': '12345678',
    'schema': 'solana_history'
}

# Redis键名前缀
REDIS_KEY_PROCESSING = "meme_spider:processing"  # 正在处理的地址
REDIS_KEY_COMPLETED = "meme_spider:completed"    # 已完成的地址
REDIS_KEY_NUMBER = "meme_spider:number"          # 已完成的地址数量
REDIS_KEY_FAILED = "meme_spider:failed_address"  # 失败的地址

# 爬取时间范围配置（半年）
START_TIMESTAMP = 1747670400000  # 2025-05-20 00:00:00
END_TIMESTAMP = 1732032000000    # 2024-11-20 00:00:00

# 重试配置
MAX_RETRIES = 50  # 最大重试次数

class MemeFailedSpider:
    def __init__(self, thread_num: int = 20, interval: str = "1m"):
        """
        初始化爬虫
        
        Args:
            thread_num: 线程数
            interval: 时间间隔，如 1m, 5m, 15m, 1h, 4h, 1d
        """
        # 添加停止标志
        self.running = True
        self.shutdown_event = threading.Event()
        
        # 注册信号处理
        try:
            signal.signal(signal.SIGINT, self.handle_shutdown)
            # SIGTERM在Windows上可能不可用
            if hasattr(signal, 'SIGTERM'):
                signal.signal(signal.SIGTERM, self.handle_shutdown)
        except Exception as e:
            logger.warning(f"注册信号处理失败: {e}")
        
        # 获取代理列表
        self.proxy_list = self.get_proxies_from_redis()
        
        # 确保线程数不超过可用代理数
        available_proxies = len(self.proxy_list)
        if available_proxies == 0:
            logger.warning("无法从Redis获取代理IP，将使用直连模式")
            # 使用空列表，后续会使用直连
            self.proxy_list = [""]
            self.thread_num = min(thread_num, 5)  # 直连模式下限制线程数
            logger.warning(f"直连模式下将使用 {self.thread_num} 个线程")
        elif available_proxies < thread_num:
            logger.warning(f"可用代理数量({available_proxies})小于指定线程数({thread_num})，将使用可用代理数量作为线程数")
            self.thread_num = available_proxies
        else:
            self.thread_num = thread_num
            
        logger.info(f"初始化爬虫，线程数: {self.thread_num}, 可用代理数: {available_proxies}")
        
        # 默认代理（用于初始化，实际执行时每个线程会分配独立代理）
        if self.proxy_list[0]:
            proxy_url = f"http://{self.proxy_list[0]}"
            self.proxies = {
                "http": proxy_url,
                "https": proxy_url
            }
            logger.info(f"默认代理设置为: {proxy_url}")
        else:
            self.proxies = {}
            logger.info("使用直连模式，不使用代理")
        
        self.headers = {
            "accept": "application/json",
            "accept-language": "zh-CN,zh;q=0.9",
            "app-type": "web",
            "referer": "https://web3.okx.com/zh-hans/token/solana/2hKSxkRZHkWJ3Mr5Fp9gRgLr4pbgP5UcYciCjvdppump",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "x-cdn": "https://web3.okx.com",
            "x-utc": "8",
            "x-zkdex-env": "0"
        }
        
        self.cookies = {
            "_gcl_gs": "2.1.k1$i1747121212$u3801134",
            "ok_global": "{%22g_t%22:2}",
            "fp_s": "0",
            "_ga_G0EKWWQGTZ": "GS2.1.s1752903913$o153$g0$t1752903913$j60$l0$h0",
            "__cf_bm": "DITCT32q3aoYh2j8leor_UW4Te7Hc_Nux831MD_WBEU-1752905362-1.0.1.1-Y0yOPbwj08tGv4Kc.dIrriG5Te28bj.oR8K00hJKq3ruN4QDhQY.qYnfuaTy1.BvhMP_UG1NgdEIAaci2wc17s5QucvWIyRwoEtdAuH_EA8"
        }
        
        self.url = "https://web3.okx.com/priapi/v5/dex/token/market/history-dex-token-hlc-candles"
        
        # 基础参数
        self.base_params = {
            "chainId": "501",  # SOL链
            "bar": interval,   # 时间间隔
            "limit": "300",     # 每次请求数量
        }
        
        # 线程相关
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.lock = threading.Lock()
        
        # 初始化Redis连接
        self.redis_checkpoint = redis.Redis(
            host=REDIS_CHECKPOINT_CONFIG['host'],
            port=REDIS_CHECKPOINT_CONFIG['port'],
            password=REDIS_CHECKPOINT_CONFIG['password'],
            db=REDIS_CHECKPOINT_CONFIG['db']
        )
        
        # 初始化飞书通知
        self.notifier = DB_BASE()
        
        # 初始化PostgreSQL连接
        self.init_postgresql()
        
    def get_proxies_from_redis(self) -> List[str]:
        """
        从Redis获取代理IP列表
        
        Returns:
            List[str]: 代理IP列表，格式为 ["ip:port", ...]
        """
        try:
            # 连接Redis
            redis_client = redis.Redis(
                host=REDIS_CONFIG['host'],
                port=REDIS_CONFIG['port'],
                password=REDIS_CONFIG['password'],
                db=REDIS_CONFIG['db']
            )
            
            # 获取所有代理IP
            proxy_keys = redis_client.keys("proxy:*")
            
            if not proxy_keys:
                logger.warning("Redis中没有找到代理IP")
                return []
            
            # 获取所有代理IP的值
            proxies = []
            for key in proxy_keys:
                proxy = redis_client.get(key)
                if proxy:
                    proxies.append(proxy.decode('utf-8'))
            
            logger.info(f"从Redis获取到 {len(proxies)} 个代理IP")
            return proxies
            
        except Exception as e:
            logger.error(f"从Redis获取代理IP时出错: {e}")
            logger.error(traceback.format_exc())
            return []

    def init_postgresql(self):
        """初始化PostgreSQL连接和表"""
        try:
            # 连接到PostgreSQL
            self.pg_conn = psycopg2.connect(
                host=POSTGRESQL_CONFIG['host'],
                port=POSTGRESQL_CONFIG['port'],
                database=POSTGRESQL_CONFIG['database'],
                user=POSTGRESQL_CONFIG['user'],
                password=POSTGRESQL_CONFIG['password']
            )
            
            # 创建游标
            self.pg_cursor = self.pg_conn.cursor()
            
            # 设置schema
            self.pg_cursor.execute(f"SET search_path TO {POSTGRESQL_CONFIG['schema']}")
            
            # 确保表存在
            self.pg_cursor.execute("""
                CREATE TABLE IF NOT EXISTS meme_price_history (
                    id SERIAL PRIMARY KEY,
                    address VARCHAR(255) NOT NULL,
                    timestamp BIGINT NOT NULL,
                    open NUMERIC(30, 18),
                    high NUMERIC(30, 18),
                    low NUMERIC(30, 18),
                    close NUMERIC(30, 18),
                    volume NUMERIC(30, 18),
                    datetime TIMESTAMP WITHOUT TIME ZONE,
                    UNIQUE(address, timestamp)
                )
            """)
            
            # 提交事务
            self.pg_conn.commit()
            
            logger.info("PostgreSQL初始化完成")
            
        except Exception as e:
            logger.error(f"初始化PostgreSQL时出错: {e}")
            logger.error(traceback.format_exc())
            raise

    def update_address_progress(self, address: str, timestamp: int, status: str = "processing"):
        """
        更新地址的处理进度
        
        Args:
            address: 代币地址
            timestamp: 当前处理的时间戳
            status: 状态，可以是 processing 或 completed
        """
        try:
            if status == "processing":
                # 更新正在处理的地址和时间戳
                self.redis_checkpoint.hset(REDIS_KEY_PROCESSING, address, timestamp)
            elif status == "completed":
                # 从处理中移除，添加到已完成
                self.redis_checkpoint.hdel(REDIS_KEY_PROCESSING, address)
                self.redis_checkpoint.sadd(REDIS_KEY_COMPLETED, address)
                
                # 更新完成数量
                self.redis_checkpoint.incr(REDIS_KEY_NUMBER)
                
                # 记录日志
                logger.info(f"地址 {address} 处理完成，时间戳: {timestamp}")
        except Exception as e:
            logger.error(f"更新地址进度时出错: {address}, {e}")
            logger.error(traceback.format_exc())

    def process_kline_data(self, address: str, data_list: List) -> List[Dict]:
        """
        处理K线数据
        
        Args:
            address: 代币地址
            data_list: K线数据列表
            
        Returns:
            List[Dict]: 处理后的数据列表
        """
        processed_data = []
        
        try:
            for item in data_list:
                # K线数据格式：[timestamp, open, high, low, close, volume]
                if len(item) < 6:
                    logger.warning(f"数据格式不正确: {item}")
                    continue
                
                timestamp = int(item[0])
                
                # 转换为datetime
                dt = datetime.datetime.fromtimestamp(timestamp / 1000)
                
                processed_item = {
                    "address": address,
                    "timestamp": timestamp,
                    "open": float(item[1]),
                    "high": float(item[2]),
                    "low": float(item[3]),
                    "close": float(item[4]),
                    "volume": float(item[5]) if len(item) > 5 else 0,
                    "datetime": dt
                }
                
                processed_data.append(processed_item)
                
            logger.debug(f"处理了 {len(processed_data)} 条K线数据")
            return processed_data
            
        except Exception as e:
            logger.error(f"处理K线数据时出错: {e}")
            logger.error(traceback.format_exc())
            return []

    def save_to_postgresql(self, data_list: List[Dict]):
        """
        保存数据到PostgreSQL
        
        Args:
            data_list: 处理后的数据列表
        """
        if not data_list:
            return
            
        try:
            # 准备批量插入的数据
            values = [(
                item["address"],
                item["timestamp"],
                item["open"],
                item["high"],
                item["low"],
                item["close"],
                item["volume"],
                item["datetime"]
            ) for item in data_list]
            
            # 批量插入
            execute_batch(
                self.pg_cursor,
                """
                INSERT INTO meme_price_history 
                (address, timestamp, open, high, low, close, volume, datetime)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (address, timestamp) DO UPDATE 
                SET open = EXCLUDED.open,
                    high = EXCLUDED.high,
                    low = EXCLUDED.low,
                    close = EXCLUDED.close,
                    volume = EXCLUDED.volume,
                    datetime = EXCLUDED.datetime
                """,
                values,
                page_size=100
            )
            
            # 提交事务
            self.pg_conn.commit()
            
            logger.info(f"成功保存 {len(data_list)} 条数据到PostgreSQL")
            
        except Exception as e:
            logger.error(f"保存数据到PostgreSQL时出错: {e}")
            logger.error(traceback.format_exc())
            # 回滚事务
            self.pg_conn.rollback()

    def timestamp_to_str(self, timestamp: int) -> str:
        """
        将时间戳转换为可读字符串
        
        Args:
            timestamp: 毫秒时间戳
            
        Returns:
            str: 格式化的时间字符串
        """
        try:
            dt = datetime.datetime.fromtimestamp(timestamp / 1000)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except Exception:
            return str(timestamp)

    def monitor_progress(self):
        """监控进度的线程"""
        try:
            last_count = 0
            start_time = time.time()
            
            while self.running and not self.shutdown_event.is_set():
                try:
                    # 获取当前完成数量
                    current_count = int(self.redis_checkpoint.get(REDIS_KEY_NUMBER) or 0)
                    
                    # 计算速度
                    elapsed = time.time() - start_time
                    if elapsed > 0:
                        speed = current_count / elapsed
                    else:
                        speed = 0
                    
                    # 计算增量
                    increment = current_count - last_count
                    last_count = current_count
                    
                    # 获取队列大小
                    queue_size = self.task_queue.qsize()
                    
                    # 输出进度
                    logger.info(f"进度: 已完成 {current_count} 个地址, 增量: {increment}, 速度: {speed:.2f} 地址/秒, 队列: {queue_size}")
                    
                    # 每60秒报告一次
                    time.sleep(60)
                    
                except Exception as e:
                    logger.error(f"监控进度时出错: {e}")
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"监控线程异常: {e}")
            logger.error(traceback.format_exc())

    def get_statistics(self) -> Dict:
        """
        获取爬虫运行的统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            # 获取完成数量
            completed_count = int(self.redis_checkpoint.get(REDIS_KEY_NUMBER) or 0)
            
            # 获取失败数量
            failed_count = self.redis_checkpoint.scard(REDIS_KEY_FAILED)
            
            # 获取正在处理的数量
            processing_count = self.redis_checkpoint.hlen(REDIS_KEY_PROCESSING)
            
            return {
                "completed": completed_count,
                "failed": failed_count,
                "processing": processing_count
            }
            
        except Exception as e:
            logger.error(f"获取统计信息时出错: {e}")
            return {"error": str(e)}

    def cleanup(self):
        """清理资源"""
        try:
            # 关闭PostgreSQL连接
            if hasattr(self, 'pg_cursor') and self.pg_cursor:
                self.pg_cursor.close()
                
            if hasattr(self, 'pg_conn') and self.pg_conn:
                self.pg_conn.close()
                
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")
            logger.error(traceback.format_exc())
            
    def handle_shutdown(self, signum, frame):
        """处理关闭信号"""
        logger.warning(f"收到信号 {signum}，准备关闭...")
        self.running = False
        self.shutdown_event.set()
        
        # 保存当前进度
        logger.info("正在保存当前进度...")
        
        # 等待所有任务完成
        try:
            self.task_queue.join(timeout=10)
            logger.info("所有任务已完成")
        except Exception:
            logger.warning("等待任务完成超时")
            
        # 清理资源
        self.cleanup()

    def get_failed_addresses(self) -> List[str]:
        """
        从Redis中获取失败的地址列表
        
        Returns:
            List[str]: 失败的地址列表
        """
        try:
            # 获取所有失败的地址
            failed_addresses = self.redis_checkpoint.smembers(REDIS_KEY_FAILED)
            # 将bytes转换为字符串
            addresses = [addr.decode('utf-8') for addr in failed_addresses]
            logger.info(f"从Redis获取到 {len(addresses)} 个失败的地址")
            return addresses
        except Exception as e:
            logger.error(f"从Redis获取失败地址列表时出错: {e}")
            logger.error(traceback.format_exc())
            return []

    def remove_failed_address(self, address: str):
        """
        从失败地址集合中移除指定地址
        
        Args:
            address: 要移除的地址
        """
        try:
            self.redis_checkpoint.srem(REDIS_KEY_FAILED, address)
            logger.info(f"从失败地址集合中移除地址: {address}")
        except Exception as e:
            logger.error(f"移除失败地址时出错: {address}, 错误: {e}")
            logger.error(traceback.format_exc())

    def handle_request_failure(self, address: str, error_msg: str):
        """
        处理请求失败的情况 - 对于状态码不是200的情况
        
        Args:
            address: 代币地址
            error_msg: 错误信息
        """
        try:
            # 发送飞书通知
            error_message = f"\naddress处理失败\n地址: {address}\n错误信息: {error_msg}"
            self.notifier.send_to_fs(error_message)
            
            logger.error(f"地址 {address} 处理失败，已发送通知")
        except Exception as e:
            logger.error(f"处理失败地址时发生错误: {e}")
            logger.error(traceback.format_exc())

    def fetch_data(self, address: str, after_time: int, thread_id: int) -> Tuple[List, int, bool]:
        """
        获取指定地址和时间的K线数据
        
        Returns:
            Tuple[List, int, bool]: (数据列表, 下一个时间戳, 是否成功)
        """
        retry_count = 0
        last_error = None
        
        while retry_count < MAX_RETRIES:
            try:
                # 为当前线程分配代理
                proxy_index = thread_id % len(self.proxy_list)
                proxy = self.proxy_list[proxy_index]
                
                # 设置代理
                if proxy:
                    proxy_url = f"http://{proxy}"
                    proxies = {
                        "http": proxy_url,
                        "https": proxy_url
                    }
                    logger.debug(f"使用代理: {proxy_url} 请求地址: {address}")
                else:
                    proxies = {}
                    logger.debug(f"使用直连模式请求地址: {address}")
                
                # 动态修改referer
                headers = self.headers.copy()
                headers["referer"] = f"https://web3.okx.com/zh-hans/token/solana/{address}"
                
                # 构造请求参数
                params = self.base_params.copy()
                params["address"] = address
                params["after"] = str(after_time)
                params["t"] = str(int(time.time() * 1000))
                
                # 发送请求
                response = requests.get(
                    self.url, 
                    headers=headers, 
                    cookies=self.cookies, 
                    params=params, 
                    proxies=proxies,
                    timeout=10
                )
                
                # 检查响应状态
                if response.status_code != 200:
                    error_msg = f"请求失败，状态码: {response.status_code}, 响应: {response.text}"
                    logger.warning(f"第 {retry_count + 1} 次重试: {error_msg}")
                    last_error = error_msg
                    
                    # 根据重试次数设置等待时间
                    wait_time = random.uniform(5, 7) if retry_count >= 2 else random.uniform(3, 5)
                    time.sleep(wait_time)
                    retry_count += 1
                    continue
                
                # 解析响应数据
                result = response.json()
                
                # 检查是否返回特定错误码51001（表示没有数据，不是真正的错误）
                if result.get("code") == "51001" and "Instrument ID or Spread ID doesn't exist" in result.get("msg", ""):
                    logger.info(f"地址 {address} 没有历史数据（API返回51001），这是正常情况")
                    return [], after_time, True
                
                if result.get("code") != "0":
                    error_msg = f"API返回错误: {result}"
                    logger.warning(f"第 {retry_count + 1} 次重试: {error_msg}")
                    last_error = error_msg
                    wait_time = random.uniform(5, 7) if retry_count >= 2 else random.uniform(3, 5)
                    time.sleep(wait_time)
                    retry_count += 1
                    continue
                
                data_list = result.get("data", [])
                
                # 如果没有数据，也认为是成功的情况
                if not data_list:
                    logger.warning(f"线程ID: {thread_id}, 地址: {address}, 没有获取到数据，时间: {self.timestamp_to_str(after_time)}")
                    return [], after_time, True
                
                logger.info(f"线程ID: {thread_id}, {'代理: '+proxy if proxy else '直连'}, 地址: {address}, 成功获取 {len(data_list)} 条数据")
                
                # 返回数据列表和最后一条数据的时间戳
                next_timestamp = int(data_list[-1][0])
                return data_list, next_timestamp, True
                
            except Exception as e:
                error_msg = f"请求异常: {str(e)}"
                logger.warning(f"第 {retry_count + 1} 次重试: {error_msg}")
                last_error = error_msg
                wait_time = random.uniform(5, 7) if retry_count >= 2 else random.uniform(3, 5)
                time.sleep(wait_time)
                retry_count += 1
        
        # 如果达到最大重试次数仍然失败
        logger.error(f"地址 {address} 达到最大重试次数 {MAX_RETRIES}，放弃处理")
        self.handle_request_failure(address, last_error or "未知错误")
        return [], after_time, False

    def worker(self, thread_id: int):
        """工作线程"""
        logger.info(f"线程 {thread_id} 启动")
        
        while self.running and not self.task_queue.empty():
            try:
                # 获取任务
                task = self.task_queue.get(block=False)
                address = task["address"]
                after_time = task["after_time"]
                is_first_request = task.get("is_first_request", False)
                
                # 如果时间戳已经早于结束时间，则跳过
                if after_time <= END_TIMESTAMP:
                    logger.info(f"线程 {thread_id}, 地址 {address} 已达到目标时间范围，跳过")
                    self.update_address_progress(address, after_time, "completed")
                    self.remove_failed_address(address)  # 移除失败地址
                    self.task_queue.task_done()
                    continue
                
                # 获取数据
                data_list, next_timestamp, success = self.fetch_data(address, after_time, thread_id)
                
                if success:  # 如果请求成功（包括返回空数据的情况）
                    if data_list:
                        # 处理数据
                        processed_data = self.process_kline_data(address, data_list)
                        
                        # 保存到PostgreSQL
                        if processed_data:
                            self.save_to_postgresql(processed_data)
                        
                        # 更新爬取进度
                        self.update_address_progress(address, next_timestamp)
                        
                        # 如果还没有达到目标时间范围，继续添加任务
                        if next_timestamp > END_TIMESTAMP:
                            next_task = {
                                "address": address,
                                "after_time": next_timestamp,
                                "is_first_request": False
                            }
                            time.sleep(3.5)
                            self.task_queue.put(next_task)
                            logger.info(f"线程 {thread_id}, 地址 {address} 添加下一个任务，时间戳: {next_timestamp}")
                        else:
                            # 已达到目标时间范围，标记为完成
                            self.update_address_progress(address, next_timestamp, "completed")
                            self.remove_failed_address(address)  # 移除失败地址
                            logger.info(f"线程 {thread_id}, 地址 {address} 已完成爬取，达到目标时间范围")
                    else:
                        # 没有数据也视为成功，移除失败地址
                        if not is_first_request:
                            self.update_address_progress(address, after_time, "completed")
                            self.remove_failed_address(address)
                            logger.info(f"线程 {thread_id}, 地址 {address} 没有更多数据，标记为完成")
                        else:
                            self.update_address_progress(address, END_TIMESTAMP, "completed")
                            self.remove_failed_address(address)
                            logger.info(f"线程 {thread_id}, 地址 {address} 首次请求没有数据，标记为完成")
                
                # 标记任务完成
                self.task_queue.task_done()
                
            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"线程 {thread_id} 异常: {e}")
                logger.error(traceback.format_exc())
                self.task_queue.task_done()
        
        logger.info(f"线程 {thread_id} 结束")

    def run(self):
        """运行爬虫"""
        try:
            logger.info("开始运行爬虫")
            logger.info(f"目标时间范围: {self.timestamp_to_str(START_TIMESTAMP)} - {self.timestamp_to_str(END_TIMESTAMP)}")
            
            # 从Redis获取失败的地址列表
            self.token_addresses = self.get_failed_addresses()
            
            if not self.token_addresses:
                logger.error("没有获取到失败的地址，无法生成任务")
                return
            
            total_addresses = len(self.token_addresses)
            logger.info(f"获取到 {total_addresses} 个失败的地址")
            
            # 创建线程池
            logger.info("创建线程池...")
            with ThreadPoolExecutor(max_workers=self.thread_num) as executor:
                # 提交任务监控线程
                monitor_future = executor.submit(self.monitor_progress)
                
                # 分批处理地址
                batch_size = 5000
                total_batches = (total_addresses + batch_size - 1) // batch_size
                
                for batch_idx in range(total_batches):
                    batch_start = batch_idx * batch_size
                    batch_end = min(batch_start + batch_size, total_addresses)
                    batch_addresses = self.token_addresses[batch_start:batch_end]
                    
                    # 为这批地址生成任务
                    for address in batch_addresses:
                        # 构造任务
                        task = {
                            "address": address,
                            "after_time": START_TIMESTAMP,
                            "is_first_request": True
                        }
                        
                        # 将任务加入队列
                        self.task_queue.put(task)
                    
                    # 报告批次进度
                    progress = (batch_idx + 1) / total_batches * 100
                    logger.info(f"批次 {batch_idx+1}/{total_batches} ({progress:.1f}%) 完成")
                    
                    # 第一批任务生成后，启动工作线程
                    if batch_idx == 0:
                        logger.info(f"第一批任务生成完成，启动 {self.thread_num} 个工作线程...")
                        worker_futures = [executor.submit(self.worker, i) for i in range(self.thread_num)]
                
                # 等待所有工作线程完成
                logger.info("所有任务已生成，等待工作线程完成...")
                for future in worker_futures:
                    future.result()
                
                # 取消监控线程
                monitor_future.cancel()
            
            # 获取统计信息
            stats = self.get_statistics()
            logger.info(f"爬虫运行完成，统计信息: {stats}")
            
        except Exception as e:
            logger.error(f"爬虫运行异常: {e}")
            logger.error(traceback.format_exc())
        finally:
            self.cleanup()

if __name__ == "__main__":
    try:
        # 创建爬虫实例
        spider = MemeFailedSpider(
            thread_num=20,
            interval="1m"
        )
        
        # 运行爬虫
        spider.run()
    except KeyboardInterrupt:
        logger.warning("收到键盘中断信号")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        sys.exit(0)
