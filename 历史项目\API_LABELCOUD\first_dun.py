import redis
import requests
import time
import json
from datetime import date
from loguru import logger
import re

crawl_date = date.today()

redis_client = redis.Redis(
    host='**************',
    port=6379,
    db=14,  # 14为测试库，原库为6
    password='123456',
    decode_responses=True
)
# 代理设置
# proxy_yj = {
#     "http": "*************************************************",
#     "https": "*************************************************"
# }

proxy_yj = {
    "http": "*************************************************"
}

'''
yuan: "proxy": "*************************************************",
'''


def create_task_for_cookie():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}

    data_task = {
        "clientKey": "a4eb359df78983d82fdbe816f4c699829cddf67856298",
        "task": {
            "type": "CloudFlareTaskS2",
            "websiteURL": "https://optimistic.etherscan.io/login",
            "proxy": "*************************************************",
            "waitLoad": True,
            "requiredCookies": ["cf_clearance"]
        }
    }
    response = requests.post(url, headers=headers, json=data_task)
    response_json = response.json()
    if response_json['errorId'] == 0:
        task_id_for_cookie = response_json['taskId']
        logger.info(f"Task ID: {task_id_for_cookie}")
        return task_id_for_cookie
    else:
        logger.info(f"Error: {response_json['errorDescription']}")
        return None


def get_task_result_for_cookie(task_id_for_cookie):
    logger.info('等待获取cookie等信息')
    time.sleep(5)
    logger.info('正在获取cookie等信息')
    url_result = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data_result = {
        "clientKey": 'a4eb359df78983d82fdbe816f4c699829cddf67856298',
        "taskId": task_id_for_cookie
    }

    response = requests.post(url_result, headers=headers, json=data_result)
    result = response.json()
    for i in range(40):
        if result['errorId'] == 0:
            if result['status'] == 'ready':
                logger.info("Task is ready. Solution:", result['solution'])
                logger.info("User Agent:", result['solution'].get('user_agent', 'No user agent provided'))
                # print("Cookies:", result['solution'].get('cookies', 'No cookies provided'))
                # print("Request Headers:", result['solution'].get('request_headers', 'No request headers provided'))
                # print("Headers:", result['solution'].get('headers', 'No headers provided'))
                # print("Content:", result['solution'].get('content', 'No content provided'))

                cookies = result['solution'].get('cookies', {})
                # required_cookies = ["__stripe_mid","_gid","__cflb","cf_clearance","ASP.NET_SessionId"]
                # filtered_cookies = {name: value for name, value in cookies.items() if name in required_cookies}
                # print("Filtered cookies:", filtered_cookies)
                redis_client.set('API_optimistic_cookie_for_5s', json.dumps(cookies))
                logger.info("Filtered cookies have been saved to Redis")
                user_agent = result['solution'].get('user_agent', {})
                user_agent_with_prefix = f"User-Agent:{user_agent}"
                redis_client.set("API_user_agent_for_5s", user_agent_with_prefix)
                logger.info("User-Agent have been saved to Redis")
                return result

            elif result['status'] == 'processing':
                logger.info(f'retry to get cookie, num: {i}')
                time.sleep(3)
                response = requests.post(url_result, headers=headers, json=data_result)
                result = response.json()
                continue
            else:
                logger.info("Task is not ready yet. Status:", result['status'])
                return result
        else:
            logger.info("Error in getting task result:", result['errorDescription'])
            return result

    logger.info("Max retries reached. Task is still processing.")
    return None


def create_task_for_token():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": "a4eb359df78983d82fdbe816f4c699829cddf67856298",
        "task": {
            "type": "TurnstileTaskProxyless",
            "websiteURL": "https://optimistic.etherscan.io/myaccount",
            "websiteKey": "0x4AAAAAAAEa1DD36OluMD6w"
        }
    }
    response = requests.post(url, headers=headers, json=data)
    response_json = response.json()
    if response_json['errorId'] == 0:
        task_id_for_tooken = response_json['taskId']
        logger.info(f"Task ID: {task_id_for_tooken}")
        logger.info('已获取到task_id_for_token')
        return task_id_for_tooken
    else:
        logger.info(f"Error: {response_json['errorDescription']}")
        return None


def get_task_result_for_token(task_id_for_tooken):
    time.sleep(3)
    logger.info('开始获取token')
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": 'a4eb359df78983d82fdbe816f4c699829cddf67856298',
        "taskId": task_id_for_tooken
    }

    response = requests.post(url, headers=headers, json=data)
    token_result = response.json()
    for i in range(40):
        if token_result['errorId'] == 0:
            if token_result['status'] == 'ready':
                # print("Task is ready. Solution:", token_result['solution'])
                token = token_result['solution']['token']
                redis_client.set('token_for_yzm', token)
                logger.info("Token has been saved to Redis.")
                return token_result
            elif token_result['status'] == 'processing':
                logger.info(f'retry to get tooken, num: {i}')
                time.sleep(3)
                response = requests.post(url, headers=headers, json=data)
                token_result = response.json()
                continue
            else:
                logger.info("Task is not ready yet. Status:", token_result['status'])
                return token_result
        else:
            logger.info("Error in getting task result:", token_result['errorDescription'])
            return token_result

    logger.info("Max retries reached. Task is still processing.")
    return None


@staticmethod
def check_login_status(text):
    """ 检查登录状态 """

    login_status: bool
    error_msg: str = ''
    if 'Sign In for Continued Access' in text:
        login_status = False
        error_msg = '登录失败, 已过掉cloudflare检测, 但未登录成功!'
    elif 'OP Mainnet Top Accounts by ETH Balance' in text:
        login_status = True
        logger.info('检测登录-成功')
    else:
        login_status = False
        error_msg = '登录失败, 没有过掉cloudflare检测'
    return login_status, error_msg


def get_cookies_ua_from_redis():
    cookies_json = redis_client.get('API_optimistic_cookie_for_5s')
    logger.info(f'已经拿到通过5s盾的cookie: {cookies_json}')

    ua = redis_client.get('API_user_agent_for_5s')
    logger.info(f'已经拿到通过5s盾的user-agent: {ua}')

    token_for_5s = redis_client.get('token_for_yzm')
    logger.info(f'已经拿到token: {token_for_5s}')

    cookies = json.loads(cookies_json) if cookies_json else None
    ua = ua if ua else None
    token = token_for_5s if token_for_5s else None

    headers = {
        'User-Agent': ua if ua else 'default_user_agent'
    }

    if cookies is None or ua is None or token is None:
        logger.info("未能获取到必要的cookie、user-agent或token，退出程序。")
        return None, None, None

    return cookies, headers, token


def login_for_5s():
    cookies, headers, token = get_cookies_ua_from_redis()
    if not cookies or not headers or not token:
        logger.info("未能获取到必要的cookie、user-agent或token,退出程序。")
        return
    logger.info('正在拿取载荷参数')
    login_url = "https://optimistic.etherscan.io/login"
    resp1 = requests.get(login_url, headers=headers, cookies=cookies, proxies=proxy_yj)
    time.sleep(1)
    if resp1.status_code != 200:
        logger.info(f'获取登录页面失败，状态码：{resp1.status_code}')
        logger.info(f'{resp1.text}')
        return

    viewstate = re.search(r'__VIEWSTATE" value="(.*?)"', resp1.text)
    viewstate_generator = re.search(r'__VIEWSTATEGENERATOR" value="(.*?)"', resp1.text)
    event_validation = re.search(r'__EVENTVALIDATION" value="(.*?)"', resp1.text)
    # cf_turnstile_response = re.search(r'cf-turnstile-response" value="(.*?)"', resp1.text)

    if viewstate and viewstate_generator and event_validation:
        post_data = {
            "__VIEWSTATE": viewstate.group(1),
            "__VIEWSTATEGENERATOR": viewstate_generator.group(1),
            "__EVENTVALIDATION": event_validation.group(1),
            "ctl00$ContentPlaceHolder1$txtUserName": "Krickliu",
            "ctl00$ContentPlaceHolder1$txtPassword": "DOMYBEST0922",
            "cf-turnstile-response": token,
            "ctl00$ContentPlaceHolder1$btnLogin": "LOGIN"
        }
        url = "https://optimistic.etherscan.io/login"
        logger.info('尝试登录....')
        resp = requests.post(url=url, headers=headers, data=post_data, cookies=cookies,
                             proxies=proxy_yj, timeout=10)
        logger.info(resp.text)
        if resp.status_code != 200:
            logger.info(f'登录请求失败，状态码：{resp.status_code}，退出程序。')
            return
        logger.info('登录成功！')
    else:
        logger.info("未能提取到必要的载荷参数，退出程序。")



if __name__ == '__main__':

    task_id_for_cookie = create_task_for_cookie()
    if task_id_for_cookie:
        get_task_result_for_cookie(task_id_for_cookie)

    task_id_for_tooken = create_task_for_token()
    if task_id_for_tooken:
        get_task_result_for_token(task_id_for_tooken)

    session = requests.Session()
    cookies, headers, token = get_cookies_ua_from_redis()
    login_for_5s()