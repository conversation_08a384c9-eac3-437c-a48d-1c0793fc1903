from curl_cffi import requests
import json

proxies = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=20250512-954-8c75333,sentry-public_key=93c25bab7246077dc3eb85b59d6e7d40,sentry-trace_id=d3705d6ccaa8419d974997d2b7f3bad3,sentry-sample_rate=0.01,sentry-sampled=false",
    "priority": "u=1, i",
    "referer": "https://gmgn.ai/sol/token/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "d3705d6ccaa8419d974997d2b7f3bad3-8fd2e66914cc0b66-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
}
cookies = {
    "_ga": "GA1.1.1787124478.1747114971",
    "GMGN_LOCALE": "zh-CN",
    "GMGN_CHAIN": "sol",
    "GMGN_THEME": "dark",
    "__cf_bm": "rlQlq_NKNs_WfZIiIzEqJcMgU8mAwXjmgZcaUKFe0eg-1747117682-*******-Y2ZHbhHWAiby8cAYN1KO6yahRtoHMLjjeZBe6uOn4mGhJmyv4YXByuKFSoY_Nr9VO6vkpJhJ4hBPPv9IsMO9DFkI1BuxUunQ6TrS0XUQurc",
    "cf_clearance": "m3vbYM8zlrKb5FsY_BbopiA5YEP96HTX0SWu9mCq7oM-1747118140-*******-X9KIo9Ffq69dsU.DG.4VtU5YxOJZCV7r6spL1MIy_wPGXEtQbaPB5Y.ZU6bM9sMYy_.HRX4.BJJE7k9vur158EOK7kEJP0T85HlhpELsVeRazYfeELe4kViwe2UavnmqBIvuxBb44MnEMiQarJeKXtA2rve.2p2Imm395hgNGMtHjsWpagoyczCRsBRyMusCKBfZw8_euF48awGipNB.9TjCjYwPilL0Wy1VKqVQd2fWdCglVXO62csq.83D.0ee6EO9rRq2P_kHolUkN5xsa347Z6eoHZaN298iSA101e3CG6pYKC15X_SLpUHz6eSwyIMhRJXh9aFHtJzQn0s9xL3_lFKtTwjGjwD1TcjO6p0",
    "_ga_0XM0LYXGC8": "GS2.1.s1747118076$o2$g1$t1747118157$j0$l0$h0"
}
url = "https://gmgn.ai/api/v1/token_pool_fee_info/sol/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"
params = {
    "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
    "client_id": "gmgn_web_20250512-954-8c75333",
    "from_app": "gmgn",
    "app_ver": "20250512-954-8c75333",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a0adc6758070914b5d3cd4679349eed1",
    "os": "web",
    "from": "0",
    "to": "1747031121000",
}
response = requests.get(url, headers=headers, cookies=cookies, params=params, proxies=proxies, impersonate="chrome120")

print(response.text)
print(response)