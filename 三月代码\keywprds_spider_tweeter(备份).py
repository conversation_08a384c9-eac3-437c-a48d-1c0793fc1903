# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2025/2/11 13:45
@Python  :  Python3.7
@Desc    :  根据关键词搜索推文
"""

import requests
import json
import time
import random
import threading
import urllib.parse
from loguru import logger
from datetime import datetime
from math import ceil
from db import RedisClient
from Spiders.utils import DB_BASE
from concurrent.futures import ThreadPoolExecutor


class XSpider(DB_BASE):

    def __init__(self, max_pages=None, start_time=None):
        super().__init__()
        self.max_pages = max_pages
        self.start_time = start_time
        self.redis_client = RedisClient(redis_name='cookies:twitter_LJY_local')
        self.redis_client_keywords = RedisClient(redis_name='keywords:tweet_keywords')
        self.redis_processed_keywords = RedisClient(redis_name='keywords:processed_keywords')
        self.redis_comment_wait_spider = RedisClient(redis_name='twitter:comment_wait_spider')
        self.base_params = {
            "variables": "{\"rawQuery\":\"\",\"count\":20,\"querySource\":\"typed_query\",\"product\":\"Top\"}",
            "features": "{\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"responsive_web_graphql_exclude_directive_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"rweb_video_timestamps_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":true,\"responsive_web_enhance_cards_enabled\":false}"
        }

    def get_cookie_from_redis(self):
        """从Redis中获取cookie信息"""
        try:
            account_name, cookie_json = self.redis_client.get_random_account()

            if not account_name or not cookie_json:
                logger.error("无法从Redis获取cookie信息：'cookies:twitter'哈希表为空")
                return {}
            logger.info(f"成功提取账号 {account_name} 的cookie信息")

            try:
                cookie_data = json.loads(cookie_json)
                if not isinstance(cookie_data, dict):
                    logger.error("从Redis获取的cookie信息不是有效的字典格式")
                    return {}
                return cookie_data
            except json.JSONDecodeError as e:
                logger.error(f"从Redis获取的cookie信息不是有效的JSON格式: {e}")
                return {}
        except Exception as e:
            logger.error(f"从Redis获取cookie信息失败: {e}")
            return {}


    def get_keywords_from_redis(self):
        """从Redis中获取关键词列表"""
        try:
            keywords = self.redis_client_keywords.all()
            if not keywords:
                logger.error("Redis中没有找到关键词")
                return []
            return [keyword for keyword in keywords.values()]
        except Exception as e:
            logger.error(f"从Redis获取关键词失败: {e}")
            return []


    def get_thread_cookie(self):
        """为线程获取独立的cookie"""
        cookie_data = self.get_cookie_from_redis()
        headers = {
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "accept-language": "zh-CN,zh;q=0.9",
            "referer": "",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": cookie_data.get('csrf_token', '')
        }
        cookies = {
            "auth_token": cookie_data.get('auth_token', ''),
            "ct0": cookie_data.get('csrf_token', '')
        }
        proxy = {
            "http": cookie_data.get('proxy_ip', ''),
            "https": cookie_data.get('proxy_ip', '')
        }
        return headers, cookies, proxy

    def is_keyword_processed(self, keyword):
        """检查关键词是否已处理"""
        try:
            return bool(self.redis_processed_keywords.get(keyword))
        except Exception as e:
            logger.error(f"检查关键词处理状态失败: {e}")
            return False


    def mark_keyword_as_processed(self, keyword):
        """将关键词标记为已处理"""
        try:
            self.redis_processed_keywords.set(keyword, '1')
            logger.success(f"已将关键词 {keyword} 标记为处理完成")
        except Exception as e:
            logger.error(f"标记关键词处理状态失败: {e}")

    # def clear_processed_keywords(self):
    #     """清除已处理的关键词记录"""
    #     try:
    #         self.redis_processed_keywords.clear()
    #         logger.success("已清除所有已处理的关键词记录")
    #     except Exception as e:
    #         logger.error(f"清除已处理关键词记录失败: {e}")


    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间格式到标准格式"""
        try:
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}")
            return None


    def fetch_data(self, keyword, cursor=None, headers=None, cookies=None, proxy=None):
        """发送请求获取数据"""
        url = "https://x.com/i/api/graphql/U3QTLwGF8sZCHDuWIMSAmg/SearchTimeline"

        variables = {
            "rawQuery": keyword,
            "count": 20,
            "querySource": "typed_query",
            "product": "Top"
        }
        if cursor:
            variables["cursor"] = cursor

        variables_json = json.dumps(variables, ensure_ascii=False)

        params = self.base_params.copy()
        params["variables"] = variables_json

        encoded_keyword = urllib.parse.quote(keyword)

        # 使用传入的headers或默认headers
        request_headers = headers or self.headers
        request_headers["referer"] = f"https://x.com/search?q={encoded_keyword}&src=typed_query"

        try:
            response = requests.get(
                url,
                headers=request_headers,
                cookies=cookies or self.cookies,
                params=params,
                proxies=proxy or self.proxy
            )
            response.raise_for_status()
            logger.success(f"请求成功 [{response.status_code}]")
            return response.json()
        except Exception as e:
            logger.error(f"请求失败: {e}")
            return None


    def extract_entries(self, data, keyword, start_time=None):
        """从响应数据中提取目标字段
           start_time: 起始时间，格式为'YYYY-MM-DD HH:MM:SS'，早于此时间的推文将被过滤"""
        tweet_batch = []
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get(
                'timeline', {}).get('instructions', [])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    for entry in instruction.get('entries', []):
                        try:
                            result = entry.get('content', {}).get('itemContent', {}).get('tweet_results', {}).get(
                                'result', {})
                            if not result:
                                continue

                            user_result = result.get('core', {}).get('user_results', {}).get('result', {})
                            legacy_user = user_result.get('legacy', {})

                            quoted_tweet_id = result.get('quoted_status_result', {}).get('result', {}).get('legacy', {}).get('conversation_id_str', '')
                            retweeted_tweet_id = result.get('legacy', {}).get('retweeted_status_result', {}).get('result', {}).get('rest_id', 'null')

                            label_info = {}
                            if user_result.get('affiliates_highlighted_label', {}).get('label', {}):
                                label_data = user_result.get('affiliates_highlighted_label', {}).get('label', {})
                                label_info = {
                                    'description': label_data.get('description', ''),
                                    'userLabelDisplayType': label_data.get('userLabelDisplayType', ''),
                                    'userLabelType': label_data.get('userLabelType', '')
                                }
                            else:
                                label_info = {}

                            user_data = {
                                'user_id': user_result.get('rest_id'),
                                'username': legacy_user.get('screen_name'),
                                'created_at': self.convert_twitter_time(legacy_user.get('created_at')),
                                'location': legacy_user.get('location'),
                                'followers': legacy_user.get('followers_count', 0),
                                'following': legacy_user.get('friends_count', 0),
                                'posts': legacy_user.get('statuses_count', 0),
                                'verified': 1 if legacy_user.get('verified') else 0,
                                'verified_type': user_result.get('verified_type'),
                                'label': json.dumps(label_info, ensure_ascii=False) if label_info else None,
                                'description': legacy_user.get('description', '')
                            }

                            if user_data.get('user_id'):
                                self.save_user_to_mysql(user_data)
                            else:
                                logger.warning("用户数据缺少user_id，跳过保存")

                            tweet_result = result.get('legacy', {})
                            views_count = result.get('views', {})

                            edit_control = result.get('edit_control', {})
                            editable_until_msecs = edit_control.get('editable_until_msecs')
                            editable_time = None
                            if editable_until_msecs:
                                try:
                                    editable_time = datetime.fromtimestamp(
                                        int(editable_until_msecs) / 1000
                                    ).strftime('%Y-%m-%d %H:%M:%S')
                                except Exception as e:
                                    logger.warning(f"时间转换失败: {e}")

                            if start_time and editable_time:
                                if editable_time < start_time:
                                    logger.debug(f"推文时间 {editable_time} 早于起始时间 {start_time}，跳过保存")
                                    continue

                            username = legacy_user.get('screen_name')
                            tweet_id = tweet_result.get('id_str')
                            if username and tweet_id:
                                try:
                                    self.redis_comment_wait_spider.set(username, tweet_id)
                                    logger.success(f"成功保存用户推文映射 {username} -> {tweet_id} 到Redis")
                                except Exception as e:
                                    logger.error(f"保存用户推文映射到Redis失败: {e}")

                            tweet_data = {
                                'tweet_id': tweet_result.get('id_str'),
                                'user_id': user_data.get('user_id'),
                                'keyword': keyword,
                                'tweet_text': tweet_result.get('full_text', ''),
                                'tweet_time': editable_time,
                                'views': views_count.get('count', 0),
                                'replies': tweet_result.get('reply_count', 0),
                                'retweets': tweet_result.get('retweet_count', 0),
                                'favorites': tweet_result.get('favorite_count', 0),
                                'bookmarks': tweet_result.get('bookmark_count', 0),
                                'quoted_tweet_id': quoted_tweet_id,
                                'retweeted_tweet_id': retweeted_tweet_id
                            }

                            if tweet_data.get('tweet_id'):
                                self.save_tweet_to_mysql(tweet_data)
                                tweet_batch.append(tweet_data)
                            else:
                                logger.warning("推文数据缺少tweet_id，跳过保存")

                        except Exception as e:
                            logger.error(f"处理单条数据时出错: {e}")
                            continue

            return tweet_batch
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return []

    def save_user_to_mysql(self, data):
        """将用户数据保存到MySQL"""
        try:
            if not data.get('user_id'):
                logger.warning(f"缺少user_id，无法保存用户数据")
                return

            username = data.get('username', '')
            if username and not username.startswith('@'):
                username = f"@{username}"

            sql = """
            INSERT INTO users (user_id, username, created_at, location, followers, 
                            following, posts, verified, verified_type, label, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            username=COALESCE(VALUES(username), username),
            followers=COALESCE(VALUES(followers), followers),
            following=COALESCE(VALUES(following), following),
            posts=COALESCE(VALUES(posts), posts),
            verified=COALESCE(VALUES(verified), verified),
            verified_type=COALESCE(VALUES(verified_type), verified_type),
            label=COALESCE(VALUES(label), label),
            description=COALESCE(VALUES(description), description)
            """

            values = [
                data.get('user_id'),
                username,
                data.get('created_at'),
                data.get('location', ''),
                data.get('followers', 0),
                data.get('following', 0),
                data.get('posts', 0),
                data.get('verified', 0),
                data.get('verified_type', 'null'),
                data.get('label'),
                data.get('description', '')
            ]

            super().insert_mysql(sql, values)
            logger.success(f"成功保存用户 ID:{data.get('user_id')} 用户名:{username} 到数据库")
        except Exception as e:
            logger.error(f"保存用户数据失败: {e}")

    def save_tweet_to_mysql(self, data):
        """将推文数据保存到MySQL"""
        try:
            if not data.get('tweet_id'):
                logger.warning(f"缺少tweet_id，无法保存推文数据")
                return

            sql = """
            INSERT INTO tweets (tweet_id, user_id, keyword, tweet_text, tweet_time,
                            views, replies, retweets, favorites, bookmarks, quoted_tweet_id, retweeted_tweet_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            user_id=COALESCE(VALUES(user_id), user_id),
            keyword=COALESCE(VALUES(keyword), keyword),
            tweet_text=COALESCE(VALUES(tweet_text), tweet_text),
            tweet_time=COALESCE(VALUES(tweet_time), tweet_time),
            views=COALESCE(VALUES(views), views),
            replies=COALESCE(VALUES(replies), replies),
            retweets=COALESCE(VALUES(retweets), retweets),
            favorites=COALESCE(VALUES(favorites), favorites),
            bookmarks=COALESCE(VALUES(bookmarks), bookmarks),
            quoted_tweet_id=COALESCE(VALUES(quoted_tweet_id), quoted_tweet_id),
            retweeted_tweet_id=COALESCE(VALUES(quoted_tweet_id), quoted_tweet_id)
            """

            values = [
                data.get('tweet_id'),
                data.get('user_id', ''),
                data.get('keyword', ''),
                data.get('tweet_text', ''),
                data.get('tweet_time'),
                data.get('views', 0),
                data.get('replies', 0),
                data.get('retweets', 0),
                data.get('favorites', 0),
                data.get('bookmarks', 0),
                data.get('quoted_tweet_id', ''),
                data.get(('retweeted_tweet_id', ''),)
            ]

            super().insert_mysql(sql, values)
            logger.success(f"成功保存推文 ID:{data.get('tweet_id')} 到数据库")
        except Exception as e:
            logger.error(f"保存推文数据失败: {e}")

    def get_next_cursor(self, data):
        """翻页逻辑"""
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get(
                'timeline', {}).get('instructions', [])

            for instruction in instructions:
                entries = []
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                elif instruction.get('type') == 'TimelineReplaceEntry':
                    entry = instruction.get('entry')
                    if entry:
                        entries = [entry]

                logger.debug(f"处理 {instruction.get('type')} 指令，发现 {len(entries)} 个条目")

                for entry in entries:
                    entry_id = entry.get('entryId')
                    if entry_id == 'cursor-bottom-0' or (isinstance(entry_id, str) and 'cursor-bottom' in entry_id):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineCursor':
                            cursor_value = content.get('value')
                            if cursor_value:
                                logger.success(f"成功提取游标: {cursor_value[:30]}...")
                                return cursor_value
            logger.warning("未找到有效游标")
            return None
        except Exception as e:
            logger.error(f"游标解析异常: {str(e)}")
            return None

    def process_keywords(self, keywords, thread_id):
        """处理关键词列表"""
        # 为当前线程获取独立的cookie和headers
        thread_headers, thread_cookies, thread_proxy = self.get_thread_cookie()
        logger.info(f"线程 {thread_id} 已获取独立cookie")

        for keyword in keywords:
            # 检查关键词是否已处理
            if self.is_keyword_processed(keyword):
                logger.info(f"关键词 {keyword} 已处理，跳过")
                continue

            logger.debug(f'线程 {thread_id} 开始处理关键词: {keyword}')
            next_cursor = None
            page = 1
            max_retry = 3

            try:
                while True:
                    # 页数限制检查
                    if self.max_pages and page > self.max_pages:
                        logger.info(f"已达到设置的最大页数 {self.max_pages}，停止翻页")
                        break

                    logger.info(f"正在爬取第 {page} 页...")
                    data = None

                    # 带重试的请求
                    for attempt in range(max_retry):
                        # 使用线程独立的cookie和headers发送请求
                        data = self.fetch_data(keyword, cursor=next_cursor, headers=thread_headers, cookies=thread_cookies, proxy=thread_proxy)

                        if data:
                            break
                        logger.warning(f"线程 {thread_id} 第 {attempt + 1} 次重试...")
                        if attempt == max_retry - 1:
                            logger.info(f"线程 {thread_id} 尝试更换新的cookie")
                            thread_headers, thread_cookies, thread_proxy = self.get_thread_cookie()
                        time.sleep(random.uniform(18, 30))

                    if not data:
                        logger.error("请求失败，停止翻页")
                        break

                    # 数据提取和保存
                    extracted = self.extract_entries(data, keyword, self.start_time)
                    if not extracted:
                        logger.warning("未提取到有效数据")

                    # 获取下一页游标
                    new_cursor = self.get_next_cursor(data)

                    # 更新循环终止条件
                    if not extracted or (self.max_pages and page >= self.max_pages):
                        logger.info("已到达最后一页或达到设置的最大页数")
                        break

                    # 更新游标并继续
                    next_cursor = new_cursor
                    page += 1

                    # 随机等待
                    wait_time = random.uniform(30, 90)
                    logger.info(f"等待 {wait_time:.1f} 秒后继续")
                    time.sleep(wait_time)

                self.mark_keyword_as_processed(keyword)
                logger.success(f"完成关键词 {keyword} 的采集")

            except Exception as e:
                logger.error(f"处理关键词 {keyword} 时发生错误: {e}")
                continue

    def run_with_threads(self, num_threads=2):
        """使用多线程运行爬虫"""
        try:
            keywords = self.get_keywords_from_redis()
            if not keywords:
                logger.error("未从Redis中获取到关键词，程序终止")
                return

            keywords_per_thread = ceil(len(keywords) / num_threads)
            keyword_chunks = [
                keywords[i:i + keywords_per_thread]
                for i in range(0, len(keywords), keywords_per_thread)
            ]

            logger.info(f"总关键词数: {len(keywords)}, 每个线程处理: {keywords_per_thread}")

            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = []
                for i, chunk in enumerate(keyword_chunks):
                    thread_id = i + 1
                    logger.info(f"线程 {thread_id} 分配到 {len(chunk)} 个关键词")
                    # 传入thread_id参数
                    future = executor.submit(self.process_keywords, chunk, thread_id)
                    futures.append(future)

                for i, future in enumerate(futures):
                    try:
                        future.result()
                        logger.success(f"线程 {i + 1} 完成任务")
                    except Exception as e:
                        logger.error(f"线程 {i + 1} 发生错误: {e}")

        except Exception as e:
            logger.error(f"多线程运行时发生错误: {e}")
        finally:
            logger.info("所有线程任务已结束")

    def run(self):
        """保留原有的单线程运行方法"""
        self.process_keywords(self.get_keywords_from_redis())

    # def run(self):
    #     """主运行方法"""
    #     keywords = self.get_keywords_from_redis()
    #     if not keywords:
    #         logger.error("未从Redis中获取到关键词，程序终止")
    #         return
    #
    #     for keyword in keywords:
    #         logger.debug(f'开始处理关键词:{keyword}')
    #         next_cursor = None
    #         page = 1
    #         max_retry = 3
    #
    #         try:
    #             while True:
    #                 # 页数限制检查
    #                 if self.max_pages and page > self.max_pages:
    #                     logger.info(f"已达到设置的最大页数 {self.max_pages}，停止翻页")
    #                     break
    #
    #                 logger.info(f"正在爬取第 {page} 页...")
    #                 data = None
    #
    #                 # 带重试的请求
    #                 for attempt in range(max_retry):
    #                     data = self.fetch_data(keyword, next_cursor)
    #                     if data:
    #                         break
    #                     logger.warning(f"第 {attempt + 1} 次重试...")
    #                     time.sleep(random.uniform(18, 30))
    #
    #                 if not data:
    #                     logger.error("请求失败，停止翻页")
    #                     break
    #
    #                 # 数据提取和保存
    #                 extracted = self.extract_entries(data, keyword, self.start_time)
    #                 if not extracted:
    #                     logger.warning("未提取到有效数据")
    #
    #                 # 获取下一页游标
    #                 new_cursor = self.get_next_cursor(data)
    #
    #                 # 更新循环终止条件
    #                 if not extracted or (self.max_pages and page >= self.max_pages):
    #                     logger.info("已到达最后一页或达到设置的最大页数")
    #                     break
    #
    #                     # 更新游标并继续
    #                 next_cursor = new_cursor
    #                 page += 1
    #
    #                 # 随机等待
    #                 wait_time = random.uniform(30, 80)
    #                 logger.info(f"等待 {wait_time:.1f} 秒后继续")
    #                 time.sleep(wait_time)
    #
    #             self.mark_keyword_as_processed(keyword)
    #             logger.success(f"完成关键词 {keyword} 的采集")
    #
    #         except Exception as e:
    #             logger.error(f"处理关键词 {keyword} 时发生错误: {e}")
    #             # 发生错误时不标记为已处理，下次会重试
    #             continue


if __name__ == "__main__":
    start_time = "2025-01-20 00:00:00"
    spider = XSpider(max_pages=30, start_time=start_time)
    # spider.run()
    spider.run_with_threads(num_threads=3)