"""
google reCaptcha:
    api: https://yescaptcha.atlassian.net/wiki/spaces/YESCAPTCHA/pages/4685944/reCaptcha
    后台管理：https://yescaptcha.com/dashboard.html
"""
import requests
# from curl_cffi import requests
import json
import time
from loguru import logger
from lxml import etree
from Spiders import Spider


class LoginException(Exception):
    pass


class Login(Spider.Spider_Crawler_HTTP):
    def __init__(self, *args, **kwargs):
        super(Login, self).__init__(*args, **kwargs)
        # self.websiteURL = 'https://etherscan.io/login'
        self.websiteURL = 'https://optimistic.etherscan.io/login'
        self.clientKey = '86529fa1d01339f34ff2b5f77f5ff0d1622065ed56034'
        self.BASE_URL = 'https://api.yescaptcha.com'  # https://china.yescaptcha.com
        self.websiteKey = '6Le1YycTAAAAAJXqwosyiATvJ6Gs2NLn8VEzTVlS'

    def create_task(self):
        post_data = {
            "clientKey": self.clientKey,
            "task": {
                "websiteURL": self.websiteURL,
                "websiteKey": self.websiteKey,
                # "type" : "CloudFlareTaskS2"
                "type": "RecaptchaV2TaskProxyless",
            }
        }
        url = f"{self.BASE_URL}/createTask"
        logger.info(url)
        response = self.request(url=url, method='POST', json=post_data)
        if response is None:
            raise 'yescaptcha create task failed'

        if response.status_code == 200:
            data = response.json()
            return data.get('taskId')

    def polling_task(self, task_id):
        url = f"{self.BASE_URL}/getTaskResult"
        post_data = {
            "clientKey": self.clientKey,
            "taskId": task_id
        }
        count = 0
        while count < 120:
            logger.info(f"{url}, count: {count}")
            response = self.request(url=url, method='POST', json=post_data)
            if response and response.status_code == 200:
                data = response.json()
                # {'errorId': 0, 'status': 'processing'}
                # {'errorId': 0, 'status': 'ready', 'solution': {'gRecaptchaResponse': '03AF...'}
                # {'errorId': 1, ...}
                logger.info(data)  # {'errorId': 0, 'status': 'processing'}

                error_id = data.get('errorId')
                if error_id == 1:
                    if data.get('errorCode') == 'ERROR_RECAPTCHA_INVALID_DOMAIN':
                        return False

                    self.send_to_fs({'Program': 'eth_label_cloud', 'Error': data})
                    # raise LoginException(data)
                    return False

                status = data.get('status')
                if status == 'ready':
                    return data.get('solution', {}).get('gRecaptchaResponse')

            time.sleep(1)
            count += 1

    def get_verification_code(self):
        g_task_id = self.create_task()
        return self.polling_task(g_task_id)

    @staticmethod
    def check_login_status(text):
        """ 检查登录状态 """

        login_status: bool
        error_msg: str = ''
        # if 'Sign In for Continued Access' in text:
        if 'Sign In for Continued Access' in text:
            login_status = False
            error_msg = '登录失败, 已过掉cloudflare检测, 但未登录成功!'
        # elif 'Ethereum Top Accounts by ETH Balance' in text:
        elif 'OP Mainnet Top Accounts by ETH Balance' in text:
            login_status = True
            logger.info('检测登录-成功')
        else:
            login_status = False
            error_msg = '登录失败, 没有过掉cloudflare检测'
        return login_status, error_msg

    # def login(self, session, login_cf_params):
    #     logger.info('开始登陆......')
    #     verification_code = self.get_verification_code()
    #     if verification_code is False:
    #         return False
    #
    #     post_data = {
    #         "__VIEWSTATE": login_cf_params['VIEWSTATE'],
    #         "__VIEWSTATEGENERATOR": login_cf_params['VIEWSTATEGENERATOR'],
    #         "__EVENTVALIDATION": login_cf_params['EVENTVALIDATION'],
    #         # "ctl00$ContentPlaceHolder1$txtUserName": "jay777777",
    #         # "ctl00$ContentPlaceHolder1$txtPassword": "970706",
    #         "ctl00$ContentPlaceHolder1$txtUserName": "Krickliu",
    #         "ctl00$ContentPlaceHolder1$txtPassword": "DOMYBEST0922",
    #         "g-recaptcha-response": verification_code,
    #         "ctl00$ContentPlaceHolder1$btnLogin": "LOGIN"
    #     }
    #
    #     # 登录（激活session id）
    #     url = "https://optimistic.etherscan.io/login"
    #     session.post(url=url, data=post_data, timeout=10)
    #     logger.info('登陆结束')
    #     return session

    def login(self, session, login_cf_params):
        logger.info('开始登陆......')
        verification_code = self.get_verification_code()
        if verification_code is False:
            logger.error('验证码处理失败')
            return False

        post_data = {
            "__VIEWSTATE": login_cf_params['VIEWSTATE'],
            "__VIEWSTATEGENERATOR": login_cf_params['VIEWSTATEGENERATOR'],
            "__EVENTVALIDATION": login_cf_params['EVENTVALIDATION'],
            "ctl00$ContentPlaceHolder1$txtUserName": "Krickliu",
            "ctl00$ContentPlaceHolder1$txtPassword": "DOMYBEST0922",
            "cf-turnstile-response" : login_cf_params['cf-turnstile-response'],
            "ctl00$ContentPlaceHolder1$btnLogin": "LOGIN"
        }

        url = "https://optimistic.etherscan.io/login"
        response = session.post(url=url, data=post_data, timeout=10)
        if response.status_code != 200:
            logger.error(f'登录请求失败，状态码：{response.status_code}')
            return False

        logger.info('登陆结束')
        return session

    def test(self, session):
        response = session.get('https://optimistic.etherscan.io/accounts/label/aave', timeout=10)

        login_status, error_msg = self.check_login_status(response.text)
        if not login_status:
            print(error_msg)
        print(session.cookies)


if __name__ == '__main__':
    """
    经测试：
        登录成功后，请求参数需要 __cflb、sessionID、cf_clearance、User-Agent，并且User-Agent不能改变、cf_clearance可以变
    """

    lg = Login()
    # 要用 “from curl_cffi import requests”, 网站对登录做过验证，判断是否是爬虫在登录
    session1 = requests.Session()
    lg.test(session1)
