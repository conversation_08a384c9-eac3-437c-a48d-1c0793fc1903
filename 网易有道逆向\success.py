from decrypt_python import preprocess_encrypted_data, decrypt_youdao
from loguru import logger

def decrypt():
    
    aes_key = "ydsecret://query/key/B*RGygVywfNBwpmBaZg*WT7SIOUP2T0C9WHMZN39j^DAdaZhAnxvGcCY6VYFwnHl"
    aes_iv = "ydsecret://query/iv/C@lZe2YzHtZ2CYgaXKSVfsb7Y4QWHjITPPZ0nQp87fBeJ!Iv6v^6fvi2WN@bYpJ4"
    raw_encrypted_string = "_jsUyA02rwkOJ4enKX7c4dhd7CjvGkcKfbRx0BjNGW_11cfgdNLu7B5ul5TPIzqpIRT__ZAx0nMXwqwwd1utI5zYsOPR7WBBfJa0-z-qqW8689kM_1yFkqxQQAagWO02ruHEH0rWzlTVBA0kaaV-gAXls-IcwGUAFjGc-f-_NBMSW7yzvUxXwutLXiJkzvCs76NaGVH_yHB70Lo3HeQgzWR4V_DlHK29BrSfaAjSKicHyFB3OP6vm7bjAkAj8psNW873VaWFSPORoKQcfdeZp_PWe81ABFhnqqoI3mk2d00wdyUQNEXkX3yJ0Qu4u1m2TERcdDfIJXDDRpwkUOnHczzXlU9JTZtbhR7g_sy23bhvAW5yiG4iHo5SLXfHgObrk1Xa095L8pXMoC8hIkUjY_nTSJOHRSWsAj-nwqFQg4T-1 FtpJR_ZfzdRurMedGk0A__XpTMCfIzMHMXnfMEyDJ9GN8fMfKOEZfgwJp_HkB20hRskZ-LJ4tFd12evPtT_fx04U8t1Yn-skroaB_gGIUANxqd3_ZUoZcD0d_8cuT9T79wpERHrvI1KXAWKINTb3WLtkOVOhrOHtWu8-yLi_qAP8EXKx1jpMAt26C--nLDpQtjRxA81Krf7-Npl3iDHTJu5KPRaWlQnFFEvzzuSzvMwVXgCRQ9Zj7X3rczbuNNke_4mGh1T9_jzoBXx2ETAeUXmVVj3-3 PRbznCfb30CZBtfZXc3qCa4unBpaDQNTz3trLz1Cz8zybFqI3hg_OQTvXOfrZwKK2VdcOv0fi9BJXGB2X1TNAIFk3xbogOIGRLbUvlcrH1UQRRSVohYleGB_kjbx9boi14XAr2kZbMED_QDe_Yhw410ah-OkMoKWo7GkpC1VdHGA2pPze5LHn8rAp5EBucQmrIaLkdpiobWG2JhgDfCI54ZVTfvnR-ETuizduxhDh7AePNVpxAI9At30jhMvSEA0kd33N8jx-kWLEa_VBNseejqMObt4eKiYoFtCzhcj8Ta05lX6fabbeSn_Yz4cLP_iRhZy1OHOlvluul1My9bYfcp-cc4CVNyZyy0chbhV5uV6ls1PPjWiQIMDRKc3rK-TjNtiSWYE-HhRNyXkRd8pCbTqEdf42_-hc="

    clean_encrypted_data = preprocess_encrypted_data(raw_encrypted_string)
    result = decrypt_youdao(clean_encrypted_data, aes_key, aes_iv)

    if result and result.get('success'):
        logger.info(f"翻译结果: {result['translation']}")
        src_texts = []
        for sentence_group in result['raw_json']['translateResult']:
            for sentence in sentence_group:
                if 'src' in sentence:
                    src_texts.append(sentence['src'])
        
        logger.info(f"原始文本: {''.join(src_texts)}")
        
    else:
        logger.info("解密失败")
        if result:
            logger.info(f"错误: {result.get('error', '未知错误')}")


if __name__ == "__main__":
    decrypt()