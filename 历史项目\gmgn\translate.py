from translate import Translator
import pandas as pd
 
 
def extract_column_as_array(file_path, sheet_name, column_name):
    try:
        # 读取 Excel 文件
        df = pd.read_excel(file_path, sheet_name=sheet_name)
 
        # 提取指定列数据为数组
        column_data = df[column_name].tolist()
 
        return column_data
    except Exception as e:
        print(f"发生异常：{e}")
        return []
 
 
# 指定 Excel 文件路径、工作表名称和要提取的列名
file_path = "cryptocurrency_need_ts.xlsx"
sheet_name = "cryptocurrency_regulations"
column_name = "country"
 
# 提取指定列数据并返回数组
result_array = extract_column_as_array(file_path, sheet_name, column_name)
 
 
def translate_text(text, src='zh-cn', dest='en'):
    try:
        translator = Translator(from_lang=src, to_lang=dest)
        translation = translator.translate(text)
        return translation
    except Exception as e:
        print(f"发生异常：{e}")
        return "翻译服务发生异常，请稍后重试"
 
 
# 翻译文本数组并逐条输出翻译结果
for text in result_array:
    translated_text = translate_text(text)
    print(f"{text} -> {translated_text}")