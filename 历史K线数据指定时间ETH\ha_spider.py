#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HA平台历史K线数据爬虫
功能：从HA平台API获取指定时间段的历史K线数据并存储到MySQL数据库（三平台对比表）
"""

import requests
import pymysql
import time
import json
from typing import List, Dict, Optional
from loguru import logger
from datetime import datetime
import random


class HASpider:
    def __init__(self):
        """初始化HA爬虫"""
        self.db_config = {
            'host': '**************',
            'port': 33060,
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8',
            'database': 'kline_data'
        }

        self.api_base_url = "https://valuescan.ai"
        self.kline_endpoint = "/api/v1/dex/market/kline-history"
        # self.url = "https://www.valuescan.ai/api/v1/dex/market/kline-history"
        self.url = "https://pre-deploy.valuescan.ai/api/v1/dex/market/kline-history"
        # self.url = "http://**************/api/v1/dex/market/kline-history"

        self.chain_name = "SOL"
        self.bar = "1m"
        # 设置时间范围：从2025-07-17 13:00:00，获取这个范围内的数据
        self.start_timestamp = 1752692400000  # 2025-07-17 03:00:00
        self.end_timestamp = 1752728400000    # 2025-07-17 13:00:00
        self.limit = 1500

        self.headers = {
            'Content-Type': 'application/json'
        }
        
        # 代理配置
        self.proxies = {
            "http": "http://127.0.0.1:33210",
            "https": "http://127.0.0.1:33210"
        }
        
        # 重试配置
        self.max_retries = 5
        self.retry_delay_base = 2  # 基础延时秒数

    def timestamp_to_readable(self, timestamp: int) -> str:
        """将时间戳转换为可读格式"""
        try:
            dt = datetime.fromtimestamp(timestamp / 1000)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return f"无效时间戳: {timestamp}"

    def get_database_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            logger.info("数据库连接成功")
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def get_token_addresses(self) -> List[str]:
        """从数据库获取所有token地址"""
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = "SELECT DISTINCT token_address FROM kline_top200_sol WHERE token_address IS NOT NULL AND token_address != ''"
                cursor.execute(sql)
                results = cursor.fetchall()
                token_addresses = [result[0] for result in results]
                logger.info(f"获取到 {len(token_addresses)} 个token地址")
                return token_addresses
        except Exception as e:
            logger.error(f"获取token地址失败: {e}")
            raise
        finally:
            connection.close()

    def make_request_with_retry(self, url: str, method: str = 'POST', **kwargs) -> Optional[requests.Response]:
        """带重试机制的请求方法"""
        for attempt in range(self.max_retries):
            try:
                logger.info(f"尝试第 {attempt + 1} 次请求: {url}")
                
                # 添加代理到请求参数
                kwargs['proxies'] = self.proxies
                
                if method.upper() == 'POST':
                    response = requests.post(url, **kwargs)
                elif method.upper() == 'GET':
                    response = requests.get(url, **kwargs)
                else:
                    raise ValueError(f"不支持的请求方法: {method}")
                
                # 检查响应状态
                if response.status_code == 200:
                    logger.info(f"请求成功，状态码: {response.status_code}")
                    return response
                else:
                    logger.warning(f"请求失败，状态码: {response.status_code}，第 {attempt + 1} 次尝试")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"请求异常 (第 {attempt + 1} 次尝试): {e}")
            except Exception as e:
                logger.error(f"未知错误 (第 {attempt + 1} 次尝试): {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries - 1:
                delay = self.retry_delay_base * (2 ** attempt) + random.uniform(0, 1)
                logger.info(f"等待 {delay:.2f} 秒后重试...")
                time.sleep(delay)
        
        logger.error(f"所有重试均失败，放弃请求: {url}")
        return None

    def fetch_kline_data(self, token_address: str) -> Optional[List]:
        """获取指定token的K线数据"""
        
        # 使用after参数获取指定时间之前（更旧）的数据
        payload = {
            "chainName": self.chain_name,
            "tokenContractAddress": token_address,
            "bar": self.bar,
            "after": str(self.end_timestamp),  # 获取2025-06-01 15:00:00之前（更旧）的数据
            "limit": 1500,
            "useNativePricing": False
        }
        
        # 直接使用这个请求获取数据
        result = self._make_request(token_address, payload)
        return result
    
    def _make_request(self, token_address: str, payload: dict) -> Optional[List]:
        
        try:
            response = self.make_request_with_retry(
                self.url,
                method='POST',
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response is None:
                logger.error(f"token {token_address} 所有重试均失败")
                return None
            
            response_data = response.json()
            logger.info(f"token {token_address} 请求响应: {response_data}")
            
            if response.status_code == 200:
                data = response_data
                
                if data.get('code') == 200 and 'data' in data:
                    kline_data = data['data'].get('kline', [])
                    
                    # 筛选符合时间要求的数据
                    target_range_data = []
                    
                    for kline in kline_data:
                        if len(kline) >= 7:  # 确保数据完整
                            timestamp = int(kline[0])
                            # 筛选目标时间范围内的数据
                            if self.start_timestamp <= timestamp <= self.end_timestamp:
                                target_range_data.append(kline)
                    
                    logger.info(f"token {token_address} API返回 {len(kline_data)} 条数据，符合时间要求: {len(target_range_data)} 条")
                    
                    # 如果没有找到目标时间范围的数据，也返回结果（可能需要调整时间范围）
                    if len(target_range_data) == 0:
                        logger.warning(f"token {token_address} 没有找到目标时间范围的数据，请检查时间范围设置")
                        # 显示API返回的实际时间范围供参考
                        if kline_data:
                            timestamps = [int(k[0]) for k in kline_data if len(k) >= 7]
                            if timestamps:
                                min_time = min(timestamps)
                                max_time = max(timestamps)
                                logger.info(f"  API返回的时间范围: {self.timestamp_to_readable(min_time)} - {self.timestamp_to_readable(max_time)}")
                                logger.info(f"  您设置的时间范围: {self.timestamp_to_readable(self.start_timestamp)} - {self.timestamp_to_readable(self.end_timestamp)}")
                    
                    return target_range_data
                else:
                    logger.warning(f"token {token_address} API响应异常")
                    return None
            else:
                logger.error(f"token {token_address} 请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取token {token_address} K线数据失败: {e}")
            return None

    def save_kline_data(self, token_address: str, kline_data: List):
        """保存K线数据到数据库（只保存指定时间范围内的有效数据）"""
        if not kline_data:
            logger.info(f"token {token_address} 没有符合时间要求的数据，跳过保存")
            return
        
        connection = self.get_database_connection()
        saved_count = 0
        try:
            with connection.cursor() as cursor:
                for kline in kline_data:
                    try:
                        # 解析K线数据
                        timestamp = int(kline[0])
                        
                        # 再次验证时间范围（确保只保存有效数据）
                        if not (self.start_timestamp <= timestamp <= self.end_timestamp):
                            continue
                        
                        # 将时间戳转换为标准时间格式
                        ha_time = self.timestamp_to_readable(timestamp)
                        ha_open = float(kline[1])   # 开盘价
                        ha_high = float(kline[2])   # 最高价
                        ha_low = float(kline[3])    # 最低价
                        ha_close = float(kline[4])  # 收盘价
                        ha_vol = float(kline[6])    # 交易量
                        
                        # 检查是否已存在该时间的记录
                        check_sql = """
                        SELECT id FROM kline_top200_sol 
                        WHERE token_address = %s AND ha_time = %s
                        """
                        cursor.execute(check_sql, (token_address, ha_time))
                        existing_record = cursor.fetchone()
                        
                        if existing_record:
                            # 更新现有记录
                            update_sql = """
                            UPDATE kline_top200_sol 
                            SET ha_open = %s, ha_high = %s, ha_low = %s, ha_close = %s, ha_vol = %s, updated_at = NOW()
                            WHERE token_address = %s AND ha_time = %s
                            """
                            cursor.execute(update_sql, (ha_open, ha_high, ha_low, ha_close, ha_vol, token_address, ha_time))
                        else:
                            # 插入新记录
                            insert_sql = """
                            INSERT INTO kline_top200_sol (token_address, ha_time, ha_open, ha_high, ha_low, ha_close, ha_vol, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                            """
                            cursor.execute(insert_sql, (token_address, ha_time, ha_open, ha_high, ha_low, ha_close, ha_vol))
                        
                        saved_count += 1
                    
                    except (ValueError, IndexError) as e:
                        logger.error(f"解析K线数据失败: {e}")
                        continue
                
                connection.commit()
                logger.info(f"token {token_address} 成功保存 {saved_count} 条有效数据")
                
        except Exception as e:
            connection.rollback()
            logger.error(f"保存token {token_address} K线数据失败: {e}")
            raise
        finally:
            connection.close()

    def run(self):
        """运行爬虫主程序"""
        target_time_range = f"{self.timestamp_to_readable(self.start_timestamp)} - {self.timestamp_to_readable(self.end_timestamp)}"
        logger.info(f"开始运行HA K线数据爬虫，目标时间: {target_time_range}")
        
        try:
            # 获取所有token地址
            token_addresses = self.get_token_addresses()
            
            if not token_addresses:
                logger.warning("没有找到任何token地址")
                return
            
            total_tokens = len(token_addresses)
            success_count = 0
            
            for i, token_address in enumerate(token_addresses, 1):
                logger.info(f"[{i}/{total_tokens}] 处理: {token_address}")
                
                try:
                    # 获取K线数据
                    kline_data = self.fetch_kline_data(token_address)
                    
                    if kline_data:
                        # 保存数据
                        self.save_kline_data(token_address, kline_data)
                        success_count += 1
                    
                    # 添加延时，避免请求过于频繁
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"处理token {token_address} 时发生错误: {e}")
                    continue
            
            logger.info(f"爬虫完成！成功处理 {success_count}/{total_tokens} 个token")
            
        except Exception as e:
            logger.error(f"爬虫运行失败: {e}")
            raise


if __name__ == "__main__":
    spider = HASpider()
    spider.run()