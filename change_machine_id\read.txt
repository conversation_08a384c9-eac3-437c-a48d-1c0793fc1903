1、改变machineId

	文件地址：C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage
	找到文件夹中的 'storage.json'

	打开此文件后找到"telemetry.machineId"

	完全关闭cursor

	运行Pythone脚本  change_machine_id.py 来自动生成一个新的machineId，并写入storage.json

	源地址：
    https://blog.csdn.net/2201_75490194/article/details/144476309?ops_request_misc=%257B%2522request%255Fid%2522%253A%2522f104e61d4b07c477600da1707199847d%2522%252C%2522scm%2522%253A%252220140713.130102334..%2522%257D&request_id=f104e61d4b07c477600da1707199847d&biz_id=0&utm_medium=distribute.pc_search_result.none-task-blog-2~all~baidu_landing_v2~default-1-144476309-null-null.142^v101^pc_search_result_base6&utm_term=cursor%E6%94%B9machine%20id&spm=1018.2226.3001.4187