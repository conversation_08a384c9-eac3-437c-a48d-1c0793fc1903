from project_002.Spiders.utils import DB_BASE
from loguru import logger
from datetime import date

class TestSeed(DB_BASE):
    def __init__(self):
        super().__init__()
        self.seed_key = 'op_token_urls'

    # def check_seeds(self):
    #     while True:
    #         seed = self.get_seed(self.seed_key)
    #         if not seed:
    #             break
    #         logger.info(f"Retrieved URL from seed queue: {seed}")
    #         logger.info(f"当前种子URL数量: {self.get_scard(self.seed_key)}")
    #         logger.info('提取完成')


    # 只提取一个种子
    def check_seeds(self):
        seed = self.get_seed(self.seed_key)
        if seed:
            logger.info(f"Retrieved URL from seed queue: {seed}")
            logger.info(f"当前种子URL数量: {self.get_scard(self.seed_key)}")
            logger.info('提取完成')
        else:
            logger.info('没有可用的种子')

if __name__ == '__main__':
    tester = TestSeed()
    tester.check_seeds()