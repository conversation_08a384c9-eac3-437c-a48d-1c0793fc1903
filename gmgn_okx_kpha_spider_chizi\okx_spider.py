import requests
import json

# proxies = {
#     "http": "socks5://192.168.224.75:30889",
#     "https": "socks5://192.168.224.75:30889"
# }

proxies = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

headers = {
    "authority": "web3.okx.com",
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "app-type": "web",
    "content-type": "application/json",
    "devid": "79566de0-ec54-4c4d-8559-9b4568d80d28",
    "ok-timestamp": "1747060200000",
    "ok-verify-sign": "igX57xNprm2NMiafSsZEIvj5SNyoyFvpFExXvOVoTUY=",
    "ok-verify-token": "32a2704c-3e0a-4fd1-9c1b-7cb164d8a839",
    "origin": "https://web3.okx.com",
    "referer": "https://web3.okx.com/zh-hans/token/solana/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "x-cdn": "https://web3.okx.com",
    "x-fptoken": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "x-fptoken-signature": "{P1363}TBRszCgZWTusc5hBvn4ilgvfVKU2623Y0ZDRNWEXHs9OPVMY34O/5G22AGyqjjJy/wdHHraptEYgkmEMZcrcqg==",
    "x-id-group": "2120771023558490001-c-53",
    "x-locale": "zh_CN",
    "x-request-timestamp": "1747060200000",
    "x-simulated-trading": "undefined",
    "x-site-info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIySIJiOi42bpdWZyJye",
    "x-utc": "8",
    "x-zkdex-env": "0"
}
cookies = {
    "_gcl_gs": "2.1.k1$i1746767445$u3801134",
    "_ym_uid": "1746767455756016886",
    "_ym_d": "1746767455",
    "ok_login_type": "OKX_GLOBAL",
    "intercom-device-id-ny9cf50h": "38ee8fb1-c78a-4875-bcd7-51596b054278",
    "devId": "79566de0-ec54-4c4d-8559-9b4568d80d28",
    "locale": "zh_CN",
    "ok_prefer_udColor": "0",
    "ok_prefer_udTimeZone": "0",
    "fingerprint_id": "79566de0-ec54-4c4d-8559-9b4568d80d28",
    "first_ref": "https%3A%2F%2Fwww.okx.com%2F",
    "_gid": "GA1.2.1626718822.1747017981",
    "ok-exp-time": "1747018311581",
    "tmx_session_id": "9kd5hblychl_1747018312753",
    "fp_s": "0",
    "ok_site_info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIySIJiOi42bpdWZyJye",
    "__cf_bm": "Bx_orhjgLC4CLzwzwVzjHf8qkCJ.cXKHNMfQFM1sYO0-1747102296-1.0.1.1-RmA91pAOLsF9051sVs.nb75Z4.wAhpKtn8saSSzIEu37dnJpQRJUPF03ZKm7SRbscbnxffG8cdYCEYpSk7749jqEe6y2JA6iL.iDcJkKqTc",
    "_ym_isad": "2",
    "_ym_visorc": "b",
    "intercom-session-ny9cf50h": "OGZucXNYQy80bjdSQTFBWVM0UExzckQ4TUN2dExpcldQZTAyVTJLNHJ6VzF2NDNsUkZESHNsZEN4U2FYYVpWQkllNHdaK0orNTk4TXcyOVdjV2NKTjJCNDBYSFNaOGhnZm9LWmljNi8zNnM9LS1zT3BNVzVNUDNJZzhLK0t4QTFma3dnPT0=--6ec7597f1d69b51dd09b3ac19e2beb491f47880d",
    "ok_prefer_currency": "%7B%22currencyId%22%3A50%2C%22isDefault%22%3A0%2C%22isPremium%22%3Afalse%2C%22isoCode%22%3A%22HKD%22%2C%22precision%22%3A2%2C%22symbol%22%3A%22HK%24%22%2C%22usdToThisRate%22%3A7.79075%2C%22usdToThisRatePremium%22%3A7.79075%2C%22displayName%22%3A%22%E6%B8%AF%E5%85%83%22%7D",
    "okg.currentMedia": "lg",
    "traceId": "2120771023558490001",
    "_ga_G0EKWWQGTZ": "GS2.1.s1747102301$o9$g1$t1747102357$j4$l0$h0",
    "_ga": "GA1.2.1807604405.1746767449",
    "ok-ses-id": "lB9YMIMP88qkdeiPqU6YsV8g31tc90yag3qYRlK21yrTpe/bYIrwMbRh/wCXhccNoQ35SvKnDSB+rWsZ865MSU6uLtVsU22bhzCNSq9BptH3vLm9YGrp9t9XuibGSa1C",
    "_monitor_extras": "{\"deviceId\":\"G1EGctpUBMsdRGanofOKot\",\"eventId\":298,\"sequenceNumber\":298}"
}
url = "https://web3.okx.com/priapi/v1/dx/market/v2/trading-history/filter-list"
params = {
    "t": "1747060200000"
}
data = {
    "desc": True,
    "orderBy": "timestamp",
    "limit": 100,
    "tradingHistoryFilter": {
        "chainId": "501",
        "tokenContractAddress": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
        "type": "0",
        "userAddressList": [],
        "volumeMin": "",
        "volumeMax": ""
    }
}
data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, params=params, data=data, proxies=proxies)

print(response.text)
print(response)