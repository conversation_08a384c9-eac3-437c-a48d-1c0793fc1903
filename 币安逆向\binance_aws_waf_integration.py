#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币安AWS WAF Token集成模块
专门用于币安网站的AWS WAF绕过
"""

import requests
import time
from aws_waf_token_generator import AWSWAFTokenGenerator
from typing import Dict, Any, Optional

class BinanceAWSWAFIntegration:
    """币安AWS WAF集成类"""
    
    def __init__(self, base_url: str = "https://www.binance.com"):
        self.base_url = base_url
        self.session = requests.Session()
        self.current_token = None
        self.token_expiry = None
        
        # 设置币安请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Origin': 'https://www.binance.com',
            'Referer': 'https://www.binance.com/',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        })
    
    def detect_aws_waf_challenge(self, response: requests.Response) -> Optional[str]:
        """
        检测AWS WAF挑战并提取challenge URL
        
        Args:
            response: HTTP响应对象
            
        Returns:
            Challenge URL，如果没有挑战返回None
        """
        # 检查状态码
        if response.status_code == 405:
            print("🔍 检测到AWS WAF Challenge (405状态码)")
            
            # 检查响应头
            if 'x-amzn-waf-action' in response.headers:
                print(f"📝 WAF动作: {response.headers['x-amzn-waf-action']}")
            
            # 从响应中寻找challenge URL
            content = response.text
            
            # 寻找challenge.js的URL模式
            import re
            challenge_pattern = r'https://[a-zA-Z0-9]+\.ap-southeast-1\.token\.awswaf\.com/[a-zA-Z0-9/]+/challenge\.js'
            match = re.search(challenge_pattern, content)
            
            if match:
                challenge_url = match.group(0)
                print(f"✅ 找到Challenge URL: {challenge_url}")
                return challenge_url
            
            # 备用模式：检查脚本标签
            script_pattern = r'<script[^>]*src="([^"]*challenge\.js[^"]*)"'
            script_match = re.search(script_pattern, content)
            
            if script_match:
                challenge_url = script_match.group(1)
                if not challenge_url.startswith('http'):
                    # 处理相对URL
                    if challenge_url.startswith('//'):
                        challenge_url = 'https:' + challenge_url
                    elif challenge_url.startswith('/'):
                        challenge_url = 'https://www.binance.com' + challenge_url
                
                print(f"✅ 从脚本标签找到Challenge URL: {challenge_url}")
                return challenge_url
        
        return None
    
    def generate_aws_waf_token(self, challenge_url: str) -> Optional[str]:
        """
        生成AWS WAF Token
        
        Args:
            challenge_url: 挑战URL
            
        Returns:
            生成的token，失败返回None
        """
        print(f"🔧 开始生成AWS WAF Token")
        print(f"📝 Challenge URL: {challenge_url}")
        
        try:
            # 创建token生成器
            generator = AWSWAFTokenGenerator(challenge_url)
            
            # 生成token
            token = generator.generate_token()
            
            if token:
                self.current_token = token
                # 设置token过期时间（通常5-10分钟）
                self.token_expiry = time.time() + 300  # 5分钟
                
                print(f"✅ Token生成成功!")
                print(f"📝 Token: {token[:50]}...")
                
                return token
            else:
                print(f"❌ Token生成失败")
                return None
                
        except Exception as e:
            print(f"❌ Token生成异常: {e}")
            return None
    
    def is_token_valid(self) -> bool:
        """检查当前token是否有效"""
        if not self.current_token:
            return False
        
        if not self.token_expiry:
            return False
        
        return time.time() < self.token_expiry
    
    def make_request_with_waf_bypass(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        发送带有AWS WAF绕过的请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            HTTP响应对象
        """
        print(f"🌐 发送请求: {method} {url}")
        
        # 如果有有效token，添加到请求头
        if self.is_token_valid():
            kwargs.setdefault('headers', {})
            kwargs['headers']['x-aws-waf-token'] = self.current_token
            print(f"✅ 使用现有WAF Token")
        
        # 发送请求
        response = self.session.request(method, url, **kwargs)
        
        # 检查是否遇到AWS WAF挑战
        challenge_url = self.detect_aws_waf_challenge(response)
        
        if challenge_url:
            print(f"🚧 遇到AWS WAF挑战，尝试绕过...")
            
            # 生成新token
            token = self.generate_aws_waf_token(challenge_url)
            
            if token:
                # 重新发送请求
                kwargs.setdefault('headers', {})
                kwargs['headers']['x-aws-waf-token'] = token
                
                print(f"🔄 使用新Token重新发送请求...")
                response = self.session.request(method, url, **kwargs)
                
                if response.status_code != 405:
                    print(f"✅ AWS WAF绕过成功! 状态码: {response.status_code}")
                else:
                    print(f"❌ AWS WAF绕过失败，状态码仍为: {response.status_code}")
            else:
                print(f"❌ 无法生成WAF Token")
        
        return response
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET请求"""
        return self.make_request_with_waf_bypass('GET', url, **kwargs)
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """POST请求"""
        return self.make_request_with_waf_bypass('POST', url, **kwargs)
    
    def test_binance_api(self) -> bool:
        """测试币安API访问"""
        print(f"🧪 测试币安API访问")
        print("=" * 50)
        
        test_urls = [
            "https://www.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT",
            "https://www.binance.com/bapi/asset/v2/public/asset-service/product/get-products?includeEtf=true",
            "https://www.binance.com/bapi/composite/v1/public/promo/cmc-cryptocurrency-map"
        ]
        
        success_count = 0
        
        for i, url in enumerate(test_urls, 1):
            print(f"\n🔄 测试 {i}/{len(test_urls)}: {url}")
            
            try:
                response = self.get(url)
                
                if response.status_code == 200:
                    print(f"✅ 请求成功! 状态码: {response.status_code}")
                    print(f"📝 响应长度: {len(response.text)} 字符")
                    
                    # 尝试解析JSON响应
                    try:
                        data = response.json()
                        if isinstance(data, dict):
                            print(f"📋 响应类型: JSON对象，包含 {len(data)} 个字段")
                        elif isinstance(data, list):
                            print(f"📋 响应类型: JSON数组，包含 {len(data)} 个元素")
                    except:
                        print(f"📋 响应类型: 非JSON格式")
                    
                    success_count += 1
                else:
                    print(f"❌ 请求失败! 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
        
        success_rate = success_count / len(test_urls) * 100
        print(f"\n📊 测试结果:")
        print(f"   成功率: {success_rate:.1f}% ({success_count}/{len(test_urls)})")
        print(f"   当前Token状态: {'有效' if self.is_token_valid() else '无效'}")
        
        return success_count == len(test_urls)

def demo_binance_integration():
    """演示币安集成功能"""
    print("🎯 币安AWS WAF集成演示")
    print("=" * 60)
    
    # 创建集成实例
    binance = BinanceAWSWAFIntegration()
    
    # 测试API访问
    success = binance.test_binance_api()
    
    if success:
        print(f"\n🎉 币安AWS WAF集成测试成功!")
        print(f"✅ 所有API请求都正常工作")
        
        if binance.current_token:
            print(f"📝 当前Token: {binance.current_token[:50]}...")
            print(f"⏰ Token过期时间: {time.ctime(binance.token_expiry) if binance.token_expiry else 'N/A'}")
    else:
        print(f"\n⚠️  币安AWS WAF集成测试部分失败")
        print(f"💡 部分API可能仍受到WAF保护")
    
    return binance

if __name__ == "__main__":
    demo_binance_integration() 