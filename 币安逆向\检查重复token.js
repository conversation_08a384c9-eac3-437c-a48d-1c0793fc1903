// 检查重复aws-waf-token的诊断脚本
console.log("=== AWS WAF Token 诊断分析 ===");

// 1. 检查当前所有cookies
console.log("\n📋 当前所有cookies:");
const allCookies = document.cookie.split(';');
const awsTokens = allCookies.filter(cookie => cookie.trim().startsWith('aws-waf-token'));

console.log("aws-waf-token 数量:", awsTokens.length);
awsTokens.forEach((token, index) => {
    console.log(`Token ${index + 1}:`, token.trim());
});

// 2. 检查localStorage中的token
console.log("\n💾 localStorage中的token:");
const localToken = localStorage.getItem('aws-waf-token');
if (localToken) {
    console.log("localStorage token:", localToken);
} else {
    console.log("localStorage中没有token");
}

// 3. 检查sessionStorage中的token
console.log("\n🔄 sessionStorage中的token:");
const sessionToken = sessionStorage.getItem('aws-waf-token');
if (sessionToken) {
    console.log("sessionStorage token:", sessionToken);
} else {
    console.log("sessionStorage中没有token");
}

// 4. 分析token的结构
console.log("\n🔍 Token结构分析:");
awsTokens.forEach((cookieStr, index) => {
    const tokenValue = cookieStr.split('=')[1];
    if (tokenValue) {
        const parts = tokenValue.split(':');
        console.log(`Token ${index + 1} 结构:`);
        console.log(`  - UUID: ${parts[0]}`);
        console.log(`  - Timestamp: ${parts[1]}`);
        console.log(`  - Signature Length: ${parts[2] ? parts[2].length : 'N/A'}`);
        
        // 解码timestamp
        if (parts[1]) {
            try {
                const timestampBytes = atob(parts[1]);
                const timestamp = new DataView(timestampBytes.buffer || new ArrayBuffer(8));
                console.log(`  - Decoded Time: ${new Date(timestamp.getFloat64(0, false))}`);
            } catch (e) {
                console.log(`  - Timestamp decode error: ${e.message}`);
            }
        }
    }
});

// 5. 检查是否有其他脚本在运行
console.log("\n⚙️ 检查其他脚本:");
if (window.awsWafTokenManager) {
    console.log("✅ 发现 awsWafTokenManager");
} else {
    console.log("❌ 未发现 awsWafTokenManager");
}

// 6. 监控新的cookie设置
console.log("\n👀 开始监控cookie变化...");
const originalCookie = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie') || 
                      Object.getOwnPropertyDescriptor(HTMLDocument.prototype, 'cookie');

Object.defineProperty(document, 'cookie', {
    get() {
        return originalCookie.get.call(document);
    },
    set(val) {
        if (val.includes('aws-waf-token')) {
            console.log("🔔 检测到新的aws-waf-token设置:", val);
            console.trace("设置来源:");
        }
        return originalCookie.set.call(document, val);
    }
});

// 7. 清除重复token的函数
function cleanupDuplicateTokens() {
    console.log("\n🧹 清理重复token...");
    
    // 获取所有cookie
    const cookies = document.cookie.split(';');
    const nonAwsTokens = cookies.filter(cookie => !cookie.trim().startsWith('aws-waf-token'));
    
    // 清除所有aws-waf-token
    document.cookie = "aws-waf-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "aws-waf-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.binance.com;";
    
    console.log("已清除所有aws-waf-token");
    
    // 重新设置其他cookie
    // nonAwsTokens.forEach(cookie => {
    //     if (cookie.trim()) {
    //         document.cookie = cookie.trim();
    //     }
    // });
}

// 8. 设置单一token的函数
function setSingleToken(token) {
    cleanupDuplicateTokens();
    setTimeout(() => {
        document.cookie = `aws-waf-token=${token}; path=/; domain=.binance.com; secure; samesite=none`;
        console.log("✅ 设置单一token:", token);
    }, 100);
}

// 导出清理函数
window.cleanupDuplicateTokens = cleanupDuplicateTokens;
window.setSingleToken = setSingleToken;

console.log("\n🛠️ 可用命令:");
console.log("- cleanupDuplicateTokens() : 清除重复token");
console.log("- setSingleToken('your-token') : 设置单一token"); 