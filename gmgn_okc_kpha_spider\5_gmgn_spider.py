import csv
import json
from datetime import datetime
from curl_cffi import requests
from loguru import logger
import time
from datetime import datetime, timezone, timedelta
import sqlite3


# proxy = {
#     "http": "socks5://192.168.224.75:30889",
#     "https": "socks5://192.168.224.75:30889"
# }

proxy = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

class Trump():
    def __init__(self):
        self.headers = {
        "referer": "https://gmgn.ai/sol/token/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
        }
        self.params = {
            "device_id": "e17ccaad-5aa6-43a9-a219-ae220bc8a6e4",
            "client_id": "gmgn_web_20250510-918-0737323",
            "from_app": "gmgn",
            "tz_name": "Asia/Shanghai",
            "tz_offset": "28800",
            "app_lang": "zh-CN",
            "resolution": "1s",
            "from": "0",
            "to": "1747031121000",
            "limit": "100"
        }
        self.cookies = {
            "_ga": "GA1.1.1784117668.1746768932",
            "cf_clearance": "dptNgiURru.hjCsumsd8uwwc8vhjiD1rjfslUrsePrU-1747038030-1.2.1.1-3HPsUAV2sbB5rHhJhqSKLMsDi3PFuuuIQBmR_ZRCJ95l89BMKdgXtNMtTl43x02G.n8XNBaQejJ608xCzdqg6im3pDf4v6533pVZcZ7WkfbsYsxlxH5R5LF9__s.nJCkh69Q0DtGy2ej7hdbKgcYsaYcgjKMUfuIodID.wpq4bqPEWdkfI_7mE2oQuaVej3Ajd7d2B7SbURdjyhou6Vpx01PIl_PJ_5UL2p8f0U5Uh0LUCJoFsIKyxvvZsDKJMFzoCUiDKZYt8iqaArpk8kqcoMz1PNgNpMVCuH5m2Z7cRpgEWNylwwmmrElQws5.Yx3vSoXKMrgKqR_db7sMKisCcIF4VoVfze81GN29ODz3vE",
            "_ga_0XM0LYXGC8": "GS2.1.s1747034871$o4$g1$t1747039295$j0$l0$h0"
        }

    def parse_data(self):
        url = "https://gmgn.ai/api/v1/token_candles/sol/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"
        response = requests.get(url, headers=self.headers, cookies=self.cookies, params=self.params, proxies=proxy, impersonate='chrome110')

        if response.status_code == 200:
            print(response.text)
            data = response.json()
            if data.get("message") == "success":
                logger.info(f'访问成功,状态码:{response.status_code},正在获取数据')
                self.save_to_sqlite(data["data"]["list"])
        elif response.status_code == 403 and "Just a moment..." in response.text:
            logger.info(f'未通过校验,状态码:{response.status_code},请检查cookie')

    def save_to_sqlite(self, data_list):
        db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS gmgn_data_2 (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT COMMENT '时间（K线秒级）',
            token_symbol TEXT COMMENT '代币symbol',
            contract_address TEXT COMMENT '合约地址',
            ha_open_price REAL COMMENT '($)恒安价格-开',
            ha_high_price REAL COMMENT '($)恒安价格-高',
            ha_low_price REAL COMMENT '($)恒安价格-低',
            ha_close_price REAL COMMENT '($)恒安价格-收',
            okx_open_price REAL COMMENT '($)OKX价格-开',
            okx_high_price REAL COMMENT '($)OKX价格-高',
            okx_low_price REAL COMMENT '($)OKX价格-低',
            okx_close_price REAL COMMENT '($)OKX价格-收',
            gmgn_open_price REAL COMMENT '($)Gmgn价格-开',
            gmgn_high_price REAL COMMENT '($)Gmgn价格-高',
            gmgn_low_price REAL COMMENT '($)Gmgn价格-低',
            gmgn_close_price REAL COMMENT '($)Gmgn价格-收',
            tvl REAL COMMENT '($)TVL - 获取价格时的 TVL'
        )
        """
        cursor.execute(create_table_sql)

        # 插入数据
        insert_sql = """
        INSERT INTO gmgn_data_2 (
            timestamp, token_symbol, contract_address,
            gmgn_open_price, gmgn_high_price, gmgn_low_price, gmgn_close_price
        )
        VALUES (?, 'SOL', '9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump', ?, ?, ?, ?)
        """
        
        # 创建Asia/Shanghai时区对象
        shanghai_tz = timezone(timedelta(hours=8))
        
        for item in data_list:
            open_price = item["open"]
            high = item["high"]
            low = item["low"]
            close = item["close"]
            time_stamp = int(item["time"]) / 1000
            # 先转换为UTC时间，然后转换为上海时间
            utc_time = datetime.fromtimestamp(time_stamp, timezone.utc)
            shanghai_time = utc_time.astimezone(shanghai_tz)
            time_str = shanghai_time.strftime('%Y-%m-%d %H:%M:%S')
            
            cursor.execute(insert_sql, (time_str, open_price, high, low, close))
        
        conn.commit()
        conn.close()
        logger.info('数据已成功保存到SQLite数据库')


if __name__ == '__main__':
    trump = Trump()
    trump.parse_data()