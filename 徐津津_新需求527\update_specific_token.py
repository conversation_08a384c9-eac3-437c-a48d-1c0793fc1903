#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新指定Solana代币的价格到数据库
"""

import sqlite3
import sys

# 设置控制台输出编码
if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库路径
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "okx_gmgn_ha"  # 表名

def update_token_price(token_address, price):
    """更新指定代币的价格到数据库"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        # 更新数据库
        cursor.execute(
            f'''
            UPDATE {DB_TABLE}
            SET "data OKX" = ?
            WHERE "Token Address" = ?
            ''',
            (price, token_address)
        )
        
        if cursor.rowcount > 0:
            print(f"成功更新 {token_address} 的价格: {price}")
            conn.commit()
            return True
        else:
            print(f"未找到Token地址 {token_address}")
            return False
    
    except sqlite3.Error as e:
        print(f"更新数据库时出错: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python update_specific_token.py <代币地址> <价格>")
        print("示例: python update_specific_token.py 2LWGvjnrRQM2XRNFs3hcMHRv4int9aMdChLFe7zZpump 0.000013916999201098")
        sys.exit(1)
    
    token_address = sys.argv[1]
    price = sys.argv[2]
    
    print(f"正在更新代币 {token_address} 的价格为 {price}...")
    success = update_token_price(token_address, price)
    
    if success:
        print("价格更新成功!")
    else:
        print("价格更新失败!") 