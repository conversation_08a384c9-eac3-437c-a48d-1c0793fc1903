import requests


headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "Origin": "https://fanyi.youdao.com",
    "Referer": "https://fanyi.youdao.com/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
cookies = {
    # "OUTFOX_SEARCH_USER_ID_NCOO": "1373042093.4709904",
    "OUTFOX_SEARCH_USER_ID": "-964980022@171.223.207.50",
    "DICT_DOCTRANS_SESSION_ID": "MTMxZTQ1NDctMmZhYS00M2UxLTg5ZjUtODc2NjNhZGQ2MzJi",
    "_uetsid": "949ff9d05e3311f0b4e2ed6988db27da",
    "_uetvid": "94a03d305e3311f09147b373d57a9bed"
}
url = "https://dict.youdao.com/webtranslate"
data = {
    "i": "什么是python逆向工程呢",
    "from": "zh-CHS",
    "to": "en",
    "useTerm": "false",
    "domain": "0",
    "dictResult": "true",
    "keyid": "webfanyi",
    "sign": "023f1bd1f4da525e7100ff54216c3c15",
    "client": "fanyideskweb",
    "product": "webfanyi",
    "appVersion": "1.0.0",
    "vendor": "web",
    "pointParam": "client,mysticTime,product",
    "mysticTime": "1752310291945",
    "keyfrom": "fanyi.web",
    "mid": "1",
    "screen": "1",
    "model": "1",
    "network": "wifi",
    "abtest": "0",
    "yduuid": "abcdefg"
}
response = requests.post(url, headers=headers, cookies=cookies, data=data)

print(response.text)
print(response)