/**
 * 🔐 AWS WAF Token验证器
 * 用于测试生成的token并尝试获取最终的aws-waf-token cookie
 */

class AWSWAFTokenVerifier {
    constructor() {
        this.possibleEndpoints = [
            // 可能的验证端点
            'https://api.binance.com/api/v3/time',
            'https://api.binance.com/api/v3/ping',
            'https://www.binance.com/gateway/api/v1/friendly/waf/verify',
            'https://www.binance.com/bapi/asset/v1/public/asset-service/product/get-products',
            'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT',
            // 币安的一些常见API端点
        ];
        
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.binance.com/',
            'Origin': 'https://www.binance.com',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        };
    }

    /**
     * 验证Token并尝试获取aws-waf-token cookie
     */
    async verifyToken(token) {
        console.log("🔍 开始Token验证流程...");
        console.log("📝 Token:", token.substring(0, 50) + "...");
        
        const results = [];
        
        for (let i = 0; i < this.possibleEndpoints.length; i++) {
            const endpoint = this.possibleEndpoints[i];
            console.log(`\n🎯 测试端点 ${i + 1}/${this.possibleEndpoints.length}: ${endpoint}`);
            
            try {
                const result = await this.testEndpoint(endpoint, token);
                results.push(result);
                
                if (result.success) {
                    console.log("✅ 验证成功！", result);
                    if (result.awsWafToken) {
                        console.log("🎉 获得aws-waf-token cookie:", result.awsWafToken);
                        return result;
                    }
                } else {
                    console.log("❌ 验证失败:", result.error);
                }
                
            } catch (error) {
                console.log("💥 请求异常:", error.message);
                results.push({
                    endpoint,
                    success: false,
                    error: error.message
                });
            }
            
            // 避免请求过快
            await this.sleep(500);
        }
        
        console.log("\n📊 验证结果汇总:");
        results.forEach((result, index) => {
            console.log(`${index + 1}. ${result.endpoint} - ${result.success ? '✅' : '❌'}`);
        });
        
        return results;
    }

    /**
     * 测试单个端点
     */
    async testEndpoint(endpoint, token) {
        const requestHeaders = {
            ...this.headers,
            'x-aws-waf-token': token
        };

        try {
            const response = await fetch(endpoint, {
                method: 'GET',
                headers: requestHeaders,
                mode: 'cors',
                credentials: 'include'
            });

            console.log(`  📡 状态码: ${response.status}`);
            console.log(`  📋 响应头:`, [...response.headers.entries()]);

            const result = {
                endpoint,
                success: response.ok,
                status: response.status,
                headers: Object.fromEntries(response.headers.entries()),
                awsWafToken: null
            };

            // 检查响应头中的Set-Cookie
            const setCookieHeader = response.headers.get('Set-Cookie');
            if (setCookieHeader) {
                console.log(`  🍪 Set-Cookie: ${setCookieHeader}`);
                
                // 查找aws-waf-token cookie
                const awsWafMatch = setCookieHeader.match(/aws-waf-token=([^;]+)/);
                if (awsWafMatch) {
                    result.awsWafToken = awsWafMatch[1];
                    console.log(`  🎯 找到aws-waf-token: ${result.awsWafToken}`);
                }
            }

            // 尝试读取响应内容
            if (response.ok) {
                try {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        result.data = data;
                        console.log(`  📄 响应数据:`, data);
                    } else {
                        const text = await response.text();
                        result.data = text.substring(0, 200) + (text.length > 200 ? '...' : '');
                        console.log(`  📄 响应文本:`, result.data);
                    }
                } catch (parseError) {
                    console.log(`  ⚠️ 解析响应失败:`, parseError.message);
                }
            } else {
                result.error = `HTTP ${response.status} ${response.statusText}`;
            }

            return result;

        } catch (error) {
            console.log(`  💥 网络错误:`, error.message);
            throw error;
        }
    }

    /**
     * 分析网络请求模式
     */
    async analyzeNetworkPattern() {
        console.log("🕵️ 开始分析网络请求模式...");
        
        // 模拟正常用户行为
        const userActions = [
            {
                name: "访问首页",
                url: "https://www.binance.com/",
                method: "GET"
            },
            {
                name: "获取时间",
                url: "https://api.binance.com/api/v3/time",
                method: "GET"
            },
            {
                name: "获取价格",
                url: "https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT",
                method: "GET"
            }
        ];

        for (const action of userActions) {
            console.log(`\n🎬 执行: ${action.name}`);
            try {
                const response = await fetch(action.url, {
                    method: action.method,
                    headers: this.headers,
                    mode: 'cors',
                    credentials: 'include'
                });

                console.log(`  📊 状态: ${response.status}`);
                
                // 检查是否触发了WAF challenge
                const isChallenge = this.isWAFChallenge(response);
                if (isChallenge) {
                    console.log("  🛡️ 检测到AWS WAF Challenge！");
                }

            } catch (error) {
                console.log(`  ❌ 错误: ${error.message}`);
            }
        }
    }

    /**
     * 检查是否是WAF Challenge响应
     */
    isWAFChallenge(response) {
        // 检查状态码和响应头
        if (response.status === 405 || response.status === 403) {
            return true;
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('text/html')) {
            // 可能是challenge页面
            return true;
        }
        
        return false;
    }

    /**
     * 提取浏览器现有的cookies
     */
    extractExistingCookies() {
        console.log("🍪 提取现有cookies...");
        
        try {
            const cookies = document.cookie;
            console.log("📋 当前cookies:", cookies);
            
            // 解析cookies
            const cookieObj = {};
            cookies.split(';').forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name && value) {
                    cookieObj[name] = value;
                }
            });
            
            console.log("📊 解析后的cookies:", cookieObj);
            
            // 检查是否已有aws-waf-token
            if (cookieObj['aws-waf-token']) {
                console.log("🎯 找到现有的aws-waf-token:", cookieObj['aws-waf-token']);
            } else {
                console.log("❌ 未找到aws-waf-token cookie");
            }
            
            return cookieObj;
            
        } catch (error) {
            console.log("❌ cookie提取失败:", error.message);
            return {};
        }
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

/**
 * 🚀 快速验证函数
 */
async function quickVerifyToken(token) {
    const verifier = new AWSWAFTokenVerifier();
    
    console.log("🔍 开始快速Token验证...");
    
    // 1. 提取现有cookies
    const existingCookies = verifier.extractExistingCookies();
    
    // 2. 验证token
    const results = await verifier.verifyToken(token);
    
    // 3. 分析结果
    const successfulResults = results.filter(r => r.success);
    const cookieResults = results.filter(r => r.awsWafToken);
    
    console.log("\n📊 验证汇总:");
    console.log(`✅ 成功请求: ${successfulResults.length}/${results.length}`);
    console.log(`🍪 获得cookie: ${cookieResults.length}/${results.length}`);
    
    if (cookieResults.length > 0) {
        console.log("\n🎉 成功获得aws-waf-token cookies:");
        cookieResults.forEach((result, index) => {
            console.log(`${index + 1}. ${result.endpoint}`);
            console.log(`   Token: ${result.awsWafToken}`);
        });
    } else {
        console.log("\n💡 建议下一步:");
        console.log("1. 检查CORS设置");
        console.log("2. 尝试在币安官网页面内运行");
        console.log("3. 分析更多可能的验证端点");
    }
    
    return results;
}

/**
 * 🎯 完整的验证流程
 */
async function fullVerificationFlow(token) {
    console.log("🎪 开始完整验证流程...");
    
    const verifier = new AWSWAFTokenVerifier();
    
    // 步骤1: 分析网络模式
    await verifier.analyzeNetworkPattern();
    
    // 步骤2: 验证token
    await verifier.quickVerifyToken(token);
    
    console.log("🏁 完整验证流程结束");
}

// 导出供外部使用
if (typeof window !== 'undefined') {
    window.AWSWAFTokenVerifier = AWSWAFTokenVerifier;
    window.quickVerifyToken = quickVerifyToken;
    window.fullVerificationFlow = fullVerificationFlow;
    
    console.log("✅ AWS WAF Token验证器加载完成");
    console.log("🎯 使用方法:");
    console.log("  quickVerifyToken(yourToken) - 快速验证");
    console.log("  fullVerificationFlow(yourToken) - 完整流程");
} 