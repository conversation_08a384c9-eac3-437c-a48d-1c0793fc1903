
from flask import Flask, g

from settings import *
from db import RedisClient

__all__ = ['app']

app = Flask(__name__)


@app.route('/')
def index():
    return 'hello'


def get_redis_conn(website):
    if not hasattr(g, website):
        setattr(g, website + '_cookies', RedisClient(REDIS_COOKIES_NAME))
        setattr(g, website + '_accounts', RedisClient(REDIS_ACCOUNTS_NAME))
    return g


@app.route('/<website>/random')
def random(website):
    """
    获取随机的Cookie
    :return:
    """
    get_redis_conn(website)
    cookies = getattr(g, website + '_cookies').random()
    return cookies


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5003)