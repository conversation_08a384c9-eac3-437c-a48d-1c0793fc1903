'''
获取非小号所有币种的代码
存入redis中和数据库中
'''

import time
import random
import requests
from loguru import logger
import redis
import pymysql
from pymysql.cursors import DictCursor


class FXH_PAGE_Spider():
    def __init__(self):
        super().__init__()
        self.base_url = "https://dncapi.flink1.com/api/coin/web-coinrank"
        self.headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.params = {
            "page": "1",
            "type": "-1",
            "pagesize": "100",
            "webp": "1"
        }

        # 修改为新的数据库配置
        self.mysql_config = {
            'host': '***************',
            'port': 3306,
            'user': 'spider',
            'password': 'ha13579.',
            'db': 'spider',  # 直接指定数据库
            'charset': 'utf8',
            'cursorclass': DictCursor
        }

        self.init_database()

        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            password='123456',
            db=2,
            decode_responses=True
        )
        self.proxy = {
            "http": "http://127.0.0.1:33210",
            "https": "http://127.0.0.1:33210"
        }

    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.mysql_config)

    def init_database(self):
        """初始化表结构"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cursor:
                # 币种表 - 添加 name 和 full_name 字段
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS fxh_code (
                        code VARCHAR(50) PRIMARY KEY,
                        name VARCHAR(100),
                        full_name VARCHAR(200),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)

                # 钱包表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS fxh_wallet_info (
                        wallet_name VARCHAR(100) PRIMARY KEY,
                        defi INT,
                        types INT,
                        keystorage INT,
                        score INT,
                        logo_route TEXT,
                        logo_url TEXT
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)

                # 关联表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS fxh_code_wallet (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        code_name VARCHAR(50),
                        wallet_name VARCHAR(100),
                        FOREIGN KEY (code_name) REFERENCES fxh_code(code),
                        FOREIGN KEY (wallet_name) REFERENCES fxh_wallet_info(wallet_name),
                        UNIQUE KEY unique_code_wallet (code_name, wallet_name)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)
            conn.commit()
            logger.success("数据表初始化成功")
        except Exception as e:
            logger.error(f"初始化数据表时出错: {e}")
        finally:
            conn.close()

    def save_code_to_redis(self, code):
        try:
            self.redis_client.sadd('feixiaohao:coin_codes_fullname', code)
            logger.info(f'成功将代码 {code} 保存到Redis')
        except Exception as e:
            logger.error(f'保存到Redis失败: {e}')

    def save_code_to_db(self, code, name, full_name):
        """将code及相关信息保存到数据库"""
        try:
            conn = self.get_connection()
            with conn.cursor() as cursor:
                sql = '''
                    INSERT INTO fxh_code (code, name, full_name) 
                    VALUES (%s, %s, %s)
                    ON DUPLICATE KEY UPDATE 
                    name = VALUES(name),
                    full_name = VALUES(full_name)
                '''
                cursor.execute(sql, (code, name, full_name))
            conn.commit()
            logger.info(f'成功将代码 {code} 及其信息保存到数据库')
        except Exception as e:
            logger.error(f'保存到数据库失败: {e}')
        finally:
            conn.close()

    def featch_data(self, page):
        try:
            self.params['page'] = str(page)
            response = requests.get(url=self.base_url, headers=self.headers, params=self.params, proxies=self.proxy)
            logger.info(f'第 {page} 页请求状态码: {response.status_code}')
            data = response.json()
            return data
        except Exception as e:
            logger.error(f'获取数据时出错: {e}')
            return None

    def parse_data(self, data):
        data_detail = data.get('data', [])
        parsed_data = []
        for item in data_detail:
            code = item.get('code', '')
            name = item.get('name', '')
            full_name = item.get('fullname', '')

            append_data = {
                'code': code,
                'name': name,
                'full_name': full_name
            }

            if code:
                # 同时保存到Redis和数据库
                self.save_code_to_redis(code)
                self.save_code_to_db(code, name, full_name)
            parsed_data.append(append_data)

        logger.success(f'共获取到{len(data_detail)}条数据')

    def run(self):
        # 51
        for page in range(1, 232):
            data = self.featch_data(page)
            if data:
                self.parse_data(data)
                # logger.info(f'第{page}页爬取完成')
            time.sleep(random.uniform(2, 5))


if __name__ == '__main__':
    spider = FXH_PAGE_Spider()
    spider.run()