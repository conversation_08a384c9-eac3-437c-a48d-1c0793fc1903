import requests
from loguru import logger
import redis
import threading
import time
import random
import pymysql
from pymysql.cursors import DictCursor
from lxml import etree
import re

class UPDATE_FULLNAME_SPIDER():
    def __init__(self):
        self.url = "https://www.feixiaohao.com/currencies/ethereum/"
        self.headers = {
            "authority": "www.feixiaohao.com",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "max-age=0",
            "referer": "https://www.feixiaohao.com/",
            "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.mysql_config = {
            'host': '***************',
            'port': 3306,
            'user': 'spider',
            'password': 'ha13579.',
            'db': 'spider',
            'charset': 'utf8',
            'cursorclass': DictCursor
        }
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            password='123456',
            db=2,
            decode_responses=True
        )
        self.proxies = {
            "http": "http://127.0.0.1:33210",
            "https": "http://127.0.0.1:33210"
        }
        self.thread_local = threading.local()

    def get_code_from_redis(self):
        """从Redis中获取并移除一个code"""
        try:
            # 添加调试信息
            # logger.debug("尝试从Redis获取code")
            code = self.redis_client.spop('feixiaohao:coin_codes_fullname')
            
            # 检查code的值和类型
            # logger.debug(f"获取到的code类型: {type(code)}, 值: {code}")
            
            if code:
                # 如果是bytes类型，解码为字符串
                if isinstance(code, bytes):
                    code = code.decode('utf-8')
                logger.info(f'从Redis获取到code: {code}')
                return code
            else:
                # 尝试获取Redis中的所有key
                all_keys = self.redis_client.keys('*')
                logger.debug(f"Redis中的所有key: {all_keys}")
                
                # 获取指定key的类型
                key_type = self.redis_client.type('feixiaohao:coin_codes_fullname')
                logger.debug(f"feixiaohao:coin_codes的类型: {key_type}")
                
                # 如果是集合，获取集合大小
                if key_type == b'set':
                    size = self.redis_client.scard('feixiaohao:coin_codes_fullname')
                    logger.debug(f"集合大小: {size}")
                
                return None
        except Exception as e:
            logger.error(f'从Redis获取code失败: {str(e)}')
            # 打印详细的错误信息
            import traceback
            logger.error(f'错误详情: {traceback.format_exc()}')
            return None

    def get_db_connection(self):
        """为每个线程获取独立的数据库连接"""
        if not hasattr(self.thread_local, 'connection') or self.thread_local.connection.open == False:
            self.thread_local.connection = pymysql.connect(
                **self.mysql_config,
                autocommit=False,
                connect_timeout=10,
                read_timeout=30,
                write_timeout=30
            )
        return self.thread_local.connection

    def fetch_data(self, code):
        try:
            url = f"https://www.feixiaohao.com/currencies/{code}/"
            response = requests.get(url=url, headers=self.headers, proxies=self.proxies)
            logger.info(f'请求URL: {url}, 状态码：{response.status_code}')
            return response.text
        except Exception as e:
            logger.error(f'获取数据时出错：{e}')
            return None

    def parse_data(self, html_data):
        if not html_data:
            return None

        try:
            # 使用正则表达式提取full_name
            pattern = r'data:{.*?name:"([^"]+)"'
            match = re.search(pattern, html_data)
            
            if match:
                full_name = match.group(1)
                return {'full_name': full_name}
            else:
                logger.warning("未找到full_name数据")
                return None

        except Exception as e:
            logger.error(f'解析数据时出错：{e}')
            return None

    def update_full_name(self, code, full_name):
        """更新数据库中的full_name"""
        conn = self.get_db_connection()
        try:
            with conn.cursor() as cursor:
                sql = """
                    UPDATE fxh_code 
                    SET full_name = %s 
                    WHERE code = %s
                """
                cursor.execute(sql, (full_name, code))
                conn.commit()
                logger.success(f'成功更新代码 {code} 的full_name为: {full_name}')
        except Exception as e:
            logger.error(f'更新数据时出错: {e}')
            conn.rollback()

    def process_codes(self, thread_id):
        """处理单个线程的代码获取和数据抓取"""
        try:
            request_count = 0
            
            while True:
                code = self.get_code_from_redis()
                if not code:
                    logger.info(f'线程 {thread_id} 没有更多的code可处理')
                    break

                try:
                    if request_count >= 5:
                        wait_time = random.uniform(3, 4)
                        logger.info(f'线程 {thread_id} 已发送 {request_count} 次请求，休息 {wait_time:.2f} 秒')
                        time.sleep(wait_time)
                        request_count = 0

                    data = self.fetch_data(code)
                    request_count += 1
                    logger.info(f'线程 {thread_id} 处理 code: {code}，当前请求次数: {request_count}')
                    
                    if data:
                        parsed_data = self.parse_data(data)
                        if parsed_data and parsed_data.get('full_name'):
                            self.update_full_name(code, parsed_data['full_name'])
                        else:
                            logger.warning(f'线程 {thread_id} 解析 {code} 的数据为空')
                    else:
                        logger.warning(f'线程 {thread_id} 获取 {code} 的数据失败')

                    time.sleep(random.uniform(1, 2))

                except Exception as e:
                    logger.error(f'线程 {thread_id} 处理 code: {code} 时出错: {e}')
                    time.sleep(1)

        finally:
            if hasattr(self.thread_local, 'connection'):
                self.thread_local.connection.close()
                logger.info(f'线程 {thread_id} 关闭数据库连接')

    def run(self):
        try:
            # 先验证Redis连接和数据
            try:
                # 检查Redis连接
                self.redis_client.ping()
                logger.info("Redis连接正常")
                
                # 获取集合大小
                total_codes = self.redis_client.scard('feixiaohao:coin_codes_fullname')
                logger.info(f'Redis中共有 {total_codes} 个code待处理')
                
                # 验证是否能获取数据
                test_code = self.get_code_from_redis()
                if test_code:
                    logger.info(f"测试获取code成功: {test_code}")
                    # 将测试获取的code放回Redis
                    self.redis_client.sadd('feixiaohao:coin_codes', test_code)
                else:
                    logger.error("测试获取code失败")
                    return

                if total_codes == 0:
                    logger.warning('Redis中没有数据需要处理')
                    return

                thread_count = 25  # 设置线程
                threads = []

                # 创建并启动线程
                for i in range(thread_count):
                    thread = threading.Thread(
                        target=self.process_codes,
                        args=(i+1,),
                        name=f'Spider-Thread-{i+1}'
                    )
                    threads.append(thread)
                    thread.start()
                    logger.info(f'启动线程 {i+1}')
                    time.sleep(0.5)

                # 等待所有线程完成
                for thread in threads:
                    thread.join()

                logger.info('所有线程处理完成')
                
            except Exception as e:
                logger.error(f'程序运行出错: {str(e)}')
                import traceback
                logger.error(f'错误详情: {traceback.format_exc()}')
        finally:
            logger.info('程序执行结束')

if __name__ == '__main__':
    try:
        spider = UPDATE_FULLNAME_SPIDER()
        spider.run()
    except KeyboardInterrupt:
        logger.warning('程序被手动中断')
    except Exception as e:
        logger.error(f'程序运行出错: {e}')
    finally:
        logger.info('程序结束')
