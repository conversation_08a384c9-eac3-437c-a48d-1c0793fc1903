import requests
from loguru import logger

def search_tweets(keyword, bearer_token):
    url = "https://api.twitter.com/2/tweets/search/recent"
    headers = {
        "Authorization": f"Bearer {bearer_token}"
    }
    params = {
        "query": keyword,
        "max_results": 15
    }

    response = requests.get(url, headers=headers, params=params)

    if response.status_code == 200:
        logger.info(f'请求成功,状态码: {response.status_code}')
        return response.json()
    else:
        logger.info(f"请求失败，状态码: {response.status_code}")
        return None

def main():
    bearer_token = "AAAAAAAAAAAAAAAAAAAAAJ98yAEAAAAASEGiXsGYk8aUmsPXVz6pRiw1kJU%3Dg7PqTaZGOUqyKnMuUjdMttIW8vfDH4FHo532upgwXsT8r776GJ"
    keyword = "BTC"

    data = search_tweets(keyword, bearer_token)
    if data:
        print("搜索结果:", data)

if __name__ == "__main__":
    main()