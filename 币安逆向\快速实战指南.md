# 快速实战指南 - Cookie获取与API测试

## 🎯 目标
使用生成的AWS WAF Token + 真实Cookie，成功请求币安API

## 🚀 第一种方法：一键测试（推荐）

### 步骤1：准备环境
```
1. 打开浏览器，访问：https://www.binance.com
2. 按F12打开开发者工具 → Console标签
```

### 步骤2：加载Token生成器
```javascript
// 复制粘贴 complete_js_aws_waf_decoder_fixed.js 的全部内容到控制台
// 看到 "🚀 AWS WAF Token生成器初始化完成" 表示成功
```

### 步骤3：加载API测试器
```javascript
// 复制粘贴 binance_api_tester.js 的全部内容到控制台
// 看到使用说明表示加载成功
```

### 步骤4：开始测试
```javascript
// 运行完整测试套件
await startBinanceAPITest()
```

**预期结果：**
```
🎯 启动币安API测试...
🔄 生成新的AWS WAF Token...
✅ Token生成成功
📋 检测到 15 个Cookie
🚀 开始币安API测试套件...
============================================================
🔑 Token: 54f88a70-c77d-4b8a-90f4-299048b561e1:BgoAk4w...
🍪 Cookie: bnc-uuid=6208334d-cc3d-4455-a42e-52531d072aeb...
============================================================

📍 [1/6] 基础Ping
📝 测试API基础连通性

🧪 测试端点: https://api.binance.com/api/v3/ping
🔑 Token: 54f88a70-c77d-4b8a-90f4-299048b561...
🍪 Cookie数量: 15 个
📱 设备信息: eyJzY3JlZW5fcmVzb2x1dGlvbiI6IjE5MjAsMTA4MCIsImF...
📡 响应状态: 200 OK
⏱️ 响应时间: 234.56ms
📋 响应头数量: 12 个
✅ 请求成功!
📄 响应数据: 对象 (0 个字段: )

... (更多测试结果)

============================================================
📊 测试结果详细报告
============================================================
1. ✅ 🔥 基础Ping
   状态: 200 | 耗时: 234.56ms
   URL: https://api.binance.com/api/v3/ping

2. ✅ 🔥 服务器时间  
   状态: 200 | 耗时: 187.23ms
   URL: https://api.binance.com/api/v3/time

============================================================
📈 统计摘要:
总体成功率: 6/6 (100.0%)
核心API成功率: 2/2 (100.0%)
平均响应时间: 210.45ms
============================================================
🎉 所有核心API测试通过！Token工作正常！
```

---

## 🔧 第二种方法：手动构造请求

### 步骤1：获取Cookie
在币安网站控制台运行：
```javascript
// 获取完整Cookie字符串
const cookies = document.cookie;
console.log('完整Cookie:', cookies);

// 查看重要Cookie
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}

console.log('重要Cookie值:');
console.log('bnc-uuid:', getCookie('bnc-uuid'));
console.log('lang:', getCookie('lang'));
console.log('theme:', getCookie('theme'));
```

### 步骤2：生成Token
```javascript
// 使用修复版生成器
const generator = new AWSWAFTokenGenerator();
const result = await generator.generateToken("https://api.binance.com");
const token = result.token;
console.log('生成的Token:', token);
```

### 步骤3：构造请求头
```javascript
// 构造完整请求头
const headers = {
    'x-aws-waf-token': token,
    'Cookie': cookies,
    'User-Agent': navigator.userAgent,
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Referer': 'https://www.binance.com/',
    'Origin': 'https://www.binance.com',
    'Content-Type': 'application/json'
};
```

### 步骤4：发送测试请求
```javascript
// 测试API请求
async function testBinanceAPI() {
    try {
        const response = await fetch('https://api.binance.com/api/v3/ping', {
            method: 'GET',
            headers: headers,
            mode: 'cors'
        });
        
        console.log('响应状态:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ 请求成功!', data);
        } else {
            console.log('❌ 请求失败:', await response.text());
        }
    } catch (error) {
        console.log('❌ 错误:', error.message);
    }
}

await testBinanceAPI();
```

---

## 💡 重要Cookie字段解释

### 从浏览器网络面板获取完整Cookie：
```
1. 在币安网站按F12
2. Network标签页
3. 刷新页面
4. 找到任意请求 → Headers → Request Headers → Cookie
```

### 关键Cookie字段：
```javascript
const keyFields = {
    'bnc-uuid': '6208334d-cc3d-4455-a42e-52531d072aeb',  // 用户唯一ID
    'lang': 'en',                                        // 语言设置
    'theme': 'dark',                                     // 主题
    'sensorsdata2015session': '%7B%22sessionId%22%3A1',  // 埋点会话
    '_gid': 'GA1.2.123456789.1234567890',               // Google Analytics
    'source': 'organic'                                  // 流量来源
};
```

### 设备信息（device-info）：
从您之前的浏览器代码自动获取，包含：
```
- 屏幕分辨率
- Canvas指纹  
- WebGL信息
- 音频指纹
- 浏览器指纹
- 设备名称
```

---

## 🎯 常见API端点测试

### 1. 基础连通性
```javascript
await quickAPITest('https://api.binance.com/api/v3/ping');
```

### 2. 获取BTC价格
```javascript
await quickAPITest('https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT');
```

### 3. 24小时统计
```javascript
await quickAPITest('https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT');
```

### 4. 订单簿
```javascript
await quickAPITest('https://api.binance.com/api/v3/depth?symbol=BTCUSDT&limit=5');
```

---

## 🚨 错误处理指南

### 状态码含义：
- **200**: ✅ 成功
- **403**: ❌ 权限不足，Token可能无效
- **405**: ❌ 方法不允许，需要重新生成Token
- **429**: ❌ 请求过于频繁
- **500**: ❌ 服务器错误

### 常见问题解决：

#### 问题1：405错误
```javascript
// 重新生成Token
const newResult = await generator.generateToken("https://api.binance.com");
const newToken = newResult.token;
```

#### 问题2：CORS错误
```javascript
// 确保在币安网站域名下运行
if (window.location.hostname !== 'www.binance.com') {
    console.log('⚠️ 请在 https://www.binance.com 上运行此脚本');
}
```

#### 问题3：Cookie过期
```javascript
// 重新获取Cookie
const freshCookies = document.cookie;
```

---

## 📊 成功指标

### 完全成功：
- ✅ 所有核心API（Ping, Time）返回200
- ✅ 数据API（价格、订单簿）正常返回
- ✅ 平均响应时间 < 500ms

### 部分成功：
- ✅ 核心API正常
- ⚠️ 部分数据API失败（可能是权限限制）

### 完全失败：
- ❌ 所有API返回403/405
- 💡 需要重新生成Token或检查Cookie

---

## 🎉 成功示例

完整成功的测试结果应该看起来像这样：
```
📈 统计摘要:
总体成功率: 6/6 (100.0%)
核心API成功率: 2/2 (100.0%)  
平均响应时间: 210.45ms
🎉 所有核心API测试通过！Token工作正常！
```

这表明您的Token生成器完全正常工作，可以成功绕过AWS WAF防护！

---

*最后更新: 2024年1月 | 适用版本: 实战测试版* 