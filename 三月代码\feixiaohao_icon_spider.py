import requests
from loguru import logger
import sqlite3


class WALLET_SPIDER():
    def __init__(self, chain_name):
        self.url = "https://dncapi.flink1.com/api/v1/wallet/page"
        self.headers = {
            "authority": "dncapi.flink1.com",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "origin": "https://www.feixiaohao.com",
            "referer": "https://www.feixiaohao.com/",
            "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "cross-site",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        }
        self.params = {
            "page": "1",
            "asc": "1",
            "type": "-1",
            "per_page": "100",
            "chain": chain_name,
            "webp": "1"
        }
        self.db_path = "C:\\Users\\<USER>\\AppData\\Roaming\\DBeaverData\\workspace6\\.metadata\\sample-database-sqlite-1\\Chinook.db"
        self.conn = sqlite3.connect(self.db_path)
        self.create_table()


    def create_table(self):
        cursor = self.conn.cursor()
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS fxh_wallet (
                name TEXT PRIMARY KEY,
                defi INTEGER,
                types INTEGER,
                keystorage INTEGER,
                score INTEGER,
                logo_url TEXT
            )
        """)
        self.conn.commit()


    def featch_data(self):
        try:
            response = requests.get(url=self.url, headers=self.headers, params=self.params)
            logger.info(f'请求状态码: {response.status_code}')
            data = response.json()
            return data
        except Exception as e:
            logger.warning(f'获取数据时出错: {e}')
            return None


    def parse_data(self, data):
        parsed_data = []
        data_detail = data.get('data', {}).get('list', [])
        for data_need in data_detail:
            append_data = {
                'name': data_need.get('name', ''),
                'defi': data_need.get('defi', 0),
                'types': data_need.get('types', 0),
                'keystorage': data_need.get('keystorage', 0),
                'score': data_need.get('score', 0),
                'logo_url': data_need.get('logo', '')
            }
            logger.info(f'数据: {append_data}')
            parsed_data.append(append_data)
        logger.success(f'共获取到{len(data_detail)}条数据')
        if parsed_data:
            self.insert_data(parsed_data)
        else:
            logger.warning('获取到的数据为空')


    def get_logo(self, data):
        url = ''
        headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        }
        response = requests.get(url, headers=headers)
        logger.info(f'请求状态码: {response.status_code}')
        return response.content


    def insert_data(self, parsed_data):
        cursor = self.conn.cursor()
        for item in parsed_data:
            cursor.execute('''
                INSERT OR IGNORE INTO fxh_wallet (name, defi, types, keystorage, score, logo_url)
                VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                      item['name'],
                      item['defi'],
                      item['types'],
                      item['keystorage'],
                      item['score'],
                      item['logo_url']
            ))
        logger.success(f'成功向数据库插入{len(parsed_data)}条数据')
        self.conn.commit()


    def run(self):
        cursor = self.conn.cursor()
        data = self.featch_data()
        if data:
            self.parse_data(data)
        logger.info('程序结束')


if __name__ == '__main__':
    chain_name = "bitcoin"
    activate = WALLET_SPIDER(chain_name)
    activate.run()