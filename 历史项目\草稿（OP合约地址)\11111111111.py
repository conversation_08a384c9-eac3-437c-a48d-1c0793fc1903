import requests
from loguru import logger
from datetime import date
from lxml import etree
import time


headers = {
    "authority": "scan.coredao.org",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}


cookies = {
    "_ga": "GA1.1.1863956034.1735280009",
    "cf_clearance": "H0BclopYEyKq61dL775KDQtu9BYIskghDh8k7fy5A5M-1735282887-1.2.1.1-Qx8lABz_AOPgQpaUvwACLulGJYfiyHe61gBnLsS1GDN.sb_BL6hC_59j0Ayl8Tzv9vuXprCvq_.g6dEG3MbTQ8iEJQKV7rCE3nHuN7qxIrr8l5bxiiB.uLg5KHHJ0R5X9rH9T8yrmB9Su5Hpa9.I8McH.HFu1mUcV_o_gmZKVAYDcS7kb8se__yvC5Bs5Z0vFaXJvChez1wCZ6xBVtUK.mo_BYgKZ38i2QsyKBWtbkqdAmwfukIE44K0BbhppM1KSyt6a22ldlkAl3t8i7CeYjM3KL768k_0Jkaz2d8KkJ_YAGzoteeapcl.22.DwkPJ5HHiLp4.W6GoLNgVOhZGxlu24pNuhLekJNLy3OsEyttOGq5nOf0oAXOv2YaXVMZI",
    "_ga_EC8BHQGGMF": "GS1.1.1735280008.1.1.1735282941.54.0.0",
    "AWSALBTG": "LxSXEQm12y1ExOhAlYURVrdbnS008BVu6/RdfOEXag+cNzTG+mCQKQTKMqMQrcufwiFCUl7yHIpWL7+BlT4WrrxJyv53p/0dWoaHMbJ4/BQmJWZOhNPF7fgeUArE61nBPgbMo9HDql0W/Y5I6sDWtCELRBX6ZXKE/uURVDaouHDlzVBnSa0=",
    "AWSALBTGCORS": "LxSXEQm12y1ExOhAlYURVrdbnS008BVu6/RdfOEXag+cNzTG+mCQKQTKMqMQrcufwiFCUl7yHIpWL7+BlT4WrrxJyv53p/0dWoaHMbJ4/BQmJWZOhNPF7fgeUArE61nBPgbMo9HDql0W/Y5I6sDWtCELRBX6ZXKE/uURVDaouHDlzVBnSa0="
}

url = "https://scan.coredao.org/token/0xae7b1b78ec278e2a1ab47e55f24fe781c607b1c9"

response = requests.get(url, headers=headers, cookies=cookies)
html = etree.HTML(response.text)
logger.info(response.status_code)
time.sleep(1)
logger.info(response.text)
contractAbi = ''.join(html.xpath('//*[@id="el-id-3420-129"]'))
logger.info(contractAbi)