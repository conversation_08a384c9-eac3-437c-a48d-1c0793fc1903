// 🎯 修复CORS问题的Token验证代码

const token = "57cc1a01-8fc6-47ad-8bb0-32c3f63c8240:AAABmBIZN3I=:yuXy+Xw+n89ns9nsdrvdbrfb7fZ7vV6v1+v1+v3+///K5fL5fD6fz2ez2ex2u91ut9vt9nu9Xq/X6/X6/f7//8rl8vl8Pp/PZ7PZ7Ha73W632+32e71er9fr9fr9/v//yuXy+Xw+n89ns9nsdrvdbrfb7fZ7vV6v1+v1+v3+//8=";

async function verifyToken() {
    console.log("🚀 开始验证Token（无CORS版本）...");
    
    // 方法1: 使用XMLHttpRequest（更宽松的CORS策略）
    function testWithXHR(url) {
        return new Promise((resolve) => {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', url);
            xhr.setRequestHeader('x-aws-waf-token', token);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    console.log(`\n🎯 XHR测试: ${url}`);
                    console.log(`📊 状态: ${xhr.status}`);
                    console.log(`📋 响应头: ${xhr.getAllResponseHeaders()}`);
                    
                    // 检查Set-Cookie（注意：浏览器可能隐藏这个头部）
                    const setCookie = xhr.getResponseHeader('Set-Cookie');
                    if (setCookie) {
                        console.log(`🍪 Set-Cookie: ${setCookie}`);
                    }
                    
                    if (xhr.status === 200) {
                        console.log(`✅ 响应: ${xhr.responseText}`);
                    }
                    
                    resolve(xhr.status === 200);
                }
            };
            
            xhr.onerror = function() {
                console.log(`❌ XHR错误: ${url}`);
                resolve(false);
            };
            
            xhr.send();
        });
    }
    
    // 方法2: 简化的fetch（移除problematic选项）
    async function testWithFetch(url) {
        try {
            console.log(`\n🎯 Fetch测试: ${url}`);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'x-aws-waf-token': token
                }
                // 移除 credentials: 'include' 和其他可能导致CORS的选项
            });
            
            console.log(`📊 状态: ${response.status}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ 数据: ${JSON.stringify(data)}`);
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.log(`❌ Fetch错误: ${error.message}`);
            return false;
        }
    }
    
    // 方法3: 检查当前页面的网络请求
    function interceptNetworkRequests() {
        console.log("\n🕵️ 开始监控网络请求...");
        
        // 重写fetch来监控请求
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            console.log('🌐 检测到fetch请求:', args[0]);
            return originalFetch.apply(this, args);
        };
        
        console.log("✅ 网络监控已启用，刷新页面或进行任何操作来观察请求");
    }
    
    const endpoints = [
        'https://api.binance.com/api/v3/time',
        'https://api.binance.com/api/v3/ping'
    ];
    
    // 尝试不同方法
    for (const endpoint of endpoints) {
        await testWithXHR(endpoint);
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await testWithFetch(endpoint);
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // 启用网络监控
    interceptNetworkRequests();
    
    // 检查当前cookies
    console.log("\n🍪 当前页面Cookies:");
    console.log(document.cookie);
    
    console.log("\n💡 如果仍有CORS问题，请尝试:");
    console.log("1. 在Network面板手动查看请求");
    console.log("2. 使用Postman等外部工具测试");
    console.log("3. 检查币安页面本身的网络请求模式");
}

verifyToken(); 