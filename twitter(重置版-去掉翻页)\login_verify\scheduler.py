import time
from multiprocessing import Process

from loguru import logger
import json

from settings import *
from flask_api import app
from tester import TwitterValidTester
from login_get_cookie import Twitter_login_API
from db import RedisClient
from account_unlock import TweetUnlocke


class Scheduler:
    @staticmethod
    def valid_cookie(cycle=60*15):
        while True:
            logger.info('Cookies检测进程开始运行')
            try:
                tester = TwitterValidTester(
                    redis_cookies_name=REDIS_COOKIES_NAME, redis_accounts_name=REDIS_ACCOUNTS_NAME)
                tester.run()
                logger.info('Cookies检测完成')
                del tester

            except Exception as e:
                logger.error(repr(e))

            time.sleep(cycle)


    @staticmethod
    def generate_cookie(cycle=25):
        while True:
            logger.info('Cookies生成进程开始运行')
            try:
                redis_client = RedisClient(redis_name='proxy_ip:twitter')
                accounts_redis = RedisClient(redis_name='accounts:twitter_third')
                result = accounts_redis.get_account_from_redis()
                
                if result == (None, None):
                    logger.info('当前没有需要生成cookie的账号，等待下一次检查')
                    time.sleep(cycle)
                    continue
                    
                username, account_data = result
                if account_data:
                    account_info = json.loads(account_data)
                    password = account_info.get("password")
                    secret_key = account_info.get("secret_key")
                    email = account_info.get("email")
                    email_pwd = account_info.get("email_password")

                    logger.info(f"从Redis提取到的账号信息 - 用户名: {username}, 密码: {password}, 密钥: {secret_key}, 邮箱: {email}, 邮密: {email_pwd}")
                    
                    # 先创建一个临时实例来获取guest_token
                    temp_instance = Twitter_login_API(
                        guest_token="",  # 临时的，后面会获取真正的
                        user_identifier=username,
                        password=password,
                        secret_key=secret_key,
                        redis_client=redis_client
                    )
                    guest_token = temp_instance.get_guest_token()

                    if all([username, password, secret_key, guest_token]):
                        generator = Twitter_login_API(
                            guest_token=guest_token,
                            user_identifier=username,
                            password=password,
                            secret_key=secret_key,
                            redis_client=redis_client
                        )
                        generator.first_request()
                        logger.info('Cookies生成完成')
                        del generator

                time.sleep(cycle)
            except Exception as e:
                logger.error(repr(e))

    @staticmethod
    def api():
        logger.info('API接口开始运行')
        app.run(host='0.0.0.0', port=5003)

    @staticmethod
    def unlock_accounts(cycle=50):
        """检查被锁定的账号并尝试解锁"""
        while True:
            logger.info('账号解锁进程开始运行')
            try:
                locked_accounts_db = RedisClient(redis_name='accounts:locked')
                locked_accounts = locked_accounts_db.all()
                
                if locked_accounts:
                    logger.info(f'发现 {len(locked_accounts)} 个被锁定的账号，开始解锁流程')
                    
                    try:
                        bot = TweetUnlocke()
                        if bot.run():
                            logger.success("账号解锁执行完成")
                        else:
                            logger.warning("账号解锁执行失败,或者此账号错误被认为被封锁")
                    except Exception as e:
                        logger.error(f"账号解锁过程发生错误: {e}")
                else:
                    logger.info('没有发现被锁定的账号，等待下次检查')

            except Exception as e:
                logger.error(f'账号解锁进程发生错误: {repr(e)}')

            time.sleep(cycle)

    def run(self):
        """
        运行调度器
        """
        # api_process = Process(target=Scheduler.api)
        # api_process.start()

        login_process = Process(target=Scheduler.generate_cookie)
        login_process.start()

        # valid_process = Process(target=Scheduler.valid_cookie)
        # valid_process.start()

        # unlock_process = Process(target=Scheduler.unlock_accounts)
        # unlock_process.start()


if __name__ == '__main__':
    sch = Scheduler()
    sch.run()