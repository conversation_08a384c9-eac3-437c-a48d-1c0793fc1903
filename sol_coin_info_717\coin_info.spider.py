#!/usr/bin/env python312
"""
SOL链获取coin_info
存入aws中
支持断点续传
"""

import pymysql
import boto3
import time
import json
import redis
import re
import signal
import sys
import threading
from threading import Lock
from queue import Queue
from loguru import logger
from datetime import datetime
from curl_cffi import requests as cf_requests
from urllib.parse import urlparse
from utils import DB_BASE

class SolSpider():

    def __init__(self):
        """初始化"""
        self.db_config = {
            'host': '**********',
            'port': 6000,
            'user': 'root',
            'password': 'iAn7*+154-j9r3_dcm',
            'database': 'solana'
        }
        self.aws_config = {
            'aws_access_key_id': '********************',
            'aws_secret_access_key': 'Jax967rCDcH8uVdp3EQzd+wRR1NEm56IJgJUrWRn',
            'region_name': 'ap-northeast-1'
        }
        self.bucket_name = 'chainsight-dex-spider'
        self.s3 = boto3.client('s3', **self.aws_config)
        self.redis_config = {
            'host': '**************',
            'password': 123456,
            'port': 6379,
            'db': 4,
            'decode_responses': True
        }
        self.redis_client = redis.Redis(**self.redis_config)
        self.failed_url_key = 'sol_url:failed'
        self.db_base = DB_BASE()
        self.max_retry = 3
        self.timeout = 10
        
        # 断点续传相关配置
        self.progress_keys = {
            'last_address': 'sol_spider:last_address',
            'processed_count': 'sol_spider:processed_count',
            'total_count': 'sol_spider:total_count'
        }
        
        # 设置信号处理器
        self.setup_signal_handlers()
        self.is_interrupted = False
        
        # 多线程配置
        self.thread_count = 24
        self.progress_lock = Lock()
        
        # 已分发但未完成的任务集合（用于防重复）
        self.dispatched_addresses = set()
        self.dispatched_lock = Lock()
        
        # 代理池配置 - 三个代理平均分配
        self.proxy_pool = [
            {  # 代理1
                "http": "http://9bc99966eb7353:<EMAIL>:5001",
                "https": "http://9bc99966eb7353:<EMAIL>:5001"
            },
            {  # 代理2
                "http": "http://e045b6d42d7e5a:<EMAIL>:5001",
                "https": "http://e045b6d42d7e5a:<EMAIL>:5001"
            },
            {  # 代理3
                "http": "http://29be0ced785e61:<EMAIL>:5001",
                "https": "http://29be0ced785e61:<EMAIL>:5001"
            }
        ]

    def setup_signal_handlers(self):
        """设置信号处理器，处理Ctrl+C和程序终止"""
        def signal_handler(signum, frame):
            logger.warning(f"接收到信号 {signum}，正在安全退出...")
            self.is_interrupted = True
            self.save_interrupt_state()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # 程序终止

    def save_interrupt_state(self):
        """保存中断状态到Redis"""
        try:
            logger.info("程序被中断，进度已自动保存")
        except Exception as e:
            logger.error(f"保存中断状态失败: {e}")

    def initialize_progress(self):
        """初始化或恢复进度统计"""
        try:
            # 清理已分发集合
            self.dispatched_addresses.clear()
            
            # 检查是否有之前的进度
            last_address = self.redis_client.get(self.progress_keys['last_address'])
            processed_count = self.redis_client.get(self.progress_keys['processed_count'])
            
            if last_address and processed_count:
                logger.info(f"检测到之前的进度，继续从地址 {last_address} 开始处理")
                logger.info(f"已处理数量: {processed_count}")
            else:
                logger.info("开始新的处理任务")
                self.redis_client.set(self.progress_keys['processed_count'], 0)
                self.redis_client.set(self.progress_keys['last_address'], '')
            
            # 设置或获取总数量
            if not self.redis_client.exists(self.progress_keys['total_count']):
                total = self.get_total_count()
                self.redis_client.set(self.progress_keys['total_count'], total)
                logger.info(f"总共需要处理 {total} 条数据")
            
        except Exception as e:
            logger.error(f"初始化进度失败: {e}")
            raise

    def get_total_count(self):
        """获取总数量与address、uri，只统计icon为空的数据"""
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = """SELECT COUNT(DISTINCT address) FROM coin_info 
                         WHERE uri != '' AND uri IS NOT NULL 
                         AND (icon IS NULL OR icon = '')"""
                cursor.execute(sql)
                return cursor.fetchone()[0]
        finally:
            connection.close()

    def get_next_batch_by_address(self, batch_size=1000):
        """基于address主键获取下一批数据，只获取icon为空的数据"""
        last_address = self.redis_client.get(self.progress_keys['last_address']) or ''
        
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = """SELECT DISTINCT address, uri FROM coin_info 
                         WHERE address > %s AND uri != '' AND uri IS NOT NULL
                         AND (icon IS NULL OR icon = '')
                         ORDER BY address LIMIT %s"""
                cursor.execute(sql, (last_address, batch_size))
                result = cursor.fetchall()
                return result
        finally:
            connection.close()

    def update_progress(self, address):
        """更新处理进度（线程安全）"""
        try:
            with self.progress_lock:
                # 更新正式的处理进度
                self.redis_client.set(self.progress_keys['last_address'], address)
                self.redis_client.incr(self.progress_keys['processed_count'])
                
            # 从已分发集合中移除已完成的任务
            with self.dispatched_lock:
                self.dispatched_addresses.discard(address)  # 使用discard避免KeyError
                    
        except Exception as e:
            logger.error(f"更新进度失败: {e}")

    def get_progress_info(self):
        """获取进度信息"""
        try:
            total = int(self.redis_client.get(self.progress_keys['total_count']) or 0)
            processed = int(self.redis_client.get(self.progress_keys['processed_count']) or 0)
            last_address = self.redis_client.get(self.progress_keys['last_address']) or '未开始'
            
            if total > 0:
                progress = (processed / total) * 100
                remaining = total - processed
                
                return {
                    'total': total,
                    'processed': processed,
                    'remaining': remaining,
                    'progress': f"{progress:.2f}%",
                    'last_address': last_address
                }
            return None
        except Exception as e:
            logger.error(f"获取进度信息失败: {e}")
            return None

    def worker_thread(self, thread_id, task_queue):
        """工作线程函数"""
        processed_count = 0  # 移到try块外部，确保在异常处理中可以访问
        try:
            # 动态分配代理：0-7线程用代理1，8-15线程用代理2，16-23线程用代理3
            proxy_index = 0 if thread_id < self.thread_count // 3 else 1 if thread_id < self.thread_count // 3 * 2 else 2
            proxy = self.proxy_pool[proxy_index]
            logger.info(f"[线程{thread_id}] 启动，使用代理{proxy_index + 1}: {proxy}")
            
            while not self.is_interrupted:
                try:
                    # 从任务队列获取任务
                    task = task_queue.get(timeout=5)  # 5秒超时
                    if task is None:  # 结束信号
                        break
                        
                    address, uri = task
                    success = self.process_single_record(address, uri, thread_id, proxy)
                    
                    if success:
                        self.update_progress(address)
                        processed_count += 1
                        
                        # 定期显示线程统计
                        if processed_count % 20 == 0:
                            logger.info(f"[线程{thread_id}] 已处理 {processed_count} 条记录")
                    
                    # 添加延时避免请求过快
                    time.sleep(1)
                    
                except Exception as e:
                    error_type = type(e).__name__
                    error_msg = str(e)
                    
                    # 处理队列超时异常（正常情况，不是错误）
                    if "Empty" in error_type or "timeout" in error_msg.lower():
                        logger.debug(f"[线程{thread_id}] 队列等待超时，继续等待任务...")
                        continue
                    
                    # 其他异常则记录并退出
                    logger.error(f"[线程{thread_id}] 工作线程异常 - 类型: {error_type}, 消息: {error_msg}")
                    break
                    
        except Exception as e:
            # 捕获线程启动时的异常
            logger.error(f"[线程{thread_id}] 线程启动失败: {type(e).__name__} - {str(e)}")
        
        logger.info(f"[线程{thread_id}] 退出，共处理 {processed_count} 条记录")

    def run(self, batch_size=1000):
        """主执行方法 - 多线程处理，支持断点续传"""
        try:
            # 0. 预检查：测试基础连接
            logger.info("正在检查基础服务连接...")
            self._check_connections()
            
            # 提示过滤条件
            logger.info("📌 程序已配置为只处理 icon 字段为空的数据")
            
            # 1. 初始化进度
            self.initialize_progress()
            
            # 2. 显示当前状态
            progress_info = self.get_progress_info()
            if progress_info and progress_info['processed'] > 0:
                logger.info(f"继续执行：已处理 {progress_info['processed']}/{progress_info['total']} "
                           f"({progress_info['progress']})，最后处理地址：{progress_info['last_address']}")
            else:
                logger.info("开始新的多线程处理任务")
            
            # 3. 创建任务队列
            task_queue = Queue(maxsize=batch_size * 2)  # 队列大小为批次的2倍
            
            # 4. 启动工作线程
            threads = []
            for i in range(self.thread_count):
                thread = threading.Thread(target=self.worker_thread, args=(i, task_queue))
                thread.daemon = True
                thread.start()
                threads.append(thread)
                
                # 每个线程启动后延迟0.1秒，避免同时争抢相同的address范围
                if i < self.thread_count - 1:  # 最后一个线程不需要延迟
                    time.sleep(0.1)  # 减少延迟时间从1秒到0.1秒
            
            logger.info(f"已启动 {self.thread_count} 个工作线程（延迟启动以避免重复处理）")
            
            batch_count = 0
            total_tasks_added = 0
            
            # 5. 主循环：获取数据并分发任务
            while not self.is_interrupted:
                # 获取下一批数据
                batch_data = self.get_next_batch_by_address(batch_size)
                
                # 如果没有数据，说明全部处理完成
                if not batch_data:
                    logger.info("所有数据已分发完成，等待线程处理完毕...")
                    break
                
                # 过滤掉已经处理过的和已经分发的数据
                current_last_address = self.redis_client.get(self.progress_keys['last_address']) or ''
                filtered_data = []
                
                with self.dispatched_lock:
                    for address, uri in batch_data:
                        # 同时检查：1.未处理过 2.未分发过
                        if address > current_last_address and address not in self.dispatched_addresses:
                            filtered_data.append((address, uri))
                            # 标记为已分发
                            self.dispatched_addresses.add(address)
                
                if not filtered_data:
                    logger.warning(f"批次数据已全部处理过或已分发，跳过本批次")
                    time.sleep(2)  # 等待一下再尝试
                    continue
                
                batch_count += 1
                logger.info(f"开始分发第 {batch_count} 批，共 {len(filtered_data)} 条数据（过滤前：{len(batch_data)}条），已分发集合大小：{len(self.dispatched_addresses)}")
                
                # 将任务添加到队列
                for address, uri in filtered_data:
                    if self.is_interrupted:
                        break
                    task_queue.put((address, uri))
                    total_tasks_added += 1
                
                logger.info(f"第 {batch_count} 批数据已分发到任务队列")
                
                # 定期显示整体进度
                if batch_count % 5 == 0:
                    progress_info = self.get_progress_info()
                    if progress_info:
                        logger.info(f"整体进度：{progress_info['progress']} "
                                   f"({progress_info['processed']}/{progress_info['total']})")
                
                # 等待队列处理一段时间，避免过快获取下一批
                time.sleep(2)
            
            # 6. 发送结束信号给所有线程
            for _ in range(self.thread_count):
                task_queue.put(None)
            
            # 7. 等待所有线程完成
            logger.info("等待所有工作线程完成...")
            for thread in threads:
                thread.join(timeout=30)  # 最多等待30秒
            
            # 8. 根据结束原因记录日志
            if self.is_interrupted:
                logger.info("程序被中断，进度已保存")
            else:
                logger.info("多线程处理完成")
                
        except Exception as e:
            logger.error(f"多线程执行过程中发生错误: {e}")
            raise

    def process_single_record(self, address, uri, thread_id=0, proxy=None):
        """处理单条记录"""
        try:
            # 检查是否被中断
            if self.is_interrupted:
                logger.info(f"[线程{thread_id}] 检测到中断信号，停止处理")
                return False
                
            logger.info(f"[线程{thread_id}] 处理地址: {address}")
            
            # 发送请求
            response_data = self.make_request(uri, proxy)
            if not response_data:
                return False
                
            # 解析响应数据
            parsed_data = self.parse_data(response_data)
            if not parsed_data:
                return False
                
            image_url = parsed_data['image_url']
            extra_json = parsed_data['extra_json']
            
            # 下载图片并上传到S3
            s3_info = self.download_and_upload_image(image_url, address, proxy)
            
            # 更新数据库extra字段
            db_updated = self.update_database_extra(address, extra_json, s3_info)
            
            # 更新icon字段
            icon_updated = False
            if s3_info and s3_info.get('s3_key'):
                icon_updated = self.update_database_icon(address, s3_info['s3_key'])
            
            logger.info(f"[线程{thread_id}] 处理成功: {address}")
            return True
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"[线程{thread_id}] 处理失败 {address}: {error_msg}")
            
            # 检查是否是数据库连接问题
            if self._is_database_connection_error(error_msg):
                logger.error(f"[线程{thread_id}] 检测到数据库连接失败，准备终止程序")
                self.is_interrupted = True
                return False
            
            return False

    def cleanup_progress(self):
        """清理进度信息（可选）"""
        try:
            for key in self.progress_keys.values():
                self.redis_client.delete(key)
            logger.info("进度信息已清理")
        except Exception as e:
            logger.error(f"清理进度信息失败: {e}")

    def show_status(self):
        """显示当前状态"""
        progress_info = self.get_progress_info()
        if progress_info:
            print(f"""
        === SOL Spider 状态 ===
        总数量: {progress_info['total']} (仅统计icon为空的数据)
        已处理: {progress_info['processed']}
        剩余量: {progress_info['remaining']}
        进度: {progress_info['progress']}
        最后地址: {progress_info['last_address']}
        处理条件: 只处理 icon 字段为空的数据
        ========================
            """)
        else:
            print("没有找到进度信息")

    def get_database_connection(self):
        """连接数据库"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def get_address_and_uri_from_db(self):
        """从数据库中读取address和uri，只获取icon为空的数据"""
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = """SELECT DISTINCT address, uri FROM coin_info 
                         WHERE uri != '' AND (icon IS NULL OR icon = '')"""
                cursor.execute(sql)
                result = cursor.fetchall()
                return result
        except Exception as e:
            logger.error(f"查询失败: {e}")
            raise
        finally:
            connection.close()

    def make_request(self, url, proxy=None):
        """万能请求方法 - 使用curl_cffi统一处理所有类型的URL，支持重试和失败预警"""
        if proxy is None:
            # 如果没有传入代理，使用默认代理1，但这种情况不应该发生
            logger.warning("make_request方法未收到代理配置，使用默认代理")
            proxy = {
                "http": "http://9bc99966eb7353:<EMAIL>:5001",
                "https": "http://9bc99966eb7353:<EMAIL>:5001"
            }
        
        retry_count = 0
        
        while retry_count < self.max_retry:
            try:
                retry_count += 1
                logger.info(f"正在请求URL (第{retry_count}次尝试): {url}")
                
                response = cf_requests.get(url, proxies=proxy, impersonate="chrome116", timeout=self.timeout)
                response.raise_for_status()

                try:
                    return response.json()
                except:
                    return response
                    
            except Exception as e:
                if retry_count < self.max_retry:
                    time.sleep(2)
                else:
                    # 超过最大重试次数，记录失败并发送预警
                    self._handle_request_failure(url, str(e))
                    return None

        return None

    def _handle_request_failure(self, url, error_msg):
        """处理请求失败的情况：存入Redis并发送飞书预警"""
        try:
            failure_data = {
                'url': url,
                'error': error_msg,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'retry_count': self.max_retry
            }

            self.redis_client.lpush(self.failed_url_key, json.dumps(failure_data, ensure_ascii=False))
            # 发送飞书预警
            warning_msg = f"{url}解析失败，已写入redis"
            # self.db_base.send_to_fs(warning_msg)  # 暂时关闭飞书报警

        except Exception as e:
            logger.error(f"处理请求失败时出错: {e}")

    def get_failed_urls_count(self):
        """获取失败URL的数量"""
        try:
            count = self.redis_client.llen(self.failed_url_key)
            return count
        except Exception as e:
            return 0

    def get_failed_urls_sample(self, limit=5):
        """获取失败URL样例"""
        try:
            failed_urls = self.redis_client.lrange(self.failed_url_key, 0, limit - 1)
            sample_data = []
            for url_data in failed_urls:
                try:
                    data = json.loads(url_data)
                    sample_data.append(data)
                except:
                    continue
            return sample_data
        except Exception as e:
            return []

    def parse_data(self, response_data):
        """解析数据，提取image URL和社交媒体字段"""
        if not response_data:
            return None
            
        try:
            if hasattr(response_data, 'json'):
                try:
                    data = response_data.json()
                except:
                    return None
            elif isinstance(response_data, dict):
                data = response_data
            else:
                return None

            if 'error' in data:
                return None

            image_url = data.get('image', '')
            if not image_url:
                return None

            extra_data = {}
            
            # 第一步：常规字段提取
            if 'twitter' in data and data['twitter']:
                extra_data['twitter'] = data['twitter']
            if 'telegram' in data and data['telegram']:
                extra_data['telegram'] = data['telegram']
            if 'createdOn' in data and data['createdOn']:
                extra_data['createdOn'] = data['createdOn']
            
            # 从extensions对象提取
            if 'extensions' in data and isinstance(data['extensions'], dict):
                extensions = data['extensions']
                if 'twitter' in extensions and extensions['twitter'] and 'twitter' not in extra_data:
                    extra_data['twitter'] = extensions['twitter']
                if 'telegram' in extensions and extensions['telegram'] and 'telegram' not in extra_data:
                    extra_data['telegram'] = extensions['telegram']
            
            # 第二步：如果还没有提取到twitter或telegram，使用正则表达式作为保底方法
            if 'twitter' not in extra_data or 'telegram' not in extra_data:
                logger.info("使用正则表达式作为保底方法提取社交媒体链接")
                regex_extracted = self._extract_social_links_with_regex(data)
                
                # 只添加还没有提取到的字段
                if 'twitter' not in extra_data and 'twitter' in regex_extracted:
                    extra_data['twitter'] = regex_extracted['twitter']
                
                if 'telegram' not in extra_data and 'telegram' in regex_extracted:
                    extra_data['telegram'] = regex_extracted['telegram']
            
            # 转换为JSON字符串，只要有任意一个字段就生成
            extra_json = json.dumps(extra_data, ensure_ascii=False) if extra_data else None
            
            result = {
                'image_url': image_url,
                'extra_json': extra_json,
                'raw_data': data
            }
            
            return result
            
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return None

    def download_and_upload_image(self, image_url, address, proxy=None):
        """下载图片并上传到S3"""
        try:
            # 下载图片
            if proxy is None:
                # 如果没有传入代理，使用默认代理1，但这种情况不应该发生
                logger.warning("download_and_upload_image方法未收到代理配置，使用默认代理")
                proxy = {
                    "http": "http://9bc99966eb7353:<EMAIL>:5001",
                    "https": "http://9bc99966eb7353:<EMAIL>:5001"
                }
            
            retry_count = 0
            image_data = None
            
            while retry_count < self.max_retry:
                try:
                    retry_count += 1
                    
                    response = cf_requests.get(image_url, proxies=proxy, impersonate="chrome116", timeout=self.timeout)
                    response.raise_for_status()

                    content_type = response.headers.get('content-type', '').lower()
                    if not any(img_type in content_type for img_type in ['image/', 'application/octet-stream']):
                        pass  # 但仍然尝试上传，有些图片服务器不返回正确的content-type
                    
                    image_data = response.content
                    break
                    
                except Exception as e:
                    if retry_count < self.max_retry:
                        time.sleep(2)
                    else:
                        # 记录失败的图片URL
                        self._handle_image_download_failure(image_url, address, str(e))
                        return None
            
            if not image_data:
                return None
            
            # 生成S3对象名称：logo/address_101.png格式
            s3_object_name = f"logo/101-{address}.png"
            
            try:
                self.s3.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_object_name,
                    Body=image_data,
                    ContentType=self._get_content_type(s3_object_name),
                    ACL='public-read'
                )

                # 构建S3 URL
                s3_url = f"https://{self.bucket_name}.s3.{self.aws_config['region_name']}.amazonaws.com/{s3_object_name}"
                logger.info(f"图片上传成功: {s3_url}")
                
                return {
                    's3_url': s3_url,
                    's3_key': s3_object_name,
                    'file_size': len(image_data),
                    'original_url': image_url
                }
                
            except Exception as e:
                return None
                
        except Exception as e:
            return None
    
    def _get_file_extension(self, url):
        """从URL获取文件扩展名"""
        try:
            # 从URL路径中提取扩展名
            parsed = urlparse(url)
            path = parsed.path.lower()
            
            if path.endswith(('.jpg', '.jpeg')):
                return '.jpg'
            elif path.endswith('.png'):
                return '.png'
            elif path.endswith('.gif'):
                return '.gif'
            elif path.endswith('.webp'):
                return '.webp'
            elif path.endswith('.svg'):
                return '.svg'
            else:
                return '.jpg'  # 默认使用jpg
        except:
            return '.jpg'
    
    def _get_content_type(self, file_path):
        """根据文件路径或扩展名获取Content-Type"""
        # 如果是完整路径，提取扩展名
        if '/' in file_path:
            file_extension = '.' + file_path.split('.')[-1].lower()
        else:
            file_extension = file_path.lower()
            
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.svg': 'image/svg+xml'
        }
        return content_types.get(file_extension, 'image/png')  # 默认使用PNG
    
    def _handle_image_download_failure(self, image_url, address, error_msg):
        """处理图片下载失败的情况"""
        try:
            failure_data = {
                'type': 'image_download',
                'image_url': image_url,
                'address': address,
                'error': error_msg,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'retry_count': self.max_retry
            }
            
            failed_image_key = 'sol_image:failed'
            self.redis_client.lpush(failed_image_key, json.dumps(failure_data, ensure_ascii=False))
            
            # 发送飞书预警
            warning_msg = f"图片下载失败: {image_url} (地址: {address})，已写入redis"
            # self.db_base.send_to_fs(warning_msg)  # 暂时关闭飞书报警
            
        except Exception as e:
            pass

    def _extract_social_links_with_regex(self, data):
        """使用正则表达式作为保底方法提取社交媒体链接"""
        extracted_data = {}
        
        # 将整个数据转换为字符串进行正则匹配
        data_str = json.dumps(data, ensure_ascii=False).lower()
        
        # Twitter正则表达式模式
        twitter_patterns = [
            r'https?://(?:www\.)?(?:twitter\.com|x\.com)/[^\s"\'<>]+',
            r'https?://t\.co/[^\s"\'<>]+',
        ]
        
        # Telegram正则表达式模式
        telegram_patterns = [
            r'https?://(?:www\.)?t\.me/[^\s"\'<>]+',
            r'https?://(?:www\.)?telegram\.me/[^\s"\'<>]+',
        ]
        
        # 提取Twitter链接
        for pattern in twitter_patterns:
            matches = re.findall(pattern, data_str, re.IGNORECASE)
            if matches:
                # 选择第一个匹配的链接
                extracted_data['twitter'] = matches[0]
                break
        
        # 提取Telegram链接
        for pattern in telegram_patterns:
            matches = re.findall(pattern, data_str, re.IGNORECASE)
            if matches:
                # 选择第一个匹配的链接
                extracted_data['telegram'] = matches[0]
                break
        
        return extracted_data

    def update_database_icon(self, address, s3_key):
        """更新数据库中的icon字段"""
        if not s3_key:
            return False
            
        # 从s3_key提取路径，格式：logo/101-address.png -> /logo/101-address.png
        icon_path = f"/{s3_key}"
        
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = """UPDATE coin_info SET icon = %s WHERE address = %s"""
                cursor.execute(sql, (icon_path, address))
                connection.commit()
                
                logger.info(f"Icon字段更新成功: {address} - 路径: {icon_path}")
                return True
                
        except Exception as e:
            logger.error(f"Icon字段更新失败: {address} - 错误: {e}")
            connection.rollback()
            return False
        finally:
            connection.close()

    def update_database_extra(self, address, extra_json, s3_info=None):
        """更新数据库中的extra字段，只写入twitter、telegram、createdOn字段"""
        # 如果没有有效的社交媒体字段，则不更新数据库
        if not extra_json:
            return False
            
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                # 只写入用户需要的字段：twitter、telegram、createdOn
                # 不包含s3_image信息
                final_extra_json = extra_json
                
                # 更新数据库
                sql = """UPDATE coin_info SET extra = %s WHERE address = %s"""
                cursor.execute(sql, (final_extra_json, address))
                connection.commit()
                
                # 解析字段用于日志显示
                update_data = json.loads(extra_json) if extra_json else {}
                logger.info(f"数据库更新成功: {address} - 更新内容: {list(update_data.keys())}")
                return True
                
        except Exception as e:
            connection.rollback()
            return False
        finally:
            connection.close()

    def _is_database_connection_error(self, error_msg):
        """检查是否是数据库连接错误"""
        db_error_keywords = [
            "Can't connect to MySQL server",
            "timed out",
            "Connection refused", 
            "Lost connection to MySQL",
            "MySQL server has gone away",
            "Too many connections",
            "Access denied for user"
        ]
        
        error_lower = error_msg.lower()
        return any(keyword.lower() in error_lower for keyword in db_error_keywords)

    def _check_connections(self):
        """检查数据库和Redis连接"""
        try:
            # 测试数据库连接
            connection = self.get_database_connection()
            connection.close()
            logger.info("数据库连接成功")
            
            # 测试Redis连接
            self.redis_client.ping()
            logger.info("Redis连接成功")
            
        except Exception as e:
            logger.error(f"连接检查失败: {e}")
            raise



if __name__ == '__main__':
    spider = SolSpider()
    
    # 显示当前状态
    spider.show_status()
    
    try:
        # 执行爬虫
        spider.run(batch_size=1000)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
    finally:
        # 显示最终状态
        spider.show_status()