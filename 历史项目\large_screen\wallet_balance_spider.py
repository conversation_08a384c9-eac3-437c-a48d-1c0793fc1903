from settings import *
from Spiders.Spider import Spider_Crawler_Request
import requests
from loguru import logger
import base64
from datetime import datetime

class WalletBalance(Spider_Crawler_Request):

    def __init__(self):
        super().__init__()
        self.url = "https://api.coinank.com/indicatorapi/getExBalance"
        self.headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
        }
        self.crawl_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def fetch_data(self):
        response = self.get(self.url, headers=self.headers)
        if response.status_code == 200:
            return response.json().get("data", [])
        else:
            logger.error(f'请求失败，状态码: {response.status_code}')
            return []

    def fetch_icon(self, name):
        icon_url = f"https://s.cdnblock.com/image/exchange/64/{name}.png"
        response = requests.get(icon_url, headers=self.headers)
        if response.status_code == 200:
            return 'data:image/png;base64,' + base64.b64encode(response.content).decode('utf-8')
        elif response.status_code == 404:
            logger.error(f'图标请求失败，状态码: {response.status_code}，交易所: {name}')
            return None
        else:
            logger.error(f'图标请求失败，状态码: {response.status_code}')
            return None

    def update_database(self, exchanges):
        for exchange in exchanges:
            name = exchange.get("exchangeName")
            balance = exchange.get("balance")
            balance_change = exchange.get("balanceChange")
            d7_balance_change = exchange.get("d7BalanceChange")
            d30_balance_change = exchange.get("d30BalanceChange")
            icon = self.fetch_icon(name)

            sql_create_table = """
            CREATE TABLE IF NOT EXISTS wallet_balance (
                name VARCHAR(255) PRIMARY KEY,
                balance FLOAT,
                balance_change FLOAT,
                d7_balance_change FLOAT,
                d30_balance_change FLOAT,
                icon TEXT,
                crawl_date DATETIME
            )
            """
            self.insert_mysql(sql_create_table)

            sql_replace = """
            REPLACE INTO wallet_balance (name, balance, balance_change, d7_balance_change, d30_balance_change, icon, crawl_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            if icon is None:
                continue  # 如果图标请求失败，跳过此条记录的更新
            v_list = [name, balance, balance_change, d7_balance_change, d30_balance_change, icon, self.crawl_date]
            self.insert_mysql(sql=sql_replace, v_list=v_list)
            logger.info(f'数据库更新: 交易所 {name}, 余额 {balance}, 今日变化{balance_change}, 本周变化{d7_balance_change}, 本月变化{d30_balance_change}, 图标已更新, 爬取日期 {self.crawl_date}')

    def run(self):
        exchanges = self.fetch_data()
        if exchanges:
            self.update_database(exchanges)

if __name__ == '__main__':
    wallet_balance = WalletBalance()
    wallet_balance.run()
