from curl_cffi import requests
import json

address = "******************************************"

PRIMARY_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

headers = {
    "accept": "application/json, text/plain, */*",
    "referer": f"https://gmgn.ai/eth/token/{address}"
}
cookies = {
    "_ga": "GA1.1.1787124478.1747114971",
    "cf_clearance": "XVkOIWlIwhRjB3Aaj5_ng_enlmeZg5.qVC_qpvPT_Xk-1753768898-*******-cEBQnjBA_msp3FA0KwfJieBQuPtgMIyMBlgKC7rK7sAOi7xy5lUuYnREnDj4oopTb88DnMflEW9Y6bR4E.Oirssy3rDpWr1O2wrllpnC665s_z0nShBAN1ZTRg94IuDBuKPDKb36LBb87gQcGUoBt89Z_B_o67skYH0WZymlSLJUVhxpkSkIzCJZYy38F7b.s6oFGds3IP4kKvbNlWzE6cIsFLTEHNCSv3DvDX.S.5E",
    "_ga_0XM0LYXGC8": "GS2.1.s1753767626$o63$g1$t1753768902$j60$l0$h0",
    "__cf_bm": "Yk_GVIw.QPPExyLHeWFrJxgU.KU5HInsopp07Or4J2o-1753768896-*******-i5rqmt5UhlLNauWC5ZvkcFmfoPW.mFFtczRFnkDintb6Cr3aQW_a9uAXt07deFemEZtQiSTttYHQglJQI_4_9u_IhIoK9ri61akXHDbq9Io"
}
url = "https://gmgn.ai/api/v1/mutil_window_token_info"
params = {
    "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
    "client_id": "gmgn_web_*************-0d68c0a",
    "from_app": "gmgn",
    "app_ver": "*************-0d68c0a",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a0adc6758070914b5d3cd4679349eed1",
    "os": "web"
}
data = {
    "chain": "eth",
    "addresses": [
        address
    ]
}
data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, params=params, data=data, proxies=PRIMARY_PROXY, impersonate="chrome116")
print(response)
liquidity = response.json()['data'][0]['liquidity']
print(liquidity)
