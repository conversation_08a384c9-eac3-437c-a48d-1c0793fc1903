import redis
import requests
import json
from loguru import logger

redis_client = redis.Redis(
    host='**************',
    port=6379,
    db=14,
    password='123456',
    decode_responses=True
)

proxy_api = "***************************************************"
proxies_api = {
    'http': proxy_api,
    'https': proxy_api,
}

SITES_CONFIG = {
    'bsc': {
        'cookie_key': 'cf_cookies:bscscan',
        'header_key': 'cf_headers:bscscan',
        'verify_url': 'https://bscscan.com/tokens'
    }
}


def verify_single_site(site_name, config):
    """验证单个网站的cookie"""
    try:
        cookies_json = redis_client.get(config['cookie_key'])
        if not cookies_json:
            logger.error(f"{site_name} - 未在Redis中找到cookie")
            return False

        cookies = json.loads(cookies_json)

        user_agent = redis_client.get(config['header_key'])
        if not user_agent:
            logger.error(f"{site_name} - 未在Redis中找到user agent")
            return False

        headers = {'User-Agent': user_agent}

        if site_name == 'bsc':
            response = requests.get(
                url=config['verify_url'],
                headers=headers,
                cookies=cookies,
                timeout=10
            )
        else:
            response = requests.get(
                url=config['verify_url'],
                headers=headers,
                cookies=cookies,
                proxies=proxies_api,
                timeout=10
            )

        logger.info(f"{site_name} - 状态码: {response.status_code}")

        if response.status_code == 200:
            logger.info(f"{site_name} - Cookie验证成功！")
            return True
        else:
            logger.error(f"{site_name} - Cookie验证失败！状态码: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"{site_name} - 验证过程中出错: {str(e)}")
        return False


def verify_all_cookies():
    """验证所有网站的cookie"""
    results = {}
    invalid_sites = []

    for site_name, config in SITES_CONFIG.items():
        logger.info(f"\n开始验证 {site_name} 的cookie...")
        is_valid = verify_single_site(site_name, config)
        results[site_name] = is_valid

        if not is_valid:
            invalid_sites.append(site_name)

    logger.info("\n=== Cookie验证总结报告 ===")
    logger.info(f"总计检查站点数: {len(SITES_CONFIG)}")
    logger.info(f"有效cookie数: {len(SITES_CONFIG) - len(invalid_sites)}")
    logger.info(f"无效cookie数: {len(invalid_sites)}")

    if invalid_sites:
        logger.warning("以下站点的cookie无效或验证失败：")
        for site in invalid_sites:
            logger.warning(f"- {site}")
    else:
        logger.success("所有站点的cookie都有效！")

    return results


if __name__ == "__main__":
    verify_all_cookies()
