# import requests
# import re
# import json
# import pyotp
# import redis
# from loguru import logger

# class Twitter_login_API():

#     def __init__(self, guest_token, user_identifier, password, sceret_key):
#         self.redis_client = redis.Redis(
#             host='**************',
#             port=6379,
#             db=14,
#             password='123456',
#             decode_responses=True
#         )
#         self.headers = {
#             "authority": "api.x.com",
#             "accept": "*/*",
#             "accept-language": "zh-CN,zh;q=0.9",
#             "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
#             "cache-control": "no-cache",
#             "content-type": "application/json",
#             "origin": "https://x.com",
#             "pragma": "no-cache",
#             "referer": "https://x.com/",
#             "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
#             "sec-ch-ua-mobile": "?0",
#             "sec-ch-ua-platform": "\"Windows\"",
#             "sec-fetch-dest": "empty",
#             "sec-fetch-mode": "cors",
#             "sec-fetch-site": "same-site",
#             "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
#             "x-client-transaction-id": "dO+bEKTvB9FD9OHxhLCHnNSZGRWEdVU/00+ciYp00sKdrVifEZEBdU2iiDKftspO/UDLEHe7TGkOjU86P33cLN5zfRTNdw",
#             "x-guest-token": guest_token,
#             "x-twitter-active-user": "yes",
#             "x-twitter-client-language": "zh-cn"
#         }
#         self.cookies = {
#             "guest_id": "v1%3A173985854443457262",
#             "night_mode": "2",
#             "guest_id_marketing": "v1%3A173985854443457262",
#             "guest_id_ads": "v1%3A173985854443457262",
#             "gt": guest_token,
#             "personalization_id": "\"v1_CkttitDq5IdDtc8ha8TCtw==\""
#         }
#         self.user_identifier = user_identifier
#         self.password = password
#         self.sceret_key = sceret_key


#     @staticmethod
#     def get_guest_token():
#         '''此方法是获取最新的guest_token 以此保证请求有效'''
#         headers = {
#             "authority": "x.com",
#             "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
#             "referer": "https://twitter.com/",
#             "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
#         }
#         url = "https://x.com/"
#         params = {
#             "mx": "2"
#         }
#         response = requests.get(url, headers=headers, params=params)
#         # 获取guest_token
#         gt_match = re.search(r'gt=(\d+);', response.text)
#         if gt_match:
#             guest_token = gt_match.group(1)
#             logger.success("获取到'guest_token'")
#             return guest_token
#         else:
#             logger.info("gt value not found")
#             return None


#     def first_request(self):
#         '''此次请求获取flow_token、subtask_id、att'''
#         headers = self.headers
#         cookies = self.cookies
#         url = "https://api.x.com/1.1/onboarding/task.json"
#         params = {
#             "flow_name": "login"
#         }
#         data = {
#             "input_flow_data": {
#                 "flow_context": {
#                     "debug_overrides": {},
#                     "start_location": {
#                         "location": "splash_screen"
#                     }
#                 }
#             },
#             "subtask_versions": {
#                 "action_list": 2,
#                 "alert_dialog": 1,
#                 "app_download_cta": 1,
#                 "check_logged_in_account": 1,
#                 "choice_selection": 3,
#                 "contacts_live_sync_permission_prompt": 0,
#                 "cta": 7,
#                 "email_verification": 2,
#                 "end_flow": 1,
#                 "enter_date": 1,
#                 "enter_email": 2,
#                 "enter_password": 5,
#                 "enter_phone": 2,
#                 "enter_recaptcha": 1,
#                 "enter_text": 5,
#                 "enter_username": 2,
#                 "generic_urt": 3,
#                 "in_app_notification": 1,
#                 "interest_picker": 3,
#                 "js_instrumentation": 1,
#                 "menu_dialog": 1,
#                 "notifications_permission_prompt": 2,
#                 "open_account": 2,
#                 "open_home_timeline": 1,
#                 "open_link": 1,
#                 "phone_verification": 4,
#                 "privacy_options": 1,
#                 "security_key": 3,
#                 "select_avatar": 4,
#                 "select_banner": 2,
#                 "settings_list": 7,
#                 "show_code": 1,
#                 "sign_up": 2,
#                 "sign_up_review": 4,
#                 "tweet_selection_urt": 1,
#                 "update_users": 1,
#                 "upload_media": 1,
#                 "user_recommendations_list": 4,
#                 "user_recommendations_urt": 1,
#                 "wait_spinner": 3,
#                 "web_modal": 1
#             }
#         }
#         data = json.dumps(data, separators=(',', ':'))
#         response = requests.post(url, headers=headers, cookies=cookies, params=params, data=data)
#         if response.status_code == 200:
#             response_data = response.json()
#             flow_token = response_data.get("flow_token")
#             subtasks = response_data.get("subtasks", [])
#             subtask_id = subtasks[0].get("subtask_id") if subtasks else None

#             # att获取
#             set_cookie = response.headers.get("Set-Cookie", "")
#             att_value = None
#             if set_cookie:
#                 for cookie in set_cookie.split(";"):
#                     if cookie.strip().startswith("att="):
#                         att_value = cookie.strip()
#                         break

#             # logger.info(f"第一次:获取到'Flow Token': {flow_token}")
#             # logger.info(f"第一次:获取到'Subtask ID': {subtask_id}")
#             logger.success("获取到'Att Value'")
#             # logger.info("=====================================================")

#             self.seconed_request(flow_token, subtask_id, att_value)
#         else:
#             logger.info("Failed to get response:", response.text)


#     def seconed_request(self, flow_token, subtask_id, att_value=None):
#         '''此次请求进行验证,开始登录,且更新Subtask ID'''
#         headers = self.headers
#         cookies = self.cookies
#         # 如果提取到att值，则更新cookies
#         if att_value:
#             self.cookies.update({att_value.split("=")[0]: att_value.split("=")[1]})
#         url = "https://api.x.com/1.1/onboarding/task.json"
#         data = {
#             "flow_token": flow_token,
#             "subtask_inputs": [
#                 {
#                     "subtask_id": subtask_id,
#                     "js_instrumentation": {
#                         "link": "next_link"
#                     }
#                 }
#             ]
#         }
#         data = json.dumps(data, separators=(',', ':'))
#         response = requests.post(url, headers=headers, cookies=cookies, data=data)
#         if response.status_code == 200:
#             response_data = response.json()
#             flow_token = response_data.get("flow_token")
#             subtasks = response_data.get("subtasks", [])
#             subtask_id = subtasks[0].get("subtask_id") if subtasks else None

#             # logger.info(f"第二次:获取到'Flow Token': {flow_token}")
#             # logger.info(f"第二次:获取到'Subtask ID': {subtask_id}")
#             # logger.info("=====================================================")

#             self.third_request(flow_token, subtask_id)
#         else:
#             logger.info("Failed to get response:", response.text)
        

#     def third_request(self, flow_token, subtask_id):
#         '''此次请求是输入账号,且更新Subtask ID'''
#         headers = self.headers
#         cookies = self.cookies
#         url = "https://api.x.com/1.1/onboarding/task.json"
#         data = {
#             "flow_token": flow_token,
#             "subtask_inputs": [
#                 {
#                     "subtask_id": subtask_id,
#                     "settings_list": {
#                         "setting_responses": [
#                             {
#                                 "key": "user_identifier",
#                                 "response_data": {
#                                     "text_data": {
#                                         "result": self.user_identifier
#                                     }
#                                 }
#                             }
#                         ],
#                         "link": "next_link"
#                     }
#                 }
#             ]
#         }
#         data = json.dumps(data, separators=(',', ':'))
#         response = requests.post(url, headers=headers, cookies=cookies, data=data)
#         if response.status_code == 200:
#             response_data = response.json()
#             flow_token = response_data.get("flow_token")
#             subtasks = response_data.get("subtasks", [])
#             subtask_id = subtasks[0].get("subtask_id") if subtasks else None

#             # logger.info(f"第三次:获取到'Flow Token': {flow_token}")
#             # logger.info(f"第三次:获取到'Subtask ID': {subtask_id}")
#             # logger.info("=====================================================")
#             logger.success("账号输入成功")

#             self.four_request(flow_token, subtask_id)
#         else:
#             logger.info("Failed to get response:", response.text)
        

#     def four_request(self, flow_token, subtask_id):
#         '''此次请求是输入密码,且更新Subtask ID'''
#         headers = self.headers
#         cookies = self.cookies
#         url = "https://api.x.com/1.1/onboarding/task.json"
#         data = {
#             "flow_token": flow_token,
#             "subtask_inputs": [
#                 {
#                     "subtask_id": subtask_id,
#                     "enter_password": {
#                         "password": self.password,
#                         "link": "next_link"
#                     }
#                 }
#             ]
#         }
#         data = json.dumps(data, separators=(',', ':'))
#         response = requests.post(url, headers=headers, cookies=cookies, data=data)
#         if response.status_code == 200:
#             response_data = response.json()
#             flow_token = response_data.get("flow_token")
#             subtasks = response_data.get("subtasks", [])
#             subtask_id = subtasks[0].get("subtask_id") if subtasks else None

#             # logger.info(f"第四次:获取到'Flow Token': {flow_token}")
#             # logger.info(f"第四次:获取到'Subtask ID': {subtask_id}")
#             # logger.info("=====================================================")
#             logger.success("密码输入成功")

#             self.get_code()
#             self.five_request(flow_token, subtask_id)
#         else:
#             logger.info("Failed to get response:", response.text)


#     def get_code(self):    
#         '''此方法为获取验证码,接第五次请求'''
#         # sceret_key = "FGNE7S6MRG6CIQYM"
#         totp = pyotp.TOTP(self.sceret_key)
#         verify_code = totp.now()
#         logger.success("获取验证码成功")
#         # logger.info(f"获取到的验证码: {verify_code}")
#         return verify_code


#     def five_request(self, flow_token, subtask_id, verify_code=None):
#         '''此次请求是接收获取到的验证码,然后再发送请求,且更新Subtask ID'''
#         '''获取到cookie并存储到Redis'''
#         headers = self.headers
#         cookies = self.cookies
#         verify_code = self.get_code()
#         # logger.info(f"发送请求的验证码:{verify_code}")
#         url = "https://api.x.com/1.1/onboarding/task.json"
#         data = {
#             "flow_token": flow_token,
#             "subtask_inputs": [
#                 {
#                     "subtask_id": subtask_id,
#                     "enter_text": {
#                         "text": verify_code,
#                         "link": "next_link"
#                     }
#                 }
#             ]
#         }
#         data = json.dumps(data, separators=(',', ':'))
#         response = requests.post(url, headers=headers, cookies=cookies, data=data)
#         if response.status_code == 200:
#             response_data = response.json()
#             flow_token = response_data.get("flow_token")
#             subtasks = response_data.get("subtasks", [])
#             subtask_id = subtasks[0].get("subtask_id") if subtasks else None

#             # logger.info(f"Flow Token: {flow_token}")
#             # logger.info(f"Subtask ID: {subtask_id}")
#             set_cookie = response.headers.get("Set-Cookie", "")
#             # print(set_cookie)

#             # 解析并存储cookie到Redis
#             cookie_dict = {}
#             cookies = re.findall(r'(\w+)=([^;,]+)', set_cookie)
#             for key, value in cookies:
#                 if key in ["kdt", "twid", "ct0", "auth_token"]:
#                     value = value.strip('"')
#                     cookie_dict[key] = value
#             self.redis_client.hset("twitter_cookie_api", mapping=cookie_dict)
#             logger.success(f"账号:{user_identifier}---Cookie存储成功")

#         else:
#             logger.info("Failed to get response:", response.text)

        
# if __name__ == '__main__':
#     guest_token = Twitter_login_API.get_guest_token()
#     user_identifier = "SaddatP16063"
#     password = "ClX4iIAwAeS"
#     secret_key = "XLM7BKZGE3TBRBPW"
#     if guest_token:
#         action = Twitter_login_API(guest_token, user_identifier, password, secret_key)
#         action.first_request()


# '''
# benneth91131----vzsh6RNS2xFc----OO7GGPCOAGZ6JPQT
# BrasUlysse19234----jF54LqQ1IMy----LEYQCJ43PNNI5GGG
# SaddatP16063----ClX4iIAwAeS----XLM7BKZGE3TBRBPW
# AmeeMenter68583----v9Taq2e6G0Ua----2DJPK6IUAH62F4JK
# MMolino14333----ZxT1i4c7MGb----FGNE7S6MRG6CIQYM
# NGrossen89627----nNUPgoP2YcD----AYCZFN5XJ2IREJSW
# CourieT7623----eYrsLD8jGtD----QOWKQIKAZ5KTKAWA
# swim_logan47535----DCKKuzgCUbB----AY7JJZC3NH3EVWP5
# LeslieTeff90534----aSzUU73Ppy7A----BMDJWNZNNWCKY6Z2
# NimickSoon55558----LwyBAvPXLXol----W5VV3I4AXW5B6SIA
# '''


'''账号池版本'''
import requests
import re
import json
import pyotp
import redis
import random
from loguru import logger

class Twitter_login_API():

    def __init__(self, guest_token, user_identifier, password, sceret_key):
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            db=14,
            password='123456',
            decode_responses=True
        )
        self.headers = {
            # "authority": "api.x.com",
            # "accept": "*/*",
            # "accept-language": "zh-CN,zh;q=0.9",
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            # "cache-control": "no-cache",
            "content-type": "application/json",
            # "origin": "https://x.com",
            # "pragma": "no-cache",
            # "referer": "https://x.com/",
            # "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
            # "sec-ch-ua-mobile": "?0",
            # "sec-ch-ua-platform": "\"Windows\"",
            # "sec-fetch-dest": "empty",
            # "sec-fetch-mode": "cors",
            # "sec-fetch-site": "same-site",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
            "x-client-transaction-id": "dO+bEKTvB9FD9OHxhLCHnNSZGRWEdVU/00+ciYp00sKdrVifEZEBdU2iiDKftspO/UDLEHe7TGkOjU86P33cLN5zfRTNdw",
            "x-guest-token": guest_token,
            "x-twitter-active-user": "yes",
            "x-twitter-client-language": "zh-cn"
        }
        self.cookies = {
            "guest_id": "v1%3A173985854443457262",
            "night_mode": "2",
            "guest_id_marketing": "v1%3A173985854443457262",
            "guest_id_ads": "v1%3A173985854443457262",
            "gt": guest_token,
            "personalization_id": "\"v1_CkttitDq5IdDtc8ha8TCtw==\""
        }
        self.user_identifier = user_identifier
        self.password = password
        self.sceret_key = sceret_key


    @staticmethod
    def get_guest_token():
        '''此方法是获取最新的guest_token 以此保证请求有效'''
        headers = {
            "authority": "x.com",
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "referer": "https://twitter.com/",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        }
        url = "https://x.com/"
        params = {
            "mx": "2"
        }
        response = requests.get(url, headers=headers, params=params)
        # 获取guest_token
        gt_match = re.search(r'gt=(\d+);', response.text)
        if gt_match:
            guest_token = gt_match.group(1)
            logger.success("获取到'guest_token'")
            return guest_token
        else:
            logger.info("gt value not found")
            return None


    def first_request(self):
        '''此次请求获取flow_token、subtask_id、att'''
        headers = self.headers
        cookies = self.cookies
        url = "https://api.x.com/1.1/onboarding/task.json"
        params = {
            "flow_name": "login"
        }
        data = {
            "input_flow_data": {
                "flow_context": {
                    "debug_overrides": {},
                    "start_location": {
                        "location": "splash_screen"
                    }
                }
            },
            "subtask_versions": {
                "action_list": 2,
                "alert_dialog": 1,
                "app_download_cta": 1,
                "check_logged_in_account": 1,
                "choice_selection": 3,
                "contacts_live_sync_permission_prompt": 0,
                "cta": 7,
                "email_verification": 2,
                "end_flow": 1,
                "enter_date": 1,
                "enter_email": 2,
                "enter_password": 5,
                "enter_phone": 2,
                "enter_recaptcha": 1,
                "enter_text": 5,
                "enter_username": 2,
                "generic_urt": 3,
                "in_app_notification": 1,
                "interest_picker": 3,
                "js_instrumentation": 1,
                "menu_dialog": 1,
                "notifications_permission_prompt": 2,
                "open_account": 2,
                "open_home_timeline": 1,
                "open_link": 1,
                "phone_verification": 4,
                "privacy_options": 1,
                "security_key": 3,
                "select_avatar": 4,
                "select_banner": 2,
                "settings_list": 7,
                "show_code": 1,
                "sign_up": 2,
                "sign_up_review": 4,
                "tweet_selection_urt": 1,
                "update_users": 1,
                "upload_media": 1,
                "user_recommendations_list": 4,
                "user_recommendations_urt": 1,
                "wait_spinner": 3,
                "web_modal": 1
            }
        }
        data = json.dumps(data, separators=(',', ':'))
        response = requests.post(url, headers=headers, cookies=cookies, params=params, data=data)
        if response.status_code == 200:
            response_data = response.json()
            flow_token = response_data.get("flow_token")
            subtasks = response_data.get("subtasks", [])
            subtask_id = subtasks[0].get("subtask_id") if subtasks else None

            # att获取
            set_cookie = response.headers.get("Set-Cookie", "")
            att_value = None
            if set_cookie:
                for cookie in set_cookie.split(";"):
                    if cookie.strip().startswith("att="):
                        att_value = cookie.strip()
                        break

            # logger.info(f"第一次:获取到'Flow Token': {flow_token}")
            # logger.info(f"第一次:获取到'Subtask ID': {subtask_id}")
            logger.success("获取到'Att Value'")
            # logger.info("=====================================================")

            self.seconed_request(flow_token, subtask_id, att_value)
        else:
            logger.info("Failed to get response:", response.text)


    def seconed_request(self, flow_token, subtask_id, att_value=None):
        '''此次请求进行验证,开始登录,且更新Subtask ID'''
        headers = self.headers
        cookies = self.cookies
        # 如果提取到att值，则更新cookies
        if att_value:
            self.cookies.update({att_value.split("=")[0]: att_value.split("=")[1]})
        url = "https://api.x.com/1.1/onboarding/task.json"
        data = {
            "flow_token": flow_token,
            "subtask_inputs": [
                {
                    "subtask_id": subtask_id,
                    "js_instrumentation": {
                        "link": "next_link"
                    }
                }
            ]
        }
        data = json.dumps(data, separators=(',', ':'))
        response = requests.post(url, headers=headers, cookies=cookies, data=data)
        if response.status_code == 200:
            response_data = response.json()
            flow_token = response_data.get("flow_token")
            subtasks = response_data.get("subtasks", [])
            subtask_id = subtasks[0].get("subtask_id") if subtasks else None

            # logger.info(f"第二次:获取到'Flow Token': {flow_token}")
            # logger.info(f"第二次:获取到'Subtask ID': {subtask_id}")
            # logger.info("=====================================================")

            self.third_request(flow_token, subtask_id)
        else:
            logger.info("Failed to get response:", response.text)
        

    def third_request(self, flow_token, subtask_id):
        '''此次请求是输入账号,且更新Subtask ID'''
        headers = self.headers
        cookies = self.cookies
        url = "https://api.x.com/1.1/onboarding/task.json"
        data = {
            "flow_token": flow_token,
            "subtask_inputs": [
                {
                    "subtask_id": subtask_id,
                    "settings_list": {
                        "setting_responses": [
                            {
                                "key": "user_identifier",
                                "response_data": {
                                    "text_data": {
                                        "result": self.user_identifier
                                    }
                                }
                            }
                        ],
                        "link": "next_link"
                    }
                }
            ]
        }
        data = json.dumps(data, separators=(',', ':'))
        response = requests.post(url, headers=headers, cookies=cookies, data=data)
        if response.status_code == 200:
            response_data = response.json()
            flow_token = response_data.get("flow_token")
            subtasks = response_data.get("subtasks", [])
            subtask_id = subtasks[0].get("subtask_id") if subtasks else None

            # logger.info(f"第三次:获取到'Flow Token': {flow_token}")
            # logger.info(f"第三次:获取到'Subtask ID': {subtask_id}")
            # logger.info("=====================================================")
            logger.success("账号输入成功")

            self.four_request(flow_token, subtask_id)
        else:
            logger.info("Failed to get response:", response.text)
        

    def four_request(self, flow_token, subtask_id):
        '''此次请求是输入密码,且更新Subtask ID'''
        headers = self.headers
        cookies = self.cookies
        url = "https://api.x.com/1.1/onboarding/task.json"
        data = {
            "flow_token": flow_token,
            "subtask_inputs": [
                {
                    "subtask_id": subtask_id,
                    "enter_password": {
                        "password": self.password,
                        "link": "next_link"
                    }
                }
            ]
        }
        data = json.dumps(data, separators=(',', ':'))
        response = requests.post(url, headers=headers, cookies=cookies, data=data)
        if response.status_code == 200:
            response_data = response.json()
            flow_token = response_data.get("flow_token")
            subtasks = response_data.get("subtasks", [])
            subtask_id = subtasks[0].get("subtask_id") if subtasks else None

            # logger.info(f"第四次:获取到'Flow Token': {flow_token}")
            # logger.info(f"第四次:获取到'Subtask ID': {subtask_id}")
            # logger.info("=====================================================")
            logger.success("密码输入成功")

            self.get_code()
            self.five_request(flow_token, subtask_id)
        else:
            logger.info("Failed to get response:", response.text)


    def get_code(self):    
        '''此方法为获取验证码,接第五次请求'''
        # sceret_key = "FGNE7S6MRG6CIQYM"
        totp = pyotp.TOTP(self.sceret_key)
        verify_code = totp.now()
        logger.success("获取验证码成功")
        # logger.info(f"获取到的验证码: {verify_code}")
        return verify_code


    def five_request(self, flow_token, subtask_id, verify_code=None):
        '''此次请求是接收获取到的验证码,然后再发送请求,且更新Subtask ID'''
        '''获取到cookie并存储到Redis'''
        headers = self.headers
        cookies = self.cookies
        verify_code = self.get_code()
        # logger.info(f"发送请求的验证码:{verify_code}")
        url = "https://api.x.com/1.1/onboarding/task.json"
        data = {
            "flow_token": flow_token,
            "subtask_inputs": [
                {
                    "subtask_id": subtask_id,
                    "enter_text": {
                        "text": verify_code,
                        "link": "next_link"
                    }
                }
            ]
        }
        data = json.dumps(data, separators=(',', ':'))
        response = requests.post(url, headers=headers, cookies=cookies, data=data)
        if response.status_code == 200:
            response_data = response.json()
            flow_token = response_data.get("flow_token")
            subtasks = response_data.get("subtasks", [])
            subtask_id = subtasks[0].get("subtask_id") if subtasks else None

            # logger.info(f"Flow Token: {flow_token}")
            # logger.info(f"Subtask ID: {subtask_id}")
            set_cookie = response.headers.get("Set-Cookie", "")
            # print(set_cookie)

            # 解析并存储cookie到Redis
            cookie_dict = {}
            cookies = re.findall(r'(\w+)=([^;,]+)', set_cookie)
            for key, value in cookies:
                if key in ["kdt", "twid", "ct0", "auth_token"]:
                    value = value.strip('"')
                    cookie_dict[key] = value
            self.redis_client.hset("twitter_cookie_api", mapping=cookie_dict)
            logger.success(f"账号:{user_identifier}---Cookie存储成功")

        else:
            logger.info("Failed to get response:", response.text)

        
if __name__ == '__main__':

    # 账号池
    account_pool = [
        "benneth91131----vzsh6RNS2xFc----OO7GGPCOAGZ6JPQT",
        "BrasUlysse19234----jF54LqQ1IMy----LEYQCJ43PNNI5GGG",
        "SaddatP16063----ClX4iIAwAeS----XLM7BKZGE3TBRBPW",
        "AmeeMenter68583----v9Taq2e6G0Ua----2DJPK6IUAH62F4JK",
        "MMolino14333----ZxT1i4c7MGb----FGNE7S6MRG6CIQYM",
        "NGrossen89627----nNUPgoP2YcD----AYCZFN5XJ2IREJSW",
        "CourieT7623----eYrsLD8jGtD----QOWKQIKAZ5KTKAWA",
        "swim_logan47535----DCKKuzgCUbB----AY7JJZC3NH3EVWP5",
        "LeslieTeff90534----aSzUU73Ppy7A----BMDJWNZNNWCKY6Z2",
        "NimickSoon55558----LwyBAvPXLXol----W5VV3I4AXW5B6SIA"
    ]

    selected_account = random.choice(account_pool)
    user_identifier, password, secret_key = selected_account.split("----")

    guest_token = Twitter_login_API.get_guest_token()
    if guest_token:
        action = Twitter_login_API(guest_token, user_identifier, password, secret_key)
        action.first_request()