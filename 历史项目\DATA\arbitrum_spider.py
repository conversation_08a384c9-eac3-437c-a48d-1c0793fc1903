import os
import time
import json
import random

from loguru import logger
from lxml import etree

from .common import CommonFun, del_expire_hash, redis_sentry_connection
from .bypass_cf import BypassCF


class ArbitrumSpider:
    def __init__(self):
        self.chain_name = 'Arbitrum'
        self.abi_addr = f'spider:abi:{self.chain_name}'
        self.max_bypass_cf_fail_count = 5  # 过cf失败的最大次数
        self.common_fun: CommonFun = CommonFun()
        self.bypass_cf = BypassCF()
        self.redis_master, self.redis_slave = redis_sentry_connection()
        self.headers = None
        self.cookies = None
        self.proxies = {
            'http': self.common_fun.proxy,
            'https': self.common_fun.proxy,
        }

    def run(self):
        while True:
            address = self.redis_master.spop(self.abi_addr)
            if not address:
                # logger.info('暂无地址，等待...')
                time.sleep(5)
                continue

            is_expire = self.common_fun.judge_expire(address, self.chain_name)
            if is_expire:
                status = self.parse_data(address)
                if status is False:
                    return

    def set_header_cookie(self):
        headers_cookies_str = self.common_fun.get_hash(self.bypass_cf.redis_key_headers_cookies, self.chain_name)
        if headers_cookies_str:
            headers_cookies_dict = json.loads(headers_cookies_str)
            self.headers = {
                'user-agent': headers_cookies_dict['user-agent']
            }
            self.cookies = {
                'cf_clearance': headers_cookies_dict['cf_clearance']
            }

    def parse_data(self, address):
        if not self.cookies:
            self.set_header_cookie()

        url = f"https://arbiscan.io/address/{address}"
        time.sleep(1.2 + random.random())
        response = self.common_fun.get(url, headers=self.headers, cookies=self.cookies, proxies=self.proxies)

        if response is None or isinstance(response, bool):
            logger.error(f'response is {response}, url: {url}')
            return

        if response.status_code == 403:
            logger.error(f'status_code: {response.status_code}')
            response = self.bypass_cf.run(self.chain_name, url, self.max_bypass_cf_fail_count)
            if response is False:
                self.common_fun.send_to_fs({
                    'Program': f"abi/src/website_with_cf/spider/{os.path.basename(__file__)}",
                    'Error': f"连续{self.max_bypass_cf_fail_count}次都没有通过5秒盾，请检查！",
                    'URL': url
                })
                return False
            else:
                self.set_header_cookie()

        if response.status_code != 200:
            logger.error(f'status_code: {response.status_code}')
            return

        html = etree.HTML(response.text)
        contractAbi = ''.join(html.xpath('//*[@id="js-copytextarea2"]/text()'))
        contract_name = ''.join(html.xpath(
            '//*[@id="ContentPlaceHolder1_contractCodeDiv"]/div[2]/div[1]/div[1]/div[2]/span/text()'))

        proxy_contract = ''.join(html.xpath('//*[@id="ContentPlaceHolder1_readProxyMessage"]/strong/span/a[1]/text()'))
        proxy_contract = proxy_contract.strip().lower()  # 是代理合约

        if contractAbi:
            item = {
                'ADDRESS': address,
                'CHAIN_NAME': self.chain_name,
                'CONTRACT_NAME': contract_name,
                'CONTRACT_ABI': contractAbi
            }

            # 如果是代理合约，则把代理合约的abi也拿到
            if proxy_contract.startswith('0x'):
                item['IMP_CONTRACT'] = proxy_contract
                self.common_fun.save_to_phoenix(item)

                existed = self.common_fun.check_exist(proxy_contract, self.chain_name)
                if not existed:  # 没有的话则爬
                    self.parse_data(proxy_contract)
            else:
                self.common_fun.save_to_phoenix(item)

            # 已爬取到abi，则将过期记录删除（防止冗余）
            del_expire_hash(address, self.chain_name)
        else:
            logger.info('no abi')
            self.common_fun.set_expire_hash(address, self.chain_name)
