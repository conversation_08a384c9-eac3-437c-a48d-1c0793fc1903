import asyncio
import json
import aiohttp

from aiohttp_socks import ProxyConnector


async def connect11():
    # 设置 SOCKS5 代理，假设代理地址是 "127.0.0.1" 端口是 "1080"
    socks_connector = ProxyConnector.from_url('socks5://192.168.224.75:30889')

    # 使用 aiohttp 代理连接 WebSocket
    async with aiohttp.ClientSession(connector=socks_connector) as session:
        params = {"device_id":"0582b587-c74b-4d09-9764-9a10c2cc8b87","client_id":"gmgn_web_*************-f88ca03","from_app":"gmgn","app_ver":"*************-f88ca03","tz_name":"Asia/Shanghai","tz_offset":28800,"app_lang":"zh-CN","fp_did":"a0adc6758070914b5d3cd4679349eed1","os":"web","uuid":"57bbc82ad4dd6129"}
        # 通过代理连接 WebSocket 服务器
        uri = "wss://ws.gmgn.ai/quotation"  # WebSocket 服务器地址
        async with session.ws_connect(uri, params=params) as ws:
            subscribe_message = json.dumps({"action":"subscribe","channel":"token_stat","id":"b91074122b3d4503","data":[{"chain":"sol","addresses":"G1jydhghPMLBXRFPR3wyJ9HbpKPR3Rv7M8Yakfo5pump"}]})
            await ws.send_str(subscribe_message)
            while 1:
                response = await ws.receive_json(timeout=30)
                print(f"收到服务器消息: {response}")


if __name__ == "__main__":
    asyncio.run(connect11())


# {"action":"subscribe","channel":"token_stat","id":"b91074122b3d4503","data":[{"chain":"sol","addresses":"G1jydhghPMLBXRFPR3wyJ9HbpKPR3Rv7M8Yakfo5pump"}]}
