import os
from Spiders import utils
from loguru import logger
utils.REDIS_DB_U = 14 # 14为测试库，原库为6
utils.RETRY_NUM_U = 3

seed_key = 'ETH'




proxy_eth = {
    'http': "socks5://192.168.224.75:30889",
    'https': "socks5://192.168.224.75:30889"
}



proxy_api = "*************************************************"
proxies_api = {
    'http': proxy_api,
    'https': proxy_api
}


proxy_k8s = {
    'http': "socks5://192.168.224.250:10808",
    'https': "socks5://192.168.224.250:10808"
}



logger.add('log.log', level='INFO')


def clear_log():
    with open("log.log", "w") as f:
        f.truncate(0)
# 测试代理是否工作
import requests
try:
    response = requests.get('https://exmaple.com', proxies=proxies_api)
    logger.info(f"Proxy test response code: {response.status_code}")
except Exception as e:
    logger.error(f"Proxy test failed: {e}")

if __name__ == '__main__':
    clear_log()

