#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AWS WAF gokuProps AES解密实现
处理AWS KMS加密数据的解密逻辑
"""

import base64
import hashlib
import hmac
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad, pad
from typing import Dict, Any, Optional, Tuple

class AWSSigV4Signer:
    """AWS Signature Version 4 签名器"""
    
    def __init__(self, access_key: str, secret_key: str, region: str = 'ap-southeast-1'):
        self.access_key = access_key
        self.secret_key = secret_key
        self.region = region
        self.service = 'kms'
    
    def sign(self, request_data: Dict[str, Any]) -> Dict[str, str]:
        """生成AWS SigV4签名"""
        # 简化实现 - 实际项目中需要完整的SigV4实现
        return {'Authorization': 'AWS4-HMAC-SHA256 ...'}

class AWSKMSDecryptor:
    """AWS KMS解密器"""
    
    def __init__(self, access_key: str = None, secret_key: str = None, region: str = 'ap-southeast-1'):
        self.access_key = access_key
        self.secret_key = secret_key
        self.region = region
        self.signer = AWSSigV4Signer(access_key, secret_key, region) if access_key else None
    
    def decrypt_kms_data(self, encrypted_data: str) -> Optional[bytes]:
        """
        解密AWS KMS加密的数据
        
        Args:
            encrypted_data: Base64编码的KMS加密数据
            
        Returns:
            解密后的原始数据，如果失败返回None
        """
        if not self.signer:
            print("⚠️  警告: 没有AWS凭证，无法进行KMS解密")
            print("💡 实际环境中需要配置AWS_ACCESS_KEY_ID和AWS_SECRET_ACCESS_KEY")
            return None
        
        try:
            # 解码base64数据
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            # 构建KMS解密请求
            kms_request = {
                'CiphertextBlob': encrypted_bytes,
                'EncryptionContext': {}
            }
            
            # 这里需要实际的AWS KMS API调用
            print("🔄 正在调用AWS KMS API进行解密...")
            # result = kms_client.decrypt(**kms_request)
            # return result['Plaintext']
            
            print("❌ KMS解密需要有效的AWS凭证")
            return None
            
        except Exception as e:
            print(f"❌ KMS解密失败: {e}")
            return None

class GokuPropsDecryptor:
    """gokuProps解密器"""
    
    def __init__(self):
        self.kms_decryptor = AWSKMSDecryptor()
    
    def parse_goku_props(self, goku_props: Dict[str, str]) -> Dict[str, Any]:
        """
        解析gokuProps参数
        
        Args:
            goku_props: 包含key, iv, context的字典
            
        Returns:
            解析后的参数字典
        """
        print("🔍 解析gokuProps参数...")
        print(f"📝 Key长度: {len(goku_props.get('key', ''))}")
        print(f"📝 IV: {goku_props.get('iv', '')}")
        print(f"📝 Context长度: {len(goku_props.get('context', ''))}")
        
        result = {
            'encrypted_key': goku_props.get('key', ''),
            'iv_b64': goku_props.get('iv', ''),
            'context_b64': goku_props.get('context', ''),
            'iv_bytes': None,
            'context_bytes': None
        }
        
        # 解码IV
        try:
            result['iv_bytes'] = base64.b64decode(goku_props['iv'])
            print(f"✅ IV解码成功: {result['iv_bytes'].hex()}")
        except Exception as e:
            print(f"❌ IV解码失败: {e}")
        
        # 解码Context
        try:
            result['context_bytes'] = base64.b64decode(goku_props['context'])
            print(f"✅ Context解码成功，长度: {len(result['context_bytes'])}")
        except Exception as e:
            print(f"❌ Context解码失败: {e}")
        
        return result
    
    def simulate_aes_decryption(self, iv_bytes: bytes, context_bytes: bytes, simulated_key: bytes = None) -> Optional[bytes]:
        """
        模拟AES解密过程（用于测试和验证）
        
        Args:
            iv_bytes: 初始化向量
            context_bytes: 加密的上下文数据
            simulated_key: 模拟的解密密钥（32字节）
            
        Returns:
            解密后的数据
        """
        if simulated_key is None:
            # 生成一个模拟密钥用于测试
            simulated_key = hashlib.sha256(b"test_key_for_aws_waf_token_generation").digest()
        
        print(f"📝 密钥长度: {len(simulated_key)} 字节")
        print(f"📝 IV长度: {len(iv_bytes)} 字节")
        print(f"📝 数据长度: {len(context_bytes)} 字节")
        
        # 根据IV长度选择不同的AES模式
        if len(iv_bytes) == 12:
            return self._try_aes_gcm_decryption(iv_bytes, context_bytes, simulated_key)
        elif len(iv_bytes) == 16:
            return self._try_aes_cbc_decryption(iv_bytes, context_bytes, simulated_key)
        else:
            print(f"❌ 不支持的IV长度: {len(iv_bytes)} 字节")
            return None
    
    def _try_aes_gcm_decryption(self, nonce: bytes, ciphertext: bytes, key: bytes) -> Optional[bytes]:
        """尝试AES-GCM解密"""
        try:
            print("🔄 尝试AES-GCM解密...")
            
            # 对于GCM模式，通常需要分离tag（认证标签）
            # 假设最后16字节是tag
            if len(ciphertext) < 16:
                print("❌ 数据太短，无法包含GCM标签")
                return None
            
            tag = ciphertext[-16:]
            actual_ciphertext = ciphertext[:-16]
            
            print(f"📝 分离后的密文长度: {len(actual_ciphertext)} 字节")
            print(f"📝 认证标签长度: {len(tag)} 字节")
            
            # 创建AES-GCM解密器
            cipher = AES.new(key, AES.MODE_GCM, nonce=nonce)
            
            # 解密并验证
            decrypted = cipher.decrypt_and_verify(actual_ciphertext, tag)
            print("✅ AES-GCM解密和验证成功")
            return decrypted
            
        except Exception as e:
            print(f"❌ AES-GCM解密失败: {e}")
            
            # 如果GCM失败，尝试其他分割方式
            print("🔄 尝试不同的标签分割方式...")
            try:
                # 尝试前16字节作为tag
                tag = ciphertext[:16]
                actual_ciphertext = ciphertext[16:]
                
                cipher = AES.new(key, AES.MODE_GCM, nonce=nonce)
                decrypted = cipher.decrypt_and_verify(actual_ciphertext, tag)
                print("✅ AES-GCM解密成功 (前16字节作为标签)")
                return decrypted
            except Exception as e2:
                print(f"❌ 另一种GCM解密方式也失败: {e2}")
                
                # 最后尝试：扩展nonce到16字节进行CBC解密
                print("🔄 尝试扩展nonce进行CBC解密...")
                return self._try_extended_cbc_decryption(nonce, ciphertext, key)
    
    def _try_aes_cbc_decryption(self, iv: bytes, ciphertext: bytes, key: bytes) -> Optional[bytes]:
        """尝试AES-CBC解密"""
        try:
            print("🔄 开始AES-CBC解密...")
            
            # 创建AES-CBC解密器
            cipher = AES.new(key, AES.MODE_CBC, iv)
            
            # 解密数据
            decrypted = cipher.decrypt(ciphertext)
            
            # 尝试去除PKCS7填充
            try:
                unpadded = unpad(decrypted, AES.block_size)
                print("✅ PKCS7去填充成功")
                return unpadded
            except ValueError:
                print("⚠️  去填充失败，返回原始解密数据")
                return decrypted
                
        except Exception as e:
            print(f"❌ AES-CBC解密失败: {e}")
            return None
    
    def _try_extended_cbc_decryption(self, short_iv: bytes, ciphertext: bytes, key: bytes) -> Optional[bytes]:
        """尝试扩展短IV进行CBC解密"""
        try:
            print("🔄 扩展IV进行AES-CBC解密...")
            
            # 扩展IV到16字节（使用零填充）
            extended_iv = short_iv + b'\x00' * (16 - len(short_iv))
            print(f"📝 扩展后IV: {extended_iv.hex()}")
            
            return self._try_aes_cbc_decryption(extended_iv, ciphertext, key)
            
        except Exception as e:
            print(f"❌ 扩展IV的CBC解密失败: {e}")
            return None
    
    def extract_hashcash_params(self, decrypted_data: bytes) -> Optional[Dict[str, Any]]:
        """
        从解密数据中提取HashcashScrypt参数
        
        Args:
            decrypted_data: 解密后的数据
            
        Returns:
            HashcashScrypt参数字典
        """
        try:
            # 尝试作为JSON解析
            json_str = decrypted_data.decode('utf-8')
            params = json.loads(json_str)
            print("✅ 成功解析JSON格式的参数")
            return params
        except:
            pass
        
        try:
            # 尝试作为文本数据解析
            text_data = decrypted_data.decode('utf-8')
            print(f"📝 解密数据文本内容: {text_data[:100]}...")
            
            # 从已知的challenge.js分析中提取默认参数
            default_params = {
                'difficulty': 4,  # logN = 4
                'memory_cost': 8,  # r = 8
                'parallelization': 1,  # p = 1
                'key_length': 32,  # dkLen = 32
                'algorithm': 'scrypt'
            }
            print("📋 使用从challenge.js中分析得到的默认参数")
            return default_params
        except:
            pass
        
        # 如果都失败，返回硬编码的参数
        print("⚠️  使用硬编码的HashcashScrypt参数")
        return {
            'difficulty': 4,
            'memory_cost': 8,
            'parallelization': 1,
            'key_length': 32,
            'algorithm': 'scrypt'
        }

def test_goku_props_decryption():
    """测试gokuProps解密功能"""
    print("🧪 测试gokuProps解密功能")
    print("=" * 60)
    
    # 从bian_test.js中的实际数据
    goku_props = {
        "key": "AQIDAHjcYu/GjX+QlghicBgQ/7bFaQZ+m5FKCMDnO+vTbNg96AFe5qPVJjlBcLicGIp1DWcnAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMXBD3MVVKhU9DfptbAgEQgDtqRRs/hlECu5ia7EQndp8CCQn0BXGkrAH3dt0k9RjxGjbFCmNvMvdy43NHyZa8WtnabLfOHaP9LIwSWQ==",
        "iv": "A6weaADBywAAoIx+",
        "context": "smGqEk+mXPIYng9xMpRWcjYS85r913+PYsfMrU1/Vaz0m761PVPuRv45DTZ7Y253iOzD37+u8a3hQ8VgsJrTi+qDtPzI5wEfiJf4vp9LPmHrKpm0d5X+mvj/96BfEed3TSgieuACnp15YO9O2jo3Du00NtHje1kSeNaq3hu+m0aL58RpRu0Xfzkn2MmmrYJjwf5skmefEwlVhYUVeArwXYsPQ6zAEli3rF+WWqBdAXCl51T6hk5+yTBxPZ3sae9kCQ6TPBMTlmTYo8hPS7veUCZITQnmdZMjX5AeSXaixUKPiXRWOqa3eFIw9Q2evaGQw1qAK5tzEwk0oWIoaBUdZkdwD3ZHfJfe55oL0p43SMXS8Y1G0L/t/eb3rf7B75Z/BnX5NLB1hai1d1GwlNNeZ88AOYI="
    }
    
    decryptor = GokuPropsDecryptor()
    
    # 解析参数
    parsed = decryptor.parse_goku_props(goku_props)
    
    if parsed['iv_bytes'] and parsed['context_bytes']:
        # 模拟解密
        decrypted = decryptor.simulate_aes_decryption(
            parsed['iv_bytes'], 
            parsed['context_bytes']
        )
        
        if decrypted:
            print(f"✅ 模拟解密成功，数据长度: {len(decrypted)}")
            
            # 提取参数
            params = decryptor.extract_hashcash_params(decrypted)
            print("📋 提取的HashcashScrypt参数:")
            for key, value in params.items():
                print(f"   {key}: {value}")
            
            return params
    
    return None

if __name__ == "__main__":
    test_goku_props_decryption() 