# 未经开会讨论，禁止修改框架，私自修改框架造成项目不能正常运行后果自负
### 该框架是我们自己编写的网络爬虫框架，支持HTTP1，HTTP2网络协议，该框架为单线程，单进程，开发人员可根据需要自己开启高并发
#### Spider.py 框架主体程序，具体的爬虫程序需要继承该文件下的类
---- check_status该函数用于判断是否需要继续重试
---- Spider_Crawler_HTTP类用于发送httpx版本的请求，支持HTTP1和HTTP2的网络协议
---- Spider_Crawler_Request类用于发送requests版本的请求，仅仅支持HTTP1的网络协议
#### settings.py 框架需要的一些公共配置，以及默认配置，开发人员开在具体的爬虫程序中导入该文件相关变量进行修改
#### utils.py 框架运行所需要的一些公共方法，主要是和数据库操作相关的一些公共方法
#### Scheduler.py 用于将种子调度到redis的种子队列，开发人员在开发具体的爬虫程序时最好有调度程序
#### 框架需要依赖的模块在requirements.txt文件里面
#### 框架使用前先编写配置文件，注意不要使用settings.py作为文件名
