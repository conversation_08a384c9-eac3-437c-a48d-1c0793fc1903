import requests
import time

def create_task_for_token():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": "2b4fde5fbff503cd5921b70ee7911ce0b7f9387856034",
        "task": {
            "type": "TurnstileTaskProxyless",
            "websiteURL": "https://optimistic.etherscan.io/myaccount",
            "websiteKey": "0x4AAAAAAAEa1DD36OluMD6w"
        }
    }
    response = requests.post(url, headers=headers, json=data)
    response_json = response.json()
    if response_json['errorId'] == 0:
        task_id_for_tooken = response_json['taskId']
        print(f"Task ID: {task_id_for_tooken}")
        print('已获取到task_id_for_token')
        return task_id_for_tooken
    else:
        print(f"Error: {response_json['errorDescription']}")
        return None

def get_task_result_for_token(task_id_for_tooken):
    time.sleep(3)
    print('开始获取token')
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": '2b4fde5fbff503cd5921b70ee7911ce0b7f9387856034',
        "taskId": task_id_for_tooken
    }

    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    for i in range(40):
        if result['errorId'] == 0:
            if result['status'] == 'ready':
                print("Task is ready. Solution:", result['solution'])
                return result
            elif result['status'] == 'processing':
                print(f'retrying {i}')
                time.sleep(3)
                response = requests.post(url, headers=headers, json=data)
                result = response.json()
                continue
            else:
                print("Task is not ready yet. Status:", result['status'])
                return result
        else:
            print("Error in getting task result:", result['errorDescription'])
            return result

    print("Max retries reached. Task is still processing.")
    return None

if __name__ == '__main__':
    task_id_for_tooken = create_task_for_token()
    if task_id_for_tooken:
        get_task_result_for_token(task_id_for_tooken)