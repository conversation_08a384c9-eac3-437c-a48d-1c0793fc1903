import hashlib
from Crypto.Cipher import AES
import base64
from loguru import logger

def generate_key(t_value):
    """生成解密密钥（与JS代码一致的双SHA1处理）"""
    # 第一次SHA1
    first_sha = hashlib.sha1(str(t_value).encode()).digest()
    # 第二次SHA1
    second_sha = hashlib.sha1(first_sha).hexdigest()  # 得到40位hex
    # 取前32位作为密钥（16字节）
    return bytes.fromhex(second_sha[:32])

def aes_ecb_decrypt(ciphertext_b64, key):
    """AES-ECB解密（兼容JS的CryptoJS实现）"""
    cipher = AES.new(key, AES.MODE_ECB)
    decrypted = cipher.decrypt(base64.b64decode(ciphertext_b64))
    # 去除可能的填充（PKCS#7）
    return decrypted.decode('utf-8', errors='ignore').strip()

# 提供的密钥种子和密文
t_seed = 1739519752706
encrypted_mi = "dHZInOQwk9JkjOmpz9kNKA=="

encrypted_mi_2 = ""
encrypted_mi_3 = ""
# 生成密钥
key = generate_key(t_seed)

# 执行解密
decrypted_text = aes_ecb_decrypt(encrypted_mi, key)
print("解密结果:", decrypted_text)

# import hashlib
# from Crypto.Cipher import AES
# import base64
# import time

# def generate_key(t_value):
#     """生成解密密钥（与JS代码一致的双SHA1处理）"""
#     # 第一次SHA1
#     first_sha = hashlib.sha1(str(t_value).encode()).digest()
#     # 第二次SHA1
#     second_sha = hashlib.sha1(first_sha).hexdigest()  # 得到40位hex
#     # 取前32位作为密钥（16字节）
#     return bytes.fromhex(second_sha[:32])

# def aes_ecb_decrypt(ciphertext_b64, key):
#     """AES-ECB解密（兼容JS的CryptoJS实现）"""
#     cipher = AES.new(key, AES.MODE_ECB)
#     decrypted = cipher.decrypt(base64.b64decode(ciphertext_b64))
#     # 去除可能的填充（PKCS#7）
#     return decrypted.decode('utf-8', errors='ignore').strip()

# # 提供的密钥种子和密文
# t_seed = int(time.time() * 1000)  # 获取当前时间戳（毫秒）
# encrypted_mi = "dHZInOQwk9JkjOmpz9kNKA=="

# # 生成密钥
# key = generate_key(t_seed)

# # 执行解密
# decrypted_text = aes_ecb_decrypt(encrypted_mi, key)
# print("解密结果:", decrypted_text)