const crypto = require('crypto');

/**
 * 有道翻译解密函数
 * @param {string} encryptedData - 加密的数据
 * @param {string} aesKey - ydsecret://协议的密钥
 * @param {string} aesIv - ydsecret://协议的IV
 * @returns {string} 解密后的结果
 */
function decryptYoudao(encryptedData, aesKey, aesIv) {
    try {
        // 直接对原始ydsecret://协议字符串进行MD5哈希
        const keyMd5 = crypto.createHash('md5').update(aesKey).digest();
        const ivMd5 = crypto.createHash('md5').update(aesIv).digest();
        
        console.log('原始key:', aesKey);
        console.log('原始iv:', aesIv);
        console.log('key MD5:', Array.from(keyMd5));
        console.log('iv MD5:', Array.from(ivMd5));
        
        // 处理URL-safe base64格式（-和_需要替换为+和/）
        const normalizedData = encryptedData.replace(/-/g, '+').replace(/_/g, '/');
        
        // 创建解密器
        const decipher = crypto.createDecipheriv('aes-128-cbc', keyMd5, ivMd5);
        
        // 解密
        let decrypted = decipher.update(normalizedData, 'base64', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
        
    } catch (error) {
        console.error('解密失败:', error);
        return null;
    }
}

// 浏览器版本（不需要crypto模块）
function decryptYoudaoBrowser(encryptedData, aesKey, aesIv) {
    try {
        // 在浏览器中需要使用Web Crypto API或其他库
        // 这里提供一个简化的示例，实际使用时需要引入crypto-js库
        
        // 处理URL-safe base64格式
        const normalizedData = encryptedData.replace(/-/g, '+').replace(/_/g, '/');
        
        // 使用crypto-js库的示例代码（需要先引入crypto-js）
        /*
        const CryptoJS = require('crypto-js');
        
        // 对原始ydsecret://协议字符串进行MD5哈希
        const keyMd5 = CryptoJS.MD5(aesKey);
        const ivMd5 = CryptoJS.MD5(aesIv);
        
        // 解密
        const decrypted = CryptoJS.AES.decrypt(
            normalizedData,
            keyMd5,
            {
                iv: ivMd5,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            }
        );
        
        return decrypted.toString(CryptoJS.enc.Utf8);
        */
        
        return "浏览器版本需要引入crypto-js库";
        
    } catch (error) {
        console.error('解密失败:', error);
        return null;
    }
}

// 测试数据
const testData = {
    encryptedData: "_jsUyA02rwkOJ4enKX7c4dhd7CjvGkcKfbRx0BjNGW-jMtdI8OOvfGuVGSgVDH5exzJtkwqt_3hBtsEwFtLLFFQXgz4QBUl4XOdoQR_RDli7uM36MexoeCaPkQ5Hyy5jNdhf44HZOX3ACqT5PIoS1muVNfWHVBaMhtBeU4kmhh_gIdvnmnSDUS3M0mxoU09gEuPHz9J34nqKO6vHtR66kIp2OzkiHtY9nD64vMW1s4sMwZ__CXlxTT_6QJPTNis-62ZkxUPoXuxXqpdnedMZkkK-pwUyeJsCE8u4SdenDo17eLdAXaTeryLH5X_lzNYs9vp9V8mJMCVb9_EafE4GKKjMcT2sjNc4ukqBkFhpG3VwduKBnN_7kC7STRihPw_7h4g8LdzUCLQEfoi8zWGAbJq1Q9x7OWRj1iJQjgN_VQnoczcdkfvUEOzxuvLamQLtV-AOrxbtc1aMQWlM8DLymM_7UXLxffPl_1Zs9z0jwyTSTRCPVYKj7Ake3THoeGM-SnvriP2jkN19OP721d4o0QdhrrIjpKcwyYgWo9aTeMCIjtZEr2CZ6mxq2A58S-3F41kMTsNZOkg6Azwqb6panRwKAlVEZucySUlTK90rHhwqaF2Jtd4hHnLxgC7JLrkSqJlA4n9KIQdGXidMJ2WLwcUc9hImVsEIKaLKhPxRWrO8sQd96llgfKMfUaXMp2Kn8zrOcGF0xrMGNdxeCGhXkhLBUNaZfsesiDZV__LF9NCQ-uu73fvwziWpz4OwgXdy88yzP_XcW0l3WJTKipUG4V0TEHIWy9Lj3XB_li5wP4QGfP4mgU5jUBsk7JRv0p9vORsXdVyYrK1VpekFCtcHc_WquOK-EB5tAKpBLZZglbzfzJ_Ek_7Ez97xYrYdLC-b6uZSvle5-l1QU50w6AP3z5uqrJmWokkrV3KQ6yaT0RCkcu4QIU9sE94T4WEGNZmT4YJZXD_sGpZ00zzJAAyr5GybIDMhBmR1C7XxwYKPk9bdK7b1ZE9MvWcKorHD8jilMYEx-wGOUJOylUL7nqPJGA==",
    aesKey: "ydsecret://query/key/B*RGygVywfNBwpmBaZg*WT7SIOUP2T0C9WHMZN39j^DAdaZhAnxvGcCY6VYFwnHl",
    aesIv: "ydsecret://query/iv/C@lZe2YzHtZ2CYgaXKSVfsb7Y4QWHjITPPZ0nQp87fBeJ!Iv6v^6fvi2WN@bYpJ4"
};

// 运行测试
if (typeof require !== 'undefined') {
    console.log('🚀 开始解密有道翻译数据...');
    console.log('=' * 60);
    
    const result = decryptYoudao(testData.encryptedData, testData.aesKey, testData.aesIv);
    
    if (result) {
        console.log('\n🎉 解密成功！');
        console.log('=' * 60);
        console.log(result);
        console.log('=' * 60);
    } else {
        console.log('\n❌ 解密失败');
    }
}

// 导出函数
if (typeof module !== 'undefined') {
    module.exports = { decryptYoudao, decryptYoudaoBrowser };
}

// 浏览器中直接使用的示例
/*
在浏览器控制台中使用：

1. 首先引入crypto-js库：
   <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>

2. 使用以下代码：

function decryptYoudao(encryptedData, aesKey, aesIv) {
    // 对原始ydsecret://协议字符串进行MD5哈希
    const keyMd5 = CryptoJS.MD5(aesKey);
    const ivMd5 = CryptoJS.MD5(aesIv);
    
    // 处理URL-safe base64格式
    const normalizedData = encryptedData.replace(/-/g, '+').replace(/_/g, '/');
    
    // 解密
    const decrypted = CryptoJS.AES.decrypt(
        normalizedData,
        keyMd5,
        {
            iv: ivMd5,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }
    );
    
    return decrypted.toString(CryptoJS.enc.Utf8);
}

// 使用示例
const result = decryptYoudao(encryptedData, aesKey, aesIv);
console.log(JSON.parse(result));
*/ 