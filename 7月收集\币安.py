from binance.client import Client
from binance.client import Client as FuturesClient

# 你的正式 API Key 和 Secret
api_key = 'PyHIV2OIxBsk57INYHvhOcsWhISfrJBz2mJ2fWRZSpYHfFGm6cgzXwjSPvvf1AMV'
api_secret = 'nfPyhAJToL1nm3DSJQYIP2JqnCtfmHgtFbZFlwA4ptV7Rd5lScGnqcjLTiS1V1bE'

# ---- 1. 查询现货账户资产 ----
spot_client = Client(api_key, api_secret)

spot_account = spot_client.get_account()
balances = spot_account['balances']

print('=== 现货账户余额 ===')
for balance in balances:
    asset = balance['asset']
    free = float(balance['free'])
    locked = float(balance['locked'])
    if free > 0 or locked > 0:
        print(f'{asset}: 可用={free}, 冻结={locked}')

# ---- 2. 查询合约账户持仓 ----
futures_client = FuturesClient(api_key, api_secret)

# 使用futures_account_balance方法获取合约账户余额
try:
    futures_balance = futures_client.futures_account_balance()
    print('\n=== 合约账户余额 ===')
    for balance in futures_balance:
        asset = balance['asset']
        balance_amount = float(balance['balance'])
        if balance_amount > 0:
            print(f'{asset}: {balance_amount}')
except Exception as e:
    print(f'获取合约账户余额失败: {e}')
# 获取合约持仓信息
try:
    positions = futures_client.futures_position_information()
    print('\n=== 合约账户持仓 ===')
    for pos in positions:
        symbol = pos['symbol']
        positionAmt = float(pos['positionAmt'])
        entryPrice = float(pos['entryPrice'])
        unrealizedProfit = float(pos['unRealizedProfit'])

        if positionAmt != 0:
            print(f'{symbol}: 持仓数量={positionAmt}, 开仓均价={entryPrice}, 未实现盈亏={unrealizedProfit}')
except Exception as e:
    print(f'获取合约持仓失败: {e}')

[
  {
    "symbol": "BTCUSDT",
    "id": 28457,
    "orderId": 100234,
    "orderListId": -1,
    "pricechange_volume": "40000.00",
    "qty": "0.001",
    "quoteQty": "40.00",
    "commission": "0.00001",
    "commissionAsset": "BTC",
    "time": 1642120000000,
    "isBuyer": true,
    "isMaker": false,
    "isBestMatch": true
  }
]

[
  {
    "symbol": "BTCUSDT",
    "id": 69876,
    "orderId": 789456,
    "side": "BUY",
    "pricechange_volume": "40000.00",
    "qty": "0.01",
    "realizedPnl": "-0.5",
    "marginAsset": "USDT",
    "commission": "0.001",
    "commissionAsset": "USDT",
    "time": 1642120000000,
    "positionSide": "LONG"
  }
]
