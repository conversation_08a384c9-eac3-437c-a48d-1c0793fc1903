import requests
import sqlite3
import json
import datetime
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# 数据库路径
DB_PATH = r'C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db'
DB_TABLE = "okx_gmgn_ha"  # 表名

# 线程数量
NUM_THREADS = 10  # 修改为10个线程

# 重试配置
MAX_RETRIES = 8  # 增加重试次数
TIMEOUT_SECONDS = 10  # 超时时间
RETRY_INTERVAL = 2  # 重试间隔时间

# 线程锁，用于打印日志和数据库访问
PRINT_LOCK = threading.Lock()
DB_LOCK = threading.Lock()


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with PRINT_LOCK:
        print(*args, **kwargs)


def get_all_token_addresses():
    """从数据库中获取所有Token Address"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f"SELECT rowid, [Token Address] FROM {DB_TABLE} WHERE [Token Address] IS NOT NULL")
        return cursor.fetchall()
    except sqlite3.Error as e:
        thread_safe_print(f"数据库错误: {e}")
        return []
    finally:
        if conn:
            conn.close()


def fetch_token_price(token_data, thread_id):
    """获取特定代币的当前价格，使用重试机制"""
    row_id, token_address = token_data

    url = "https://www.valuescan.ai/api/v1/dex/market/current-price"
    headers = {
        "Content-Type": "application/json"
    }
    data = [
        {
            "chainName": "SOL",
            "tokenContractAddress": token_address
        }
    ]

    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            thread_safe_print(
                f"[线程-{thread_id}] 请求发送 (尝试 {retry_count + 1}/{MAX_RETRIES}): URL={url}, 数据={json.dumps(data)}")

            # 添加超时参数
            response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT_SECONDS)

            if response.status_code == 200:
                return response.json()
            else:
                thread_safe_print(
                    f"[线程-{thread_id}] API请求失败: 状态码 {response.status_code}, 响应: {response.text}")
                # 如果是服务器错误，继续重试
                if 500 <= response.status_code < 600:
                    thread_safe_print(f"[线程-{thread_id}] 服务器错误，将在{RETRY_INTERVAL}秒后重试...")
                    time.sleep(RETRY_INTERVAL)
                    retry_count += 1
                    continue
                else:
                    # 对于客户端错误，还是尝试重试
                    thread_safe_print(f"[线程-{thread_id}] 客户端错误，将在{RETRY_INTERVAL}秒后重试...")
                    time.sleep(RETRY_INTERVAL)
                    retry_count += 1
                    continue

        except requests.exceptions.Timeout:
            thread_safe_print(f"[线程-{thread_id}] 请求超时（超过{TIMEOUT_SECONDS}秒），将重试...")
            retry_count += 1
            # 超时后直接重试，不需要额外延迟
            continue

        except requests.exceptions.ConnectionError:
            thread_safe_print(f"[线程-{thread_id}] 连接错误，将在{RETRY_INTERVAL}秒后重试...")
            time.sleep(RETRY_INTERVAL)
            retry_count += 1
            continue

        except Exception as e:
            thread_safe_print(f"[线程-{thread_id}] 请求发生错误: {e}")
            thread_safe_print(f"[线程-{thread_id}] 将在{RETRY_INTERVAL}秒后重试...")
            time.sleep(RETRY_INTERVAL)
            retry_count += 1
            continue

    # 如果所有重试都失败
    thread_safe_print(f"[线程-{thread_id}] 已达到最大重试次数({MAX_RETRIES})，跳过此代币")
    return None


def update_database(row_id, price, timestamp):
    """更新数据库中指定行的HA和HA Time列"""
    with DB_LOCK:  # 使用锁确保线程安全的数据库访问
        conn = None
        try:
            # 将Unix时间戳转换为可读时间格式
            standard_time = datetime.datetime.fromtimestamp(int(timestamp) / 1000).strftime('%Y-%m-%d %H:%M:%S')

            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute(f"UPDATE {DB_TABLE} SET [data HA] = ?, [HA Time] = ? WHERE rowid = ?",
                           (price, standard_time, row_id))
            conn.commit()
            return True
        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库错误: {e}")
            return False
        finally:
            if conn:
                conn.close()


def worker_thread(token_batch, thread_id):
    """工作线程函数，处理一批token"""
    thread_safe_print(f"[线程-{thread_id}] 启动，负责处理 {len(token_batch)} 个代币")

    success_count = 0
    total_count = len(token_batch)

    for i, token_data in enumerate(token_batch):
        row_id, token_address = token_data
        thread_safe_print(f"[线程-{thread_id}] 处理第 {i + 1}/{total_count} 个代币: {token_address}")

        # 调用API获取价格数据
        response_data = fetch_token_price(token_data, thread_id)
        if not response_data or response_data.get("code") != 200:
            thread_safe_print(
                f"[线程-{thread_id}] 获取价格失败: {json.dumps(response_data) if response_data else 'No response'}")
            continue

        # 从响应中提取price和time
        try:
            data_item = response_data.get("data", [])[0]
            price = data_item.get("pricechange_volume")
            timestamp = data_item.get("time")

            if price and timestamp:
                # 更新数据库
                if update_database(row_id, price, timestamp):
                    thread_safe_print(
                        f"[线程-{thread_id}] 成功更新代币 {token_address} 的价格: {price}, 时间: {timestamp}")
                    success_count += 1
                    thread_safe_print(
                        f"[线程-{thread_id}] 进度: {i + 1}/{total_count} ({(i + 1) / total_count * 100:.2f}%)")
                else:
                    thread_safe_print(f"[线程-{thread_id}] 更新数据库失败")
            else:
                thread_safe_print(f"[线程-{thread_id}] 响应数据不完整: {data_item}")
        except (IndexError, KeyError) as e:
            thread_safe_print(f"[线程-{thread_id}] 解析响应数据错误: {e}, 响应: {response_data}")

        # 添加延迟以避免频繁请求，但在多线程环境下可以适当减少延迟
        if i < total_count - 1:  # 最后一个不需要延迟
            time.sleep(0.5)  # 并发环境下降低延迟时间

    thread_safe_print(f"[线程-{thread_id}] 完成! 成功处理 {success_count}/{total_count} 个代币")
    return success_count


def process_tokens_with_threads():
    """使用多线程处理所有token数据"""
    # 获取所有token数据
    all_tokens = get_all_token_addresses()
    total_count = len(all_tokens)

    if not all_tokens:
        thread_safe_print("未找到任何Token地址，请检查数据库")
        return 0, 0

    thread_safe_print(f"总共有 {total_count} 个代币需要处理，将使用 {NUM_THREADS} 个线程并行处理")

    # 将token数据平均分配给各个线程
    tokens_per_thread = total_count // NUM_THREADS
    remainder = total_count % NUM_THREADS

    token_batches = []
    start_idx = 0

    for i in range(NUM_THREADS):
        # 计算每个线程分配的token数量，考虑余数分配
        batch_size = tokens_per_thread + (1 if i < remainder else 0)
        end_idx = start_idx + batch_size

        # 分配token批次
        token_batches.append(all_tokens[start_idx:end_idx])
        thread_safe_print(f"线程-{i + 1} 将处理 {len(token_batches[-1])} 个代币，索引范围: {start_idx}-{end_idx - 1}")

        start_idx = end_idx

    # 使用线程池执行任务
    success_counts = []
    with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
        # 提交任务并收集Future对象
        futures = [executor.submit(worker_thread, token_batches[i], i + 1) for i in range(NUM_THREADS)]

        # 等待所有任务完成并收集结果
        for future in futures:
            success_counts.append(future.result())

    # 计算总成功数
    total_success = sum(success_counts)

    return total_count, total_success


def main():
    thread_safe_print("=" * 60)
    thread_safe_print("SOL代币价格获取工具 (多线程版)")
    thread_safe_print("=" * 60)
    thread_safe_print("数据库路径:", DB_PATH)
    thread_safe_print("数据库表名:", DB_TABLE)
    thread_safe_print("线程数量:", NUM_THREADS)
    thread_safe_print("最大重试次数:", MAX_RETRIES)
    thread_safe_print("超时时间:", TIMEOUT_SECONDS, "秒")
    thread_safe_print("=" * 60)

    # 获取价格并实时更新数据库
    start_time = time.time()
    total_count, success_count = process_tokens_with_threads()

    # 输出结果
    thread_safe_print("\n更新完成!")
    thread_safe_print(f"总计: {total_count} 个Token")
    thread_safe_print(f"成功更新数据库: {success_count} 个")

    end_time = time.time()
    duration = end_time - start_time
    thread_safe_print(f"程序运行耗时: {duration:.2f} 秒")


if __name__ == "__main__":
    thread_safe_print("开始获取SOL链上代币的价格数据...")

    try:
        main()
    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        import traceback

        thread_safe_print(f"程序运行出错: {e}")
        thread_safe_print(traceback.format_exc())

    thread_safe_print("程序运行结束")
