# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2024/12/17 10:39
@Python  :  Python3.7
@Desc    :  None
"""
import time

import phoenixdb
import redis
from redis.sentinel import Sentinel
from loguru import logger

import sys
sys.path.append("..")
from settings import ABI_EXPIRE_TIME_FORMAT, PROXY
from Spiders import Spider


def conn_redis(host="**************", port=6379, password='123456', db=9):
    while True:
        try:
            r = redis.Redis(host=host, port=port, password=password, db=db, decode_responses=True,
                            socket_timeout=120, socket_connect_timeout=10, retry_on_timeout=True)
            return r
        except Exception as e:
            logger.error(repr(e))


def redis_sentry_connection():
    """ redis 哨兵连接 """
    sentinel_list = [
        ("***************", "26379"),
        ("***************", "26379"),
        ("***************", "26379")
    ]
    mySentinel = Sentinel(sentinel_list, password='1qaz@WSX2022', db=0, socket_timeout=2)
    master = mySentinel.master_for("mymaster", db=0, socket_timeout=2, decode_responses=True)
    slave = mySentinel.slave_for("mymaster", db=0, socket_timeout=2, decode_responses=True)
    return master, slave


def del_expire_hash(address: str, chain_name):
    """ 删除过期记录"""
    expire_hash_name = ABI_EXPIRE_TIME_FORMAT % chain_name
    r9 = conn_redis()
    r9.hdel(expire_hash_name, address)
    r9.close()


class CommonFun(Spider.Spider_Crawler_Request):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.proxy = PROXY

    def set_expire_hash(self, address: str, chain_name):
        expire_hash_name = ABI_EXPIRE_TIME_FORMAT % chain_name
        self.set_hash(hash_name=expire_hash_name, hash_key=address, hash_values=int(time.time()))

    @staticmethod
    def save_to_phoenix(item: dict):
        with phoenixdb.connect("http://**************:8765", autocommit=False) as conn:
            with conn.cursor() as cursor:
                keys = ','.join(list(item.keys()))
                values = ','.join(['?'] * len(item))
                data = list(item.values())
                sql = f"UPSERT INTO COMMON.CONTRACT_INFO({keys}) VALUES({values})"
                cursor.execute(sql, data)
            conn.commit()
            logger.info(f'save success, CHAIN_NAME:{item["CHAIN_NAME"]}, ADDRESS:{item["ADDRESS"]}')

    def check_exist(self, address, chain_name):
        sql = f"""
            SELECT 1 
            FROM COMMON.CONTRACT_INFO
            WHERE ADDRESS='{address}' AND CHAIN_NAME='{chain_name}' AND CONTRACT_ABI IS NOT NULL
        """
        ret = self.select_of_phoenix(sql=sql, phoenixdb_conn="http://**************:8765")
        if not ret:
            return False
        else:
            return True

    def judge_expire(self, address: str, chain_name):
        """ 同一个地址一个月内不再重复爬取 """
        expire_hash_name = ABI_EXPIRE_TIME_FORMAT % chain_name
        expire_time = self.get_hash(hash_name=expire_hash_name, hash_key=address)
        logger.info(f'chain_name: {chain_name}, address: {address}, expire_time: {expire_time}')

        if expire_time is None:
            return True
        else:
            time_differ = int(time.time()) - int(expire_time)

            if time_differ < 2592000:  # 一个月之内不再重复爬
                return False
            else:
                return True


if __name__ == '__main__':
    a = CommonFun().check_exist('0x87dd4a7ad23b95cd9ff9c26b5cf325905caf8663', 'KCC')
    print(a)