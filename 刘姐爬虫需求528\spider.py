import requests
import csv
from lxml import etree


headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
cookies = {
    "Hm_lvt_4cf939490389b2b630fd1876d64c7e85": "**********",
    "HMACCOUNT": "7B8005F92DCFE15C",
    "Hm_lvt_57f4e0553d3c154c789e5795eddd1871": "**********",
    "Hm_lpvt_57f4e0553d3c154c789e5795eddd1871": "**********",
    "Hm_lpvt_4cf939490389b2b630fd1876d64c7e85": "**********"
}
url = "https://cd.bendibao.com/xiuxian/yanchanghuitime/"
response = requests.get(url, headers=headers, cookies=cookies)

# 解析HTML数据
def parse_data(html_text):
    html = etree.HTML(html_text)
    
    # 根据查看到的实际HTML结构，使用更准确的XPath
    singers = html.xpath("//tr/td[1]/a/text()")
    times = html.xpath("//tr/td[2]/text()")
    venues = html.xpath("//tr/td[3]/text()")
    ticket_urls = html.xpath("//tr/td[4]/a/@href")
    
    # 清理数据
    times = [t.strip() for t in times if t.strip()]
    venues = [v.strip() for v in venues if v.strip()]
    
    # 打印提取的结果长度，用于调试
    print(f"提取到的数据: 歌手({len(singers)}), 时间({len(times)}), 场馆({len(venues)}), 链接({len(ticket_urls)})")
    
    # 如果所有数据都为空，打印HTML源码以便检查
    if not singers and not times and not venues and not ticket_urls:
        print("未能提取到任何数据，可能需要检查HTML结构")
        # 将HTML保存到文件中以便检查
        with open("debug_html.html", "w", encoding="utf-8") as f:
            f.write(html_text)
        print("已将HTML保存到debug_html.html文件中以便检查")
    
    # 创建数据列表
    data_list = []
    max_length = max(len(singers), len(times), len(venues), len(ticket_urls))
    
    for i in range(max_length):
        singer = singers[i] if i < len(singers) else ""
        time = times[i] if i < len(times) else ""
        venue = venues[i] if i < len(venues) else ""
        ticket_url = ticket_urls[i] if i < len(ticket_urls) else ""
        
        # 跳过空数据
        if not singer and not time and not venue and not ticket_url:
            continue
            
        data_list.append({
            "歌手": singer,
            "演出时间": time,
            "场馆": venue,
            "门票_观看攻略": ticket_url
        })
    
    return data_list

# 将数据保存到CSV文件
def save_to_csv(data_list, filename):
    with open(f"{filename}.csv", "w", newline="", encoding="utf-8-sig") as f:
        # 定义表头
        fieldnames = ["歌手", "演出时间", "场馆", "门票_观看攻略"]
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        
        # 写入表头
        writer.writeheader()
        
        # 写入数据
        writer.writerows(data_list)
    
    print(f"数据已成功保存到{filename}.csv")
    if not data_list:
        print("警告: 保存的数据为空!")
    else:
        print(f"成功保存 {len(data_list)} 条数据")

# 主函数
def main():
    # 确保获取到响应
    if response.status_code == 200:
        print(f"成功获取网页，状态码: {response.status_code}")
        
        # 解析数据
        data_list = parse_data(response.text)
        
        # 保存数据到CSV
        save_to_csv(data_list, "未演出")
    else:
        print(f"请求失败，状态码: {response.status_code}")

if __name__ == "__main__":
    main()