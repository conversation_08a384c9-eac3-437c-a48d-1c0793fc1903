import requests
import json
import time
import redis


API_KEY = 'your_api_key_here'

PROXY = {
    'http': "socks5://**************:30889",
    'https': "socks5://**************:30889"
}
TARGET_URL = "https://optimistic.etherscan.io/"
REDIS_HOST = '**************'
REDIS_PORT = 6379
REDIS_DB = 14
REDIS_PASSWORD = '123456'


redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    password=REDIS_PASSWORD,
    decode_responses=True
)


def create_task():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": API_KEY,
        "task": {
            "type": "CloudFlareTaskS2",
            "websiteURL": TARGET_URL,
            "proxy": PROXY['http'],
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"
        }
    }
    response = requests.post(url, headers=headers, json=data)
    return response.json()


def get_task_result(task_id):
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": API_KEY,
        "taskId": task_id
    }
    while True:
        response = requests.post(url, headers=headers, json=data)
        result = response.json()
        if result.get('status') == 'ready':
            return result
        time.sleep(5)


def access_website_with_cookies(cookies):
    session = requests.Session()
    for cookie in cookies:
        session.cookies.set(cookie['name'], cookie['value'])
    response = session.get(TARGET_URL, proxies=PROXY)
    return response.text


def save_cookies_to_redis(cookies):
    redis_client.set('etherscan_cookies', json.dumps(cookies))
    print("Cookies have been saved to Redis")

# 主程序
def main():
    task_response = create_task()
    if task_response.get('errorId') == 0:
        task_id = task_response.get('taskId')
        print(f"Task created successfully. Task ID: {task_id}")
        result = get_task_result(task_id)
        if result.get('errorId') == 0:
            cookies = result.get('solution', {}).get('cookies', {})
            print("Cookies retrieved successfully:")
            print(json.dumps(cookies, indent=4))

            page_content = access_website_with_cookies(cookies)
            print("Accessed website content with cookies.")

            save_cookies_to_redis(cookies)
        else:
            print(f"Error retrieving task result: {result.get('errorDescription')}")
    else:
        print(f"Error creating task: {task_response.get('errorDescription')}")

if __name__ == "__main__":
    main()