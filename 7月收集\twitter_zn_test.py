import requests

proxies = {
            "http": "http://127.0.0.1:33210",
            "https": "http://127.0.0.1:33210"
}

headers = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
    "content-type": "application/json",
    "priority": "u=1, i",
    "referer": "",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
    "x-client-transaction-id": "+sz/dfqqmsPKXgIO3sJNwKfaqoxbAtx0ob9xnLCQVZAO9l1wXsSg5zt1Kv40mLB7gQA5//69ft5VtRYA2/w80LTF0Vul+Q",
    "x-csrf-token": "5cc163fd1a6996784298e99c6335be347e6802c55841c43fe4f9cefcf605c3b8f4008cc1e8c475dc7516e9408e0aacb271296d6577a8aebbc6f6cb15449f662033a649f4bfd589f18b36b5274e3cc77b",
    # "x-xp-forwarded-for": "612a0e348916f0b49f557c0f211d188330e5066423d7301abbf558042a017e13b7589528eaf1d02e9e849a39af7d9eb07a866457487e4e03c34d2df56c58fc52b8f87257bd73ac9311b14ddc45f21ef0fbdf7e087254dd92c184c5263636dabe3dcea460a459b3847621a1c5e9a64f678c18c77b3f37104aa60f5576811f14c3ce2cd581a335504fc639e2d1fb75f653625486f69cac33255c0e2a107d212f6b726ecbcfafb9c4325c94a05e31fce5764e11db14d68dcd892dc60b9b9bb684d06a779fd4c03166a41ed3cc2c2f698a18cf34b3cef6c9c5b145a3c3639ba153a7db4065a68ad46a997dc7f10fcba8d34a87f2f04314190cc82c1189"
}
cookies = {
    "auth_token": "48f33a58efa804df58e5ce6045d6539d4860f2a7",
    "ct0": "5cc163fd1a6996784298e99c6335be347e6802c55841c43fe4f9cefcf605c3b8f4008cc1e8c475dc7516e9408e0aacb271296d6577a8aebbc6f6cb15449f662033a649f4bfd589f18b36b5274e3cc77b",
}
url = "https://x.com/i/api/graphql/EP_W_cLzULmRd5E_X-PJkA/SearchTimeline"
params = {
    "variables": "{\"rawQuery\":\"侃爷\",\"count\":20,\"querySource\":\"typed_query\",\"product\":\"Top\"}",
    "features": "{\"rweb_video_screen_enabled\":false,\"payments_enabled\":false,\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_show_grok_translated_post\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":true,\"responsive_web_enhance_cards_enabled\":false}"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params, proxies=proxies)

print(response.text)
print(response)
