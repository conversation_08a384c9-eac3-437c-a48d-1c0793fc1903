# 有道翻译解密项目

## 项目概述
成功逆向工程网易有道翻译的加密算法，实现了对翻译结果的解密。

## 关键发现

### 1. 加密算法分析
- **算法类型**: AES-128-CBC
- **密钥处理**: 直接对原始ydsecret://协议字符串进行MD5哈希
- **数据格式**: URL-safe Base64编码

### 2. 核心突破
通过浏览器调试发现：
- T函数直接对原始ydsecret://协议字符串进行MD5哈希
- **不需要**解析ydsecret://协议中的特殊字符
- MD5哈希结果完全匹配浏览器中的T函数输出

### 3. 验证数据
- **key MD5**: `[8, 20, 157, 167, 60, 89, 206, 98, 85, 91, 1, 233, 47, 52, 232, 56]`
- **iv MD5**: `[210, 187, 27, 253, 232, 59, 56, 195, 68, 54, 99, 87, 183, 156, 174, 28]`

## 使用方法

### Python版本
```python
python decrypt_python.py
```

### 浏览器版本
1. 引入crypto-js库：
```html
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
```

2. 使用解密函数：
```javascript
function decryptYoudao(encryptedData, aesKey, aesIv) {
    // 对原始ydsecret://协议字符串进行MD5哈希
    const keyMd5 = CryptoJS.MD5(aesKey);
    const ivMd5 = CryptoJS.MD5(aesIv);
    
    // 处理URL-safe base64格式
    const normalizedData = encryptedData.replace(/-/g, '+').replace(/_/g, '/');
    
    // 解密
    const decrypted = CryptoJS.AES.decrypt(
        normalizedData,
        keyMd5,
        {
            iv: ivMd5,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }
    );
    
    return decrypted.toString(CryptoJS.enc.Utf8);
}
```

## 解密结果示例

### 输入数据
- **中文原文**: "金色财经报道，埃隆·马斯克在X发文表示，。全新 XChat 现已推出加密、消息消失和发送任意类型文件的功能。此外，还支持音频"
- **加密数据**: "_jsUyA02rwkOJ4enKX7c4dhd7CjvGkcKfbRx0BjNGW-jMtdI8OOvfGuVGSgVDH5exzJtkwqt_3hBtsEwFtLLFFQXgz4QBUl4XOdoQR_RDli7uM36MexoeCaPkQ5Hyy5jNdhf44HZOX3ACqT5PIoS1muVNfWHVBaMhtBeU4kmhh_gIdvnmnSDUS3M0mxoU09gEuPHz9J34nqKO6vHtR66kIp2OzkiHtY9nD64vMW1s4sMwZ__CXlxTT_6QJPTNis-62ZkxUPoXuxXqpdnedMZkkK-pwUyeJsCE8u4SdenDo17eLdAXaTeryLH5X_lzNYs9vp9V8mJMCVb9_EafE4GKKjMcT2sjNc4ukqBkFhpG3VwduKBnN_7kC7STRihPw_7h4g8LdzUCLQEfoi8zWGAbJq1Q9x7OWRj1iJQjgN_VQnoczcdkfvUEOzxuvLamQLtV-AOrxbtc1aMQWlM8DLymM_7UXLxffPl_1Zs9z0jwyTSTRCPVYKj7Ake3THoeGM-SnvriP2jkN19OP721d4o0QdhrrIjpKcwyYgWo9aTeMCIjtZEr2CZ6mxq2A58S-3F41kMTsNZOkg6Azwqb6panRwKAlVEZucySUlTK90rHhwqaF2Jtd4hHnLxgC7JLrkSqJlA4n9KIQdGXidMJ2WLwcUc9hImVsEIKaLKhPxRWrO8sQd96llgfKMfUaXMp2Kn8zrOcGF0xrMGNdxeCGhXkhLBUNaZfsesiDZV__LF9NCQ-uu73fvwziWpz4OwgXdy88yzP_XcW0l3WJTKipUG4V0TEHIWy9Lj3XB_li5wP4QGfP4mgU5jUBsk7JRv0p9vORsXdVyYrK1VpekFCtcHc_WquOK-EB5tAKpBLZZglbzfzJ_Ek_7Ez97xYrYdLC-b6uZSvle5-l1QU50w6AP3z5uqrJmWokkrV3KQ6yaT0RCkcu4QIU9sE94T4WEGNZmT4YJZXD_sGpZ00zzJAAyr5GybIDMhBmR1C7XxwYKPk9bdK7b1ZE9MvWcKorHD8jilMYEx-wGOUJOylUL7nqPJGA=="

### 最终翻译结果
**"According to Golden Finance, Elon Musk posted on X that. The brand-new XChat now offers features such as encryption, message vanishing, and sending files of any type. In addition, audio is also supported"**

### 原始JSON数据
```json
{
  "code": 0,
  "translateResult": [
    [
      {
        "tgt": "According to Golden Finance, Elon Musk posted on X that.",
        "src": "金色财经报道，埃隆·马斯克在X发文表示，。",
        "srcPronounce": "jīn sè cái jīng bào dào, āi lóng·mă sī kè zài Xfā wén biăo shì, ."
      },
      {
        "tgt": " The brand-new XChat now offers features such as encryption, message vanishing, and sending files of any type.",
        "src": "全新 XChat 现已推出加密、消息消失和发送任意类型文件的功能。",
        "srcPronounce": " quán xīn XChat xiàn yĭ tuī chū jiā mì、xiāo xī xiāo shī hé fā sòng rèn yì lèi xíng wén jiàn de gōng néng."
      },
      {
        "tgt": " In addition, audio is also supported",
        "src": "此外，还支持音频",
        "srcPronounce": " cĭ wài, hái zhī chí yīnpín"
      }
    ]
  ]
}
```

## 技术细节

### 网络请求分析
1. **GET请求**: 获取解密密钥
   - `secretKey`: 用于签名验证
   - `aesKey`: AES加密密钥（ydsecret://协议格式）
   - `aesIv`: AES初始化向量（ydsecret://协议格式）

2. **POST请求**: 获取加密的翻译结果
   - 返回URL-safe Base64编码的加密数据

### 解密流程
1. 直接对原始ydsecret://协议字符串进行MD5哈希
2. 将URL-safe Base64转换为标准Base64
3. 使用AES-128-CBC算法解密
4. 去除PKCS7填充
5. 解析JSON结果

## 文件说明
- `decrypt_python.py`: Python版本解密脚本
- `decrypt_youdao.js`: JavaScript版本解密脚本
- `README.md`: 项目说明文档

## 注意事项
- 此项目仅用于学习和研究目的
- 请遵守相关法律法规和网站使用条款
- 密钥和IV会定期更换，需要重新获取

## 成功验证
✅ MD5哈希结果完全匹配浏览器T函数输出  
✅ 解密结果完整且格式正确  
✅ 支持多种编程语言实现  

---
*项目完成时间: 2024年* 