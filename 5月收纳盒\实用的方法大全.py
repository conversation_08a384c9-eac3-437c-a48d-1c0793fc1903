# from loguru import logger
# import redis


# class FUNCTION():
#     def __init__(self):

#         self.redis_client = redis.Redis(
#                 host='**************',
#                 port=6379,
#                 password='123456',
#                 db=2,
#                 decode_responses=True
#             )


#     def get_code_from_redis(self):
#             """从Redis中获取并移除一个code"""
#             try:
#                 code = self.redis_client.spop('feixiaohao:coin_codes')
#                 if code:
#                     logger.info(f'从Redis获取到code: {code}')
#                     return code
#                 return None
#             except Exception as e:
#                 logger.error(f'从Redis获取code失败: {e}')
#                 return None
            


#     def clean_text(self, text):
#         from html import unescape
#         import re
#         """清理文本, 去除HTML标签、多余空白字符等"""
#         if not text:
#             return ''
        
#         # HTML反转义（比如将&nbsp;转换为空格）
#         text = unescape(text)
        
#         # 移除HTML标签
#         text = re.sub(r'<[^>]+>', ' ', text)
        
#         # 移除多余空白字符
#         text = re.sub(r'\s+', ' ', text)
        
#         # 移除特殊字符和控制字符
#         text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        
#         # 移除多余的空格和标点
#         text = re.sub(r'[\s\u3000]+', ' ', text)
        
#         # 移除空括号
#         text = re.sub(r'\(\s*\)', '', text)
        
#         # 移除首尾空白
#         text = text.strip()
        
#         return text
    
#     # 调用清理文本数据
#     def parse_agency_data(self, data):
#         parsed_agency_data = []
#         data_detail = data.get('data', {}).get('agency', [])
#         for data_need in data_detail:
#             # 获取原始描述文本
#             raw_description = data_need.get('description', '')
            
#             # 清理描述文本
#             cleaned_description = self.clean_text(raw_description)
            
#             append_data = {
#                 'name': self.clean_text(data_need.get('name', '')),  # 同样清理名称
#                 'description': cleaned_description,
#                 'logo': data_need.get('logo')
#             }
#             logger.info(f'数据: {append_data}')
#             parsed_agency_data.append(append_data)
        
#         logger.success(f'共获取到{len(parsed_agency_data)}条数据')
#         if parsed_agency_data:
#             self.insert_agency_data_to_mysql(parsed_agency_data)
#         else:
#             logger.warning('获取的数据为空')




import requests

proxies = {
    "http": "http://127.0.0.1:33210",  # 你的 HTTP 代理
    "https": "http://127.0.0.1:33210"
}

# proxy = {
#     "http": "socks5://127.0.0.1:33211",
#     "https": "socks5://127.0.0.1:33211"
# }

# 查询本机出口 IP 地址
response = requests.get("https://api64.ipify.org?format=json", proxies=proxies)
ip = response.json()["ip"]
print(f"当前代理 IP: {ip}")

# 查询 IP 所属城市
geo_response = requests.get(f"https://ipapi.co/{ip}/json/", proxies=proxies)
geo_data = geo_response.json()
print(f"IP 地址: {ip}")
print(f"城市: {geo_data.get('city', '未知')}")
print(f"国家: {geo_data.get('country_name', '未知')}")
