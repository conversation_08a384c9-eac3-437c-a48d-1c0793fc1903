#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX平台历史K线数据爬虫
功能：从OKX平台API获取历史K线指定时间段数据并更新到MySQL数据库（三平台对比表）
"""

import requests
import pymysql
import time
import json
import hmac
import base64
import hashlib
import random
from datetime import datetime
from typing import List, Dict, Optional
from loguru import logger


class OKXSpider:
    def __init__(self):
        """初始化OKX爬虫"""
        self.db_config = {
            'host': '**************',
            'port': 33060,
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8',
            'database': 'kline_data'
        }

        # OKX API配置
        self.api_base_url = "https://web3.okx.com"
        self.api_endpoint = "/api/v5/dex/market/historical-candles"
        
        # OKX API认证参数
        self.api_key = "d87d0a5f-a4df-4209-a3b7-573dad862d25"
        self.api_secret = "8807594F0F5B6A15F2B223638B8537D0"
        self.api_passphrase = "Dh112211!"
        
        # 请求参数配置
        self.chain_index = "501"  # SOL链
        self.bar = "1m"
        # 第一次请求时间戳
        self.after_timestamp = "1752728400000"  # 2025-07-17 13:00:00
        # 第二次请求时间戳
        self.after_timestamp_2 = "1752717600000"  # 2025-07-17 10:00:00
        # 第三次请求时间戳
        self.after_timestamp_3 = "1752703200000"  # 2025-07-17 06:00:00
        self.limit = 299
        
        # 时间范围（用于验证数据）
        self.start_timestamp = 1752692400000  # 2025-07-17 03:00:00
        self.end_timestamp = 1752728400000    # 2025-07-17 13:00:00
        
        # 代理配置
        self.proxies = {
            "http": "http://127.0.0.1:7897",
            "https": "http://127.0.0.1:7897"
        }
        
        # 重试配置
        self.max_retries = 5
        self.retry_delay_base = 2  # 基础延时秒数

    def timestamp_to_readable(self, timestamp: int) -> str:
        """将时间戳转换为可读格式"""
        try:
            dt = datetime.fromtimestamp(timestamp / 1000)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return f"无效时间戳: {timestamp}"

    def generate_signature(self, timestamp: str, method: str, path: str, params: str = "") -> str:
        """生成OKX API签名"""
        message = f"{timestamp}{method}{path}{params}"
        signature = base64.b64encode(
            hmac.new(
                self.api_secret.encode('utf-8'),
                message.encode('utf-8'),
                digestmod=hashlib.sha256
            ).digest()
        ).decode('utf-8')
        return signature

    def get_headers(self, method: str, path: str, params: str = "") -> dict:
        """生成请求头"""
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        signature = self.generate_signature(timestamp, method, path, params)
        
        headers = {
            "OK-ACCESS-KEY": self.api_key,
            "OK-ACCESS-SIGN": signature,
            "OK-ACCESS-PASSPHRASE": self.api_passphrase,
            "OK-ACCESS-TIMESTAMP": timestamp,
            "Content-Type": "application/json"
        }
        return headers

    def get_database_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.db_config)
            logger.info("数据库连接成功")
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def get_token_addresses(self) -> List[str]:
        """从数据库获取所有token地址"""
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = "SELECT DISTINCT token_address FROM kline_top200_sol WHERE token_address IS NOT NULL AND token_address != ''"
                cursor.execute(sql)
                results = cursor.fetchall()
                token_addresses = [result[0] for result in results]
                logger.info(f"获取到 {len(token_addresses)} 个token地址")
                return token_addresses
        except Exception as e:
            logger.error(f"获取token地址失败: {e}")
            raise
        finally:
            connection.close()

    def make_request_with_retry(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """带重试机制的请求方法"""
        for attempt in range(self.max_retries):
            try:
                logger.info(f"尝试第 {attempt + 1} 次请求: {url}")
                
                # 添加代理到请求参数
                kwargs['proxies'] = self.proxies
                
                if method.upper() == 'GET':
                    response = requests.get(url, **kwargs)
                elif method.upper() == 'POST':
                    response = requests.post(url, **kwargs)
                else:
                    raise ValueError(f"不支持的请求方法: {method}")
                
                # 检查响应状态
                if response.status_code == 200:
                    logger.info(f"请求成功，状态码: {response.status_code}")
                    return response
                else:
                    logger.warning(f"请求失败，状态码: {response.status_code}，第 {attempt + 1} 次尝试")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"请求异常 (第 {attempt + 1} 次尝试): {e}")
            except Exception as e:
                logger.error(f"未知错误 (第 {attempt + 1} 次尝试): {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries - 1:
                delay = self.retry_delay_base * (2 ** attempt) + random.uniform(0, 1)
                logger.info(f"等待 {delay:.2f} 秒后重试...")
                time.sleep(delay)
        
        logger.error(f"所有重试均失败，放弃请求: {url}")
        return None

    def fetch_okx_kline_data(self, token_address: str, after_timestamp: str = None) -> Optional[List]:
        """获取指定token的OKX K线数据"""
        url = f"{self.api_base_url}{self.api_endpoint}"
        
        # 如果没有指定after_timestamp，使用默认的第一个时间戳
        if after_timestamp is None:
            after_timestamp = self.after_timestamp
        
        # 构建请求参数
        params = {
            "chainIndex": self.chain_index,
            "tokenContractAddress": token_address,
            "after": after_timestamp,
            "bar": self.bar,
            "limit": self.limit
        }
        
        # 生成请求头
        headers = self.get_headers("GET", self.api_endpoint)
        
        try:
            response = self.make_request_with_retry(
                url, 
                method='GET',
                headers=headers, 
                params=params, 
                timeout=30
            )
            
            if response is None:
                logger.error(f"token {token_address} OKX所有重试均失败 (after: {after_timestamp})")
                return None
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('code') == '0' and 'data' in data:
                    kline_data = data['data']
                    
                    # 筛选符合时间要求的数据
                    target_range_data = []
                    
                    for kline in kline_data:
                        if len(kline) >= 6:  # 确保数据完整
                            timestamp = int(kline[0])  # ts字段
                            # 筛选目标时间范围内的数据
                            if self.start_timestamp <= timestamp <= self.end_timestamp:
                                target_range_data.append(kline)
                    
                    logger.info(f"token {token_address} OKX API返回 {len(kline_data)} 条数据，符合时间要求: {len(target_range_data)} 条 (after: {after_timestamp})")
                    return target_range_data
                else:
                    logger.warning(f"token {token_address} OKX API响应异常: {data} (after: {after_timestamp})")
                    return None
            else:
                logger.error(f"token {token_address} OKX请求失败，状态码: {response.status_code} (after: {after_timestamp})")
                return None
                
        except Exception as e:
            logger.error(f"获取token {token_address} OKX K线数据失败: {e} (after: {after_timestamp})")
            return None

    def fetch_okx_kline_data_triple(self, token_address: str) -> Optional[List]:
        """对指定token进行三次OKX K线数据请求并合并结果"""
        logger.info(f"开始对token {token_address} 进行三次OKX请求")
        
        all_kline_data = []
        
        # 第一次请求
        logger.info(f"第一次请求 - after: {self.after_timestamp}")
        first_data = self.fetch_okx_kline_data(token_address, self.after_timestamp)
        if first_data:
            all_kline_data.extend(first_data)
            logger.info(f"第一次请求成功，获取 {len(first_data)} 条数据")
        else:
            logger.warning(f"第一次请求失败")
        
        # 添加请求间隔
        time.sleep(2)
        
        # 第二次请求
        logger.info(f"第二次请求 - after: {self.after_timestamp_2}")
        second_data = self.fetch_okx_kline_data(token_address, self.after_timestamp_2)
        if second_data:
            all_kline_data.extend(second_data)
            logger.info(f"第二次请求成功，获取 {len(second_data)} 条数据")
        else:
            logger.warning(f"第二次请求失败")
        
        # 添加请求间隔
        time.sleep(2)
        
        # 第三次请求
        logger.info(f"第三次请求 - after: {self.after_timestamp_3}")
        third_data = self.fetch_okx_kline_data(token_address, self.after_timestamp_3)
        if third_data:
            all_kline_data.extend(third_data)
            logger.info(f"第三次请求成功，获取 {len(third_data)} 条数据")
        else:
            logger.warning(f"第三次请求失败")
        
        # 去重：根据时间戳去重
        if all_kline_data:
            unique_data = {}
            for kline in all_kline_data:
                timestamp = int(kline[0])
                # 使用时间戳作为key，如果重复则保留（数据应该是一样的）
                unique_data[timestamp] = kline
            
            # 转换回列表并按时间戳排序
            final_data = list(unique_data.values())
            final_data.sort(key=lambda x: int(x[0]))  # 按时间戳排序
            
            logger.info(f"token {token_address} 合并去重后共 {len(final_data)} 条数据（原始: {len(all_kline_data)} 条）")
            return final_data
        else:
            logger.warning(f"token {token_address} 三次请求均无有效数据")
            return None

    def update_okx_data(self, token_address: str, kline_data: List):
        """更新OKX K线数据到数据库（更新到对应的记录行）"""
        if not kline_data:
            logger.info(f"token {token_address} 没有符合时间要求的OKX数据，跳过更新")
            return
        
        connection = self.get_database_connection()
        updated_count = 0
        
        try:
            with connection.cursor() as cursor:
                for kline in kline_data:
                    try:
                        # 解析OKX K线数据
                        timestamp = int(kline[0])  # ts
                        
                        # 再次验证时间范围
                        if not (self.start_timestamp <= timestamp <= self.end_timestamp):
                            continue
                        
                        # 将时间戳转换为标准时间格式
                        ha_time = self.timestamp_to_readable(timestamp)
                        okx_open = float(kline[1])   # o - 开盘价
                        okx_high = float(kline[2])   # h - 最高价
                        okx_low = float(kline[3])    # l - 最低价
                        okx_close = float(kline[4])  # c - 收盘价
                        okx_vol = float(kline[6])    # volUsd - 交易量（美元）
                        
                        # 查找对应的记录（根据token_address和ha_time）
                        check_sql = """
                        SELECT id FROM kline_top200_sol 
                        WHERE token_address = %s AND ha_time = %s
                        """
                        cursor.execute(check_sql, (token_address, ha_time))
                        existing_record = cursor.fetchone()
                        
                        if existing_record:
                            # 更新现有记录的OKX数据
                            update_sql = """
                            UPDATE kline_top200_sol 
                            SET okx_open = %s, okx_high = %s, okx_low = %s, okx_close = %s, okx_vol = %s, updated_at = NOW()
                            WHERE token_address = %s AND ha_time = %s
                            """
                            cursor.execute(update_sql, (okx_open, okx_high, okx_low, okx_close, okx_vol, token_address, ha_time))
                            updated_count += 1
                        else:
                            # 如果没有对应的记录，插入新记录（包含OKX数据）
                            insert_sql = """
                            INSERT INTO kline_top200_sol (token_address, ha_time, okx_open, okx_high, okx_low, okx_close, okx_vol, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                            """
                            cursor.execute(insert_sql, (token_address, ha_time, okx_open, okx_high, okx_low, okx_close, okx_vol))
                            updated_count += 1
                    
                    except (ValueError, IndexError) as e:
                        logger.error(f"解析OKX K线数据失败: {e}")
                        continue
                
                connection.commit()
                logger.info(f"token {token_address} 成功更新 {updated_count} 条OKX数据")
                
        except Exception as e:
            connection.rollback()
            logger.error(f"更新token {token_address} OKX数据失败: {e}")
            raise
        finally:
            connection.close()

    def run(self):
        """运行OKX爬虫主程序"""
        target_time_range = f"{self.timestamp_to_readable(self.start_timestamp)} - {self.timestamp_to_readable(self.end_timestamp)}"
        logger.info(f"开始运行OKX K线数据爬虫，目标时间: {target_time_range}")
        logger.info(f"三次请求配置: 第一次after={self.after_timestamp}, 第二次after={self.after_timestamp_2}, 第三次after={self.after_timestamp_3}")
        
        try:
            # 获取所有token地址
            token_addresses = self.get_token_addresses()
            
            if not token_addresses:
                logger.warning("没有找到任何token地址")
                return
            
            total_tokens = len(token_addresses)
            success_count = 0
            
            for i, token_address in enumerate(token_addresses, 1):
                logger.info(f"[{i}/{total_tokens}] 处理OKX数据: {token_address}")
                
                try:
                    # 进行三次请求获取OKX K线数据
                    kline_data = self.fetch_okx_kline_data_triple(token_address)
                    
                    if kline_data:
                        # 更新数据到数据库
                        self.update_okx_data(token_address, kline_data)
                        success_count += 1
                    
                    # 添加延时，避免请求过于频繁
                    time.sleep(3)
                    
                except Exception as e:
                    logger.error(f"处理token {token_address} OKX数据时发生错误: {e}")
                    continue
            
            logger.info(f"OKX爬虫完成！成功处理 {success_count}/{total_tokens} 个token")
            
        except Exception as e:
            logger.error(f"OKX爬虫运行失败: {e}")
            raise


if __name__ == "__main__":
    spider = OKXSpider()
    spider.run()