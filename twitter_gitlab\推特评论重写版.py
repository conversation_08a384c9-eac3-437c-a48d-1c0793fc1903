# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2025/2/11 13:45
@Python  :  Python3.7
@Desc    :  根据推文地址获取推文评论数据
"""

import requests
import json
import time
import random
import pandas as pd
import os
from loguru import logger
from datetime import datetime
from db import RedisClient
from Spiders.utils import DB_BASE
from Spiders.settings import POLL_DB


class Tweet_detial_Spider(DB_BASE):

    def __init__(self):
        super().__init__()
        self.mysql_conn = POLL_DB.connection()

        self.redis_client = RedisClient(redis_name='cookies:twitter')
        self.cookie_data = self.get_cookie_from_redis()

        self.api_url = "https://x.com/i/api/graphql/Ez6kRPyXbqNlhBwcNMpU-Q/TweetDetail"
        self.headers = {
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": self.cookie_data.get('csrf_token', '')
        }
        self.cookies = {
            "auth_token": self.cookie_data.get('auth_token', ''),
            "ct0": self.cookie_data.get('csrf_token', ''),
        }
        self.base_params = {
            "variables": "",
            "features": "{\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"responsive_web_graphql_exclude_directive_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"rweb_video_timestamps_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":false,\"responsive_web_enhance_cards_enabled\":false}",
            "fieldToggles": "{\"withArticleRichContentState\":true,\"withArticlePlainText\":false,\"withGrokAnalyze\":false,\"withDisallowedReplyControls\":false}"
        }
        self.proxy = {
            "http": self.cookie_data.get('proxy_ip', ''),
            "https": self.cookie_data.get('proxy_ip', '')
        }


    def get_cookie_from_redis(self):
        """从Redis中获取cookie信息"""
        try:
            account_name, cookie_json = self.redis_client.get_random_account()
            
            if not account_name or not cookie_json:
                logger.error("无法从Redis获取cookie信息:'cookies:twitter'哈希表为空")
                return {}
            logger.success(f"成功提取账号 {account_name} 的cookie信息")
            
            try:
                cookie_data = json.loads(cookie_json)
                if not isinstance(cookie_data, dict):
                    logger.error("从Redis获取的cookie信息不是有效的字典格式")
                    return {}
                return cookie_data
            except json.JSONDecodeError as e:
                logger.error(f"从Redis获取的cookie信息不是有效的JSON格式: {e}")
                return {}
        except Exception as e:
            logger.error(f"从Redis获取cookie信息失败: {e}")
            return {}


    def get_tweets_and_users_from_mysql(self):
        """从MySQL获取推文ID和用户名信息"""
        try:
            cursor = self.mysql_conn.cursor()
            sql = """
            SELECT t.tweet_id, u.username 
            FROM tweets t 
            JOIN users u ON t.user_id = u.user_id 
            WHERE t.tweet_id IS NOT NULL 
            AND u.username IS NOT NULL
            """
            
            cursor.execute(sql)
            results = cursor.fetchall()
            cursor.close()
            
            if not results:
                logger.warning("未从MySQL获取到有效数据")
                return []
                
            data_list = []
            for result in results:
                username = result[1].lstrip('@') if result[1] else ''
                data_list.append({
                    'tweet_id': result[0],
                    'username': username
                })
                
            logger.success(f"成功从MySQL获取 {len(data_list)} 条数据")
            return data_list
        except Exception as e:
            logger.error(f"从MySQL获取数据失败: {e}")
            return []


    def update_request_params(self, tweet_id, username, is_first_request=True):
        """更新请求参数"""
        try:
            self.headers["referer"] = f"https://x.com/{username}/status/{tweet_id}"
            
            if is_first_request:
                variables_dict = {
                    "focalTweetId": tweet_id,
                    "with_rux_injections": False,
                    "rankingMode": "Relevance",
                    "includePromotedContent": True,
                    "withCommunity": True,
                    "withQuickPromoteEligibilityTweetFields": True,
                    "withBirdwatchNotes": True,
                    "withVoice": True
                }
            else:
                variables_dict = {
                    "focalTweetId": tweet_id,
                    "cursor": "",
                    "referrer": "tweet",
                    "controller_data": "DAACDAAFDAABDAABDAABCgABAAAAAAAGgAAAAAwAAgoAAQAAAAAAAAAECgACYVZG2AM5/gkLAAMAAAAFY3VycnkKAAVNf30fLMyCiAgABgAAAAQKAAfKjux4G3+d/gAAAAAA",
                    "with_rux_injections": False,
                    "rankingMode": "Relevance",
                    "includePromotedContent": True,
                    "withCommunity": True,
                    "withQuickPromoteEligibilityTweetFields": True,
                    "withBirdwatchNotes": True,
                    "withVoice": True
                }
                
            self.base_params["variables"] = json.dumps(variables_dict)
            logger.info(f"已更新请求参数: tweet_id={tweet_id}, username={username}, is_first_request={is_first_request}")
            return True
        except Exception as e:
            logger.error(f"更新请求参数失败: {e}")
            return False

    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间格式到标准格式"""
        try:
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}")
            return None

    def fetch_comments_data(self, tweet_id, username, cursor=None):
        """发送请求获取评论数据"""
        try:
            is_first_request = cursor is None
            self.update_request_params(tweet_id, username, is_first_request)

            if cursor:
                variables_dict = json.loads(self.base_params["variables"])
                variables_dict["cursor"] = cursor
                self.base_params["variables"] = json.dumps(variables_dict)
                
            response = requests.get(
                self.api_url,
                headers=self.headers,
                cookies=self.cookies,
                params=self.base_params,
                proxies=self.proxy
            )
            
            if response.status_code != 200:
                logger.error(f"请求失败 状态码: {response.status_code}")
                logger.error(f"错误响应: {response.text}")
                logger.error(f"请求URL: {response.url}")
                logger.error(f"请求头: {json.dumps(self.headers, indent=2)}")
                logger.error(f"请求cookies: {json.dumps(self.cookies, indent=2)}")
                logger.error(f"请求参数: {json.dumps(self.base_params, indent=2)}")
                response.raise_for_status()
                
            logger.success(f"请求成功 [{response.status_code}]")
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求异常: {str(e)}")
            if hasattr(e.response, 'text'):
                logger.error(f"错误响应详情: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"其他错误: {str(e)}")
            return None

    def get_next_cursor(self, data):
        """翻页逻辑"""
        try:
            instructions = data.get('data', {}).get('threaded_conversation_with_injections_v2', {}).get('instructions', [])
            
            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                    
                    for entry in entries:
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineItem':
                            itemContent = content.get('itemContent', {})
                            if itemContent.get('itemType') == 'TimelineTimelineCursor':
                                cursor_value = itemContent.get('value')
                                if cursor_value:
                                    logger.success(f"成功提取游标: {cursor_value[:30]}...")
                                    return cursor_value
                                
            logger.warning("未找到有效游标")
            return None
        except Exception as e:
            logger.error(f"游标解析异常: {str(e)}")
            return None


    def extract_comments(self, data):
        """解析评论数据并准备存入数据库"""
        comments_data = []
        try:
            instructions = data.get('data', {}).get('threaded_conversation_with_injections_v2', {}).get('instructions', [])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                    
                    for entry in entries:
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineItem':
                            itemContent = content.get('itemContent', {})
                            if itemContent.get('itemType') == 'TimelineTweet':
                                tweet_results = itemContent.get('tweet_results', {}).get('result', {})

                                edit_control = tweet_results.get('edit_control', {})
                                editable_until_msecs = edit_control.get('editable_until_msecs')
                                editable_time = None
                                if editable_until_msecs:
                                    try:
                                        editable_time = datetime.fromtimestamp(
                                            int(editable_until_msecs) / 1000
                                        ).strftime('%Y-%m-%d %H:%M:%S')
                                    except Exception as e:
                                        logger.warning(f"时间转换失败: {e}")

                                if tweet_results:
                                    legacy = tweet_results.get('legacy', {})
                                    if legacy:
                                        comment_data = {
                                            'comment_id': entry.get('entryId'),
                                            'comment_text': legacy.get('full_text'),
                                            'comment_time': editable_time,
                                            'views': legacy.get('view_count', 0),
                                            'replies': legacy.get('reply_count', 0),
                                            'retweets': legacy.get('retweet_count', 0),
                                            'favorites': legacy.get('favorite_count', 0),
                                            'bookmarks': legacy.get('bookmark_count', 0),
                                            'parent_comment_id': legacy.get('in_reply_to_status_id_str')
                                        }
                                        comments_data.append(comment_data)
                                    
            logger.success(f"成功解析 {len(comments_data)} 条评论数据")
            return comments_data
        except Exception as e:
            logger.error(f"评论数据解析失败: {e}")
            return []
        
    def get_data_and_next_page(self, tweet_id, username, cursor=None):
        """获取评论数据和下一页游标"""
        try:
            # 发送请求获取数据
            response_data = self.fetch_comments_data(tweet_id, username, cursor)
            if not response_data:
                logger.warning("未获取到响应数据")
                return [], None
                
            # 提取评论数据
            comments = self.extract_comments(response_data)
            if not comments:
                logger.warning("未提取到评论数据")
            
            # 提取下一页游标
            next_cursor = self.get_next_cursor(response_data)
            if next_cursor:
                logger.info("成功获取下一页游标")
            else:
                logger.info("已到达最后一页")
            
            return comments, next_cursor
            
        except Exception as e:
            logger.error(f"获取评论数据和游标失败: {e}")
            return [], None

    def save_comments_to_csv(self, comments_data, filename='X/x_output/comments.csv'):
        """保存评论数据到CSV文件"""
        try:
            if not comments_data:
                return

            df = pd.DataFrame(comments_data)
            
            file_exists = os.path.exists(filename)
            
            df.to_csv(
                filename,
                mode='a' if file_exists else 'w',
                header=not file_exists,
                index=False,
                encoding='utf-8-sig'
            )
            logger.success(f"成功保存 {len(comments_data)} 条评论到 {filename}")
        except Exception as e:
            logger.error(f"保存评论数据失败: {e}")


    def save_comments_to_mysql(self, comments_data, tweet_id):
        """将评论数据保存到MySQL"""
        try:
            if not comments_data:
                logger.warning("没有评论数据需要保存")
                return
                
            sql = """
            INSERT INTO comments (comment_id, tweet_id, comment_text, comment_time,
                            views, replies, retweets, favorites, bookmarks, parent_comment_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            tweet_id=COALESCE(VALUES(tweet_id), tweet_id),
            comment_text=COALESCE(VALUES(comment_text), comment_text),
            comment_time=COALESCE(VALUES(comment_time), comment_time),
            views=COALESCE(VALUES(views), views),
            replies=COALESCE(VALUES(replies), replies),
            retweets=COALESCE(VALUES(retweets), retweets),
            favorites=COALESCE(VALUES(favorites), favorites),
            bookmarks=COALESCE(VALUES(bookmarks), bookmarks),
            parent_comment_id=COALESCE(VALUES(parent_comment_id), parent_comment_id)
            """
            
            for comment in comments_data:
                values = [
                    comment.get('conversation_id_str'),
                    tweet_id,
                    comment.get('full_text', ''),
                    comment.get('editable_time'),
                    comment.get('views_count', 0),
                    comment.get('reply_count', 0),
                    comment.get('retweet_count', 0),
                    comment.get('favorite_count', 0),
                    comment.get('bookmark_count', 0),
                    comment.get('parent_comment_id', None)
                ]
                
                super().insert_mysql(sql, values)
            
            logger.success(f"成功保存 {len(comments_data)} 条评论到数据库")
        except Exception as e:
            logger.error(f"保存评论数据失败: {e}")


    def run(self):
        """主运行方法"""

        logger.info("开始运行评论采集程序...")
        # 获取推文数据
        tweet_user_data = self.get_tweets_and_users_from_mysql()
        if not tweet_user_data:
            logger.error("无可用的推文和用户数据，程序退出")
            return

        logger.info(f"共获取到 {len(tweet_user_data)} 条推文数据待处理")

        # 处理每条推文
        # for data in tweet_user_data:
        for index, data in enumerate(tweet_user_data, 1):
            tweet_id = data['tweet_id']
            username = data['username']
            
            # logger.info(f"开始处理推文: {tweet_id}, 用户: {username}")
            logger.info(f"开始处理第 {index}/{len(tweet_user_data)} 条推文 [tweet_id: {tweet_id}, username: {username}]")
            
            cursor = None
            max_retries = 3
            previous_cursor = None
            total_comments = 0
            retry_count = 0
            
            while True:
                # 如果cursor重复，说明到达最后一页
                if cursor and cursor == previous_cursor:
                    logger.info(f"推文{username}---{tweet_id} 评论数据采集完成，共获取 {total_comments} 条评论")
                    break
                    
                comments_data = None
                cursor_value = None

                for attempt in range(max_retries):
                    comments_data, cursor_value = self.get_data_and_next_page(tweet_id, username, cursor)
                    if comments_data or cursor_value is None:
                        break

                retry_count += 1
                logger.warning(f"第 {attempt + 1}/{max_retries} 次重试... [总重试次数: {retry_count}]")
                time.sleep(5)
                
                if not comments_data and not cursor_value:
                    logger.error(f"推文 {tweet_id} 评论获取失败，跳过处理")
                    break

                if comments_data:
                    self.save_comments_to_csv(comments_data)
                    # self.save_comments_to_mysql(comments_data, tweet_id)
                    total_comments += len(comments_data)
                
                # 更新游标
                previous_cursor = cursor
                cursor = cursor_value
                
                # 如果没有下一页，退出循环
                if not cursor:
                    logger.info(f"推文 {tweet_id} 评论数据采集完成，共获取 {total_comments} 条评论")
                    break
                
                # 翻页等待
                wait_time = round(random.uniform(1, 3), 2)
                logger.info(f"翻页等待 {wait_time} 秒...")
                time.sleep(wait_time)

            # 处理下一条推文前等待
            if index < len(tweet_user_data):
                logger.info("等待 1 秒后处理下一条推文...")
                time.sleep(1)

        logger.success(f"所有推文评论采集完成，共处理 {len(tweet_user_data)} 条推文")


if __name__ == "__main__":
    try:
        logger.info("初始化爬虫程序...")
        spider = Tweet_detial_Spider()
        spider.run()
    except KeyboardInterrupt:
        logger.warning("程序被手动中断")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
    finally:
        logger.info("程序结束")