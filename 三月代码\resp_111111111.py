from curl_cffi import requests

proxies = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}


headers = {
    "authority": "www.feixiaohao.com",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "max-age=0",
    "referer": "https://www.feixiaohao.com/",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}
cookies = {
    "indexUnit": "cny",
    "_ga": "GA1.1.**********.**********",
    "pairUnit": "cny",
    "compareCoinList": "",
    "showCompareWindowt": "0",
    "uid": "1a328dea-0208-42ee-b1fc-1a49aa4493b0",
    "Hm_lvt_d8e00ef8cdac9bb2d47224dc911bfc7d": "**********,**********,**********,**********",
    "HMACCOUNT": "75320AF280CCE228",
    "Hm_lpvt_d8e00ef8cdac9bb2d47224dc911bfc7d": "**********",
    "_ga_W96JF2Z8E4": "GS1.1.**********.59.1.**********.0.0.0"
}
url = "https://www.feixiaohao.com/currencies/tether/"
response = requests.get(url, headers=headers, cookies=cookies, proxies=proxies)

print(response.text)
print(response)

# 应该:**********
# 接口:**********
# 相近:**********