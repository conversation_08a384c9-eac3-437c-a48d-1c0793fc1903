#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HA平台历史K线数据爬虫
功能：从HA平台API获取历史K线数据并存储到MySQL数据库（三平台对比表）
"""

import requests
import sqlite3
import pymysql
import time
import json
from datetime import datetime
from typing import List, Dict, Optional
from loguru import logger


class HASpider:
    def __init__(self):
        # HA平台API配置
        # self.api_url = "http://**************/api/v1/dex/market/kline-history"
        self.api_url = "https://pre-deploy.valuescan.ai/api/v1/dex/market/kline-history"
        self.headers = {
            'Content-Type': 'application/json'
        }

        self.sqlite_db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        self.mysql_config = {
            'host': '**************',
            'port': 33060,
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8'
        }
        self.request_params = {
            'chainName': 'SOL',
            'bar': '1m',
            'limit': 1500,
            'useNativePricing': False
        }

        self.request_interval = 2  # 请求间隔（秒）

    def init_mysql_database(self):
        """初始化MySQL数据库连接"""
        try:
            # 设置数据库名称
            self.mysql_config['database'] = 'kline_data'
            # 测试连接
            connection = pymysql.connect(**self.mysql_config)
            cursor = connection.cursor()
            cursor.close()
            connection.close()
            logger.info("MySQL数据库连接成功")
        except Exception as e:
            logger.error(f"初始化MySQL数据库连接失败: {e}")
            raise

    def get_token_addresses(self) -> List[Dict[str, str]]:
        """从SQLite数据库获取Token Address列表"""
        try:
            conn = sqlite3.connect(self.sqlite_db_path)
            cursor = conn.cursor()

            # 查询Token Address和Symbol
            cursor.execute("""
                SELECT "Token Address", "Token symbol" 
                FROM high_low_bsc 
                WHERE "Token Address" IS NOT NULL 
                AND "Token Address" != ''
            """)

            results = cursor.fetchall()
            token_list = []

            for row in results:
                token_list.append({
                    'address': row[0],
                    'symbol': row[1] if row[1] else 'UNKNOWN'
                })

            conn.close()
            logger.info(f"从SQLite数据库获取到 {len(token_list)} 个Token地址")
            return token_list

        except Exception as e:
            logger.error(f"获取Token Address失败: {e}")
            return []

    def timestamp_to_datetime(self, timestamp: str) -> str:
        """将时间戳转换为标准时间格式"""
        try:
            # 将时间戳转换为秒（如果是毫秒）
            if len(timestamp) > 10:
                timestamp_sec = int(timestamp) / 1000
            else:
                timestamp_sec = int(timestamp)

            dt = datetime.fromtimestamp(timestamp_sec)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.error(f"时间戳转换失败 {timestamp}: {e}")
            return ""

    def fetch_kline_data(self, token_address: str) -> Optional[List[List]]:
        """获取指定Token的K线数据"""
        try:
            # 构建请求参数
            params = self.request_params.copy()
            params['tokenContractAddress'] = token_address

            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=params,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()

                if data.get('code') == 200 and 'data' in data:
                    kline_data = data['data'].get('kline', [])
                    logger.info(f"Token {token_address} 获取到 {len(kline_data)} 条K线数据")
                    return kline_data
                else:
                    logger.warning(f"Token {token_address} API返回错误: {data}")
                    return None
            else:
                logger.error(f"Token {token_address} 请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Token {token_address} 请求异常: {e}")
            return None

    def save_ha_kline_data(self, token_address: str, token_symbol: str, kline_data: List[List]):
        """保存HA平台K线数据到三平台对比表"""
        try:
            connection = pymysql.connect(**self.mysql_config)
            cursor = connection.cursor()

            successful_inserts = 0
            successful_updates = 0

            for kline in kline_data:
                if len(kline) >= 5:
                    timestamp = kline[0]  # 时间戳
                    open_price = float(kline[1])   # 开盘价
                    high_price = float(kline[2])   # 最高价
                    low_price = float(kline[3])    # 最低价
                    close_price = float(kline[4])  # 收盘价

                    # 转换时间戳
                    datetime_str = self.timestamp_to_datetime(timestamp)

                    if not datetime_str:
                        continue

                    # 检查是否已存在该时间戳的记录
                    check_sql = """
                    SELECT id FROM kline_top200_sol 
                    WHERE token_address = %s AND timestamp = %s
                    """
                    cursor.execute(check_sql, (token_address, int(timestamp)))
                    existing_record = cursor.fetchone()

                    if existing_record:
                        update_sql = """
                        UPDATE kline_top200_sol SET 
                            ha_open = %s, ha_high = %s, ha_low = %s, ha_close = %s, ha_time = %s
                        WHERE token_address = %s AND timestamp = %s
                        """
                        cursor.execute(update_sql, (
                            open_price, high_price, low_price, close_price, datetime_str,
                            token_address, int(timestamp)
                        ))
                        successful_updates += 1
                    else:
                        insert_sql = """
                        INSERT INTO kline_top200_sol 
                        (token_address, token_symbol, timestamp, datetime_str, 
                         ha_open, ha_high, ha_low, ha_close, ha_time)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(insert_sql, (
                            token_address, token_symbol, int(timestamp), datetime_str,
                            open_price, high_price, low_price, close_price, datetime_str
                        ))
                        successful_inserts += 1

            connection.commit()
            logger.info(f"Token {token_address} HA数据 - 新增: {successful_inserts}条, 更新: {successful_updates}条")

            cursor.close()
            connection.close()

        except Exception as e:
            logger.error(f"Token {token_address} 保存HA数据失败: {e}")

    def run(self):
        """运行HA平台爬虫主程序"""
        logger.info("开始运行HA平台K线数据爬虫（三平台对比模式）")

        try:
            # 1. 初始化MySQL数据库连接
            logger.info("初始化MySQL数据库连接...")
            self.init_mysql_database()

            # 2. 获取Token Address列表
            logger.info("获取Token Address列表...")
            token_list = self.get_token_addresses()

            if not token_list:
                logger.error("未获取到Token Address列表，程序结束")
                return

            # 3. 依次处理每个Token
            total_tokens = len(token_list)
            for i, token_info in enumerate(token_list, 1):
                token_address = token_info['address']
                token_symbol = token_info['symbol']

                logger.info(f"[{i}/{total_tokens}] 处理Token: {token_symbol} ({token_address})")

                # 获取K线数据
                kline_data = self.fetch_kline_data(token_address)

                if kline_data:
                    # 保存HA平台数据
                    self.save_ha_kline_data(token_address, token_symbol, kline_data)
                else:
                    logger.warning(f"Token {token_address} 未获取到数据，跳过")

                # 请求间隔
                if i < total_tokens:
                    logger.info(f"等待 {self.request_interval} 秒...")
                    time.sleep(self.request_interval)

            logger.info("HA平台K线数据爬虫运行完成")

        except Exception as e:
            logger.error(f"爬虫运行失败: {e}")
            raise

def main():
    """主函数"""
    spider = HASpider()
    spider.run()

if __name__ == "__main__":
    main()
