import requests
from loguru import logger
from datetime import date
crawl_date = date.today()
import re
import redis
import time
import json

proxy = "*************************************************"
proxies = {
    'http': proxy, 
    'https': proxy,
}

redis_client = redis.Redis(
    host='**************',
    port=6379,
    db=14,
    password='123456',
    decode_responses=True
)

def login_test():
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36'
    }

    cookies = {
        'cf_clearance': 'Vaq.apYoJiz2ANIXY4Bj_Qlk3GVXwJ.9cvb6W4zh0Ug-1734679056-*******-G4vXmkia2xfhPnW7C08uj5g6qJj6Ak4ufEhb4hl0vOX1Lu7Y9k1Z5RHUt3044jOhfGanIYYxy23GKdDIijg6ST2JKAdvVyprxOqura2iurJXAVz8bGvY1pSin2gcOyxrFRlkTsdDONUTRLE9bT_rXOZZx5dRFdJSD_.LQmrGgh.mW4XAROazsdyWGmPii0LIv.B.UyoLB7DQ5_h67nkk_lVCzInCwERBbLkO41u..o2jEybRiaiWrTZT276zputochIVNuwLROvtRB8pciC1HNEFYLjVgzE0KUsUI_9XLe7keXIlVuuGElaWpEU3fgjD994AfqfO.eBI0hiNbNKKRfcSFzxaVX4D8rJAwA3NgqkcG5KeYgcTUoX1pAYh7nu.2lX.hvyZ15WL6JT5D7HQY3.1PUq9YG2uuQheZK8GLC0', 'ASP.NET_SessionId': 'hi5nvxdnl5kzxdo1tkv3dqfb'
    }

    token = '0.CBH0Kcn5PMhaTYh6wq_IIXknlsVpdFvX1vUYFJgl1PgAOzfAz02jArd2O6OxxpdG1ByerSY12zbZNpiZ-OxbEIJvV3KlcWlJ271pnCsq5DyIRO3XiH3xC5zVXD7hyx1xEG5XjO642XVSAeG0iw84kpo3npBQNxRW7VJg9BYXPHQHS2WfcrfyL-fg7Mnki3qusMMlW7G4r3ndhJ9IFI65tIDaJTVtaets818SAXEogs8XoaY_onyh1_fcHWottB6p-gTBRhvnGYHgfx69_BTFZXatYXd9rHI95oU113TfSn35f7fdq0ZN4WvrtAAn4_I45tXaywA1Y9R-xpuUTUWHY1Igqqh63zlXG4ekYNmxl9s9EgGCK-lJEcs9RRdgNeqdsj_HN1Rg3XUT7I4MdPVtBCVSVFdJAq3FBfrWPlsdisErpx7A14mK0Bmb1vkX14x9ndQslOQDRbKtJcIxWyi3dLGvmlsxcTwY2D4L2vnUCi7WN9LRtSMtu0f91y89MX1QjWQFUkWATwdnpxUoUJDTqEHFJ8FN2Gn1E42HZo0ZTeoOlmuPjLzcU4FmAk3WHvYAX-uQOOGM2TxHrlIGR_ukOyGIWhjH1kVoaQWk0CRkJvewH-vrVKXSPfPvL2FBNt0O2iHkG9wlbUZksVlV9V7wtmR8eKUjKcpyeTo9ULabSeLiMasRmI6ScNP6I6HSqidlbzXRPmISw3diZTjl7e1Y8lsvcUYQdaGlca8YEg3GUsoFnJ_paCqB94uiTA6m0QxloJ7zJl4H7hZW5m4OTcjT67Kt8jkNSLE-L_za9sYdUx0._hspW9zuTVJwdrwYqippiQ.1b8cc144aa05a3d0bf4cefa0b394489fa389c54aea821feb2b6a516b14c68230'

    url = "https://optimistic.etherscan.io/login"
    response = requests.get(url, headers=headers, cookies=cookies, proxies=proxies)
    logger.info(f'过盾状态码:{response.status_code}')
    logger.info('================================================================================================')
    if response.status_code == 200:
        logger.info('过盾成功,开始拿取载荷参数')
        time.sleep(2)
    viewstate = re.search(r'__VIEWSTATE" value="(.*?)"', response.text)
    viewstate_generator = re.search(r'__VIEWSTATEGENERATOR" value="(.*?)"', response.text)
    event_validation = re.search(r'__EVENTVALIDATION" value="(.*?)"', response.text)
    logger.info('================================================================================================')
    logger.info('================================================================================================')
    logger.info('成功拿到载荷参数')
    logger.info('开始使用Token尝试登陆')
    post_data = {
        "__VIEWSTATE": viewstate.group(1),
        "__VIEWSTATEGENERATOR": viewstate_generator.group(1),
        "__EVENTVALIDATION": event_validation.group(1),
        "ctl00$ContentPlaceHolder1$txtUserName": "Krickliu",
        "ctl00$ContentPlaceHolder1$txtPassword": "DOMYBEST0922",
        "cf-turnstile-response": token,
        "ctl00$ContentPlaceHolder1$btnLogin": "LOGIN"
    }
    # url = "https://optimistic.etherscan.io/login"
    url = "https://optimistic.etherscan.io/labelcloud"
    logger.info('尝试登录....')

    response2 = requests.post(url=url, headers=headers , cookies=cookies,
                            proxies=proxies)

    logger.info(response2.status_code)
#     time.sleep(2)
#     if response2.status_code != 200:
#         logger.info(f'登录请求失败，状态码：{response2.status_code}，退出程序。')
#         exit()

#     elif response2.status_code == 200:
#         logger.info(f'登录页面状态：{response2.status_code}')
#         # logger.info(f'{response2.text}')
#         logger.info('登录成功！')
#         time.sleep(3)
                
#         if response2.cookies:
#             logger.info(f'登录成功后的cookies: {response2.cookies.get_dict()}')
#             logger.info('已经成功获取到cookie!')
#             logger.info(f'登录成功后的cookies: {response2.cookies.get_dict()}')
#             cookies_json = json.dumps(response2.cookies.get_dict())
#             redis_client.set("API_COOKIES", cookies_json)
#             logger.info('cookie已经保存到redis')
#         else:
#             logger.info('登录成功,但未获取到任何cookie信息。')
#             exit()


# # @staticmethod
# def check_login_status(text):
#     """ 检查登录状态 """

#     login_status: bool
#     error_msg: str = ''
#     if 'Sign In for Continued Access' in text:
#         login_status = False
#         error_msg = '登录失败, 已过掉cloudflare检测, 但未登录成功!'
#     elif 'OP Mainnet Top Accounts by ETH Balance' in text:
#         login_status = True
#         logger.info('检测登录-成功')
#     else:
#         login_status = False
#         error_msg = '登录失败, 没有过掉cloudflare检测'
#     return login_status, error_msg

if __name__ == '__main__':

    login_test()