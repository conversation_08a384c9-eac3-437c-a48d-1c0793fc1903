import redis
import requests
import time
import json
from datetime import date
from loguru import logger
import re

crawl_date = date.today()

redis_client = redis.Redis(
    host='**************',
    port=6379,
    db=9,
    password='123456',
    decode_responses=True
)

proxy_api = "http://acf9782b22eb30:<EMAIL>:5001"
# proxy_api = "socks5://**************:30889"
proxies_api = {
    'http': proxy_api,
    'https': proxy_api,
}


def create_task_for_cookie():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}
    data_task = {
        "clientKey": client_key,
        "task": {
            "type": "CloudFlareTaskS2",
            "websiteURL": "https://blastscan.io/tokens",
            "proxy": proxy_api,
            "waitLoad": True,
        }
    }
    response = requests.post(url, headers=headers, json=data_task)
    response_json = response.json()
    if response_json['errorId'] == 0:
        task_id_for_cookie = response_json['taskId']
        logger.info(f"Task ID: {task_id_for_cookie}")
        return task_id_for_cookie
    else:
        logger.info(f"Error: {response_json['errorDescription']}")
        return None


def get_task_result_for_cookie(task_id_for_cookie):
    logger.info('正在获取cookie等信息')
    url_result = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data_result = {
        "clientKey": client_key,
        "taskId": task_id_for_cookie
    }

    response = requests.post(url_result, headers=headers, json=data_result)
    result = response.json()
    for i in range(40):
        if result['errorId'] == 0:
            if result['status'] == 'ready':

                cookies = result['solution'].get('cookies', {})
                cf_clearance_value = cookies.get("cf_clearance")
                asp_net_session_id = cookies.get("ASP.NET_SessionId")
                user_agent = result['solution'].get('user_agent')
                
                required_cookies = {
                    'cf_clearance': cf_clearance_value,
                    'ASP.NET_SessionId': asp_net_session_id,
                    'proxy_ip': proxy_api,  # 添加代理IP信息
                    'user_agent': user_agent  # 直接将user_agent加入到cookie信息中
                }
                redis_client.set('cf_cookies:blastscan', json.dumps(required_cookies))
                logger.info('已经获取到cookies和headers并已经存入redis')

                # 创建不包含代理IP的cookies用于返回
                cookies_for_use = {
                    'cf_clearance': cf_clearance_value,
                    'ASP.NET_SessionId': asp_net_session_id
                }

                headers = {'User-Agent': user_agent} if user_agent else None
                return cookies_for_use, headers

            elif result['status'] == 'processing':
                time.sleep(3)
                response = requests.post(url_result, headers=headers, json=data_result)
                result = response.json()
                continue
            else:
                logger.info("Task is not ready yet. Status:", result['status'])
                return result
        else:
            logger.info("Error in getting task result:", result['errorDescription'])
            return result

    logger.info("Max retries reached. Task is still processing.")
    return None


def create_task_for_token():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": client_key,
        "task": {
            "type": "TurnstileTaskProxyless",
            "websiteURL": "https://blastscan.io/login",
            "websiteKey": "0x4AAAAAAAcC_jl_3G-nGWMQ"
        }
    }
    response = requests.post(url, headers=headers, json=data)
    response_json = response.json()
    if response_json['errorId'] == 0:
        task_id_for_token = response_json['taskId']
        return task_id_for_token
    else:
        logger.info(f"Error: {response_json['errorDescription']}")
        return None


def get_task_result_for_token(task_id_for_token):
    time.sleep(3)
    logger.info('开始获取token')
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": client_key,
        "taskId": task_id_for_token
    }

    response = requests.post(url, headers=headers, json=data)
    token_result = response.json()
    for i in range(40):
        if token_result['errorId'] == 0:
            if token_result['status'] == 'ready':
                token = token_result['solution']['token']
                # logger.info(f'token:{token}')
                return token

            elif token_result['status'] == 'processing':
                logger.info(f'retry to get token, num: {i}')
                time.sleep(3)
                response = requests.post(url, headers=headers, json=data)
                token_result = response.json()
                continue
            else:
                logger.info("Task is not ready yet. Status:", token_result['status'])
                return token_result
        else:
            logger.info("Error in getting task result:", token_result['errorDescription'])
            return token_result

    logger.info("Max retries reached. Task is still processing.")
    return None


# @staticmethod
def check_login_status(text):
    """ 检查登录状态 """

    login_status: bool
    error_msg: str = ''
    if 'Sign In for Continued Access' in text:
        login_status = False
        error_msg = '登录失败, 已过掉cloudflare检测, 但未登录成功!'
    elif 'Mainnet Top Accounts' in text:
        login_status = True
        logger.info('检测登录-成功')
    else:
        login_status = False
        error_msg = '登录失败, 没有过掉cloudflare检测'
    return login_status, error_msg


def login_for_5s(required_cookies, headers, token):
    logger.info('尝试拿取载荷参数')
    login_url = "https://blastscan.io/login"
    response1 = requests.get(login_url, headers=headers, cookies=required_cookies, proxies=proxies_api)
    time.sleep(1)

    if response1.status_code != 200:
        logger.info(f'获取登录页面失败，状态码：{response1.status_code}')
        logger.info(f'{response1.text}')
        return

    if response1.status_code == 200:
        logger.info('过盾成功,开始拿取载荷参数')

    viewstate = re.search(r'__VIEWSTATE" value="(.*?)"', response1.text)
    viewstate_generator = re.search(r'__VIEWSTATEGENERATOR" value="(.*?)"', response1.text)
    event_validation = re.search(r'__EVENTVALIDATION" value="(.*?)"', response1.text)

    if viewstate and viewstate_generator and event_validation:
        logger.info('成功拿到载荷参数，开始打码尝试登陆')
        time.sleep(2)

    post_data = {
        "__VIEWSTATE": viewstate.group(1),
        "__VIEWSTATEGENERATOR": viewstate_generator.group(1),
        "__EVENTVALIDATION": event_validation.group(1),
        "ctl00%24ContentPlaceHolder1%24txtUserName": "Krickliu",
        "ctl00%24ContentPlaceHolder1%24txtPassword": "DOMYBEST0922",
        "cf-turnstile-response": token,
        "ctl00%24ContentPlaceHolder1%24btnLogin": "LOGIN"
    }
    url = "https://blastscan.io/login"

    for i in range(2):
        response2 = requests.post(url=url, headers=headers, data=post_data, cookies=required_cookies,
                                  proxies=proxies_api)
        time.sleep(2)
        if response2.status_code == 200:
            logger.info(f'登录页面状态：{response2.status_code}')
            if response2.cookies:
                logger.info(f'登录成功后的cookies: {response2.cookies.get_dict()}')
                new_cookies = response2.cookies.get_dict()
                existing_cookies_json = redis_client.get('cf_cookies:blastscan')

                if existing_cookies_json:
                    existing_cookies = json.loads(existing_cookies_json)

                    combined_cookies = {**existing_cookies, **new_cookies}
                    redis_client.set("cf_cookies:blastscan", json.dumps(combined_cookies))
                else:

                    redis_client.set("cf_cookies:blastscan", json.dumps(new_cookies))

                logger.info(f'合并后的cookies: {combined_cookies}')
                logger.info('cookie已经保存到redis')

                break
            else:
                logger.info('blastscan登录成功,但未获取到任何cookie信息或blastscan没有"__cflb"参数')
                logger.info(f'开始重试,第{i}次')
        else:
            logger.info(f'登录请求失败，状态码：{response2.status_code}，退出程序。')
            exit()


if __name__ == '__main__':

    client_key = "0eac9c274ab19b81ecff5a9161c5c2e6d73137b256298"
    task_id_for_cookie = create_task_for_cookie()
    required_cookies = None
    headers = None
    if task_id_for_cookie:
        required_cookies, headers = get_task_result_for_cookie(task_id_for_cookie)

    task_id_for_token = create_task_for_token()

    token = None
    if task_id_for_token:
        token = get_task_result_for_token(task_id_for_token)

    session = requests.Session()
    login_for_5s(required_cookies, headers, token)
