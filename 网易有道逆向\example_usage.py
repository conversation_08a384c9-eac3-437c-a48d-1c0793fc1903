#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
有道翻译解密 - 使用示例

展示如何处理从接口获取的加密字符串
"""

from decrypt_python import preprocess_encrypted_data, decrypt_youdao

def simple_decrypt_example():
    """简化的解密示例"""
    
    # 1. 从接口获取的密钥和IV（GET请求）
    aes_key = "ydsecret://query/key/B*RGygVywfNBwpmBaZg*WT7SIOUP2T0C9WHMZN39j^DAdaZhAnxvGcCY6VYFwnHl"
    aes_iv = "ydsecret://query/iv/C@lZe2YzHtZ2CYgaXKSVfsb7Y4QWHjITPPZ0nQp87fBeJ!Iv6v^6fvi2WN@bYpJ4"
    
    # 2. 从接口获取的加密字符串（POST请求返回，可能包含空格）
    # 这是您提供的示例字符串
    raw_encrypted_string = "Z21kD9ZK1ke6ugku2ccWuwRmpItPkRr5XcmzOgAKD0GcaHTZL9kyNKkN2aYY6yiOQoPr1Cmi4UyR3RDJZ-qncFYyO8o9GNsb-nV7zu6hfV-IiFRFTU3P3WQpOdmtwzb06D2ze1YKvl6UE-KbPaT5OYroe-c1aFf_QdpPL2rV0kZRuYfLJONFKE9HjW0ajyUPLMR_3FcTy2rlP_R8Ne1mlth7vjZbO30YMZxcDykAlwlJ0DMw9ZAtJM31FxHvLYF8alpxMpLcAa_vIx1Vz7KRhQESEQEMFLDAsv-N0Qi2akMY55lpr77i2_7sxmWWfAd380ApEq5YT6we6VgxqYB8wAh6KvEdn5TVsSQB6VOlHz_BzfE1p4qDSh36Ojmk1wjz"
    
    print("=" * 60)
    print("📋 有道翻译解密示例")
    print("=" * 60)
    
    # 步骤1: 预处理（重要！）
    print("🔧 步骤1: 预处理加密字符串")
    print(f"   原始长度: {len(raw_encrypted_string)}")
    
    # 预处理：去除空格等无用字符
    clean_encrypted_data = preprocess_encrypted_data(raw_encrypted_string)
    print(f"   清理后长度: {len(clean_encrypted_data)}")
    
    # 步骤2: 解密
    print("\n🚀 步骤2: 解密翻译数据")
    result = decrypt_youdao(clean_encrypted_data, aes_key, aes_iv)
    
    # 步骤3: 显示结果
    if result and result.get('success'):
        print("\n✅ 解密成功！")
        print("🔤 翻译结果:")
        print(f"   {result['translation']}")
        
        # 可选：显示原始文本
        src_texts = []
        for sentence_group in result['raw_json']['translateResult']:
            for sentence in sentence_group:
                if 'src' in sentence:
                    src_texts.append(sentence['src'])
        
        print("\n📝 原始文本:")
        print(f"   {''.join(src_texts)}")
        
    else:
        print("❌ 解密失败")
        if result:
            print(f"错误: {result.get('error', '未知错误')}")

def practical_usage_guide():
    """实际使用指南"""
    print("\n" + "=" * 60)
    print("💡 实际使用步骤:")
    print("=" * 60)
    print("""
1. 首先发送GET请求获取密钥:
   response = requests.get('有道翻译密钥接口')
   aes_key = response.json()['aesKey']
   aes_iv = response.json()['aesIv']

2. 发送POST请求获取加密数据:
   response = requests.post('有道翻译接口', data=翻译请求)
   encrypted_data = response.text  # 可能包含空格等字符

3. 预处理加密数据:
   clean_data = preprocess_encrypted_data(encrypted_data)

4. 解密获取翻译结果:
   result = decrypt_youdao(clean_data, aes_key, aes_iv)
   translation = result['translation']
    """)
    
    print("🔄 完整代码示例:")
    print("=" * 60)
    print("""
# 导入必要的模块
from decrypt_python import preprocess_encrypted_data, decrypt_youdao

# 从接口获取的数据
raw_encrypted = "从POST接口返回的加密字符串（可能有空格）"
aes_key = "从GET接口返回的ydsecret://key"
aes_iv = "从GET接口返回的ydsecret://iv"

# 一步到位的解密
clean_data = preprocess_encrypted_data(raw_encrypted)
result = decrypt_youdao(clean_data, aes_key, aes_iv)

if result and result['success']:
    print("翻译结果:", result['translation'])
else:
    print("解密失败")
    """)

if __name__ == "__main__":
    simple_decrypt_example()
    practical_usage_guide() 