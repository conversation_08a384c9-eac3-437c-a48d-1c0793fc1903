import requests
import json
import sqlite3
from datetime import datetime


headers = {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "app-type": "web",
    "devid": "ed72f275-eb0a-418a-be47-91f58cf52649",
    "ok-timestamp": "1748323008225",
    "ok-verify-sign": "uH3HsVLBrVSZpF9J+ExefQNOfdrySY33N33vkLUauTc=",
    "ok-verify-token": "bdc5ec52-0dff-412d-b7d8-bfa561e951e4",
    "priority": "u=1, i",
    "referer": "https://web3.okx.com/zh-hans/token/ethereum/******************************************",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
    "x-cdn": "https://web3.okx.com",
    "x-fptoken": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "x-fptoken-signature": "{P1363}U8bvxZAj9rcqPmtw50UORpoHWzKVYXvkS0DWY+qdxtbCUfqGmSQqN/0u7LBUqbP0ibvs8oTedzfTORlrDZqGcQ==",
    "x-id-group": "2130583159548470007-c-1477",
    "x-locale": "zh_CN",
    "x-request-timestamp": "1748323008211",
    "x-simulated-trading": "undefined",
    "x-site-info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIySIJiOi42bpdWZyJye",
    "x-utc": "8",
    "x-zkdex-env": "0"
}
cookies = {
    "_gcl_gs": "2.1.k1$i1747121212$u3801134",
    "intercom-id-ny9cf50h": "59b1a03b-f342-4a22-9469-ca04541cfc98",
    "intercom-device-id-ny9cf50h": "e3eb8110-ee9e-4eec-8777-a8840b95118e",
    "_ym_uid": "1747121226975031991",
    "_ym_d": "1747121226",
    "devId": "ed72f275-eb0a-418a-be47-91f58cf52649",
    "locale": "zh_CN",
    "ok_prefer_udColor": "0",
    "ok_prefer_udTimeZone": "0",
    "first_ref": "https%3A%2F%2Fwww.okx.com%2F",
    "ok_login_type": "OKX_GLOBAL",
    "fingerprint_id": "286c47e1-4945-45bb-88e9-98ddc1deff0f",
    "ok_prefer_currency": "0%7C1%7Cfalse%7CUSD%7C2%7C%24%7C1%7C1%7C%E7%BE%8E%E5%85%83",
    "_gid": "GA1.2.1941574504.1748224542",
    "_ym_isad": "2",
    "intercom-session-ny9cf50h": "M1htNXU5cUludzJ2emFNYTZlTDZhVVV2ZGVpc212ZkFPdHJpSG55UHhCY3N3czU5MjFwaVFucW9nc0lsL1E0bG8rYVg4NHVDWTBhWTAwc2huVFRhdHJzaUZhc3ZqUDZac0xnVmlqaS8wa009LS16ZTJTRnpZT2szSVhYYkZxWUppN2VRPT0=--b3985f77a36046ee2e019585b1cdcc5105e865d6",
    "ok-exp-time": "1748313065850",
    "tmx_session_id": "dgltcb38im_1748313069266",
    "fp_s": "0",
    "ok_site_info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIySIJiOi42bpdWZyJye",
    "okg.currentMedia": "lg",
    "traceId": "2130583159548470007",
    "_ga": "GA1.1.2028963803.1747121216",
    "_monitor_extras": "{\"deviceId\":\"OWTCklx6Hw5J0KFQPhe8Aq\",\"eventId\":704,\"sequenceNumber\":704}",
    "_ga_G0EKWWQGTZ": "GS2.1.s1748313069$o27$g1$t1748316577$j5$l0$h0",
    "__cf_bm": "90o8ETdvTM_9rZmYNWFDRwYHCQdBqbK4lnqqUOb_AbQ-1748322796-1.0.1.1-aXMVtJrQfbsEXTYrFTbNMSXw54xmBca43ZC8KsO013HKKSonas0Tx0Q1wAP4mk.spjyVzOv75i2wS4mIFnZ6grLHv.Jha1YL7C8.YkQf3A4",
    "ok-ses-id": "YCIJdepYG3SJu+6b7YF7SHpYBFL0scg1sOD/8tnB9gkQfbEJJBLCYiJ5KJx60NDUGZ1kzymHJnQ0Wx2KPmYcFRe/e5ZWal0Hoe5pkPKaolWb/OuDuSatzdig+8Am+GYf"
}

def create_table():
    """创建SQLite数据表"""
    # 连接到SQLite数据库
    conn = sqlite3.connect(r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db")
    cursor = conn.cursor()
    
    # 创建表（如果不存在）
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS okx_holder_14 (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        holderWalletAddress TEXT NOT NULL,
        holdVolume TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    conn.commit()
    conn.close()
    print("数据表已创建")

def fetch_okx_holders():
    """获取OKX持有者数据"""
    url = "https://web3.okx.com/priapi/v1/dx/market/v2/holders/ranking-list"
    params = {
        "chainId": "1",
        "tokenAddress": "******************************************",
        "t": "1748323008211"
    }
    
    try:
        response = requests.get(url, headers=headers, cookies=cookies, params=params)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError:
        print("JSON解析失败，响应内容:")
        print(response.text)
        return None

def insert_data(holders_data):
    """解析并插入数据到SQLite数据库"""
    if not holders_data or 'data' not in holders_data or 'holderRankingList' not in holders_data['data']:
        print("没有找到持有者数据")
        return
    
    holders = holders_data['data']['holderRankingList']
    conn = sqlite3.connect(r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db")
    cursor = conn.cursor()
    
    count = 0
    for holder in holders:
        if 'holderWalletAddress' in holder and 'holdVolume' in holder:
            wallet_address = holder['holderWalletAddress']
            hold_volume = holder['holdVolume']
            
            cursor.execute(
                "INSERT INTO okx_holder_14 (holderWalletAddress, holdVolume) VALUES (?, ?)",
                (wallet_address, hold_volume)
            )
            count += 1
    
    conn.commit()
    conn.close()
    print(f"成功插入 {count} 条数据")

def main():
    """主函数"""
    # 创建表
    create_table()
    
    # 获取持有者数据
    holders_data = fetch_okx_holders()
    
    if holders_data:
        # 插入数据到数据库
        insert_data(holders_data)
        print("数据处理完成")
    else:
        print("获取数据失败")

if __name__ == "__main__":
    main()