'''
python: 3.12.0
time: 25/3/10
author: <PERSON><PERSON><PERSON><PERSON>
'''

import requests
import sqlite3
import random
import time
from loguru import logger

class LIANJIA_SPIDER():

    def __init__(self, city_name, progress_callback=None):

        self.progress_callback = progress_callback
        self.city_pool = {
            "北京市": "110000",
            "北京市东城区": "110101",
            "北京市西城区": "110102",
            "北京市朝阳区": "110105",
            "北京市丰台区": "110106",
            "北京市石景山区": "110107",
            "北京市海淀区": "110108",
            "北京市门头沟区": "110109",
            "天津市": "120000",
            "天津市和平区": "120101",
            "天津市河东区": "120102",
            "天津市河西区": "120103",
            "天津市南开区": "120104",
            "天津市河北区": "120105",
            "天津市红桥区": "120106",
            "河北省石家庄市": "130100",
            "河北省唐山市": "130200",
            "河北省秦皇岛市": "130300",
            "河北省邯郸市": "130400",
            "河北省邢台市": "130500",
            "河北省保定市": "130600",
            "吉林省长春市": "220100",
            "吉林省吉林市": "220200",
            "吉林省四平市": "220300",
            "吉林省辽源市": "220400",
            "吉林省通化市": "220500",
            "吉林省白山市": "220600",
            "吉林省松原市": "220700",
            "上海市": "310000",
            "上海市黄浦区": "310101",
            "上海市徐汇区": "310104",
            "上海市长宁区": "310105",
            "上海市静安区": "310106",
            "上海市普陀区": "310107",
            "上海市虹口区": "310109",
            "江苏省南京市": "320100",
            "江苏省无锡市": "320200",
            "江苏省徐州市": "320300",
            "江苏省常州市": "320400",
            "江苏省苏州市": "320500",
            "江苏省南通市": "320600",
            "江苏省连云港市": "320700",
            "浙江省杭州市": "330100",
            "浙江省宁波市": "330200",
            "浙江省温州市": "330300",
            "浙江省嘉兴市": "330400",
            "浙江省湖州市": "330500",
            "浙江省绍兴市": "330600",
            "浙江省金华市": "330700",
            "安徽省合肥市": "340100",
            "安徽省芜湖市": "340200",
            "安徽省蚌埠市": "340300",
            "安徽省淮南市": "340400",
            "安徽省马鞍山市": "340500",
            "安徽省淮北市": "340600",
            "福建省福州市": "350100",
            "福建省厦门市": "350200",
            "福建省泉州市": "350500",
            "福建省漳州市": "350600",
            "福建省莆田市": "350700",
            "福建省南平市": "350800",
            "江西省南昌市": "360100",
            "江西省九江市": "360400",
            "江西省赣州市": "360700",
            "江西省宜春市": "360900",
            "江西省抚州市": "361000",
            "江西省上饶市": "361100",
            "江西省新余市": "360500",
            "山东省济南市": "370100",
            "山东省青岛市": "370200",
            "山东省淄博市": "370300",
            "山东省枣庄市": "370400",
            "山东省东营市": "370500",
            "山东省烟台市": "370600",
            "河南省郑州市": "410100",
            "河南省洛阳市": "410300",
            "河南省平顶山市": "410400",
            "河南省安阳市": "410500",
            "河南省鹤壁市": "410600",
            "四川省成都市": "510100",
            "四川省兰州市": "620100",
            "山西省太原市": "140100",
            "湖北省武汉市": "420100",
            "湖北省十堰市": "420300",
            "湖北省黄冈市": "421100",
            "湖南省长沙市": "430100",
            "湖南省株洲市": "430200",
            "湖南省湘潭市": "430300",
            "湖南省衡阳市": "430400",
            "湖南省邵阳市": "430500",
            "湖南省岳阳市": "430600",
            "湖南省常德市": "430700",
            "广东省广州市": "440100",
            "广东省深圳市": "440300",
            "广东省珠海市": "440400",
            "广东省汕头市": "440500",
            "广东省佛山市": "440600",
            "重庆市": "500000",
            "重庆市涪陵区": "500102",
            "重庆市渝中区": "500103",
            "重庆市大渡口区": "500104",
            "重庆市江北区": "500105",
            "重庆市沙坪坝区": "500106",
            "重庆市九龙坡区": "500107",
            "重庆市南岸区": "500108",
            "四川省成都市": "510100",
            "四川省乐山市": "511100",
            "四川省绵阳市": "510700",
            "四川省广元市": "510800",
            "四川省宜宾市": "512500",
            "甘肃省兰州市": "620100",
            "甘肃省嘉峪关市": "620200",
            "甘肃省金昌市": "620300",
            "甘肃省白银市": "620400",
            "甘肃省天水市": "620500",
            "甘肃省武威市": "620600",
            "甘肃省张掖市": "620700",
            "甘肃省酒泉市": "620900",
            "陕西省西安市": "610100",
            "陕西省宝鸡市": "610300",
            "陕西省咸阳市": "610400",
            "陕西省渭南市": "610500",
            "陕西省延安市": "610600",
            "陕西省榆林市": "610700"
        }

        self.url = "https://m.lianjia.com/liverpool/api/ershoufang/getList"
        self.headers = {
            "Referer": "https://m.lianjia.com/cd/ershoufang/pg4",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        }
        city_id = self.city_pool.get(city_name, "110000")
        self.params = {
            "cityId": city_id,
            "curPage": "1"
        }
        self.db_path = "C:\\Users\\<USER>\\AppData\\Roaming\\DBeaverData\\workspace6\\.metadata\\sample-database-sqlite-1\\Chinook.db"
        self.conn = sqlite3.connect(self.db_path)
        # self.create_table()


    # def create_table(self):
    #     cursor = self.conn.cursor()
    #     cursor.execute('''
    #     CREATE TABLE IF NOT EXISTS LIANJIA (
    #                 cityId TEXT,
    #                 title TEXT,
    #                 description TEXT,
    #                 unitPrice TEXT,
    #                 totalPrice TEXT,
    #                 jumpUrl TEXT
    #         )
    #     ''')
    #     self.conn.commit()


    def fetch_data(self, page):
        try:
            self.params["curPage"] = str(page)
            response = requests.get(self.url, headers=self.headers, params=self.params)
            # print(response.text)
            return response.json()
        except Exception as e:
            logger.error(f'获取页面数据时出错, 页面:{page}: {e}')
            return None


    def parse_data(self, data):
        try:
            listings = data['data']['data']['getErShouFangList']['list']
            if listings:
                self.insert_data(listings)
            else:
                logger.warning("获取到'list', 但为空.")
        except KeyError as e:
            logger.warning(f"数据解析中的关键错误: {e}")
        except Exception as e:
            logger.error(f"数据分析中出现意外错误: {e}")
        


    def insert_data(self, listings):
        cursor = self.conn.cursor()
        for listing in listings:
            cursor.execute('''
                INSERT INTO LIANJIA (cityId, title, description, unitPrice, totalPrice, jumpUrl)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                listing['cityId'],
                listing['title'],
                listing['desc'],
                listing['unitPrice'],
                listing['totalPrice'],
                listing['jumpUrl']
            ))
        self.conn.commit()


    def run(self, total_pages=5):
        logger.info("开始获取链家平台数据...")
        cursor = self.conn.cursor()
        cursor.execute('DELETE FROM LIANJIA')
        
        try:
            for curPage in range(1, total_pages + 1):
                data = self.fetch_data(curPage)
                if data:
                    self.parse_data(data)
                    progress = (curPage / total_pages) * 100
                    if self.progress_callback:
                        self.progress_callback({
                            'progress': progress,
                            'current_page': curPage,
                            'total_pages': total_pages,
                            'status': f'正在爬取第{curPage}页数据，并进行数据分析中...'
                        })
                    logger.info(f'第{curPage}页数据获取完成...')
                    time.sleep(random.uniform(1, 3))
                logger.info(f'等待获取下一页数据...')
            
            if self.progress_callback:
                self.progress_callback({
                    'progress': 100,
                    'status': '数据爬取与分析完成！'
                })
            logger.success("链家数据已获取完成.")
            
            # # 爬取完成后验证数据
            # conn = sqlite3.connect("lianjia.db")
            # cursor = conn.cursor()
            # cursor.execute("SELECT COUNT(*) FROM LIANJIA")
            # count = cursor.fetchone()[0]
            
            # if count == 0:
            #     logger.error("爬取完成但没有保存任何数据")
            # else:
            #     logger.info(f"成功爬取并保存 {count} 条数据")
            
            # conn.close()
            
        except Exception as e:
            logger.error(f"爬虫运行错误: {str(e)}")
            if self.progress_callback:
                self.progress_callback({
                    'progress': 0,
                    'status': f'爬取出错: {str(e)}'
                })

    # def __del__(self):
    #     self.conn.close()


if __name__ == '__main__':
    city_name = input("请输入想查询的城市名称，(例如'杭州市','北京市') :")
    spider = LIANJIA_SPIDER(city_name)
    spider.run()