from curl_cffi import requests


headers = {
    "authority": "gmgn.ai",
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "referer": "https://gmgn.ai/eth/token/******************************************",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-arch": "\"x86\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"114.0.5735.199\"",
    "sec-ch-ua-full-version-list": "\"Not.A/Brand\";v=\"8.0.0.0\", \"Chromium\";v=\"114.0.5735.199\", \"Google Chrome\";v=\"114.0.5735.199\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-ch-ua-platform-version": "\"15.0.0\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}
cookies = {
    "_ga": "GA1.1.566489715.1737427304",
    "__cf_bm": "I5w66vhgOYMAAzdQep1JLJpp.r6k_0uwFWf.xXlYzu8-1742278074-1.0.1.1-1n3eJxXw4x89LqoHVXwfgU6wgUI_kbFWbnnVo23RqPKCbl2Xj7RSke1BLbZjytrVbyprvhEGpX1RcLNzQkt_H3P_dAvI.fhxsyK0aERj06E",
    "cf_clearance": "ymJzcKeBO1oEjiFUn6xkWPOLvNkZUvoNg32ewDGa87E-1742278728-1.2.1.1-pwPpOCCoTnO4aql8XMeEA.iI_Q280S7W8FIWaHljGgQRhM1pG9qNiYb28HsxteL3nduU6p6sJTJu81fF38tDh8.uYCtDE_bugVPc2wjAUSCK_CEgKDlAJeV6r6mGzA_BxZFGCZ_qMoJZdi2k6btD4aZUQtAwdTJGNIo3xBTMwrDFdEApR0MI36AOQlr3uW9DqrinLf.uHRybj7Hg1vag3yUgGO62GPjSn3S50U0Zfr4sl1r_GOmZdR.EdD.rZmIjMtzPq_yxQ__6bAa1ic2copGjhyiLWpryWXFYi0woOZ1GP0r4O1pmL7zt_v4flIvJk6IEXByZrrlWTMEPzSDYaL8aEndDc_86PpGkGHNwyaES9qQKaJuWlemLBvwtHbQH0Ob6Zxoc6v9aPpwG5_s4xabkn9MMWhdB16mrVessjMg",
    "_ga_0XM0LYXGC8": "GS1.1.1742274357.6.1.1742278740.0.0.0"
}
url = "https://gmgn.ai/api/v1/token_candles/eth/******************************************"

params = {
    "device_id": "1713e771-6640-405d-8c71-fe483feeb742",
    "client_id": "gmgn_web_2025.0317.175759",
    "from_app": "gmgn",
    "app_ver": "2025.0317.175759",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "resolution": "1m",
    "from": "0",
    "to": "1741121340000",
    "limit": "4"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params)

print(response.text)
print(response)