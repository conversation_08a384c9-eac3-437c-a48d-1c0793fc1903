import requests
import json
import time


API_KEY = "618c9cd2b94acdf11ee3db7aaf46f93141e080bf56034"

proxy_eth = {
    'http': "socks5://**************:30889",
    'https': "socks5://**************:30889"
}

TARGET_URL = "https://optimistic.etherscan.io/"


def create_task():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": API_KEY,
        "task": {
            "type": "CloudFlareTaskS2",
            "websiteURL": TARGET_URL,
            "proxy": proxy_eth['http'],
            "waitLoad": True,
            "requiredCookies": ["__cflb", "__stripe_sid", "cf_clearance", "_ga_XPR6BMZXSN", "ASP.NET_SessionId",
                                "_gat_gtag_UA_46998878_6", "etherscan_autologin", "_ga", "_ga_T1JC9RNQXV"]
        }
    }
    response = requests.post(url, headers=headers, json=data)
    return response.json()


def get_task_result(task_id):
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": API_KEY,
        "taskId": task_id
    }

    while True:
        response = requests.post(url, headers=headers, json=data)
        result = response.json()
        if result.get('status') == 'ready':
            return result
        time.sleep(5)

def test_request_with_cookies(cookies):
    session = requests.Session()
    for cookie in cookies:
        session.cookies.set(cookie['name'], cookie['value'])
    response = session.get(TARGET_URL, proxies=proxy_eth)
    return response

def main():
    task_response = create_task()
    if task_response.get('errorId') == 0:
        task_id = task_response.get('taskId')
        print(f"Task created successfully. Task ID: {task_id}")
        result = get_task_result(task_id)
        if result.get('errorId') == 0:
            cookies = result.get('solution', {}).get('cookies', {})
            print("Cookies retrieved successfully:")
            response = test_request_with_cookies(cookies)
            print(response.text)
            print(response)
        else:
            print(f"Error retrieving task result: {result.get('errorDescription')}")
    else:
        print(f"Error creating task: {task_response.get('errorDescription')}")

if __name__ == "__main__":
    main()