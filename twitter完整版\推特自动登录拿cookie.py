# from DrissionPage import Chromium
# import redis
# import time

# # 配置Redis
# redis_client = redis.Redis(
#     host='**************',
#     port=6379,
#     db=14,
#     password='123456',
#     decode_responses=True
# )

# # 初始化浏览器
# browser = Chromium()
# tab = browser.latest_tab

# # 打开推特登录页面
# tab.get('https://x.com/i/flow/login')
# time.sleep(3)

# # 输入用户名
# username_input = tab.ele('xpath://input[@autocomplete="username"]')
# username_input.input('AmeeMenter68583')

# # 点击下一步
# next_button = tab.ele('xpath://div/button[2]/div/span/span')
# next_button.click()
# time.sleep(2)

# # 输入密码
# password_input = tab.ele('xpath://input[@name="password"]')
# password_input.input('v9Taq2e6G0Ua')

# # 点击登录
# login_button = tab.ele('xpath://div/div/div[2]/div[2]/div/div/div[2]/div[2]/div[2]/div/div[1]/div/div/button')
# login_button.click()
# time.sleep(3)

# # 新建窗口获取2FA验证码
# new_tab = browser.new_tab('https://2fa.run/')
# time.sleep(2)

# # 点击输入框输入2FA密钥
# FA_code_input = tab.ele('xpath://*[@id="secret-input-js"]')
# FA_code_input.input('2DJPK6IUAH62F4JK')

# # 点击获取验证码按钮
# get_code_button = new_tab.ele('xpath://*[@id="btn-js"]')
# get_code_button.click()
# time.sleep(1)

# # 获取验证码
# verification_code = new_tab.ele('xpath://*[@id="code_js"]').text

# # 切换回推特页面
# tab.activate()

# # 输入验证码
# code_input = tab.ele('xpath://input[@name="text"]')
# code_input.input(verification_code)

# # 点击登录
# final_login_button = tab.ele('xpath://div/div[2]/div[2]/div/div/div[2]/div[2]/div[2]/div/div/div/button/div')
# final_login_button.click()
# time.sleep(3)

# # 获取cookies并保存到Redis
# cookies = tab.cookies
# redis_client.hmset('twitter_cookies', cookies)


# time.sleep(3)
# # 关闭浏览器
# browser.quit()


from DrissionPage import Chromium
import redis
import time

class TwitterAutoLogin:
    def __init__(self):
        # 配置Redis
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            db=14,
            password='123456',
            decode_responses=True
        )
        # 初始化浏览器
        self.browser = Chromium()
        self.tab = self.browser.latest_tab

    def login_twitter(self):
        
        self.tab.get('https://x.com/i/flow/login')
        time.sleep(3)

        username_input = self.tab.ele('xpath://input[@autocomplete="username"]')
        username_input.input('benneth91131')

        next_button = self.tab.ele('xpath://div/button[2]/div/span/span')
        next_button.click()
        time.sleep(2)

        password_input = self.tab.ele('xpath://input[@name="password"]')
        password_input.input('vzsh6RNS2xFc')

        login_button = self.tab.ele('xpath://div/div/div[2]/div[2]/div/div/div[2]/div[2]/div[2]/div/div[1]/div/div/button')
        login_button.click()
        time.sleep(3)

        # 获取2FA验证码
        verification_code = self.get_2fa_code()

        code_input = self.tab.ele('xpath://input[@name="text"]')
        code_input.input(verification_code)

        final_login_button = self.tab.ele('xpath://div/div[2]/div[2]/div/div/div[2]/div[2]/div[2]/div/div/div/button/div')
        final_login_button.click()
        time.sleep(3)

        # 获取cookies并保存到Redis
        cookies = self.tab.cookies()
        cookies_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        print(cookies_dict)
        if self.redis_client.exists('twitter_cookies'):
            self.redis_client.delete('twitter_cookies')
        self.redis_client.hset('twitter_cookies', mapping=cookies_dict)

        # 关闭浏览器
        self.browser.quit()

    def get_2fa_code(self):
        # 新建窗口获取2FA验证码
        new_tab = self.browser.new_tab('https://2fa.run/')
        time.sleep(2)

        # 输入2FA密钥
        FA_code_input = new_tab.ele('xpath://*[@id="secret-input-js"]')
        FA_code_input.clear()
        time.sleep(1)
        FA_code_input.input('OO7GGPCOAGZ6JPQT')

        # 使用JavaScript获取验证码
        js_code = """
        document.getElementById('btn-js').click();
        return document.getElementById('code_js').innerText;
        """
        verification_code = new_tab.run_js(js_code)

        # 关闭2FA页面
        new_tab.close()

        return verification_code


if __name__ == "__main__":
    twitter_login = TwitterAutoLogin()
    twitter_login.login_twitter()


"""
<EMAIL>----plaNiybxP----d6ajqz2xxkqi
<EMAIL>----prXfgk9M0----wuwuijrgje8t
<EMAIL>----BBLCSFccN----3bc2j3ra3fyp
<EMAIL>----XBjXPzNWI----7hxttk9kdzdx
<EMAIL>----Elb5Vo07HO----avhjn4kribse
<EMAIL>----gEeBTO7hB----aydwbj4rnc29
<EMAIL>----m39XiC6g2----3yu7zgnxnrfb
<EMAIL>----Fq6u18Xph----4ggq75cp5jrn
<EMAIL>----gCxkvFK7xK----4dvtzhf7z4ng
<EMAIL>----Y2dRGYfeD----qmubsekwq8d4
"""

'''
benneth91131----vzsh6RNS2xFc----OO7GGPCOAGZ6JPQT
BrasUlysse19234----jF54LqQ1IMy----LEYQCJ43PNNI5GGG
SaddatP16063----ClX4iIAwAeS----XLM7BKZGE3TBRBPW
AmeeMenter68583----v9Taq2e6G0Ua----2DJPK6IUAH62F4JK
MMolino14333----ZxT1i4c7MGb----FGNE7S6MRG6CIQYM
NGrossen89627----nNUPgoP2YcD----AYCZFN5XJ2IREJSW
CourieT7623----eYrsLD8jGtD----QOWKQIKAZ5KTKAWA
swim_logan47535----DCKKuzgCUbB----AY7JJZC3NH3EVWP5
LeslieTeff90534----aSzUU73Ppy7A----BMDJWNZNNWCKY6Z2
NimickSoon55558----LwyBAvPXLXol----W5VV3I4AXW5B6SIA
'''