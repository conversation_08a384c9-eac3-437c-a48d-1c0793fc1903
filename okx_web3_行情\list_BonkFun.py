"""头顶上的BonkFun"""
import requests

proxies = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

headers = {
    "Host": "okxweb.qsswl.com",
    "Accept": "*/*",
    "x-utc": "+08:00",
    "Referer": "https://okxweb.qsswl.com/priapi/v1/dx/market/v2/memefun/custom/detail/page",
    # "fingerprint-id": "57E28747-EEAD-4D63-A80D-D19D16842ED4",
    "User-Agent": "OKEx/6.119.0 (iPhone;U;iOS 18.3;zh-CN/zh-CN) locale=zh-CN",
    "x-cdn": "https://static.coinall.ltd",
    # "x-site-info": "9JCTBJ0TMd0XYt0TiojIlR2bjJCL0EjOikHdpRnblJye",
    # "OK-VERIFY-SIGN": "+U3m1uq4h+DcYwRxZCDQFLafiv3iE0ChwEpkxseKoxs=",
    "x-simulated-trading": "0",
    # "BuildVersion": "20250511006001",
    "app_web_mode": "web3",
    "Subdomain-Strategy": "2",
    # "risk-params": "fingerprint-id=57E28747-EEAD-4D63-A80D-D19D16842ED4&session-id=57E28747-EEAD-4D63-A80D-D19D16842ED4_txn_start_1752114125229&fp-status=3",
    # "OK-VERIFY-TOKEN": "3885a5cb9d59e049ad70a65a32883125",
    "BundleId": "com.okex.OKExAppstoreFull",
    "platform": "iOS",
    "real-app-version": "6.119.0",
    "Accept-Language": "zh-CN",
    # "x-id": "4b6fdd888bf0460966b35e5f12ee15fb",
    # "devid": "57E28747-EEAD-4D63-A80D-D19D16842ED4",
    # "lua-version": "6.123.1"
}
url = "https://**************/priapi/v1/dx/market/v2/memefun/custom/detail/page"

#BonkFun
params_BonkFun = {
    "chainId": "-100",
    "moduleType": "21",
    "timeType": "4"
}

#"Solana 生态"
params_sol = {
    "chainId": "-100",
    "moduleType": "4",
    "timeType": "4"
}

#"Startup"
params_Startup = {
    "chainId": "-100",
    "moduleType": "17",
    "timeType": "4"
}

#"Jup Studio"
params_Jup = {
    "chainId": "-100",
    "moduleType": "23",
    "timeType": "4"
}

#"Moonshot"
params_Moonshot = {
    "chainId": "-100",
    "moduleType": "16",
    "timeType": "4"
}

#"Virtuals 生态"
params_Virtuals = {
    "chainId": "-100",
    "moduleType": "11",
    "timeType": "4"
}

#"BSC"
params_bsc = {
    "chainId": "-100",
    "moduleType": "18",
    "timeType": "4"
}

#"BASE"
params_BASE = {
    "chainId": "-100",
    "moduleType": "10",
    "timeType": "4"
}

#"动物园"
params_zoo = {
    "chainId": "-100",
    "moduleType": "9",
    "timeType": "4"
}

#"AI"
params_AI = {
    "chainId": "-100",
    "moduleType": "8",
    "timeType": "4"
}

response = requests.get(url, headers=headers, params=params_bsc, proxies=proxies, verify=False)

print(response.text)
print(response)