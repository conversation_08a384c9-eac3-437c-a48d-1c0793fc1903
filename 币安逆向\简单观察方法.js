// 🎯 最简单的观察方法
console.clear();

const token = "df1e1699-e033-435e-bb57-a3d4f30365a9:AAABmBJUG/g=:QyGQyOTyeTyez2czGYxGI5HIZDKZTKbTaTQaDQYDAQBDIZDI5PJ5PJ7PZzMZjEYjkchkMplMptNpNBoNBgMBAEMhkMjk8nk8ns9nMxmMRiORyGQymUym02k0Gg0GAwEAQyGQyOTyeTyez2czGYxGI5HIZDKZTKbTaTQaDQYDAQA=";

console.log("🎯 Token观察方法启动");
console.log("📝 当前Token长度:", token.length);

// 简单存储token到各种位置
try {
    localStorage.setItem('aws-waf-token', token);
    sessionStorage.setItem('aws-waf-token', token);
    console.log("✅ Token已存储到localStorage和sessionStorage");
} catch (e) {
    console.log("❌ 存储失败:", e.message);
}

// 监控网络请求（简化版）
const originalFetch = window.fetch;
window.fetch = function(url, options = {}) {
    console.log("🌐 检测到fetch请求:", url);
    
    // 如果是API请求，自动添加我们的token
    if (url.includes('api.binance.com') || url.includes('binance')) {
        console.log("🎯 币安API请求，尝试添加token");
        if (!options.headers) options.headers = {};
        options.headers['x-aws-waf-token'] = token;
        console.log("✅ 已添加x-aws-waf-token头部");
    }
    
    return originalFetch.call(this, url, options).then(response => {
        // 检查响应头
        const setCookie = response.headers.get('Set-Cookie');
        if (setCookie && setCookie.includes('aws-waf-token')) {
            console.log("🎉 检测到Set-Cookie中的aws-waf-token!");
            console.log("Cookie:", setCookie);
        }
        return response;
    });
};

console.log("🕵️ 网络监控已启用");
console.log("\n💡 现在请执行以下操作:");
console.log("1. 打开F12 > Network面板");
console.log("2. 点击页面上的任何按钮（登录、交易、市场等）");
console.log("3. 观察Network面板中的请求是否包含x-aws-waf-token");
console.log("4. 检查响应头是否有Set-Cookie: aws-waf-token");

// 5秒后检查cookies
setTimeout(() => {
    console.log("\n🍪 5秒后检查 - 当前cookies:");
    console.log(document.cookie);
    
    // 检查是否有aws-waf-token出现
    if (document.cookie.includes('aws-waf-token')) {
        console.log("🎉 发现aws-waf-token cookie！");
    } else {
        console.log("❌ 未发现aws-waf-token cookie");
    }
}, 5000); 