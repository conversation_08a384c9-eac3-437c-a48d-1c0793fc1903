import requests
import time

# client_key = '618c9cd2b94acdf11ee3db7aaf46f93141e080bf56034'
def create_task(client_key, website_url, proxy):
    url = "https://api.yescaptcha.com/createTask"
    headers = {"Content-Type": "application/json"}
    data = {
        "clientKey": client_key,
        "task": {
            "type": "CloudFlareTaskS2",
            "websiteURL": website_url,
            "proxy": proxy,
            "waitLoad": False,
            "requiredCookies": ["__stripe_mid", "op mainnet etherscan_switch_token_amount_value", "_gid", "ASP.NET_SessionId", "__cflb",
                                "_ga_T1JC9RNQXV", "_ga", "cf_clearance", "_ga_XPR6BMZXSN"]
        }
    }
    response = requests.post(url, json=data, headers=headers)
    return response.json()

def get_task_result(client_key, task_id):
    
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {"Content-Type": "application/json"}
    data = {"clientKey": client_key, "taskId": task_id}
    while True:
        response = requests.post(url, json=data, headers=headers)
        result = response.json()
        if result['status'] == 'ready':
            return result
        time.sleep(3)

def main():
    client_key = '618c9cd2b94acdf11ee3db7aaf46f93141e080bf56034'
    website_url = 'https://optimistic.etherscan.io/labelcloud'
    proxy = {
        'http': "socks5://**************:30889",
        'https': "socks5://**************:30889"
        }

    print("正在创建任务...")
    task_response = create_task(client_key, website_url, proxy)
    task_id = task_response.get('taskId')

    if task_id:
        print(f"任务已创建，任务ID: {task_id}")
        print("正在获取任务结果...")
        result = get_task_result(client_key, task_id)
        print("任务完成。")
        print("UA:", result.get('solution', {}).get('userAgent'))
        print("Cookies:", result.get('solution', {}).get('cookies'))
    else:
        print("任务创建失败:", task_response)

if __name__ == "__main__":
    main()