// 🎯 直接验证方法 - 基于FedCM错误线索

const token = "df1e1699-e033-435e-bb57-a3d4f30365a9:AAABmBJUG/g=:QyGQyOTyeTyez2czGYxGI5HIZDKZTKbTaTQaDQYDAQBDIZDI5PJ5PJ7PZzMZjEYjkchkMplMptNpNBoNBgMBAEMhkMjk8nk8ns9nMxmMRiORyGQymUym02k0Gg0GAwEAQyGQyOTyeTyez2czGYxGI5HIZDKZTKbTaTQaDQYDAQA=";

console.clear();
console.log("🎯 基于FedCM线索的直接验证方法");

// 方法1: 直接设置cookie尝试
function tryDirectCookieSet() {
    console.log("\n🍪 方法1: 尝试直接设置aws-waf-token cookie");
    
    try {
        // 尝试直接设置cookie
        document.cookie = `aws-waf-token=${token}; domain=.binance.com; path=/; secure; samesite=none`;
        console.log("✅ Cookie设置尝试完成");
        
        // 检查是否设置成功
        const cookies = document.cookie;
        if (cookies.includes('aws-waf-token')) {
            console.log("🎉 Cookie设置成功！");
            console.log("当前cookies:", cookies);
        } else {
            console.log("❌ Cookie设置失败（可能需要服务器设置）");
        }
    } catch (error) {
        console.log("❌ Cookie设置错误:", error.message);
    }
}

// 方法2: 分析页面现有的token验证机制
function analyzeExistingMechanism() {
    console.log("\n🔍 方法2: 分析页面现有验证机制");
    
    // 查找页面中与token相关的函数
    const scripts = document.querySelectorAll('script');
    console.log(`📄 找到 ${scripts.length} 个script标签`);
    
    let tokenRelatedCode = [];
    scripts.forEach((script, index) => {
        if (script.innerHTML.includes('waf') || 
            script.innerHTML.includes('token') || 
            script.innerHTML.includes('aws')) {
            tokenRelatedCode.push({
                index,
                preview: script.innerHTML.substring(0, 200) + '...'
            });
        }
    });
    
    if (tokenRelatedCode.length > 0) {
        console.log("🎯 找到可能相关的代码片段:");
        tokenRelatedCode.forEach(code => {
            console.log(`Script ${code.index}:`, code.preview);
        });
    } else {
        console.log("❌ 未找到明显的token相关代码");
    }
    
    // 检查全局变量
    const globalVars = [];
    for (let key in window) {
        if (key.toLowerCase().includes('token') || 
            key.toLowerCase().includes('waf') || 
            key.toLowerCase().includes('aws')) {
            globalVars.push(key);
        }
    }
    
    if (globalVars.length > 0) {
        console.log("🌐 找到可能相关的全局变量:", globalVars);
    }
}

// 方法3: 触发币安本身的验证流程
function triggerBinanceValidation() {
    console.log("\n🚀 方法3: 触发币安本身的验证流程");
    
    // 尝试访问一个可能触发验证的页面元素
    const buttons = document.querySelectorAll('button, a');
    console.log(`🔘 找到 ${buttons.length} 个可点击元素`);
    
    // 查找可能触发API调用的按钮
    const apiButtons = Array.from(buttons).filter(btn => {
        const text = btn.textContent.toLowerCase();
        return text.includes('login') || 
               text.includes('sign') || 
               text.includes('trade') || 
               text.includes('market');
    });
    
    if (apiButtons.length > 0) {
        console.log("🎯 找到可能触发API的按钮:");
        apiButtons.slice(0, 5).forEach((btn, index) => {
            console.log(`${index + 1}. "${btn.textContent.trim()}"`);
        });
        
        console.log("💡 建议：点击这些按钮观察Network面板的请求");
    }
}

// 方法4: 使用我们的token进行本地localStorage设置
function useLocalStorage() {
    console.log("\n💾 方法4: 使用localStorage存储token");
    
    try {
        // 尝试存储到localStorage
        localStorage.setItem('aws-waf-token', token);
        localStorage.setItem('x-aws-waf-token', token);
        
        console.log("✅ Token已存储到localStorage");
        console.log("aws-waf-token:", localStorage.getItem('aws-waf-token')?.substring(0, 50) + '...');
        
        // 检查sessionStorage
        sessionStorage.setItem('aws-waf-token', token);
        console.log("✅ Token已存储到sessionStorage");
        
    } catch (error) {
        console.log("❌ localStorage存储失败:", error.message);
    }
}

// 方法5: 模拟正常的用户行为来触发验证
function simulateUserBehavior() {
    console.log("\n🎭 方法5: 模拟用户行为触发验证");
    
    // 触发一些常见的用户事件
    const events = ['mousemove', 'click', 'scroll', 'keydown'];
    
    events.forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        document.dispatchEvent(event);
        console.log(`✅ 触发 ${eventType} 事件`);
    });
    
    // 等待一下然后检查是否有新的网络请求
    setTimeout(() => {
        console.log("⏱️ 等待完成，检查是否有新的token相关活动");
        console.log("当前cookies:", document.cookie);
    }, 2000);
}

// 执行所有方法
async function executeAll() {
    console.log("🚀 开始执行所有验证方法...\n");
    
    tryDirectCookieSet();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    analyzeExistingMechanism();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    triggerBinanceValidation();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    useLocalStorage();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    simulateUserBehavior();
    
    console.log("\n🏁 所有方法执行完成");
    console.log("\n💡 观察要点:");
    console.log("1. 检查Network面板是否有新的请求");
    console.log("2. 查看Application>Cookies面板");
    console.log("3. 检查Console是否有新的FedCM相关消息");
    console.log("4. 尝试手动点击页面上的任何按钮观察网络活动");
}

executeAll(); 