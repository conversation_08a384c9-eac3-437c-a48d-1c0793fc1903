// 改进版AWS WAF Token生成器 - 避免重复token
console.log("🚀 启动单一Token生成器...");

class SingleTokenGenerator {
    constructor() {
        this.isGenerating = false;
        this.currentToken = null;
        this.init();
    }
    
    // 初始化
    init() {
        console.log("📋 检查当前token状态...");
        this.analyzeCurrentTokens();
        this.setupTokenMonitoring();
    }
    
    // 分析当前token状态
    analyzeCurrentTokens() {
        const cookies = document.cookie.split(';');
        const awsTokens = cookies.filter(cookie => cookie.trim().startsWith('aws-waf-token'));
        
        console.log(`发现 ${awsTokens.length} 个aws-waf-token:`);
        awsTokens.forEach((token, index) => {
            console.log(`Token ${index + 1}:`, token.trim());
        });
        
        if (awsTokens.length > 1) {
            console.warn("⚠️ 检测到重复token，建议清理");
        }
    }
    
    // 完全清除所有aws-waf-token
    clearAllTokens() {
        console.log("🧹 清除所有现有token...");
        
        // 清除所有可能的路径和域
        const clearCookies = [
            "aws-waf-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;",
            "aws-waf-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.binance.com;",
            "aws-waf-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=binance.com;",
            "aws-waf-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.binance.com; secure;",
            "aws-waf-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=none;"
        ];
        
        clearCookies.forEach(cookie => {
            document.cookie = cookie;
        });
        
        // 清除localStorage和sessionStorage
        localStorage.removeItem('aws-waf-token');
        sessionStorage.removeItem('aws-waf-token');
        
        console.log("✅ Token清理完成");
    }
    
    // 生成设备指纹
    generateDeviceFingerprint() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        
        return {
            screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language,
            platform: navigator.platform,
            userAgent: navigator.userAgent,
            canvas: canvas.toDataURL(),
            memory: navigator.deviceMemory || 'unknown',
            cores: navigator.hardwareConcurrency || 'unknown'
        };
    }
    
    // SHA-256 哈希函数
    async sha256(message) {
        const msgBuffer = new TextEncoder().encode(message);
        const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }
    
    // 生成随机UUID
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    
    // 生成时间戳
    generateTimestamp() {
        const now = Date.now();
        const buffer = new ArrayBuffer(8);
        const view = new DataView(buffer);
        view.setFloat64(0, now, false);
        return btoa(String.fromCharCode(...new Uint8Array(buffer)));
    }
    
    // 工作量证明算法
    async performProofOfWork(input, difficulty = 8) {
        console.log("⛏️ 开始工作量证明计算...");
        const startTime = Date.now();
        let nonce = 0;
        const target = '0'.repeat(Math.floor(difficulty / 4));
        
        while (true) {
            const candidate = input + nonce.toString();
            const hash = await this.sha256(candidate);
            
            if (hash.startsWith(target)) {
                const duration = Date.now() - startTime;
                console.log(`✅ 找到解决方案! Nonce: ${nonce}, 耗时: ${duration}ms`);
                console.log(`Hash: ${hash}`);
                return { nonce, hash, duration };
            }
            
            nonce++;
            
            if (nonce % 1000 === 0) {
                console.log(`尝试中... Nonce: ${nonce}`);
            }
        }
    }
    
    // 生成完整token
    async generateToken() {
        if (this.isGenerating) {
            console.log("⏳ Token生成中，请稍候...");
            return null;
        }
        
        this.isGenerating = true;
        console.log("🔄 开始生成新的AWS WAF Token...");
        
        try {
            // 1. 清除所有现有token
            this.clearAllTokens();
            
            // 等待清除生效
            await new Promise(resolve => setTimeout(resolve, 200));
            
            // 2. 生成基础组件
            const uuid = this.generateUUID();
            const timestamp = this.generateTimestamp();
            const deviceFingerprint = this.generateDeviceFingerprint();
            
            console.log("📊 设备指纹:", deviceFingerprint);
            
            // 3. 创建输入字符串
            const algorithmId = "h7b0c470f0cfe3a80a9e26526ad185f484f6817d0832712a4a67f";
            const input = `${uuid}:${timestamp}:${algorithmId}:${JSON.stringify(deviceFingerprint)}`;
            
            // 4. 执行工作量证明
            const proof = await this.performProofOfWork(input, 8);
            
            // 5. 生成签名
            const signatureData = `${input}:${proof.nonce}`;
            const signature = await this.sha256(signatureData);
            const signatureBase64 = btoa(signature);
            
            // 6. 构造最终token
            const finalToken = `${uuid}:${timestamp}:${signatureBase64}`;
            
            console.log("✅ Token生成成功:", finalToken);
            
            // 7. 设置单一token
            this.setSingleToken(finalToken);
            
            this.currentToken = finalToken;
            return finalToken;
            
        } catch (error) {
            console.error("❌ Token生成失败:", error);
            return null;
        } finally {
            this.isGenerating = false;
        }
    }
    
    // 设置单一token
    setSingleToken(token) {
        console.log("📝 设置单一token...");
        
        // 再次确保清理
        this.clearAllTokens();
        
        // 等待一下再设置
        setTimeout(() => {
            // 设置新token
            document.cookie = `aws-waf-token=${token}; path=/; domain=.binance.com; secure; samesite=none; max-age=3600`;
            
            // 验证设置结果
            setTimeout(() => {
                const cookies = document.cookie.split(';');
                const awsTokens = cookies.filter(cookie => cookie.trim().startsWith('aws-waf-token'));
                
                if (awsTokens.length === 1) {
                    console.log("✅ 单一token设置成功");
                } else {
                    console.warn(`⚠️ 检测到 ${awsTokens.length} 个token，可能有问题`);
                }
            }, 100);
        }, 300);
    }
    
    // 监控token变化
    setupTokenMonitoring() {
        console.log("👀 设置token监控...");
        
        const originalCookie = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie') || 
                              Object.getOwnPropertyDescriptor(HTMLDocument.prototype, 'cookie');
        
        Object.defineProperty(document, 'cookie', {
            get() {
                return originalCookie.get.call(document);
            },
            set(val) {
                if (val.includes('aws-waf-token')) {
                    console.log("🔔 检测到aws-waf-token设置:", val);
                    
                    // 检查是否是我们设置的
                    if (!val.includes(this.currentToken)) {
                        console.warn("⚠️ 检测到外部token设置，可能产生冲突");
                    }
                }
                return originalCookie.set.call(document, val);
            }
        });
    }
    
    // 获取当前token状态
    getCurrentStatus() {
        const cookies = document.cookie.split(';');
        const awsTokens = cookies.filter(cookie => cookie.trim().startsWith('aws-waf-token'));
        
        return {
            tokenCount: awsTokens.length,
            tokens: awsTokens,
            currentToken: this.currentToken,
            isGenerating: this.isGenerating
        };
    }
}

// 创建全局实例
const tokenGenerator = new SingleTokenGenerator();

// 导出到window
window.tokenGenerator = tokenGenerator;
window.generateNewToken = () => tokenGenerator.generateToken();
window.clearAllTokens = () => tokenGenerator.clearAllTokens();
window.getTokenStatus = () => tokenGenerator.getCurrentStatus();

console.log("🛠️ 可用命令:");
console.log("- generateNewToken() : 生成新的单一token");
console.log("- clearAllTokens() : 清除所有token");
console.log("- getTokenStatus() : 查看当前token状态");
console.log("- tokenGenerator : 访问完整的token生成器实例");

// 自动生成一个token
console.log("⚡ 自动生成初始token...");
tokenGenerator.generateToken(); 