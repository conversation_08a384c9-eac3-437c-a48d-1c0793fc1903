# 测试环境交易
from binance.client import Client

# 注意：您需要在币安期货测试网申请真实的API密钥
# 期货测试网地址：https://testnet.binancefuture.com
api_key = 'PyHIV2OIxBsk57INYHvhOcsWhISfrJBz2mJ2fWRZSpYHfFGm6cgzXwjSPvvf1AMV'
api_secret = 'nfPyhAJToL1nm3DSJQYIP2JqnCtfmHgtFbZFlwA4ptV7Rd5lScGnqcjLTiS1V1bE'

# 初始化期货测试网客户端
# 注意：python-binance的testnet参数主要用于现货测试网
# 对于期货测试网，需要使用从 https://testnet.binancefuture.com 获取的API密钥
try:
    testnet_client = Client(api_key=api_key, api_secret=api_secret, testnet=True)
    print("=== 连接到币安测试网 ===")
except Exception as e:
    print(f"初始化客户端失败: {e}")
    exit()

print("\n=== 重要提示 ===")
print("如果遇到API权限错误，请按以下步骤操作：")
print("1. 访问币安期货测试网：https://testnet.binancefuture.com")
print("2. 使用GitHub账号登录")
print("3. 在右上角账户下拉菜单中找到'API Management'")
print("4. 创建新的测试网API密钥")
print("5. 确保勾选期货交易权限")
print("6. 将新的API Key和Secret替换到代码中")
print("=" * 60)

# 首先测试连接
try:
    server_time = testnet_client.get_server_time()
    print(f"\n服务器时间: {server_time}")
    print("API连接正常")
except Exception as e:
    print(f"API连接失败: {e}")

# 查询账户余额
try:
    balance = testnet_client.futures_account_balance()
    print('\n=== 期货账户余额 ===')
    has_balance = False
    for b in balance:
        balance_amount = float(b['balance'])
        if balance_amount > 0:
            has_balance = True
            print(f"{b['asset']}: {balance_amount}")
    
    if not has_balance:
        print("账户中没有余额")
        print("提示：测试网账户通常会自动分配一些测试资金")
        
except Exception as e:
    print(f'获取账户余额失败: {e}')
    if "Invalid API-key" in str(e):
        print("API密钥无效，请检查是否使用了正确的测试网API密钥")

# 获取可用的交易对
try:
    exchange_info = testnet_client.futures_exchange_info()
    sol_symbols = [s['symbol'] for s in exchange_info['symbols'] if 'SOL' in s['symbol'] and s['status'] == 'TRADING']
    print(f'\n=== 可用的SOL交易对 ===')
    for symbol in sol_symbols[:5]:
        print(symbol)
except Exception as e:
    print(f'获取交易对信息失败: {e}')

# 尝试小额下单测试
try:
    print('\n=== 尝试测试下单 ===')
    
    # 获取SOLUSDT的当前价格
    ticker = testnet_client.futures_symbol_ticker(symbol="SOLUSDT")
    current_price = float(ticker['pricechange_volume'])
    print(f"SOLUSDT当前价格: {current_price}")
    
    # 计算一个小额的订单量（约1USDT价值）
    test_quantity = round(1.0 / current_price, 3)  # 约1USDT价值的SOL
    print(f"测试订单量: {test_quantity} SOL")
    
    # 下市价买单
    order = testnet_client.futures_create_order(
        symbol='SOLUSDT',
        side='BUY',
        type='MARKET',
        quantity=test_quantity
    )
    
    print('=== 下单成功 ===')
    print(f"订单ID: {order.get('orderId')}")
    print(f"执行数量: {order.get('executedQty')}")
    print(f"状态: {order.get('status')}")
    
except Exception as e:
    print(f'下单失败: {e}')
    if "insufficient balance" in str(e).lower():
        print("余额不足 - 测试网账户可能没有足够的USDT")
    elif "minimum order" in str(e).lower():
        print("订单量太小 - 请增加订单数量")
    elif "invalid symbol" in str(e).lower():
        print("无效交易对 - SOLUSDT可能在测试网不可用")

# 查询当前持仓
try:
    positions = testnet_client.futures_position_information()
    print('\n=== 当前持仓 ===')
    
    active_positions = []
    for pos in positions:
        positionAmt = float(pos['positionAmt'])
        if positionAmt != 0:
            active_positions.append(pos)
            symbol = pos['symbol']
            entryPrice = float(pos['entryPrice'])
            unrealizedProfit = float(pos['unRealizedProfit'])
            print(f'{symbol}: 数量={positionAmt}, 开仓价={entryPrice}, 未实现盈亏={unrealizedProfit}')
    
    if not active_positions:
        print('当前没有持仓')
        
except Exception as e:
    print(f'获取持仓信息失败: {e}')

print("\n=== 测试完成 ===")
print("如果所有操作都成功，说明测试网API配置正确")
print("您现在可以基于此代码开发您的交易策略")
