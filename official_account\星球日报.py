import requests


headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "max-age=0",
    "if-modified-since": "Sat, 26 Apr 2025 02:56:52 GMT",
    "if-none-match": "\"3456-6bi00ckAbyCzaxyECP9B94HXYkQ\"",
    "priority": "u=0, i",
    "referer": "https://w4goj58yti.feishu.cn/",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-arch": "\"x86\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"136.0.7103.114\"",
    "sec-ch-ua-full-version-list": "\"Chromium\";v=\"136.0.7103.114\", \"Google Chrome\";v=\"136.0.7103.114\", \"Not.A/Brand\";v=\"99.0.0.0\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-ch-ua-platform-version": "\"19.0.0\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "cross-site",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
}
cookies = {
    "cf_clearance": "7uon3UlB_h_XK5kf8golQH3imEEH.5ylBn4Pl83uvxs-1747134806-1.2.1.1-m6IJtXJx6W_TEIKX_v2_jWmdEMTxgLLAljK0pv1fCOelJS2Ho8iVCv.QCfPeTVOH2ODqmpq2U72eEvbz4zdSYm46ODaCr2WtiVby.FiwMmV0CTcVNv449tjd0IM2.IwsQ4U36McJ5B8oNpYLcEwYg7XQBvoUOi74Bm1RBeb5vkwhXwLElth0Jj2WyMUx8RGcREOrLjY5rFU5ki6eSLcBH_Hb2prBVoo42lTcfTzFSrWYaOkRnjZpKqWK_6eDsbARJh3w4Bi.MFRwz.ri5gncm5w4cUXOHG_aYBm8P9aJAJzUDJmARq9WvCnRUknCLYhQZAY_evspRdtkpS.77c5uaW57TBhaWor.Ww7D9vzSdGcyDzTOt2tovh6Zkdh7yEqa"
}
url = "https://rsshub.app/hellobtc/news"
response = requests.get(url, headers=headers, cookies=cookies)

print(response.text)
print(response)