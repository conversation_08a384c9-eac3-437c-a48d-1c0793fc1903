import requests

def get_task_result(client_key, task_id):
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": client_key,
        "taskId": task_id
    }

    response = requests.post(url, headers=headers, json=data)
    result = response.json()

    if result['errorId'] == 0:
        if result['status'] == 'ready':
            print("Task is ready. Solution:", result['solution'])
        else:
            print("Task is not ready yet. Status:", result['status'])
    else:
        print("Error in getting task result:", result['errorDescription'])

    return result

client_key = 'df68a19e24b83ed93ef17af14b65bc3fbb0de3db56034'
task_id = '2f30fcfa-bc50-11ef-bdf7-9669990674f1'

if __name__ == '__main__':
    get_task_result(client_key, task_id)