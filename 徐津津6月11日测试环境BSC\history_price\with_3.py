#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一调度器 - 同步运行三个平台的价格获取脚本
确保每次请求都是同时发送的，保证数据一致性
"""

import requests
import sqlite3
import json
import datetime
import time
import hmac
import base64
import hashlib
import random
import sys
from curl_cffi import requests as cf_requests

# 设置控制台输出编码
if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库配置
DB_PATH = r'C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db'
DB_TABLE = "kline_top200_bsc"

# 重试配置
MAX_RETRIES = 5
RETRY_INTERVAL = 2

# HA API配置
HA_API_URL = "http://**************/api/v1/dex/market/current-price"
HA_TIMEOUT = 10

# OKX API配置
OKX_API_HOST = "https://web3.okx.com"
OKX_PRICE_API = "/api/v5/dex/market/price"
BSC_CHAIN_ID = "56"
OKX_API_KEY = "d87d0a5f-a4df-4209-a3b7-573dad862d25"
OKX_API_SECRET = "8807594F0F5B6A15F2B223638B8537D0"
OKX_API_PASSPHRASE = "Dh112211!"

# GMGN API配置
GMGN_COOKIES = {
    "_ga": "GA1.1.1787124478.1747114971",
    "cf_clearance": "zFVwnICqkSenUiBfVwb2ttr2jWXQgp7brd2XB2bw8Ck-1749791891-*******-lAY_rtO1tpYPj7ry1gzMZBHL8sKbUPn.HN7h1LUVPQ6ibDfVs3JvKJuHD2qJ0LCGdAWk4Dxg0Aa5xWolSAIXsSH_COrj3V67J1Zkhb8RYn6ieRVprv3ywXztBFnsqYItmjoZ371LrdQBgFJZHqhhL1MPdn.DNTrq15EqbFuvq7N.DRlNJs6o5cNoeGPqBAqLDCaZ5hM3ZVGv_1pRCfJzco29R2VN14RknJVNTqYEMhnSVNTGrgNw1HSkMWuPMmdafdXKrEypeeT3cRy9SIELIkM6rBuRV76SdCg6YZeLJFw3JyHgdXyXwrnbTWerV3r4O7moIp6rul_2fCIJGHABUy7Xh1dL0ANLz8HIFV8p0fE",
    "_ga_0XM0LYXGC8": "GS2.1.s1749791891$o45$g1$t1749791898$j53$l0$h0"
}

GMGN_BASE_PARAMS = {
    "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
    "client_id": "gmgn_web_20250613-2194-6838f94",
    "from_app": "gmgn",
    "app_ver": "20250613-2194-6838f94",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a0adc6758070914b5d3cd4679349eed1",
    "os": "web",
    "resolution": "1s",
    "from": "0",
    "limit": "365"
}

GMGN_PROXY = {
    "http": "socks5://**************:30889",
    "https": "socks5://**************:30889"
}


def print_log(message):
    """统一的日志打印函数"""
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")


def get_all_token_addresses():
    """从数据库中获取所有Token Address"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f"SELECT rowid, [code_address] FROM {DB_TABLE} WHERE [code_address] IS NOT NULL")
        return cursor.fetchall()
    except sqlite3.Error as e:
        print_log(f"数据库错误: {e}")
        return []
    finally:
        if conn:
            conn.close()


def generate_okx_signature(timestamp, method, request_path, body, secret_key):
    """生成OKX API签名"""
    message = timestamp + method + request_path + body
    mac = hmac.new(
        bytes(secret_key, encoding='utf8'),
        bytes(message, encoding='utf-8'),
        digestmod=hashlib.sha256
    )
    signature = base64.b64encode(mac.digest()).decode('utf-8')
    return signature


def fetch_ha_price(token_address):
    """获取HA平台价格"""
    url = HA_API_URL
    headers = {"Content-Type": "application/json"}
    data = [{"chainName": "BSC", "tokenContractAddress": token_address}]
    
    for attempt in range(MAX_RETRIES):
        try:
            print_log(f"HA - 请求代币 {token_address} (尝试 {attempt + 1}/{MAX_RETRIES})")
            response = requests.post(url, headers=headers, json=data, timeout=HA_TIMEOUT)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200 and "data" in result and result["data"]:
                    data_item = result["data"][0]
                    price = data_item.get("price")
                    timestamp = data_item.get("time")
                    if price and timestamp:
                        print_log(f"HA - 成功获取价格: {price}, 时间: {timestamp}")
                        return {"price": price, "timestamp": timestamp, "success": True}
                
                print_log(f"HA - 响应数据不完整: {result}")
                return {"success": False, "error": "数据不完整"}
            else:
                print_log(f"HA - 请求失败: 状态码 {response.status_code}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_INTERVAL)
                    continue
                    
        except Exception as e:
            print_log(f"HA - 请求异常: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_INTERVAL)
                continue
    
    return {"success": False, "error": "达到最大重试次数"}


def fetch_okx_price(token_address):
    """获取OKX平台价格"""
    request_body = [{
        "chainIndex": BSC_CHAIN_ID,
        "tokenContractAddress": token_address
    }]
    body_json = json.dumps(request_body)
    url = f"{OKX_API_HOST}{OKX_PRICE_API}"
    
    for attempt in range(MAX_RETRIES):
        try:
            print_log(f"OKX - 请求代币 {token_address} (尝试 {attempt + 1}/{MAX_RETRIES})")
            
            # 生成时间戳和签名
            timestamp = datetime.datetime.utcnow().isoformat("T", "milliseconds") + "Z"
            signature = generate_okx_signature(timestamp, "POST", OKX_PRICE_API, body_json, OKX_API_SECRET)
            
            headers = {
                "Content-Type": "application/json",
                "OK-ACCESS-KEY": OKX_API_KEY,
                "OK-ACCESS-SIGN": signature,
                "OK-ACCESS-TIMESTAMP": timestamp,
                "OK-ACCESS-PASSPHRASE": OKX_API_PASSPHRASE
            }
            
            response = requests.post(url, headers=headers, data=body_json, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "0" and "data" in data and data["data"]:
                    price_data = data["data"][0]
                    price = price_data.get("price")
                    time_ms = price_data.get("time")
                    
                    if price and time_ms:
                        print_log(f"OKX - 成功获取价格: {price}, 时间: {time_ms}")
                        return {"price": price, "timestamp": time_ms, "success": True}
                
                print_log(f"OKX - 响应数据不完整: {data}")
                return {"success": False, "error": "数据不完整"}
            else:
                print_log(f"OKX - 请求失败: 状态码 {response.status_code}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_INTERVAL)
                    continue
                    
        except Exception as e:
            print_log(f"OKX - 请求异常: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_INTERVAL)
                continue
    
    return {"success": False, "error": "达到最大重试次数"}


def fetch_gmgn_price(token_address):
    """获取GMGN平台价格"""
    url = f"https://gmgn.ai/vas/api/v1/token_trades/bsc/{token_address}"
    headers = {"referer": f"https://gmgn.ai/bsc/token/{token_address}"}
    params = GMGN_BASE_PARAMS.copy()
    
    for attempt in range(MAX_RETRIES):
        try:
            print_log(f"GMGN - 请求代币 {token_address} (尝试 {attempt + 1}/{MAX_RETRIES})")
            
            response = cf_requests.get(
                url,
                headers=headers,
                cookies=GMGN_COOKIES,
                params=params,
                impersonate="chrome110",
                proxies=GMGN_PROXY,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and "data" in data and "history" in data["data"]:
                    history = data["data"]["history"]
                    if history and len(history) > 0:
                        first_record = history[0]
                        price_usd = first_record.get("price_usd")
                        timestamp = first_record.get("timestamp")
                        
                        if price_usd:
                            print_log(f"GMGN - 成功获取价格: {price_usd}, 时间: {timestamp}")
                            return {"price": price_usd, "timestamp": timestamp, "success": True}
                
                print_log(f"GMGN - 响应数据不完整: {data}")
                return {"success": False, "error": "数据不完整"}
            else:
                print_log(f"GMGN - 请求失败: 状态码 {response.status_code}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_INTERVAL)
                    continue
                    
        except Exception as e:
            print_log(f"GMGN - 请求异常: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_INTERVAL)
                continue
    
    return {"success": False, "error": "达到最大重试次数"}


def update_ha_database(row_id, price, timestamp):
    """更新HA价格到数据库"""
    conn = None
    try:
        if timestamp:
            try:
                if isinstance(timestamp, str):
                    timestamp_int = int(timestamp)
                else:
                    timestamp_int = timestamp
                
                if timestamp_int > 9999999999:  # 毫秒时间戳
                    timestamp_seconds = timestamp_int / 1000
                else:  # 秒级时间戳
                    timestamp_seconds = timestamp_int
                
                standard_time = datetime.datetime.fromtimestamp(timestamp_seconds).strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                standard_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        else:
            standard_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f"UPDATE {DB_TABLE} SET [ha_price] = ?, [HA Time] = ? WHERE rowid = ?",
                       (price, standard_time, row_id))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print_log(f"HA - 更新数据库错误: {e}")
        return False
    finally:
        if conn:
            conn.close()


def update_okx_database(row_id, price, time_ms):
    """更新OKX价格到数据库"""
    conn = None
    try:
        if time_ms:
            try:
                timestamp_int = int(time_ms)
                timestamp = timestamp_int / 1000
                time_obj = datetime.datetime.fromtimestamp(timestamp)
                time_str = time_obj.strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                time_str = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        else:
            time_str = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'UPDATE {DB_TABLE} SET "okx_price" = ?, "OKX Time" = ? WHERE rowid = ?',
                       (price, time_str, row_id))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print_log(f"OKX - 更新数据库错误: {e}")
        return False
    finally:
        if conn:
            conn.close()


def update_gmgn_database(row_id, price, timestamp_str):
    """更新GMGN价格到数据库"""
    conn = None
    try:
        if timestamp_str is not None:
            try:
                timestamp = int(timestamp_str)
                standard_time = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                standard_time = str(timestamp_str)
        else:
            standard_time = None

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'UPDATE {DB_TABLE} SET "gmgn_price" = ?, "GMGN Time" = ? WHERE rowid = ?',
                       (price, standard_time, row_id))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print_log(f"GMGN - 更新数据库错误: {e}")
        return False
    finally:
        if conn:
            conn.close()


def process_single_token(row_id, token_address):
    """同步处理单个代币的三个平台价格"""
    print_log(f"开始同步处理代币: {token_address} (rowid: {row_id})")
    print_log("=" * 80)
    
    # 同时发送三个平台的请求
    print_log("同时向三个平台发送请求...")
    start_time = time.time()
    
    # 这里我们按顺序调用，但实际上可以用线程池并发
    # 为了确保严格同步，我们先按顺序实现
    ha_result = fetch_ha_price(token_address)
    okx_result = fetch_okx_price(token_address)
    gmgn_result = fetch_gmgn_price(token_address)
    
    end_time = time.time()
    print_log(f"三个平台请求完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 统计成功情况
    success_count = 0
    
    # 更新HA数据
    if ha_result["success"]:
        if update_ha_database(row_id, ha_result["price"], ha_result["timestamp"]):
            print_log(f"HA - 成功更新数据库: 价格={ha_result['price']}")
            success_count += 1
        else:
            print_log("HA - 数据库更新失败")
    else:
        print_log(f"HA - 获取价格失败: {ha_result.get('error', '未知错误')}")
    
    # 更新OKX数据
    if okx_result["success"]:
        if update_okx_database(row_id, okx_result["price"], okx_result["timestamp"]):
            print_log(f"OKX - 成功更新数据库: 价格={okx_result['price']}")
            success_count += 1
        else:
            print_log("OKX - 数据库更新失败")
    else:
        print_log(f"OKX - 获取价格失败: {okx_result.get('error', '未知错误')}")
    
    # 更新GMGN数据
    if gmgn_result["success"]:
        if update_gmgn_database(row_id, gmgn_result["price"], gmgn_result["timestamp"]):
            print_log(f"GMGN - 成功更新数据库: 价格={gmgn_result['price']}")
            success_count += 1
        else:
            print_log("GMGN - 数据库更新失败")
    else:
        print_log(f"GMGN - 获取价格失败: {gmgn_result.get('error', '未知错误')}")
    
    print_log(f"代币 {token_address} 处理完成: {success_count}/3 个平台成功")
    print_log("=" * 80)
    
    return success_count


def main():
    """主函数"""
    print_log("=" * 60)
    print_log("三平台同步价格获取工具")
    print_log("=" * 60)
    print_log(f"数据库路径: {DB_PATH}")
    print_log(f"数据库表名: {DB_TABLE}")
    print_log(f"最大重试次数: {MAX_RETRIES}")
    print_log("=" * 60)
    
    # 获取所有token地址
    all_tokens = get_all_token_addresses()
    total_count = len(all_tokens)
    
    if not all_tokens:
        print_log("未找到任何Token地址，请检查数据库")
        return
    
    print_log(f"总共需要处理 {total_count} 个代币")
    print_log("开始逐个同步处理...")
    
    success_stats = {"ha": 0, "okx": 0, "gmgn": 0}
    total_success = 0
    
    start_time = time.time()
    
    for i, (row_id, token_address) in enumerate(all_tokens, 1):
        print_log(f"\n处理进度: {i}/{total_count} ({i/total_count*100:.1f}%)")
        
        # 处理单个代币
        success_count = process_single_token(row_id, token_address)
        total_success += success_count
        
        # 如果不是最后一个，等待一段时间再处理下一个
        if i < total_count:
            delay = 4  # 每个代币处理完后等待3秒
            print_log(f"等待 {delay} 秒后处理下一个代币...")
            time.sleep(delay)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 输出最终统计
    print_log("\n" + "=" * 60)
    print_log("处理完成!")
    print_log(f"总计处理: {total_count} 个代币")
    print_log(f"总成功次数: {total_success} (满分: {total_count * 3})")
    print_log(f"总耗时: {duration:.2f} 秒")
    print_log(f"平均每个代币耗时: {duration/total_count:.2f} 秒")
    print_log("=" * 60)


if __name__ == "__main__":
    print_log("开始运行三平台同步价格获取工具...")
    
    try:
        main()
    except KeyboardInterrupt:
        print_log("\n程序被用户中断")
    except Exception as e:
        import traceback
        print_log(f"程序运行出错: {e}")
        print_log(traceback.format_exc())
    
    print_log("程序运行结束")
