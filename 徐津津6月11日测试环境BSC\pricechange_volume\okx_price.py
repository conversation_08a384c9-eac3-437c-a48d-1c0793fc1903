#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OKX代币价格信息获取工具
严格按照OKX官方API文档实现
获取Solana链上代币的24小时涨跌幅和交易量数据
同时写入okx_change(涨跌幅)和okx_volume(交易量)字段
"""

import requests
import json
import sqlite3
import time
import sys
import threading
import hmac
import base64
from hashlib import sha256
from datetime import datetime, timezone
from concurrent.futures import ThreadPoolExecutor

if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库配置
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "test_data_sol"

# OKX API配置
API_BASE_URL = "https://web3.okx.com"
API_PATH = "/api/v5/dex/market/price-info"
API_URL = API_BASE_URL + API_PATH

# API密钥配置
API_KEY = "d87d0a5f-a4df-4209-a3b7-573dad862d25"
API_SECRET = "8807594F0F5B6A15F2B223638B8537D0"
API_PASSPHRASE = "Dh112211!"

# 代理配置
PRIMARY_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

BACKUP_PROXY = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

# 请求配置
MAX_RETRIES = 5
RETRY_INTERVAL = 3 # 重试等待时间
REQUEST_TIMEOUT = 10
REQUEST_INTERVAL = 0.8  # 请求间隔秒数

# 线程配置
THREAD_COUNT = 1  # 线程数

# 线程锁
PRINT_LOCK = threading.Lock()
DB_LOCK = threading.Lock()
PROXY_LOCK = threading.Lock()

# 当前代理
CURRENT_PROXY = PRIMARY_PROXY.copy()


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with PRINT_LOCK:
        print(*args, **kwargs)


def generate_signature(timestamp, method, request_path, body=''):
    """
    生成OKX API签名
    按照官方文档要求：timestamp + method + request_path + body
    """
    message = timestamp + method + request_path + body
    mac = hmac.new(
        bytes(API_SECRET, encoding='utf8'),
        bytes(message, encoding='utf-8'),
        digestmod=sha256
    )
    return base64.b64encode(mac.digest()).decode()


def get_headers(body=''):
    """
    生成符合OKX官方文档要求的请求头
    """
    # 生成ISO格式时间戳
    timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

    # 生成签名
    sign = generate_signature(timestamp, "POST", API_PATH, body)

    headers = {
        "Content-Type": "application/json",
        "OK-ACCESS-KEY": API_KEY,
        "OK-ACCESS-SIGN": sign,
        "OK-ACCESS-PASSPHRASE": API_PASSPHRASE,
        "OK-ACCESS-TIMESTAMP": timestamp
    }

    return headers


def get_token_addresses_from_db():
    """从数据库获取Token地址列表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(f'SELECT rowid, "code_address" FROM {DB_TABLE}')
        rows = cursor.fetchall()

        token_data = []
        for row_id, address in rows:
            if address and address.strip():
                token_data.append({
                    "row_id": row_id,
                    "token_address": address.strip()
                })

        thread_safe_print(f"从数据库获取到 {len(token_data)} 个Token地址")
        return token_data

    except sqlite3.Error as e:
        thread_safe_print(f"从数据库获取Token地址时出错: {e}")
        return []
    finally:
        conn.close()


def update_data_in_db(row_id, token_address, price_change_24h, volume_24h):
    """更新数据库中的价格变化和交易量数据"""
    with DB_LOCK:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            # 更新数据库 - 同时更新okx_change和okx_volume字段
            cursor.execute(f'''
                UPDATE {DB_TABLE}
                SET "okx_change" = ?, "okx_volume" = ?
                WHERE rowid = ?
            ''', (price_change_24h, volume_24h, row_id))

            if cursor.rowcount > 0:
                conn.commit()
                thread_safe_print(f"成功更新 {token_address} - 涨跌幅: {price_change_24h}, 交易量: {volume_24h}")
                return True
            else:
                thread_safe_print(f"未找到记录 rowid: {row_id}")
                return False

        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库时出错: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()


def get_token_price_info(token_address, thread_id, row_id):
    """
    获取单个代币的价格信息并立即写入数据库
    严格按照OKX官方文档发送请求
    """
    global CURRENT_PROXY

    # 按照文档要求构造请求参数（数组格式）
    request_data = [{
        "chainIndex": "501",  # 链ID bsc:56
        "tokenContractAddress": token_address
    }]

    # 转换为JSON字符串，用于生成签名
    request_body = json.dumps(request_data)

    thread_safe_print(f"[线程-{thread_id}] 请求代币 {token_address} 的价格信息...")

    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            # 获取当前代理
            with PROXY_LOCK:
                current_proxy = CURRENT_PROXY.copy()

            thread_safe_print(f"[线程-{thread_id}] 尝试 {retry_count + 1}/{MAX_RETRIES}")

            # 生成请求头（包含签名）
            headers = get_headers(request_body)

            # 发送POST请求
            response = requests.post(
                API_URL,
                headers=headers,
                data=request_body,  # 使用原始JSON字符串
                proxies=current_proxy,
                timeout=REQUEST_TIMEOUT
            )

            # 处理响应
            if response.status_code == 200:
                try:
                    result = response.json()

                    # 检查API返回状态
                    if result.get("code") == "0" and "data" in result:
                        data_list = result["data"]

                        if len(data_list) > 0:
                            token_data = data_list[0]

                            # 提取所需字段
                            price_change_24h = token_data.get("priceChange24H")
                            volume_24h = token_data.get("volume24H")

                            if price_change_24h is not None and volume_24h is not None:
                                thread_safe_print(
                                    f"[线程-{thread_id}] 成功获取 {token_address} 数据 - 涨跌幅: {price_change_24h}, 交易量: {volume_24h}")

                                # 立即写入数据库 - 同时写入价格变化和交易量
                                if update_data_in_db(row_id, token_address, price_change_24h, volume_24h):
                                    thread_safe_print(f"[线程-{thread_id}] {token_address} 数据已成功写入数据库 - 涨跌幅: {price_change_24h}, 交易量: {volume_24h}")
                                    return True  # 返回成功标志
                                else:
                                    thread_safe_print(f"[线程-{thread_id}] {token_address} 写入数据库失败")
                                    return False
                            else:
                                thread_safe_print(f"[线程-{thread_id}] {token_address} 响应数据缺少必要字段")
                        else:
                            thread_safe_print(f"[线程-{thread_id}] {token_address} 响应数据为空")
                    else:
                        error_msg = result.get("msg", "未知错误")
                        thread_safe_print(f"[线程-{thread_id}] API错误 {token_address}: {error_msg}")

                except json.JSONDecodeError:
                    thread_safe_print(f"[线程-{thread_id}] JSON解析失败: {response.text[:200]}...")
            else:
                thread_safe_print(f"[线程-{thread_id}] HTTP错误 {response.status_code}: {response.text[:200]}...")

            # 重试逻辑
            retry_count += 1
            if retry_count < MAX_RETRIES:
                thread_safe_print(f"[线程-{thread_id}] 等待 {RETRY_INTERVAL} 秒后重试...")
                time.sleep(RETRY_INTERVAL)

        except requests.exceptions.RequestException as e:
            thread_safe_print(f"[线程-{thread_id}] 请求异常: {e}")
            retry_count += 1
            if retry_count < MAX_RETRIES:
                time.sleep(RETRY_INTERVAL)

        except Exception as e:
            thread_safe_print(f"[线程-{thread_id}] 未知错误: {e}")
            retry_count += 1
            if retry_count < MAX_RETRIES:
                time.sleep(RETRY_INTERVAL)

    thread_safe_print(f"[线程-{thread_id}] {token_address} 已达到最大重试次数，获取失败")
    return False  # 返回失败标志


def switch_to_backup_proxy():
    """切换到备用代理"""
    global CURRENT_PROXY
    with PROXY_LOCK:
        CURRENT_PROXY = BACKUP_PROXY.copy()
        thread_safe_print("已切换到备用代理")


def process_all_tokens():
    """处理所有代币数据"""
    # 获取所有token地址
    all_tokens = get_token_addresses_from_db()

    if not all_tokens:
        thread_safe_print("未找到任何Token地址")
        return 0, 0

    total_count = len(all_tokens)
    thread_safe_print(f"总共需要处理 {total_count} 个代币")

    success_count = 0

    # 逐个处理代币
    for i, token_data in enumerate(all_tokens):
        row_id = token_data["row_id"]
        token_address = token_data["token_address"]

        thread_safe_print(f"处理第 {i + 1}/{total_count} 个代币: {token_address}")

        # 获取价格信息并立即写入数据库
        if get_token_price_info(token_address, 1, row_id):
            success_count += 1
            thread_safe_print(f"当前成功: {success_count}/{i + 1}")

        # 请求间隔
        if i < total_count - 1:
            thread_safe_print(f"等待 {REQUEST_INTERVAL} 秒...")
            time.sleep(REQUEST_INTERVAL)

    return total_count, success_count


def main():
    """主函数"""
    thread_safe_print("=" * 80)
    thread_safe_print("OKX代币价格信息获取工具")
    thread_safe_print("严格按照官方API文档实现")
    thread_safe_print("同时写入okx_change(涨跌幅)和okx_volume(交易量)字段")
    thread_safe_print("=" * 80)
    thread_safe_print(f"API URL: {API_URL}")
    thread_safe_print(f"数据库: {DB_PATH}")
    thread_safe_print(f"数据表: {DB_TABLE}")
    thread_safe_print(f"线程数: {THREAD_COUNT}")
    thread_safe_print("=" * 80)

    start_time = time.time()

    try:
        total_count, success_count = process_all_tokens()

        thread_safe_print("\n" + "=" * 80)
        thread_safe_print("处理完成！")
        thread_safe_print(f"总计处理: {total_count} 个代币")
        thread_safe_print(f"成功更新: {success_count} 个代币")
        thread_safe_print(f"成功率: {success_count / total_count * 100:.2f}%" if total_count > 0 else "成功率: 0%")

    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        thread_safe_print(f"程序运行错误: {e}")
        import traceback
        thread_safe_print(traceback.format_exc())

    end_time = time.time()
    thread_safe_print(f"总耗时: {end_time - start_time:.2f} 秒")
    thread_safe_print("=" * 80)


if __name__ == "__main__":
    main()