# import requests
# import json
# from datetime import datetime, timezone, timedelta
# import sqlite3
# from loguru import logger

# proxy = {
#     "http": "http://127.0.0.1:33210",
#     "https": "http://127.0.0.1:33210"
# }

# headers = {
#     "accept": "application/json",
#     "accept-language": "zh-CN,zh;q=0.9",
#     "app-type": "web",
#     "devid": "ed72f275-eb0a-418a-be47-91f58cf52649",
#     "priority": "u=1, i",
#     "referer": "https://web3.okx.com/zh-hans/token/solana/8w2PY5u2C53tcoSPN4KCjjuZRDEqhyddBNpsyBBLbonk",
#     "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
#     "sec-ch-ua-mobile": "?0",
#     "sec-ch-ua-platform": "\"Windows\"",
#     "sec-fetch-dest": "empty",
#     "sec-fetch-mode": "cors",
#     "sec-fetch-site": "same-origin",
#     "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
#     "x-cdn": "https://web3.okx.com",
#     "x-fptoken": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
#     "x-fptoken-signature": "{P1363}SDXnsNIiMsvDyyjrFVqt0dfjLFqgWu1adJYU98vb5zUP+iTSQ5hWEeAvrX2oZh/bAGSaUbKNDv3DFDsXC9p1Kg==",
#     "x-id-group": "2140178166931670001-c-50",
#     "x-locale": "zh_CN",
#     "x-request-timestamp": "1747816706356",
#     "x-simulated-trading": "undefined",
#     "x-site-info": "==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIyRTJiOi42bpdWZyJye",
#     "x-utc": "8",
#     "x-zkdex-env": "0"
# }
# cookies = {
#     "_gcl_gs": "2.1.k1$i1747121212$u3801134",
#     "intercom-id-ny9cf50h": "59b1a03b-f342-4a22-9469-ca04541cfc98",
#     "intercom-device-id-ny9cf50h": "e3eb8110-ee9e-4eec-8777-a8840b95118e",
#     "_ym_uid": "1747121226975031991",
#     "_ym_d": "1747121226",
#     "devId": "ed72f275-eb0a-418a-be47-91f58cf52649",
#     "ok_site_info": "==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIyRTJiOi42bpdWZyJye",
#     "locale": "zh_CN",
#     "ok_prefer_udColor": "0",
#     "ok_prefer_udTimeZone": "0",
#     "first_ref": "https%3A%2F%2Fwww.okx.com%2F",
#     "_gid": "GA1.2.73064293.1747641441",
#     "ok_login_type": "OKX_GLOBAL",
#     "_ym_isad": "2",
#     "__cf_bm": "1gWtctP9vXISStUZymqe2FOmtBHGTfWHHMEHQIy1lIE-1747816373-1.0.1.1-LaFj16EDLnRr_.9L9Y_xaYIHcKJ0ZW3Kr9DXrxA0vchF3g_amrItUi7q6CvpSesb_xvCNfknL_nkXeOVxUECtg0cEfp7swkOSUAZMCC76ac",
#     "_gat_UA-35324627-3": "1",
#     "ok-exp-time": "1747816693029",
#     "ok_prefer_currency": "%7B%22currencyId%22%3A0%2C%22isDefault%22%3A1%2C%22isPremium%22%3Afalse%2C%22isoCode%22%3A%22USD%22%2C%22precision%22%3A2%2C%22symbol%22%3A%22%24%22%2C%22usdToThisRate%22%3A1%2C%22usdToThisRatePremium%22%3A1%2C%22displayName%22%3A%22%E7%BE%8E%E5%85%83%22%7D",
#     "intercom-session-ny9cf50h": "WEQrY2FscVFUMHo4R0k2VkNXUEdqTmFJMTJmdCs0RjNHbmJ6M25WazdaT05hd1lBN2VQcXl4dDVhU1d2TnRBZTRBMFBQYnhNNkgwMUdDU1hWZnBHSkpQblhnZTQ0WGt5M1BjVzlUV2pCUHM9LS0vYTFDenFhcUtkeVB3dlVQMUErUEdBPT0=--f7b9abe6cad318ccebf06ac4db3b3cc0896ab913",
#     "okg.currentMedia": "xl",
#     "_ga": "GA1.1.2028963803.1747121216",
#     "_ga_G0EKWWQGTZ": "GS2.1.s1747816688$o10$g1$t1747816694$j54$l0$h0$daGaieL_AQ6vaZvr1fEIvajwMhxXcqrs5Fg",
#     "ok-ses-id": "9+G9VReTMzCzbjimftLMvHFXMf8fWn9d5LWcyr8aixnMR65jQqY3329KdvRxSNLMpu0EBlo3LAFAx+bkyj5RpJQqq7h4/KToogO/VckZgHBrlv0xmEINOXaDQGTmUycB",
#     "tmx_session_id": "5kukaqlx9uc_1747816696593",
#     "_monitor_extras": "{\"deviceId\":\"OWTCklx6Hw5J0KFQPhe8Aq\",\"eventId\":332,\"sequenceNumber\":332}",
#     "fingerprint_id": "286c47e1-4945-45bb-88e9-98ddc1deff0f",
#     "fp_s": "0",
#     "traceId": "2120678166999100003",
#     "ok_prefer_exp": "1"
# }

# class OKXSpider:
#     def __init__(self):
#         self.url = "https://web3.okx.com/priapi/v5/dex/token/market/history-dex-token-hlc-candles"
#         self.db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
#         self.time_tolerance = 0.5

#     def parse_data(self):
#         params = {
#             "chainId": "501",
#             "address": "8w2PY5u2C53tcoSPN4KCjjuZRDEqhyddBNpsyBBLbonk",
#             "after": "1747119540000", 
#             # "after": "1747106636000", # 这里修改时间戳
#             "bar": "1s",
#             "limit": "257",
#             "t": "0"
#         }
        
#         response = requests.get(
#             self.url, 
#             headers=headers, 
#             cookies=cookies, 
#             params=params,
#             # proxies=proxy
#         )
#         print(response.text)
#         if response.status_code == 200:
#             data = response.json()
#             if data.get("code") == "0":
#                 logger.info(f'访问成功,状态码:{response.status_code},正在获取数据')
#                 self.save_to_sqlite(data["data"])
#             else:
#                 logger.error(f'请求失败,错误信息:{data.get("msg")}')
#         else:
#             logger.error(f'请求失败,状态码:{response.status_code}')

#     def find_closest_timestamp(self, cursor, target_time, target_datetime):
#         """查找最接近的时间戳记录（误差2秒内）"""
#         time_range = timedelta(seconds=self.time_tolerance)
#         start_time = target_datetime - time_range
#         end_time = target_datetime + time_range
        
#         query = """
#             SELECT id, timestamp 
#             FROM gmgn_data_top_1017
#             WHERE token_symbol = 'SOL'
#             AND timestamp BETWEEN ? AND ?
#             ORDER BY ABS(strftime('%s', timestamp) - ?)
#             LIMIT 1
#         """
        
#         # 转换为本地时间字符串
#         start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
#         end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
#         cursor.execute(query, (
#             start_time_str,
#             end_time_str,
#             int(target_datetime.timestamp())
#         ))
        
#         return cursor.fetchone()

#     def save_to_sqlite(self, data_list):
#         conn = sqlite3.connect(self.db_path)
#         cursor = conn.cursor()
        
#         # 记录匹配和未匹配的数量
#         matched_count = 0
#         unmatched_count = 0
        
#         for item in data_list:
#             # 转换为北京时间
#             timestamp = int(item[0]) / 1000
#             beijing_tz = timezone(timedelta(hours=8))
#             dt = datetime.fromtimestamp(timestamp, timezone.utc).astimezone(beijing_tz)
#             time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
            
#             # 查找最接近的时间戳记录
#             closest_record = self.find_closest_timestamp(cursor, timestamp, dt)
            
#             if closest_record:
#                 record_id, record_time = closest_record
#                 # 更新找到的记录
#                 cursor.execute("""
#                     UPDATE gmgn_data_top_1017
#                     SET okx_open_price = ?,
#                         okx_high_price = ?,
#                         okx_low_price = ?,
#                         okx_close_price = ?
#                     WHERE id = ?
#                 """, (
#                     float(item[1]),
#                     float(item[2]),
#                     float(item[3]),
#                     float(item[4]),
#                     record_id
#                 ))
#                 logger.info(f'更新记录: OKX时间={time_str}, 匹配SOL时间={record_time}, 开盘价={item[1]}')
#                 matched_count += 1
#             else:
#                 logger.warning(f'未找到匹配记录: {time_str}')
#                 unmatched_count += 1
        
#         conn.commit()
        
#         # 打印匹配统计
#         logger.info(f'\n数据匹配统计:')
#         logger.info(f'成功匹配并更新的记录数: {matched_count}')
#         logger.info(f'未找到匹配的记录数: {unmatched_count}')
        
#         # 验证数据更新情况
#         cursor.execute("""
#             SELECT COUNT(*) FROM gmgn_data_top_1017
#             WHERE okx_open_price IS NOT NULL 
#             AND token_symbol = 'SOL'
#         """)
#         total_records_with_okx = cursor.fetchone()[0]
#         logger.info(f'数据库中包含OKX价格的SOL记录总数: {total_records_with_okx}')
        
#         conn.close()

# if __name__ == '__main__':
#     spider = OKXSpider()
#     spider.parse_data()



import csv
import requests
from datetime import datetime, timezone, timedelta
import sqlite3
from loguru import logger


proxy = {}


class Gmgn_spider():
    def __init__(self):
        self.headers = {}
        self.params = {}
        self.cookies = {}

    def parse_data(self):
        url = ""
        response = requests.get(url, headers=self.headers, cookies=self.cookies, params=self.params, proxies=proxy, impersonate='chrome110')
        if response.status_code == 200:
            logger.info(f'访问成功,状态码:{response.status_code},正在获取数据')
            data = response.json()
            if data.get("message") == "success":
                logger.info(f'访问成功,状态码:{response.status_code},正在获取数据')
                self.save_to_sqlite(data["data"]["list"])
        elif response.status_code == 403 and "Just a moment..." in response.text:
            logger.info(f'未通过校验,状态码:{response.status_code},请检查cookie')

    def save_to_sqlite(self, data_list):
        db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        create_tabel_sql = """"""
        cursor.execute(create_tabel_sql)
        insert_data = """
        INSERT INTO tabel_name (
            timestamp, token_price, andsoon...
        )
        VALUES (?,'SOL', 'TOKEN_ADDRESS', ?, ?, ?, ?)
        """
        shanghai_tz = timezone(timedelta(hours=8))
        for item in data_list:
            open_price = item["open"]
            high = item["high_price"]
            low = item["low_price"]
            close = item["close_price"]
            cursor.execute(insert_data, (open_price, high, low, close))
        conn.commit()
        conn.close()
        logger.info('数据已报错到数据库')
