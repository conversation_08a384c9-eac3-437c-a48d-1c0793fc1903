# Cookie获取与请求构造完整指南

## 🎯 目标
使用生成的AWS WAF Token构造完整的HTTP请求，测试币安API的实际访问。

## 📋 第一步：获取浏览器Cookie

### 方法1：浏览器开发者工具（推荐）

#### 1. 打开币安网站
```
访问：https://www.binance.com
```

#### 2. 打开开发者工具
```
按F12 → Network标签页
```

#### 3. 刷新页面并找到请求
```
1. 刷新页面（F5）
2. 在Network面板中找到任意一个对binance.com的请求
3. 点击请求 → Headers → Request Headers
4. 找到Cookie字段
```

#### 4. 复制Cookie
```
Cookie: bnc-uuid=6208334d-cc3d-4455-a42e-52531d072aeb; _gcl_au=1.1.1234; lang=en; theme=dark; ...
```

### 方法2：浏览器控制台获取

在币安网站的控制台中运行：
```javascript
// 获取当前页面的所有Cookie
console.log('页面Cookie:', document.cookie);

// 格式化显示
document.cookie.split('; ').forEach((cookie, index) => {
    console.log(`${index + 1}. ${cookie}`);
});
```

### 方法3：JavaScript代码获取

```javascript
// 获取特定的Cookie值
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}

// 获取重要的Cookie
const cookies = {
    'bnc-uuid': getCookie('bnc-uuid'),
    'lang': getCookie('lang'),
    'theme': getCookie('theme'),
    'sensorsdata2015session': getCookie('sensorsdata2015session'),
    // ... 更多Cookie
};

console.log('重要Cookie:', cookies);
```

## 🔧 第二步：构造完整请求头

### 完整的请求头模板

```javascript
const requestHeaders = {
    // 🔥 核心认证头
    'x-aws-waf-token': 'YOUR_GENERATED_TOKEN_HERE',
    
    // 🍪 Cookie（从浏览器复制）
    'Cookie': 'bnc-uuid=6208334d-cc3d-4455-a42e-52531d072aeb; lang=en; theme=dark; ...',
    
    // 🌐 基础浏览器头
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    
    // 🔒 安全相关头
    'Referer': 'https://www.binance.com/',
    'Origin': 'https://www.binance.com',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Ch-Ua': '"Chromium";v="138", "Not=A?Brand";v="99", "Google Chrome";v="138"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"',
    
    // 📱 设备信息头（从您的浏览器代码获取）
    'device-info': 'YOUR_DEVICE_INFO_BASE64_HERE',
    'X-Trace-Id': 'YOUR_TRACE_ID_HERE',
    'BNC-UUID': 'YOUR_BNC_UUID_HERE',
    
    // 🎯 API特定头
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
};
```

## 🚀 第三步：创建测试脚本

我来为您创建一个完整的测试脚本：

### JavaScript测试脚本
```javascript
class BinanceAPITester {
    constructor() {
        this.baseUrl = 'https://www.binance.com';
        this.apiUrl = 'https://api.binance.com';
    }
    
    // 🔧 构造标准请求头
    buildHeaders(awsWafToken, cookies, deviceInfo = null) {
        const headers = {
            'x-aws-waf-token': awsWafToken,
            'Cookie': cookies,
            'User-Agent': navigator.userAgent,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.binance.com/',
            'Origin': 'https://www.binance.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };
        
        // 添加设备信息（如果有）
        if (deviceInfo) {
            headers['device-info'] = deviceInfo;
        }
        
        return headers;
    }
    
    // 🧪 测试API端点
    async testAPI(endpoint, token, cookies, method = 'GET', body = null) {
        try {
            console.log(`🧪 测试端点: ${endpoint}`);
            
            const headers = this.buildHeaders(token, cookies);
            
            const options = {
                method: method,
                headers: headers,
                credentials: 'include' // 包含跨域Cookie
            };
            
            if (body && method !== 'GET') {
                options.body = JSON.stringify(body);
            }
            
            const response = await fetch(endpoint, options);
            
            console.log(`📡 响应状态: ${response.status} ${response.statusText}`);
            console.log(`📋 响应头:`, response.headers);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ 请求成功!`);
                console.log(`📄 响应数据:`, data);
                return { success: true, data: data, status: response.status };
            } else {
                const errorText = await response.text();
                console.log(`❌ 请求失败: ${response.status}`);
                console.log(`📄 错误信息:`, errorText);
                return { success: false, error: errorText, status: response.status };
            }
            
        } catch (error) {
            console.log(`❌ 网络错误:`, error.message);
            return { success: false, error: error.message, status: 0 };
        }
    }
    
    // 🎯 测试常用币安API端点
    async runTestSuite(token, cookies) {
        console.log('🚀 开始币安API测试套件...');
        console.log(`🔑 使用Token: ${token.substring(0, 50)}...`);
        
        const testEndpoints = [
            // 1. 基础连通性测试
            {
                name: '基础Ping测试',
                url: 'https://api.binance.com/api/v3/ping',
                description: '测试API基础连通性'
            },
            
            // 2. 服务器时间
            {
                name: '服务器时间',
                url: 'https://api.binance.com/api/v3/time',
                description: '获取服务器时间'
            },
            
            // 3. 24小时价格统计
            {
                name: '24小时行情',
                url: 'https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT',
                description: '获取BTC/USDT 24小时行情'
            },
            
            // 4. 交易对信息
            {
                name: '交易对信息',
                url: 'https://api.binance.com/api/v3/exchangeInfo',
                description: '获取交易对信息'
            },
            
            // 5. 深度信息
            {
                name: '订单簿深度',
                url: 'https://api.binance.com/api/v3/depth?symbol=BTCUSDT&limit=5',
                description: '获取BTC/USDT订单簿'
            }
        ];
        
        const results = [];
        
        for (const endpoint of testEndpoints) {
            console.log(`\n📍 ${endpoint.name}: ${endpoint.description}`);
            const result = await this.testAPI(endpoint.url, token, cookies);
            
            results.push({
                name: endpoint.name,
                url: endpoint.url,
                ...result
            });
            
            // 添加延迟避免频率限制
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // 📊 输出测试结果摘要
        console.log('\n📊 测试结果摘要:');
        console.log('='.repeat(50));
        
        let successCount = 0;
        results.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${result.name} (${result.status})`);
            if (result.success) successCount++;
        });
        
        console.log('='.repeat(50));
        console.log(`📈 成功率: ${successCount}/${results.length} (${(successCount/results.length*100).toFixed(1)}%)`);
        
        return results;
    }
}
```

## 🎪 第四步：实际使用方法

### 1. 在浏览器控制台中运行

```javascript
// 1. 首先生成AWS WAF Token（使用您的修复版本）
const generator = new AWSWAFTokenGenerator();
const tokenResult = await generator.generateToken("https://api.binance.com");
const awsWafToken = tokenResult.token;

// 2. 获取当前页面Cookie
const cookies = document.cookie;

// 3. 创建测试器并运行
const tester = new BinanceAPITester();
const results = await tester.runTestSuite(awsWafToken, cookies);
```

### 2. 手动测试单个端点

```javascript
// 测试特定API
const tester = new BinanceAPITester();
const result = await tester.testAPI(
    'https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT',
    'YOUR_AWS_WAF_TOKEN',
    'YOUR_COOKIES_STRING'
);

console.log('测试结果:', result);
```

## 📝 重要Cookie字段说明

### 关键Cookie字段：
```javascript
const importantCookies = {
    'bnc-uuid': '用户唯一标识符',
    'lang': '语言设置',
    'theme': '主题设置', 
    'sensorsdata2015session': '埋点会话',
    'futures-layout': '合约布局',
    '_gcl_au': 'Google分析',
    'source': '流量来源'
};
```

### 设备信息字段：
从您之前的浏览器代码中获取：
```
device-info: eyJzY3JlZW5fcmVzb2x1dGlvbiI6IjE5MjAsMTA4MCIsImF2YWlsYWJsZV9zY3JlZW5fcmVzb2x1dGlvbiI6IjE5MjAsMTAzMiIsInN5c3RlbV92ZXJzaW9uIjoiV2luZG93cyAxMCIsImJyYW5kX21vZGVsIjoidW5rbm93biIsInN5c3RlbV9sYW5nIjoiemgtQ04iLCJ0aW1lem9uZSI6IkdNVCswODowMCIsInRpbWV6b25lT2Zmc2V0IjotNDgwLCJ1c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzOC4wLjAuMCBTYWZhcmkvNTM3LjM2IiwibGlzdF9wbHVnaW4iOiJQREYgVmlld2VyLENocm9tZSBQREYgVmlld2VyLENocm9taXVtIFBERiBWaWV3ZXIsTWljcm9zb2Z0IEVkZ2UgUERGIFZpZXdlcixXZWJLaXQgYnVpbHQtaW4gUERGIiwiY2FudmFzX2NvZGUiOiIzOWNkMTMyZSIsIndlYmdsX3ZlbmRvciI6Ikdvb2dsZSBJbmMuIChJbnRlbCkiLCJ3ZWJnbF9yZW5kZXJlciI6IkFOR0xFIChJbnRlbCwgSW50ZWwoUikgVUhEIEdyYXBoaWNzIDYzMCAoMHgwMDAwOUJDOCkgRGlyZWN0M0QxMSB2c181XzAgcHNfNV8wLCBEM0QxMSkiLCJhdWRpbyI6IjEyNC4wNDM0NzUyNzUxNjA3NCIsInBsYXRmb3JtIjoiV2luMzIiLCJ3ZWJfdGltZXpvbmUiOiJBc2lhL1NoYW5naGFpIiwiZGV2aWNlX25hbWUiOiJDaHJvbWUgVjEzOC4wLjAuMCAoV2luZG93cykiLCJmaW5nZXJwcmludCI6IjNlZjk0NWE3Njc5ODkzZDk0OTJkMDU2NmJiMzQ1MDFlIiwiZGV2aWNlX2lkIjoiIiwicmVsYXRlZF9kZXZpY2VfaWRzIjoiIn0=
```

## ⚠️ 重要注意事项

### 1. 频率限制
```javascript
// 在请求之间添加延迟
await new Promise(resolve => setTimeout(resolve, 1000));
```

### 2. Token有效期
```javascript
// AWS WAF Token有5分钟有效期，需要定期重新生成
const tokenAge = Date.now() - tokenGeneratedTime;
if (tokenAge > 5 * 60 * 1000) { // 5分钟
    // 重新生成Token
    const newTokenResult = await generator.generateToken();
}
```

### 3. Cookie更新
```javascript
// Cookie可能过期，需要从浏览器重新获取
const freshCookies = document.cookie;
```

现在您可以开始测试了！使用这个指南可以构造完整的请求并验证您的Token是否有效。

---

*最后更新: 2024年1月 | 适用版本: 实战测试版* 