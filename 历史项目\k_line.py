from curl_cffi import requests

proxy = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

headers = {
    "authority": "www.okx.com",
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "app-type": "web",
    "devid": "2412b004-ebd7-4ce5-8245-51e5ed217d99",
    "referer": "https://www.okx.com/zh-hans/web3/detail/501/So11111111111111111111111111111111111111112",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "x-cdn": "https://www.okx.com",
    "x-id-group": "2140722614379660002-c-300",
    "x-locale": "zh_CN",
    "x-simulated-trading": "0",
    "x-site-info": "==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIyRTJiOi42bpdWZyJye",
    "x-utc": "8",
    "x-zkdex-env": "0"
}
cookies = {
    "devId": "2412b004-ebd7-4ce5-8245-51e5ed217d99",
    "locale": "zh_CN",
    "ok_prefer_udColor": "0",
    "ok_prefer_udTimeZone": "0",
    "AMP_MKTG_56bf9d43d5": "JTdCJTIycmVmZXJyZXIlMjIlM0ElMjJodHRwcyUzQSUyRiUyRnd3dy5qaW5zZS5jbiUyRiUyMiUyQyUyMnJlZmVycmluZ19kb21haW4lMjIlM0ElMjJ3d3cuamluc2UuY24lMjIlN0Q=",
    "AMP_56bf9d43d5": "JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjIyNDEyYjAwNC1lYmQ3LTRjZTUtODI0NS01MWU1ZWQyMTdkOTklMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzM3MzUzNDUyNDc3JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTczNzM1MzQ1MjQ5NyUyQyUyMmxhc3RFdmVudElkJTIyJTNBMSUyQyUyMnBhZ2VDb3VudGVyJTIyJTNBMCU3RA==",
    "traceId": "2140722614379660002",
    "ok_site_info": "==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIyRTJiOi42bpdWZyJye",
    "ok_prefer_currency": "%7B%22currencyId%22%3A50%2C%22isDefault%22%3A0%2C%22isPremium%22%3Afalse%2C%22isoCode%22%3A%22HKD%22%2C%22precision%22%3A2%2C%22symbol%22%3A%22HK%24%22%2C%22usdToThisRate%22%3A7.77005%2C%22usdToThisRatePremium%22%3A7.77005%2C%22displayName%22%3A%22%E6%B8%AF%E5%85%83%22%7D",
    "__cf_bm": "PIr33V.hu_5bn5s7wmOApenyjLGdSvUjOh0YVua9UI0-1742262338-*******-63jf5XgYiliEaKjgafNYVtG6cH5LKM7JunrFfS7dSPs4NFwf5731O8DANDI6bOZ.x7HPZyDwgroff2DgjP94NAZrLU32AkGZYUzGQFsCQ6s",
}
url = "https://www.okx.com/priapi/v5/dex/token/market/history-dex-token-hlc-candles"
params = {
    "chainId": "501",
    "address": "So11111111111111111111111111111111111111112",
    "after": "1733967505000",
    "bar": "1s",
    "limit": "89",
    "t": "1733968173000"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params, proxies=proxy)

print(response.text)
print(response)
