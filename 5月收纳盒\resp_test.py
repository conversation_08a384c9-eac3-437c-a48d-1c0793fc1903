import requests
import datetime

# 替换为你的 API key
API_KEY = 'c781d279-023b-4c63-a39c-2144e4334b81'
BASE_URL = f'https://mainnet.helius-rpc.com/?api-key={API_KEY}'

# 目标代币合约地址（Token Account 或 Pool Account）
TARGET_ADDRESS = '9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump'

# 时间范围（转换为 Solana slot 或 ISO 时间）
START_TIME = "2025-05-12T14:22:00Z"
END_TIME = "2025-05-12T14:30:00Z"

# 查询 Token 账户的历史变动记录
def fetch_token_changes():
    url = BASE_URL
    headers = {'Content-Type': 'application/json'}

    payload = {
        "jsonrpc": "2.0",
        "id": "token-history",
        "method": "getTokenAccountsHistory",
        "params": {
            "account": TARGET_ADDRESS,
            "startTime": START_TIME,
            "endTime": END_TIME,
            "limit": 1000
        }
    }

    response = requests.post(url, json=payload, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print("请求失败:", response.status_code, response.text)
        return None

# 运行并打印结果
data = fetch_token_changes()
if data:
    print(data)
