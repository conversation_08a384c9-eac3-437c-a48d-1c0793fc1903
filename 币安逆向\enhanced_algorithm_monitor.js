// 增强算法监控 - 重点分析_0x270d0a对象

console.log("=== 增强核心算法监控 ===");

// 🔥 分析_0x270d0a对象
if (typeof _0x270d0a !== 'undefined') {
    console.log("🎯 找到核心算法对象 _0x270d0a!");
    console.log("对象类型:", typeof _0x270d0a);
    console.log("所有算法方法:", Object.keys(_0x270d0a));
    
    // 分析每个算法实现
    for (let algorithmKey in _0x270d0a) {
        console.log(`📋 算法 ${algorithmKey}:`);
        console.log("- 类型:", typeof _0x270d0a[algorithmKey]);
        
        if (typeof _0x270d0a[algorithmKey] === 'function') {
            console.log("- 函数实现:", _0x270d0a[algorithmKey].toString().substring(0, 200) + "...");
            
            // 重写每个算法函数进行监控
            const originalAlgorithm = _0x270d0a[algorithmKey];
            _0x270d0a[algorithmKey] = function(params) {
                console.log(`🔥🔥🔥 算法 ${algorithmKey} 被调用!`);
                console.log("算法参数:", params);
                console.log("- input:", params.input);
                console.log("- checksum:", params.checksum);
                console.log("- difficulty:", params.difficulty);
                console.log("- memory:", params.memory);
                
                debugger; // 🎯 算法执行断点
                
                const startTime = performance.now();
                const result = originalAlgorithm.call(this, params);
                const endTime = performance.now();
                
                console.log("🏆 算法执行结果:", result);
                console.log("⏱️ 算法执行时间:", endTime - startTime, "ms");
                
                // 如果是Promise，监控异步结果
                if (result && typeof result.then === 'function') {
                    result.then(solution => {
                        console.log("🎊 异步算法解决方案:", solution);
                        console.log("解决方案长度:", solution ? solution.length : 'undefined');
                        console.log("解决方案类型:", typeof solution);
                        
                        // 分析解决方案结构
                        if (solution && typeof solution === 'object') {
                            console.log("解决方案结构:", Object.keys(solution));
                        }
                    }).catch(err => {
                        console.log("❌ 算法执行失败:", err);
                    });
                }
                
                return result;
            };
            
            console.log(`✅ 算法 ${algorithmKey} 监控已设置`);
        }
    }
} else {
    console.log("❌ _0x270d0a 对象未找到");
}

// 🔍 分析challenge类型映射
console.log("\n=== Challenge类型分析 ===");

// 尝试找到类型映射的解码
const testMapping = {
    '0x1d6': '算法类型标识符',
    '0x1e7': 'difficulty参数',
    '0x1da': 'memory参数',
    '0x191': 'challenge对象',
    '0x1ad': 'input字段'
};

console.log("可能的参数映射:", testMapping);

// 🎯 增强_0x363923监控
if (typeof _0x363923 !== 'undefined') {
    const original_0x363923 = _0x363923;
    _0x363923 = function(_0x2c58b3, _0x4bd7b5) {
        console.log("🚀 _0x363923 算法调度器调用:");
        console.log("参数1 (配置对象):", _0x2c58b3);
        console.log("参数2 (checksum):", _0x4bd7b5);
        
        // 详细分析配置对象
        if (_0x2c58b3) {
            console.log("📋 配置详情:");
            console.log("- 算法类型标识:", _0x2c58b3[Object.keys(_0x2c58b3)[0]]);
            console.log("- challenge数据:", _0x2c58b3.challenge);
            console.log("- difficulty:", _0x2c58b3.difficulty);
            console.log("- memory:", _0x2c58b3.memory);
        }
        
        debugger; // 调度器断点
        
        const result = original_0x363923.call(this, _0x2c58b3, _0x4bd7b5);
        console.log("调度器返回:", result);
        
        return result;
    };
    console.log("✅ _0x363923 调度器监控增强完成");
}

console.log("🎯 增强监控设置完成！现在触发token生成观察完整流程。"); 