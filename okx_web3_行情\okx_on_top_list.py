"""此请求只是拿到最头上有哪些标签"""
import requests


headers = {
    "Host": "www.ynhf1jp.com",
    "Accept": "*/*",
    "x-utc": "+08:00",
    "Referer": "https://www.ynhf1jp.com/priapi/v1/dx/market/v2/memefun/category/info/new",
    "fingerprint-id": "57E28747-EEAD-4D63-A80D-D19D16842ED4",
    "User-Agent": "OKEx/6.119.0 (iPhone;U;iOS 18.3;zh-CN/zh-CN) locale=zh-CN",
    "x-cdn": "https://static.coinall.ltd",
    "x-site-info": "9JCTBJ0TMd0XYt0TiojIlR2bjJCL0EjOikHdpRnblJye",
    "wallet-type": "0",
    "currency": "CNY",
    "x-simulated-trading": "0",
    "BuildVersion": "**************",
    "app_web_mode": "web3",
    "device-token": "57E28747-EEAD-4D63-A80D-D19D16842ED4",
    "risk-params": "fingerprint-id=57E28747-EEAD-4D63-A80D-D19D16842ED4&session-id=57E28747-EEAD-4D63-A80D-D19D16842ED4_txn_start_1752114125229&fp-status=3",
    "Subdomain-Strategy": "2",
    "BundleId": "com.okex.OKExAppstoreFull",
    "platform": "iOS",
    "real-app-version": "6.119.0",
    "x-id": "feca761304d68b3be15380fe6201e8b6",
    "Accept-Language": "zh-CN",
    "wallet-account-type": "0",
    "product": "okex-com",
    "platform-version": "18.3",
    "userUniqueId": "1C757BEF-ED5E-4D88-B254-8EA6BAF45C5B",
    "devid": "57E28747-EEAD-4D63-A80D-D19D16842ED4",
    "app-version": "4.0.78",
    "lua-version": "6.123.1"
}
url = "https://8.212.1.102/priapi/v1/dx/market/v2/memefun/category/info/new"
response = requests.get(url, headers=headers)

print(response.text)
print(response)