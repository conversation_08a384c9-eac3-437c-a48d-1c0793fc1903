# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2025/2/11 13:45
@Python  :  Python3.7
@Desc    :  根据推文地址获取推文评论数据
"""

"""
需要修改点: __init__方法中, params参数中需要添加值:"guest_token": self.cookie_data.get('guest_token','')
                           cursor游标参数的修改

需要添加方法: 从redis中提取出详细页面的地址
"""

import requests
import pandas as pd
import json
import os
import time
import random
from loguru import logger
from datetime import datetime
from db import RedisClient
from Spiders.utils import DB_BASE


class Tweet_detial_Spider(DB_BASE):

    def __init__(self):
        super().__init__()
        self.redis_client = RedisClient(redis_name='cookies:twitter')
        self.cookie_data = self.get_cookie_from_redis()

        self.headers = {
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "referer": "https://x.com/elonmusk/status/1894427923463246217",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": self.cookie_data.get('ct0', '')
        }
        self.cookies = {
            "auth_token": self.cookie_data.get('auth_token', ''),
            "ct0": self.cookie_data.get('ct0', ''),
        }
        self.base_params = {
            "variables": "",
            "features": "{\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"responsive_web_graphql_exclude_directive_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"rweb_video_timestamps_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":false,\"responsive_web_enhance_cards_enabled\":false}",
            "fieldToggles": "{\"withArticleRichContentState\":true,\"withArticlePlainText\":false,\"withGrokAnalyze\":false,\"withDisallowedReplyControls\":false}"
        }
        self.proxy = {
            "http": self.cookie_data.get('proxy_ip', ''),
            "https": self.cookie_data.get('proxy_ip', '')
        }


    def get_cookie_from_redis(self):
        """从Redis中获取cookie信息"""
        try:
            account_name, cookie_json = self.redis_client.get_random_account()
            
            if not account_name or not cookie_json:
                logger.error("无法从Redis获取cookie信息:'cookies:twitter'哈希表为空")
                return {}
            logger.info(f"成功提取账号 {account_name} 的cookie信息")
            
            try:
                cookie_data = json.loads(cookie_json)
                if not isinstance(cookie_data, dict):
                    logger.error("从Redis获取的cookie信息不是有效的字典格式")
                    return {}
                return cookie_data
            except json.JSONDecodeError as e:
                logger.error(f"从Redis获取的cookie信息不是有效的JSON格式: {e}")
                return {}
        except Exception as e:
            logger.error(f"从Redis获取cookie信息失败: {e}")
            return {}


    def get_tweets_and_users_from_mysql(self):
        """从MySQL获取推文ID和用户名信息"""
        try:
            sql = """
            SELECT t.tweet_id, u.username 
            FROM tweets t 
            JOIN users u ON t.user_id = u.user_id 
            WHERE t.tweet_id IS NOT NULL 
            AND u.username IS NOT NULL
            """
            
            results = self.select_mysql(sql)
            if not results:
                logger.warning("未从MySQL获取到有效数据")
                return []
                
            data_list = []
            for result in results:
                data_list.append({
                    'tweet_id': result[0],
                    'username': result[1]
                })
                
            logger.success(f"成功从MySQL获取 {len(data_list)} 条数据")
            return data_list
        except Exception as e:
            logger.error(f"从MySQL获取数据失败: {e}")
            return []
        

    def update_request_params(self, tweet_id, username):
        """更新请求参数"""
        try:
            self.headers["referer"] = f"https://x.com/{username}/status/{tweet_id}"
            
            variables_dict = {
                "focalTweetId": tweet_id,
                "cursor": "",
                "referrer": "tweet",
                "controller_data": "DAACDAAFDAABDAABDAABCgABAAAAAAAGgAAAAAwAAgoAAQAAAAAAAAAECgACYVZG2AM5/gkLAAMAAAAFY3VycnkKAAVNf30fLMyCiAgABgAAAAQKAAfKjux4G3+d/gAAAAAA",
                "with_rux_injections": False,
                "rankingMode": "Relevance",
                "includePromotedContent": True,
                "withCommunity": True,
                "withQuickPromoteEligibilityTweetFields": True,
                "withBirdwatchNotes": True,
                "withVoice": True
            }
            self.base_params["variables"] = json.dumps(variables_dict)
            logger.info(f"已更新请求参数: tweet_id={tweet_id}, username={username}")
            return True
        except Exception as e:
            logger.error(f"更新请求参数失败: {e}")
            return False


    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间格式到标准格式"""
        try:
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}")
            return None


    def fetch_and_save_data(self, tweet_id, username):
        """获取并保存单条推文的数据"""
        try:
            if not self.update_request_params(tweet_id, username):
                return False

            response = requests.get(
                "https://x.com/i/api/graphql/Ez6kRPyXbqNlhBwcNMpU-Q/TweetDetail",
                headers=self.headers,
                cookies=self.cookies,
                params=self.base_params,
                proxies=self.proxy
            )
            response.raise_for_status()
            
            data = response.json()
            extracted = self.extract_entries(data)
            
            if extracted:
                filename = f'X/x_output/tweet_details.csv'
                self.save_to_csv(extracted, filename)
                logger.success(f"成功处理推文: {tweet_id}")
                return True
            else:
                logger.warning(f"未提取到推文 {tweet_id} 的数据")
                return False
                
        except Exception as e:
            logger.error(f"处理推文 {tweet_id} 失败: {e}")
            return False


    def extract_entries(self, data):
        """从响应数据中提取目标字段"""
        extracted = []
        try:
            instructions = data.get('data', {}).get('threaded_conversation_with_injections_v2', {}).get('instructions', [])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                    
                    for entry in entries:
                        # 只处理推文类型的条目
                        if entry.get('entryId', '').startswith('tweet-'):
                            content = entry.get('content', {})
                            if content.get('entryType') == 'TimelineTimelineItem':
                                item_content = content.get('itemContent', {})
                                tweet_results = item_content.get('tweet_results', {}).get('result', {})
                                
                                # 获取用户信息
                                user_results = tweet_results.get('core', {}).get('user_results', {}).get('result', {})
                                legacy_user = user_results.get('legacy', {})
                                
                                # 获取推文信息
                                legacy_tweet = tweet_results.get('legacy', {})
                                
                                # 处理时间戳转换
                                editable_until_msecs = tweet_results.get('edit_control', {}).get('editable_until_msecs')
                                if editable_until_msecs:
                                    try:
                                        editable_time = datetime.fromtimestamp(
                                            int(editable_until_msecs) / 1000
                                        ).strftime('%Y-%m-%d %H:%M:%S')
                                    except Exception as e:
                                        logger.warning(f"时间转换失败: {e}")
                                        editable_time = None
                                else:
                                    editable_time = None

                                # 提取所需字段
                                tweet_data = {
                                    'screen_name': legacy_user.get('screen_name'),
                                    'followers_count': legacy_user.get('followers_count'),
                                    'location': legacy_user.get('location'),
                                    'editable_until': editable_time,
                                    'full_text': legacy_tweet.get('full_text')
                                }
                                
                                extracted.append(tweet_data)
                                
            return extracted
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return []


    def get_next_cursor(self, data):
        """获取下一页游标"""
        try:
            # 获取 instructions 数组
            instructions = data.get('data', {}).get('threaded_conversation_with_injections_v2', {}).get('instructions', [])
            
            if not instructions or len(instructions) == 0:
                logger.warning("未找到 instructions 数据")
                return None
                
            # 获取 entries 数组
            entries = instructions[0].get('entries', [])
            
            for entry in entries:
                content = entry.get('content', {})
                # 检查 entryType 是否为 TimelineTimelineItem
                if content.get('entryType') == 'TimelineTimelineItem':
                    item_content = content.get('itemContent', {})
                    cursor_value = item_content.get('value')
                    if cursor_value:
                        logger.success(f"成功提取游标: {cursor_value[:30]}...")
                        return cursor_value
                        
            logger.warning("未找到有效游标")
            return None
            
        except Exception as e:
            logger.error(f"游标解析异常: {str(e)}")
            return None
        

    def save_to_csv(self, data, filename):
        """保存数据到CSV文件"""
        if not data:
            return

        try:
            df = pd.DataFrame(data)
            
            # 确保所有列都存在
            required_columns = ['screen_name', 'followers_count', 'location', 'editable_until', 'full_text']
            for col in required_columns:
                if col not in df.columns:
                    df[col] = None
            
            # 处理空值
            df.replace({pd.NA: None}, inplace=True)
            
            # 检查文件是否存在
            file_exists = os.path.exists(filename)
            
            # 保存到CSV
            df.to_csv(
                filename,
                mode='a' if file_exists else 'w',
                header=not file_exists,
                index=False,
                encoding='utf-8-sig'
            )
            logger.success(f"成功保存 {len(data)} 条数据到 {filename}")
        except Exception as e:
            logger.error(f"保存失败: {e}")


    # def run(self):
    #     """主运行方法"""
    #     tweet_user_data = self.get_tweets_and_users_from_mysql()
    #     if not tweet_user_data:
    #         logger.error("无可用的推文和用户数据，程序退出")
    #         return
        
    #     for data in tweet_user_data:
    #         tweet_id = data['tweet_id']
    #         username = data['username']
    #         logger.info(f"开始处理推文ID: {tweet_id}, 用户名: {username}")

    #         # variables_dict = json.loads(self.base_params["variables"])
    #         # variables_dict["focalTweetId"] = tweet_id
    #         # self.base_params["variables"] = json.dumps(variables_dict)

    #         next_cursor = None
    #         # page = 1
    #         max_retry = 3

    #         while True:
    #             # 页数限制检查
    #             if self.max_pages and page > self.max_pages:
    #                 logger.info(f"已达到设置的最大页数 {self.max_pages}，停止翻页")
    #                 break

    #             logger.info(f"正在爬取第 {page} 页...")
    #             data = None

    #             # 带重试的请求
    #             for attempt in range(max_retry):
    #                 data = self.fetch_data(keyword, next_cursor)
    #                 if data:
    #                     break
    #                 logger.warning(f"第 {attempt + 1} 次重试...")
    #                 time.sleep(5)

    #             if not data:
    #                 logger.error("请求失败，停止翻页")
    #                 break

    #             # 数据提取和保存
    #             extracted = self.extract_entries(data)
    #             if extracted:
    #                 self.save_to_csv(extracted, f'X/x_output/{keyword}.csv')
    #             else:
    #                 logger.warning("未提取到有效数据")

    #             # 获取下一页游标
    #             new_cursor = self.get_next_cursor(data)

    #             # 更新循环终止条件
    #             if not new_cursor or (self.max_pages and page >= self.max_pages):
    #                 logger.info("已到达最后一页或达到设置的最大页数")
    #                 break

    #                 # 更新游标并继续
    #             next_cursor = new_cursor
    #             page += 1

    #             # 随机等待
    #             wait_time = random.uniform(5, 7)
    #             logger.info(f"等待 {wait_time:.1f} 秒后继续")
    #             time.sleep(wait_time)

    #         logger.success(f"完成关键词 {keyword} 的采集")


    def run(self):
        """主运行方法"""
        # 获取推文数据
        tweet_user_data = self.get_tweets_and_users_from_mysql()
        if not tweet_user_data:
            logger.error("无可用的推文和用户数据，程序退出")
            return

        # 处理每条推文
        for data in tweet_user_data:
            tweet_id = data['tweet_id']
            username = data['username']
            
            logger.info(f"开始处理推文: {tweet_id}, 用户: {username}")
            
            # 获取并保存数据
            if self.fetch_and_save_data(tweet_id, username):
                # 随机等待
                wait_time = random.uniform(5, 7)
                logger.info(f"等待 {wait_time:.1f} 秒后继续")
                time.sleep(wait_time)
            else:
                logger.warning(f"跳过推文 {tweet_id}")

        logger.success("所有推文处理完成")


if __name__ == "__main__":
    spider = Tweet_detial_Spider()
    spider.run()

