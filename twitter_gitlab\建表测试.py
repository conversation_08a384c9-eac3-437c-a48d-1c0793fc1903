import pymysql


config = {
    'host': '**************',
    'port': 33060,
    'user': 'root',
    'password': '12345678',
    'database': 'tweet_test',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

sql_commands = [
    """CREATE TABLE `users` (
      `user_id` bigint NOT NULL,
      `username` varchar(255) DEFAULT NULL COMMENT '@xxx',
      `created_at` datetime DEFAULT NULL COMMENT '创建时间',
      `location` varchar(255) DEFAULT NULL COMMENT '位置',
      `followers` int DEFAULT '0' COMMENT '用户粉丝数量',
      `following` int DEFAULT '0' COMMENT '用户关注数量',
      `posts` int DEFAULT '0' COMMENT '发送推文数量',
      `verified` tinyint(1) DEFAULT NULL COMMENT '是否有账户验证',
      `verified_type` varchar(255) DEFAULT NULL COMMENT '验证类型',
      `label` text COMMENT '标签',
      `description` text COMMENT '用户描述',
      PRIMARY KEY (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;"""
    
]

connection = pymysql.connect(**config)
try:
    with connection.cursor() as cursor:
        for command in sql_commands:
            cursor.execute(command)
    connection.commit()
finally:
    connection.close()

print("Tables created successfully.")