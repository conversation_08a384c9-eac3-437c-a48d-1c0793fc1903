import requests


headers = {
    "Host": "api.ishansong.com",
    "Accept": "*/*",
    "timestamp": "1752115629893",
    "Client-Id": "iss_user_app",
    "nonce": "012466",
    "Accept-Language": "zh-<PERSON>-C<PERSON>;q=1, en-CN;q=0.9",
    "beidou": "JsGs9zPWlPUyY9kb3NWfdj0xkOC4zJiRtb2RlbD1pUGhvbmUxNCw2JiRtYW51ZmFjdHVyZXI9QXBwbGUmJHBsYXRmb3JtPeeUqOaIt+eJiEFQUCYkYXBwX3Y9Ni43LjMwJiR3aWZpPTEmJHdpZmlfbmFtZT1ISyYkY2Fycmllcj0mJGNhcnJpZXI9KG51bGwpJiRkZXZpY2VfaWQ9JiRjbGllbnRfdGltZT0xNzUyMTE1NjE1NzE3JiRtYWM9MDI6MDA6MDA6MDA6MDA6MDAmJGxhdD0zMC41ODY4NzgyOTE2MzcwMyYkbG5nPTEwNC4wODIxMzAzNDYyMjI4JiRjaGFubmVsPUFwcGxlIFN0b3JlJmNpZD0zOURFODM0Ny01MkVGLTQ4NDMtOUEyNi00NEUyODE1QkFDODAmc2lkPTE3NTIxMTU2MTUxNDg0JnVpZD0xNDQxMTYwNTEmJGNvbnRhY3RfY291bnQ9MCZwcm9qZWN0PXBqX3VzZXI=",
    "ak": "/WtT1kqcV0tqVo6N1beglozX36nZ+LQ=",
    "token": "NREpGYfTAddElPDpXb8BSpPDHeFzUNXUPLRY40eo+U6JpDkdMvZ3xC4S4LaY1OI/Et+UIGqzGWz9vdEKIBcmvF5q80RHXb9CK1gyzLKhzrUAUpIFY2CKF2GQex4nuWHn9AUd2I8hG1FznWTeHt6Mfw==",
    "SSCOMMONS": "4846f32e839b7ff2e2a77a4be8004b2e98cbc04c29ecf6ff574f9977a8a691c4bebc2bb3421577ee5d855ad7b19bc7234cf325764f1da952b450af75d81e4cbb0342846e9c689d7099d62b63eb92c20688045ae4612372baa4cd87804a485f2367576e8d679bedf7aff01602062a4480272c83721ad3f65934d3c7ecc4c6a085cbfe167d1c5189fb2c3b505c3fff3ea8e2a77a4be8004b2e98cbc04c29ecf6ff574f9977a8a691c4bebc2bb3421577ee0e76b2ed38baacdf6aa209c6b305cd0e1b2192706ba4fadb534ab2a23188b69119baf412ef7ac9013cfc09a591ced3d3dcc6f73d99ebabef8caff2e8d05443c7d18b26db439ef7a034760e50f9a775c4",
    "gateWayCityId": "5101",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
    "method": "PARAMS",
    "sign": "9964218C103BBAE6CB46D476071F2C28"
}
cookies = {
    "acw_tc": "0bd17c4617521156153621157e411a43e48350ae9c8004bcccd049a53851b8",
    "bddata2019jssdkcross": "%7B%22uid%22%3A%221751417576459-5982439-0d103aa45698458-15928422%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%221751417576459-5982439-0d103aa45698458-15928422%22%7D",
    "sid": "17514267546298240",
    "sidTime": "1751763176000",
    "issUserUUID": "db9079fe-cdbb-4035-8c03-c7b823ac3ba9",
    "ISS_AT": "NREpGYfTAddElPDpXb8BSpPDHeFzUNXUPLRY40eo+U6JpDkdMvZ3xC4S4LaY1OI/Et+UIGqzGWz9vdEKIBcmvF5q80RHXb9CK1gyzLKhzrUAUpIFY2CKF2GQex4nuWHn9AUd2I8hG1FznWTeHt6Mfw==",
    "ISS_RT": "c23f44cc-47aa-425c-bf06-86ba16719691"
}
url = "https://api.ishansong.com/app/orders/v2/list/time"
params = {
    "dateType": "0",
    "orderTime": "0",
    "searchField": "",
    "type": "ALL"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params, verify=False)

print(response.text)
print(response)