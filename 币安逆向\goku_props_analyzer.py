#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gokuProps 深度分析工具
分析AWS KMS加密数据结构和加密参数
"""

import base64
import json
import struct
import binascii
from typing import Dict, Any, Optional

class AWSKMSAnalyzer:
    """AWS KMS数据格式分析器"""
    
    def analyze_kms_blob(self, kms_data: str) -> Dict[str, Any]:
        """
        分析AWS KMS加密blob的结构
        
        Args:
            kms_data: Base64编码的KMS数据
            
        Returns:
            分析结果字典
        """
        print("🔍 分析AWS KMS Blob结构...")
        
        try:
            # 解码base64
            blob_bytes = base64.b64decode(kms_data)
            print(f"📝 Blob总长度: {len(blob_bytes)} 字节")
            print(f"📝 Blob前32字节 (hex): {blob_bytes[:32].hex()}")
            print(f"📝 Blob后32字节 (hex): {blob_bytes[-32:].hex()}")
            
            result = {
                'total_length': len(blob_bytes),
                'hex_data': blob_bytes.hex(),
                'structure_analysis': self._analyze_kms_structure(blob_bytes)
            }
            
            return result
            
        except Exception as e:
            print(f"❌ KMS Blob分析失败: {e}")
            return {}
    
    def _analyze_kms_structure(self, blob_bytes: bytes) -> Dict[str, Any]:
        """分析KMS结构"""
        analysis = {}
        
        # 检查是否是标准的AWS KMS格式
        if len(blob_bytes) < 4:
            return {'error': 'Blob太短'}
        
        # AWS KMS blob通常以特定的magic bytes开始
        magic = blob_bytes[:4]
        analysis['magic_bytes'] = magic.hex()
        
        if magic == b'\x01\x02\x01\x00':
            analysis['format'] = 'AWS KMS Standard Format'
        else:
            analysis['format'] = 'Unknown Format'
        
        # 尝试解析ASN.1/DER结构（KMS使用DER编码）
        analysis['der_analysis'] = self._try_parse_der(blob_bytes)
        
        return analysis
    
    def _try_parse_der(self, data: bytes) -> Dict[str, Any]:
        """尝试解析DER编码结构"""
        try:
            # 简单的DER tag分析
            if len(data) < 2:
                return {'error': 'Data too short for DER'}
            
            tag = data[0]
            length_byte = data[1]
            
            result = {
                'first_tag': f"0x{tag:02x}",
                'length_indicator': f"0x{length_byte:02x}"
            }
            
            # 如果是SEQUENCE (0x30)，尝试进一步解析
            if tag == 0x30:
                result['type'] = 'DER SEQUENCE'
                result['content_analysis'] = self._analyze_der_sequence(data[2:])
            
            return result
            
        except Exception as e:
            return {'error': str(e)}
    
    def _analyze_der_sequence(self, sequence_data: bytes) -> Dict[str, Any]:
        """分析DER SEQUENCE内容"""
        # 简化分析 - 实际需要完整的DER解析器
        return {
            'sequence_length': len(sequence_data),
            'first_16_bytes': sequence_data[:16].hex() if len(sequence_data) >= 16 else sequence_data.hex()
        }

class GokuPropsDataAnalyzer:
    """gokuProps数据深度分析器"""
    
    def __init__(self):
        self.kms_analyzer = AWSKMSAnalyzer()
    
    def full_analysis(self, goku_props: Dict[str, str]) -> Dict[str, Any]:
        """完整分析gokuProps"""
        print("🎯 开始gokuProps深度分析")
        print("=" * 60)
        
        analysis = {
            'key_analysis': self.kms_analyzer.analyze_kms_blob(goku_props['key']),
            'iv_analysis': self._analyze_iv(goku_props['iv']),
            'context_analysis': self._analyze_context(goku_props['context']),
            'relationship_analysis': self._analyze_relationships(goku_props)
        }
        
        return analysis
    
    def _analyze_iv(self, iv_b64: str) -> Dict[str, Any]:
        """分析IV数据"""
        print("\n📊 分析IV数据...")
        
        iv_bytes = base64.b64decode(iv_b64)
        
        analysis = {
            'length': len(iv_bytes),
            'hex': iv_bytes.hex(),
            'bytes': list(iv_bytes),
            'possible_timestamp': self._check_timestamp_patterns(iv_bytes),
            'entropy_analysis': self._analyze_entropy(iv_bytes)
        }
        
        print(f"📝 IV长度: {analysis['length']} 字节")
        print(f"📝 IV (hex): {analysis['hex']}")
        
        return analysis
    
    def _analyze_context(self, context_b64: str) -> Dict[str, Any]:
        """分析Context数据"""
        print("\n📊 分析Context数据...")
        
        context_bytes = base64.b64decode(context_b64)
        
        analysis = {
            'length': len(context_bytes),
            'hex_preview': context_bytes[:64].hex(),
            'structure_hints': self._detect_structure_patterns(context_bytes),
            'padding_analysis': self._analyze_padding(context_bytes),
            'block_analysis': self._analyze_block_structure(context_bytes)
        }
        
        print(f"📝 Context长度: {analysis['length']} 字节")
        print(f"📝 Context前64字节: {analysis['hex_preview']}")
        
        return analysis
    
    def _check_timestamp_patterns(self, data: bytes) -> Dict[str, Any]:
        """检查数据中可能的时间戳模式"""
        patterns = {}
        
        # 检查32位时间戳
        if len(data) >= 4:
            for i in range(len(data) - 3):
                timestamp = struct.unpack('>I', data[i:i+4])[0]
                if 1600000000 < timestamp < 2000000000:  # 合理的时间戳范围
                    patterns[f'offset_{i}'] = {
                        'timestamp': timestamp,
                        'date': timestamp,  # 简化处理
                        'hex': data[i:i+4].hex()
                    }
        
        return patterns
    
    def _analyze_entropy(self, data: bytes) -> float:
        """分析数据熵（随机性）"""
        if not data:
            return 0.0
        
        # 简单的熵计算
        byte_counts = [0] * 256
        for byte in data:
            byte_counts[byte] += 1
        
        entropy = 0.0
        data_len = len(data)
        for count in byte_counts:
            if count > 0:
                probability = count / data_len
                entropy -= probability * (probability.bit_length() - 1)
        
        return entropy
    
    def _detect_structure_patterns(self, data: bytes) -> Dict[str, Any]:
        """检测数据结构模式"""
        patterns = {}
        
        # 检查是否有重复的块
        block_size = 16
        if len(data) >= block_size * 2:
            blocks = [data[i:i+block_size] for i in range(0, len(data), block_size)]
            unique_blocks = set(blocks)
            patterns['block_uniqueness'] = len(unique_blocks) / len(blocks)
        
        # 检查字节频率分布
        byte_freq = {}
        for byte in data:
            byte_freq[byte] = byte_freq.get(byte, 0) + 1
        
        most_common = max(byte_freq.values()) if byte_freq else 0
        patterns['max_byte_frequency'] = most_common / len(data) if data else 0
        
        return patterns
    
    def _analyze_padding(self, data: bytes) -> Dict[str, Any]:
        """分析填充模式"""
        if not data:
            return {}
        
        analysis = {}
        
        # 检查PKCS7填充
        last_byte = data[-1]
        if 1 <= last_byte <= 16:
            padding_length = last_byte
            if len(data) >= padding_length:
                padding_bytes = data[-padding_length:]
                is_pkcs7 = all(b == last_byte for b in padding_bytes)
                analysis['pkcs7_possible'] = is_pkcs7
                analysis['pkcs7_length'] = padding_length if is_pkcs7 else 0
        
        # 检查零填充
        trailing_zeros = 0
        for i in range(len(data) - 1, -1, -1):
            if data[i] == 0:
                trailing_zeros += 1
            else:
                break
        
        analysis['trailing_zeros'] = trailing_zeros
        
        return analysis
    
    def _analyze_block_structure(self, data: bytes) -> Dict[str, Any]:
        """分析块结构"""
        analysis = {}
        
        total_len = len(data)
        analysis['total_length'] = total_len
        analysis['is_16_aligned'] = (total_len % 16 == 0)
        analysis['is_8_aligned'] = (total_len % 8 == 0)
        analysis['remainder_16'] = total_len % 16
        analysis['remainder_8'] = total_len % 8
        
        return analysis
    
    def _analyze_relationships(self, goku_props: Dict[str, str]) -> Dict[str, Any]:
        """分析各部分数据的关系"""
        analysis = {}
        
        # 检查长度关系
        key_len = len(base64.b64decode(goku_props['key']))
        iv_len = len(base64.b64decode(goku_props['iv']))
        context_len = len(base64.b64decode(goku_props['context']))
        
        analysis['length_relationships'] = {
            'key_length': key_len,
            'iv_length': iv_len,
            'context_length': context_len,
            'context_div_16': context_len // 16,
            'context_mod_16': context_len % 16
        }
        
        return analysis

def test_goku_props_analysis():
    """测试gokuProps分析"""
    print("🧪 开始gokuProps深度分析")
    
    # 从bian_test.js中的实际数据
    goku_props = {
        "key": "AQIDAHjcYu/GjX+QlghicBgQ/7bFaQZ+m5FKCMDnO+vTbNg96AFe5qPVJjlBcLicGIp1DWcnAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMXBD3MVVKhU9DfptbAgEQgDtqRRs/hlECu5ia7EQndp8CCQn0BXGkrAH3dt0k9RjxGjbFCmNvMvdy43NHyZa8WtnabLfOHaP9LIwSWQ==",
        "iv": "A6weaADBywAAoIx+",
        "context": "smGqEk+mXPIYng9xMpRWcjYS85r913+PYsfMrU1/Vaz0m761PVPuRv45DTZ7Y253iOzD37+u8a3hQ8VgsJrTi+qDtPzI5wEfiJf4vp9LPmHrKpm0d5X+mvj/96BfEed3TSgieuACnp15YO9O2jo3Du00NtHje1kSeNaq3hu+m0aL58RpRu0Xfzkn2MmmrYJjwf5skmefEwlVhYUVeArwXYsPQ6zAEli3rF+WWqBdAXCl51T6hk5+yTBxPZ3sae9kCQ6TPBMTlmTYo8hPS7veUCZITQnmdZMjX5AeSXaixUKPiXRWOqa3eFIw9Q2evaGQw1qAK5tzEwk0oWIoaBUdZkdwD3ZHfJfe55oL0p43SMXS8Y1G0L/t/eb3rf7B75Z/BnX5NLB1hai1d1GwlNNeZ88AOYI="
    }
    
    analyzer = GokuPropsDataAnalyzer()
    analysis = analyzer.full_analysis(goku_props)
    
    # 输出关键发现
    print("\n🎯 关键发现汇总:")
    print("=" * 60)
    
    # IV分析结果
    iv_analysis = analysis['iv_analysis']
    print(f"📋 IV: {iv_analysis['length']}字节, 熵值: {iv_analysis['entropy_analysis']:.2f}")
    
    # Context分析结果
    context_analysis = analysis['context_analysis']
    print(f"📋 Context: {context_analysis['length']}字节")
    print(f"   - 16字节对齐: {context_analysis['block_analysis']['is_16_aligned']}")
    print(f"   - 填充分析: {context_analysis['padding_analysis']}")
    
    # 长度关系
    relationships = analysis['relationship_analysis']['length_relationships']
    print(f"📋 长度关系:")
    print(f"   - Key: {relationships['key_length']}字节")
    print(f"   - IV: {relationships['iv_length']}字节") 
    print(f"   - Context: {relationships['context_length']}字节 ({relationships['context_div_16']}块 + {relationships['context_mod_16']}字节)")
    
    return analysis

if __name__ == "__main__":
    test_goku_props_analysis() 