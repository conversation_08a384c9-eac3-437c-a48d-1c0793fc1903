import requests
import sqlite3

def get_coin_data(coin_name):
    url = f"API_ENDPOINT/{coin_name}"
    headers = {"Authorization": "Bearer YOUR_API_KEY"}
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        return None

def read_coin_names(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        coins = [line.strip() for line in file.readlines()]
    return coins

def format_coin_data(coin_data):
    formatted_data = {
        "name": coin_data.get('name', ''),
        "pricechange_volume": coin_data.get('pricechange_volume', '')
    }
    return formatted_data

def store_to_database(formatted_data):
    conn = sqlite3.connect('coins.db')
    cursor = conn.cursor()
    
    cursor.execute('''CREATE TABLE IF NOT EXISTS coins (name TEXT, pricechange_volume TEXT)''')
    
    cursor.execute("INSERT INTO coins (name, pricechange_volume) VALUES (?, ?)",
                   (formatted_data['name'], formatted_data['pricechange_volume']))
    
    conn.commit()
    conn.close()

def main(file_path):
    coins = read_coin_names(file_path)
    
    for coin_name in coins:
        coin_data = get_coin_data(coin_name)
        if coin_data:
            formatted_data = format_coin_data(coin_data)
            store_to_database(formatted_data)
        else:
            print(f"Failed to get data for {coin_name}")

# 运行主程序
main('coins.txt')
