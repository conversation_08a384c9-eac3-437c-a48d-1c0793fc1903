import requests
from loguru import logger

def test_mail_api():
    try:
        # API基础URL
        base_url = "https://api.mail.tm"
        
        # 登录信息
        login_data = {
            "address": "<EMAIL>",
            "password": "pFqH9o4Ek6"
        }
        
        logger.info("开始获取token...")
        # 获取token
        login_response = requests.post(
            f"{base_url}/token",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code != 200:
            logger.error(f"邮箱登录失败: {login_response.text}")
            return
            
        token = login_response.json()["token"]
        logger.success(f"成功获取邮箱token: {token}")

        logger.info("开始获取消息列表...")
        # 获取消息列表
        messages_response = requests.get(
            f"{base_url}/messages",
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
        )
        
        if messages_response.status_code != 200:
            logger.error(f"获取消息列表失败: {messages_response.text}")
            return
            
        messages = messages_response.json()
        if not messages["hydra:member"]:
            logger.error("没有找到任何消息")
            return
            
        # 获取最新消息的ID
        latest_message_id = messages["hydra:member"][0]["id"]
        logger.success(f"成功获取最新消息ID: {latest_message_id}")

        logger.info("开始获取消息详情...")
        # 获取消息详情
        message_response = requests.get(
            f"{base_url}/messages/{latest_message_id}",
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
        )
        
        if message_response.status_code != 200:
            logger.error(f"获取消息详情失败: {message_response.text}")
            return
            
        message_content = message_response.json()
        
        # 打印完整的消息内容以供分析
        logger.info(f"消息完整内容: {message_content}")
        
        # 从消息内容中提取验证码
        verification_code = ''.join(filter(str.isdigit, message_content["text"]))[:6]
        logger.success(f"成功获取邮箱验证码: {verification_code}")

    except Exception as e:
        logger.error(f"API测试失败: {e}")

if __name__ == "__main__":
    test_mail_api()