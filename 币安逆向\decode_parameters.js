// AWS WAF 关键参数解码脚本

// 在币安网站控制台执行以下代码来解码参数

console.log("=== AWS WAF 算法参数解码 ===");

// 解码关键配置参数
console.log("_0x106a4e 完整配置:");
console.log("difficulty:", _0x106a4e.difficulty);
console.log("memory:", _0x106a4e.memory);
console.log("challenge_type:", _0x106a4e.challenge_type);
console.log("challenge:", _0x106a4e.challenge);

// 解码 _0x5ed7e3 参数（算法配置）
console.log("\n_0x5ed7e3 算法参数:");
console.log(_0x5ed7e3);

// 解码关键函数
console.log("\n关键函数解码:");
console.log("_0x33b1b6:", typeof _0x33b1b6, _0x33b1b6);
console.log("_0x43fc64:", typeof _0x43fc64);
console.log("_0x5d4130:", typeof _0x5d4130);

// 解码字符串数组中的关键值
console.log("\n关键字符串解码:");
console.log("0xc28位置的值 (memory):", _0x5be4e7(0xc28));
console.log("0xc47位置的值 (challenge_type):", _0x5be4e7(0xc47));
console.log("0x465位置的值 (input):", _0x5be4e7(0x465));
console.log("0xd13位置的值 (hmac):", _0x5be4e7(0xd13));
console.log("0x24b位置的值 (region):", _0x5be4e7(0x24b));

// 重写关键函数进行深度监控
console.log("\n=== 设置深度监控 ===");

// 监控 _0x43fc64 (可能是HashcashScrypt算法)
if (typeof _0x43fc64 !== 'undefined') {
    const original_0x43fc64 = _0x43fc64;
    _0x43fc64 = function(_0x35463d, _0x591d55) {
        console.log("🔥 _0x43fc64 (可能的HashcashScrypt) 调用:");
        console.log("参数1 (challenge数据):", _0x35463d);
        console.log("参数2 (_0x33b1b6对象):", _0x591d55);
        debugger; // 重要断点
        
        const result = original_0x43fc64.call(this, _0x35463d, _0x591d55);
        console.log("_0x43fc64 结果:", result);
        return result;
    };
}

// 监控 _0x5d4130 (可能是Worker执行器)
if (typeof _0x5d4130 !== 'undefined') {
    const original_0x5d4130 = _0x5d4130;
    _0x5d4130 = {
        call: function(fn) {
            console.log("🎯 _0x5d4130.call 执行:");
            console.log("执行的函数:", fn);
            debugger;
            
            const result = original_0x5d4130.call.call(this, fn);
            console.log("_0x5d4130 结果:", result);
            return result;
        }
    };
}

console.log("监控设置完成！请触发token生成来查看详细过程。"); 