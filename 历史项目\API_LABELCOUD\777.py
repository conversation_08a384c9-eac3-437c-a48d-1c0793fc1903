import requests
import time


def get_task_result(task_id):
    print('等待获取cookie等信息')
    time.sleep(3)
    print('正在获取cookie等信息')
    url_result = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data_result = {
        "clientKey": '86529fa1d01339f34ff2b5f77f5ff0d1622065ed56034',
        "taskId": task_id
    }

    response = requests.post(url_result, headers=headers, json=data_result)
    result = response.json()
    for i in range(40):
        if result['errorId'] == 0:
            if result['status'] == 'ready':
                print("Task is ready. Solution:", result['solution'])
                print("User Agent:", result['solution'].get('user_agent', 'No user agent provided'))
                print("Cookies:", result['solution'].get('cookies', 'No cookies provided'))
                print("Request Headers:", result['solution'].get('request_headers', 'No request headers provided'))
                print("Headers:", result['solution'].get('headers', 'No headers provided'))
                print("Content:", result['solution'].get('content', 'No content provided'))
                return result
            elif result['status'] == 'processing':
                print(f'retrying {i}')
                time.sleep(3)
                response = requests.post(url_result, headers=headers, json=data_result)
                result = response.json()
                continue
            else:
                print("Task is not ready yet. Status:", result['status'])
                return result
        else:
            print("Error in getting task result:", result['errorDescription'])
            return result

    print("Max retries reached. Task is still processing.")
    return None

if __name__ == '__main__':

    task_id = '1339aa0a-bd14-11ef-8000-3ad2376c5156'
    get_task_result(task_id)