from curl_cffi import requests
import json
import sqlite3
from loguru import logger


class GmgnDataSpider:
    def __init__(self):
        # 数据库路径
        self.DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        self.DB_TABLE = "the_new_code_data"  # 表名

        # 代理配置
        self.PRIMARY_PROXY = {
            "http": "http://127.0.0.1:33210",
            "https": "http://127.0.0.1:33210"
        }

        self.BACKUP_PROXY = {
            "http": "socks5://192.168.224.75:30889",
            "https": "socks5://192.168.224.75:30889"
        }

        self.headers = {
            "referer": "https://gmgn.ai/new-pair/fFPs2O6D?chain=sol"
        }
        
        self.cookies = {
            "_ga": "GA1.1.1787124478.1747114971",
            "cf_clearance": "BAfayQqrVd4o1Xk8KNzReeD47.3YHREpyDBE2w.ssq8-1749087895-1.2.1.1-tZ2Yqi87KCCgsTLk4OM7Q1XeYalRL9lf296uw65p5gqogaZl8uhxrAvTqT4sx5yBmMB3V_6K7tcNQHpfGQCEoE1TaQWsv_puNhZc9BruF.1TtS7ZbCwIcNrbmAktGaSiREl.4a4ZOU2X0iwDhHxNCLzMmu3kn4sjNHfRJJ.bfn1mCGJJeIUkMDM2KpHsctygy.4eBeN1PK00ShMMDfxZhEehHwuXKxc2ibKMKyFC6q3q62ZNugMTnH8uXHHUOF6m5u22RHE93m4_VKWT1zKxh4ZBE6TXzaub5vrb4TkninFklETcV0N0uba7_avKuVMF2oA0KGMtTd5WIZnns.77SGrmUR0IT_RWNKbijE9aGa4",
            "_ga_0XM0LYXGC8": "GS2.1.s1749087892$o30$g1$t1749088040$j60$l0$h0"
        }
        
        self.url = "https://gmgn.ai/defi/quotation/v1/pairs/sol/new_pairs/1m"
        self.params = {
            "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
            "client_id": "gmgn_web_20250604-1844-cef5291",
            "from_app": "gmgn",
            "app_ver": "20250604-1844-cef5291",
            "tz_name": "Asia/Shanghai",
            "tz_offset": "28800",
            "app_lang": "zh-CN",
            "fp_did": "a0adc6758070914b5d3cd4679349eed1",
            "os": "web",
            "limit": "100",
            "orderby": "open_timestamp",
            "direction": "desc",
            "period": "1m",
            "platforms\\[\\]": "boop",
            "filters\\[\\]": "not_honeypot"
        }

    def get_data(self):
        """发送HTTP请求获取数据"""
        try:
            response = requests.get(
                self.url, 
                headers=self.headers, 
                cookies=self.cookies, 
                params=self.params, 
                impersonate="chrome116", 
                proxies=self.PRIMARY_PROXY
            )
            
            if response.status_code == 200:
                logger.info(f"请求成功, 状态码: {response.status_code}")
                return response.json()
            else:
                logger.error(f"请求失败, 状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"请求异常: {e}")
            # 尝试使用备用代理
            try:
                logger.info("尝试使用备用代理...")
                response = requests.get(
                    self.url, 
                    headers=self.headers, 
                    cookies=self.cookies, 
                    params=self.params, 
                    impersonate="chrome116", 
                    proxies=self.BACKUP_PROXY
                )
                
                if response.status_code == 200:
                    logger.info(f"备用代理请求成功, 状态码: {response.status_code}")
                    return response.json()
                else:
                    logger.error(f"备用代理请求失败, 状态码: {response.status_code}")
                    return None
                    
            except Exception as backup_e:
                logger.error(f"备用代理请求异常: {backup_e}")
                return None
    
    def check_address_exists(self, cursor, base_address):
        """检查数据库中是否已存在相同的base_address"""
        try:
            cursor.execute(f"""
                SELECT COUNT(*) FROM {self.DB_TABLE} 
                WHERE code_address = ?
            """, (base_address,))
            count = cursor.fetchone()[0]
            return count > 0
        except sqlite3.Error as e:
            logger.error(f"检查重复数据失败: {e}")
            return False
    
    def insert_to_database(self, base_addresses):
        """将base_address列表插入SQLite数据库"""
        try:
            conn = sqlite3.connect(self.DB_PATH)
            cursor = conn.cursor()
            
            insert_count = 0
            skip_count = 0
            
            for base_address in base_addresses:
                # 检查是否已存在相同的数据
                if self.check_address_exists(cursor, base_address):
                    logger.debug(f"跳过重复数据: {base_address}")
                    skip_count += 1
                    continue
                
                try:
                    cursor.execute(f"""
                        INSERT INTO {self.DB_TABLE} (code_address)
                        VALUES (?)
                    """, (base_address,))
                    insert_count += 1
                    logger.debug(f"成功插入数据: {base_address}")
                except sqlite3.Error as e:
                    logger.error(f"插入数据失败: {e}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"数据库操作完成: 新插入 {insert_count} 条数据，跳过重复数据 {skip_count} 条")
            return insert_count
            
        except sqlite3.Error as e:
            logger.error(f"数据库操作失败: {e}")
            return 0
    
    def parse_data(self, json_data):
        """解析JSON数据，提取base_address字段"""
        if not json_data or json_data.get('code') != 0:
            logger.error("数据格式错误或请求失败")
            return []
        
        base_addresses = []
        
        # 获取pairs数组
        pairs = json_data.get('data', {}).get('pairs', [])
        
        for pair in pairs:
            base_address = pair.get('base_address')
            if base_address:
                base_addresses.append(base_address)
                logger.info(f"提取到base_address: {base_address}")

        logger.info(f"总共提取到 {len(base_addresses)} 个base_address")
        return base_addresses
    
    def run(self):
        """运行完整的数据获取和处理流程"""
        logger.info("开始获取数据...")
        
        # 获取数据
        json_data = self.get_data()
        
        if json_data:
            logger.info("数据获取成功，开始解析...")
            
            # 解析数据
            base_addresses = self.parse_data(json_data)
            
            if base_addresses:
                logger.info("开始写入数据库...")
                # 插入数据库
                insert_count = self.insert_to_database(base_addresses)
                logger.info(f"数据处理完成，共插入 {insert_count} 条新记录")
            else:
                logger.warning("没有提取到有效的base_address数据")
        else:
            logger.error("数据获取失败")


if __name__ == '__main__':
    # 创建爬虫实例并运行
    spider = GmgnDataSpider()
    spider.run()