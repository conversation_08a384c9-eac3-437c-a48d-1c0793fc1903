"""网易有道词典翻译
测试：3秒一次做循环，一直可用

需要翻译的内容直接修改data中的[q]参数即可"""

import requests
import time

proxies = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "Origin": "https://www.youdao.com",
    "Referer": "https://www.youdao.com/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
cookies = {
    "OUTFOX_SEARCH_USER_ID_NCOO": "1373042093.4709904",
    "OUTFOX_SEARCH_USER_ID": "-964980022@171.223.207.50"
}
url = "https://dict.youdao.com/jsonapi_s"
params = {
    "doctype": "json",
    "jsonversion": "4"
}
data = {
    "q": "该代币在多个群组中被提及CA地址，但缺乏具体讨论内容。用户a和用户b仅抛出链上地址，用户c提到拉盘导致的滑点问题，但无实质性情绪反馈。整体社群对项目认知模糊，尚未形成明确交易逻辑。",
    "le": "en",
    "t": "9",
    "client": "web",
    # "sign": "558e4011b1e488dd4d1e325b913a546a",
    "keyfrom": "webdict"
}

for i in range(1000):
    print(f"--- 第 {i + 1} 次请求 ---")
    try:
        response = requests.post(url, headers=headers, params=params, data=data, proxies=proxies, timeout=10)
        response.raise_for_status()

        response_text = response.text
        print(f"响应内容 (部分): {response_text[:200]}...")

        validation_string = "This token has been mentioned in multiple groups with CA addresses"

        if validation_string in response_text:
            print("【成功】验证通过，目标字符串存在于响应中！")
        else:
            print("【失败】验证未通过，响应中未找到目标字符串。")

    except requests.exceptions.RequestException as e:
        print(f"【错误】请求失败: {e}")

    print("等待3秒后继续...")
    time.sleep(3)
    print("============================================================")