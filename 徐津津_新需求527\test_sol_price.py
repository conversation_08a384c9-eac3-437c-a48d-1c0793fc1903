#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试脚本 - 获取SOL链上指定代币的价格
"""

import requests
import json


def get_token_price(chain_name, token_contract_address):
    """
    获取代币当前价格并直接打印结果
    
    Args:
        chain_name: 链名称，如 "SOL"
        token_contract_address: 代币合约地址
    """
    base_url = "https://valuescan.ai/api/v1/dex/market/current-price"
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    
    # 修改请求格式为数组
    data = [{
        "chainName": chain_name,
        "tokenContractAddress": token_contract_address
    }]
    
    print(f"正在获取 {chain_name} 链上 {token_contract_address} 代币的价格...")
    print(f"请求 URL: {base_url}")
    print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            base_url,
            headers=headers,
            json=data,
            timeout=10
        )
        
        # 打印原始响应内容
        print("\n原始响应内容:")
        print(response.text)
        
        # 检查请求是否成功
        response.raise_for_status()
        
        # 解析响应数据
        result = response.json()
        
        if result.get("code") == 200:
            print("\n请求成功!")
            print("=" * 50)
            
            # 打印完整的响应数据
            print("\n完整响应数据:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 如果有数据，提取并打印价格信息
            if "data" in result and isinstance(result["data"], list):
                for item in result["data"]:
                    if (item.get("chainName") == chain_name and 
                        item.get("tokenContractAddress") == token_contract_address):
                        
                        print("\n价格信息:")
                        print(f"链名称: {item.get('chainName')}")
                        print(f"代币地址: {item.get('tokenContractAddress')}")
                        print(f"价格: {item.get('pricechange_volume')}")
                        print(f"时间戳: {item.get('time')}")
                        break
                else:
                    print(f"\n未找到匹配的数据: {chain_name}/{token_contract_address}")
            else:
                print("\n返回数据格式异常，没有找到data字段或data不是列表")
        else:
            print(f"\n请求失败: {result.get('msg')}")
            
    except requests.exceptions.RequestException as e:
        print(f"\n请求异常: {e}")
    except json.JSONDecodeError:
        print(f"\nJSON解析失败，响应不是有效的JSON格式")
    except Exception as e:
        print(f"\n发生其他错误: {e}")


if __name__ == "__main__":
    # SOL链上的指定代币
    CHAIN_NAME = "SOL"
    TOKEN_ADDRESS = "HNg5PYJmtqcmzXrv6S9zP1CDKk5BgDuyFBxbvNApump"
    
    get_token_price(CHAIN_NAME, TOKEN_ADDRESS) 