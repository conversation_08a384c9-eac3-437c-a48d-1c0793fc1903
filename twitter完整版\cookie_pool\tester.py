# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2025/2/19 15:07
@Python  :  Python3.7
@Desc    :  None
"""
import json
import time

import requests
from loguru import logger

from db import RedisClient


class ValidTester(object):
    def __init__(self, redis_cookies_name, redis_accounts_name):
        self.redis_cookies_name = redis_cookies_name
        self.redis_accounts_name = redis_accounts_name
        self.cookies_db = RedisClient(self.redis_cookies_name)
        self.accounts_db = RedisClient(self.redis_accounts_name)

    def test(self, username, cookies_info):
        raise NotImplementedError

    def run(self):
        cookies_groups = self.cookies_db.all()
        for username, cookies_info in cookies_groups.items():
            self.test(username, cookies_info)


class TwitterValidTester(ValidTester):
    def __init__(self, redis_cookies_name, redis_accounts_name):
        super().__init__(redis_cookies_name, redis_accounts_name)

    def test(self, username, cookies_info):
        logger.info(f'正在测试Cookies, 用户名:{username}, Cookies:{cookies_info}')
        try:
            cookies_info = json.loads(cookies_info)
        except TypeError:
            logger.warning(f'Cookies不合法, 删除Cookies')
            self.cookies_db.delete(username)
            return

        url = "https://x.com/i/api/graphql/Y9WM4Id6UcGFE8Z-hbnixw/UserTweets"
        variables = {"userId": "********", "count": 20, "includePromotedContent": True,
                     "withQuickPromoteEligibilityTweetFields": True, "withVoice": True, "withV2Timeline": True}
        params = {
            "variables": json.dumps(variables),
            "features": "{\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"responsive_web_graphql_exclude_directive_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"rweb_video_timestamps_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":true,\"responsive_web_enhance_cards_enabled\":false}",
            "fieldToggles": "{\"withArticlePlainText\":false}"
        }

        headers = {
            "authorization": cookies_info.get('authorization', ''),
            "user-agent": cookies_info.get('user_agent', ''),
            "x-csrf-token": cookies_info.get('csrf_token', ''),
        }
        cookies = {
            "auth_token": cookies_info.get('auth_token', ''),
            "ct0": cookies_info.get('csrf_token', ''),
        }
        proxies = {
            'http': cookies_info.get('proxy_ip', ''),
            'https': cookies_info.get('proxy_ip', ''),
        }

        count = 5
        while count > 0:
            try:
                response = requests.get(
                    url, headers=headers, cookies=cookies, params=params, proxies=proxies, timeout=5)
                break
            except Exception as e:
                logger.warning(f'请求失败<测试cookies>, {repr(e)}')

            count -= 1
            time.sleep(3)
        else:
            return

        if response.status_code == 200 or response.status_code == 429:
            logger.info('Cookies有效')
        else:
            logger.warning(f'Cookies无效, response.status_code is {response.status_code}, 删除Cookies')
            self.cookies_db.delete(username)
