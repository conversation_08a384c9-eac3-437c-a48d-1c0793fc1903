import hashlib
import base64
import json
from Crypto.Cipher import A<PERSON>
from Crypto.Util.Padding import unpad

def preprocess_encrypted_data(raw_data):

    if not raw_data:
        return ""
    return ''.join(raw_data.split()).strip('"\'')

def decrypt_youdao(encrypted_data, aes_key, aes_iv):

    try:
        clean_data = preprocess_encrypted_data(encrypted_data)
        key_md5 = hashlib.md5(aes_key.encode('utf-8')).digest()
        iv_md5 = hashlib.md5(aes_iv.encode('utf-8')).digest()
        normalized_data = clean_data.replace('-', '+').replace('_', '/')

        cipher = AES.new(key_md5, AES.MODE_CBC, iv_md5)
        decrypted = cipher.decrypt(base64.b64decode(normalized_data))

        try:
            decrypted = unpad(decrypted, AES.block_size)
        except ValueError:
            padding_length = decrypted[-1]
            if padding_length <= AES.block_size:
                decrypted = decrypted[:-padding_length]

        result_json = json.loads(decrypted.decode('utf-8'))

        if result_json.get('code') == 0 and 'translateResult' in result_json:
            translation_parts = []
            source_parts = []
            
            for sentence_group in result_json['translateResult']:
                for sentence in sentence_group:
                    if 'tgt' in sentence:
                        translation_parts.append(sentence['tgt'])
                    if 'src' in sentence:
                        source_parts.append(sentence['src'])
            
            translation = ''.join(translation_parts)
            source_text = ''.join(source_parts)

            print(source_text)
            print("\n")
            print(translation)
            
            return True
            
        else:
            print("解密失败：解析翻译结果出错")
            return False
            
    except json.JSONDecodeError as e:
        print(f"解密失败：JSON解析错误 - {e}")
        return False
    except Exception as e:
        print(f"解密失败：{e}")
        return False

if __name__ == "__main__":

    encrypted_data = "Z21kD9ZK1ke6ugku2ccWuwRmpItPkRr5XcmzOgAKD0GcaHTZL9kyNKkN2aYY6yiOeYeABP7k7C8tefpdYJaAUwyzaIV42IUWd2Xcez5kFd2SSwgl-psTDX4ea2EnD_cWeK7Q483Os3gza-no4Ne4N_3W6EvZ96lGXvC11eYLXI6bjG_J_fQZdR8Bz-0hEj9y3tFUUA9v8rM039WySS9XS9egki6ZY2GCu9rREhg9NWGrCVcRtPGh_adR0p2FA4_Cl5YiYXjO58WRATNee6i75mT1v1BytSREjt5-z7LIgOs="
    aes_key = "ydsecret://query/key/B*RGygVywfNBwpmBaZg*WT7SIOUP2T0C9WHMZN39j^DAdaZhAnxvGcCY6VYFwnHl"
    aes_iv = "ydsecret://query/iv/C@lZe2YzHtZ2CYgaXKSVfsb7Y4QWHjITPPZ0nQp87fBeJ!Iv6v^6fvi2WN@bYpJ4"
    decrypt_youdao(encrypted_data, aes_key, aes_iv)
