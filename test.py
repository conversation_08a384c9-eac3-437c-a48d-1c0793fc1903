import requests

proxies = {
    "http": "http://127.0.0.1:7897",
    "https": "http://127.0.0.1:7897"
}

headers = {
    "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "x-client-transaction-id": "HlraDT1yb6IliYguiLQ/R6kVmMxHp3a/becY0u2kdd102jkIfvDfubibvk4EVrG3EXxYJBq89SZV8Ye0lQZR/+FDGvwcHQ",
    "x-csrf-token": "da859d230b67d60ff77f98fcdee0fc6048214cd1cf2b6ca7f86eda24e9fee5aa6f91b6e685c0d533f5edaba30fffc0d2534345c1c380ea471c1f3fa8e48e145e491405f5486bc8242c9520e8b97cf6ef",
}
cookies = {
    "auth_token": "f0e9b6d1f7d84ea3716f676aeb70dc7631b55d58",
    "ct0": "da859d230b67d60ff77f98fcdee0fc6048214cd1cf2b6ca7f86eda24e9fee5aa6f91b6e685c0d533f5edaba30fffc0d2534345c1c380ea471c1f3fa8e48e145e491405f5486bc8242c9520e8b97cf6ef"
}
url = "https://x.com/i/api/graphql/Ax7CF-QwwexcwNqLvOY8bg/SearchTimeline"
params = {
    "variables": "{\"rawQuery\":\"james\",\"count\":20,\"querySource\":\"recent_search_click\",\"product\":\"Top\"}",
    "features": "{\"rweb_video_screen_enabled\":false,\"payments_enabled\":false,\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":true,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_show_grok_translated_post\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":true,\"responsive_web_grok_community_note_auto_translation_is_enabled\":false,\"responsive_web_enhance_cards_enabled\":false}"
}
try:
    response = requests.get(url, headers=headers, cookies=cookies, params=params, proxies=proxies)
    print(response.text)
    print(response)
except Exception as e:
    print(e)