#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取GMGN上Solana代币的TVL数据
使用GMGN API获取代币的流动性数据作为TVL
支持超时重试、随机等待、IP切换和多线程并发
"""

from curl_cffi import requests
import json
import sqlite3
import time
import sys
import random
import traceback
import threading
from queue import Queue
from datetime import datetime

# 设置控制台输出编码
if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库路径
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "okx_gmgn_ha_TVL"  # 表名

# 代理配置
PRIMARY_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

BACKUP_PROXY = {
    "http": "socks5://**************:30889",
    "https": "socks5://**************:30889"
}

# 请求配置
REQUEST_TIMEOUT = 30  # 秒
MAX_RETRIES = 5
RETRY_DELAY = 3  # 秒
MIN_REQUEST_INTERVAL = 3  # 秒
MAX_REQUEST_INTERVAL = 5  # 秒

# 线程配置
THREAD_COUNT = 3  # 线程数

# 创建线程锁，用于同步输出和数据库操作
print_lock = threading.Lock()
db_lock = threading.Lock()

# 创建统计计数器
success_counter = 0
total_counter = 0


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with print_lock:
        print(*args, **kwargs)


# GMGN API请求头和cookies
def get_headers(token_address):
    return {
        "referer": f"https://gmgn.ai/sol/token/{token_address}"
    }


cookies = {
    "_ga": "GA1.1.1787124478.1747114971",
    "cf_clearance": "xih_8Gqsnxi3HW8w41ZiLtVJAU58EWwqvVl3Ku7AkS0-1748500834-*******-gLFD2fo_qbiI8_.Go7RMHi5cljBUwLAZRLtXbDGk8OPOEgorTDW9iayUwFpQxZsboT2mTe1HEjyRhepvVPIg7RvPZ7AkDOQOudWMW6Uun5wSpCdlH4HcenB6hHzv1B6A.vLdLFb6Zy0rws5wig5cuPj.W7VmF_aU3mJr0ZgG66n1B3gYXWMkUGr8IyUvbSPH54E3A1BgPfXAJFrhXPULjyFAYUrn6A2evg2FO4XKn.ggci4lGGRw4cpWuD3SiWGcEPgkdmG3jyAAhF7nvV4MW0bdWLsk1HpjKUTRqnvLo0wdKN7XyIo9qO1DBAzvhigpzB_ohDeenrecxfMQvLNqYKT61sUhWQIIwpNk7uY5qho",
    "_ga_0XM0LYXGC8": "GS2.1.s1748500822$o19$g1$t1748501212$j60$l0$h0"
}

params = {
    "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
    "client_id": "gmgn_web_20250528-1646-f0a534f",
    "from_app": "gmgn",
    "app_ver": "20250528-1646-f0a534f",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a0adc6758070914b5d3cd4679349eed1",
    "os": "web"
}


def get_token_addresses_from_db():
    """从数据库中获取Token地址列表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # 获取Token地址
        cursor.execute(f'SELECT rowid, "Token Address" FROM {DB_TABLE}')
        rows = cursor.fetchall()

        # 提取行ID和地址并过滤空值
        token_data = []
        for row_id, address in rows:
            if address:  # 确保Token Address不为空
                token_data.append({
                    "row_id": row_id,
                    "token_address": address
                })

        thread_safe_print(f"从数据库获取到 {len(token_data)} 个Token地址")
        return token_data

    except sqlite3.Error as e:
        thread_safe_print(f"从数据库获取Token地址时出错: {e}")
        return []

    finally:
        conn.close()


def update_tvl_in_db(row_id, token_address, tvl_value):
    """更新数据库中的TVL值"""
    # 使用线程锁确保数据库操作的线程安全
    with db_lock:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            # 更新数据库，只更新data GMGN列
            cursor.execute(
                f'''
                UPDATE {DB_TABLE}
                SET "data GMGN" = ?
                WHERE rowid = ?
                ''',
                (tvl_value, row_id)
            )

            if cursor.rowcount > 0:
                thread_safe_print(f"成功更新 {token_address} (rowid: {row_id}) 的TVL值: ${tvl_value}")
                conn.commit()
                return True
            else:
                thread_safe_print(f"未找到记录 (rowid: {row_id})")
                conn.rollback()
                return False

        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库时出错: {e}")
            conn.rollback()
            return False

        finally:
            conn.close()


def get_token_tvl(token_address, use_backup_proxy=False):
    """
    获取代币的TVL数据
    返回：TVL值或None（如果获取失败）
    """
    url = "https://gmgn.ai/api/v1/mutil_window_token_info"

    # 选择使用的代理
    current_proxy = BACKUP_PROXY if use_backup_proxy else PRIMARY_PROXY
    proxy_name = "备用代理" if use_backup_proxy else "主要代理"

    # 生成headers（包含当前token_address的referer）
    headers = get_headers(token_address)

    # 构建请求数据
    data = {
        "chain": "sol",
        "addresses": [token_address]
    }
    json_data = json.dumps(data, separators=(',', ':'))

    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            thread_safe_print(
                f"请求代币 {token_address} 的TVL信息... (使用{proxy_name}, 尝试 {retry_count + 1}/{MAX_RETRIES})")

            response = requests.post(
                url,
                headers=headers,
                cookies=cookies,
                params=params,
                data=json_data,
                impersonate='chrome116',
                proxies=current_proxy,
                timeout=REQUEST_TIMEOUT
            )

            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('code') == 0 and 'data' in data and len(data['data']) > 0:
                        token_data = data['data'][0]
                        liquidity = token_data.get('liquidity')

                        if liquidity:
                            thread_safe_print(f"获取到代币 {token_address} 的TVL: {liquidity}")
                            return float(liquidity)
                        else:
                            thread_safe_print(f"代币 {token_address} 的TVL数据不存在")
                    else:
                        thread_safe_print(f"API返回错误: {data.get('message', '未知错误')}")
                except json.JSONDecodeError:
                    thread_safe_print(f"无法解析JSON响应: {response.text[:200]}...")
            else:
                thread_safe_print(f"请求失败，状态码: {response.status_code}")
                thread_safe_print(f"响应内容: {response.text[:200]}...")

                # 如果是API限制类错误，等待更长时间
                if response.status_code == 429:
                    thread_safe_print(f"API请求频率限制，等待时间延长")
                    time.sleep(RETRY_DELAY * 2)
                else:
                    time.sleep(RETRY_DELAY)

            retry_count += 1

        except Exception as e:
            thread_safe_print(f"获取TVL数据时出错: {e}")
            retry_count += 1
            time.sleep(RETRY_DELAY)

    thread_safe_print(f"已达到最大重试次数 {MAX_RETRIES}，放弃获取此代币的数据")
    return None


def worker(token_queue, thread_id):
    """工作线程函数，处理队列中的token"""
    global success_counter, total_counter

    thread_safe_print(f"线程 {thread_id} 开始运行")

    while not token_queue.empty():
        try:
            # 从队列获取一个token数据
            token_data = token_queue.get()
            row_id = token_data["row_id"]
            token_address = token_data["token_address"]

            # 更新计数器
            with print_lock:
                total_counter += 1
                current_count = total_counter

            thread_safe_print(
                f"\n线程 {thread_id} 处理第 {current_count}/{token_queue.qsize() + current_count} 个代币: {token_address}")

            # 尝试使用主要代理获取TVL
            tvl = get_token_tvl(token_address, use_backup_proxy=False)

            # 如果主要代理失败，尝试使用备用代理
            if tvl is None:
                thread_safe_print(f"使用主要代理获取失败，切换到备用代理...")
                tvl = get_token_tvl(token_address, use_backup_proxy=True)

            # 如果获取到TVL，更新数据库
            if tvl is not None:
                if update_tvl_in_db(row_id, token_address, tvl):
                    with print_lock:
                        success_counter += 1
                        current_success = success_counter

                    thread_safe_print(f"线程 {thread_id} 成功更新代币 {token_address} 的TVL: ${tvl}")
                    thread_safe_print(
                        f"当前进度: {current_success}/{current_count} ({current_success / current_count * 100:.2f}%)")
                else:
                    thread_safe_print(f"线程 {thread_id} 更新代币 {token_address} 的TVL到数据库失败")
            else:
                thread_safe_print(f"线程 {thread_id} 无法获取代币 {token_address} 的TVL数据")

            # 标记任务完成
            token_queue.task_done()

            # 随机等待3-5秒
            wait_time = random.uniform(MIN_REQUEST_INTERVAL, MAX_REQUEST_INTERVAL)
            thread_safe_print(f"线程 {thread_id} 等待 {wait_time:.2f} 秒...")
            time.sleep(wait_time)

        except Exception as e:
            thread_safe_print(f"线程 {thread_id} 处理任务时出错: {e}")
            token_queue.task_done()

    thread_safe_print(f"线程 {thread_id} 已完成所有任务")


def process_tokens_multi_thread(token_data_list):
    """使用多线程处理所有代币"""
    # 创建任务队列
    token_queue = Queue()

    # 将所有token数据添加到队列
    for token_data in token_data_list:
        token_queue.put(token_data)

    total_tokens = token_queue.qsize()
    thread_safe_print(f"总共 {total_tokens} 个Token将被处理，使用 {THREAD_COUNT} 个线程")

    # 创建并启动工作线程
    threads = []
    for i in range(THREAD_COUNT):
        t = threading.Thread(target=worker, args=(token_queue, i + 1))
        t.daemon = True  # 设置为守护线程，主线程结束时会自动结束
        threads.append(t)
        t.start()

    # 等待所有任务完成
    token_queue.join()

    # 等待所有线程结束
    for t in threads:
        t.join()

    return success_counter


def main():
    """主函数"""
    thread_safe_print("=" * 60)
    thread_safe_print("GMGN Solana代币TVL数据获取工具 (多线程版)")
    thread_safe_print("=" * 60)
    thread_safe_print("数据库路径:", DB_PATH)
    thread_safe_print("数据库表名:", DB_TABLE)
    thread_safe_print("最大重试次数:", MAX_RETRIES)
    thread_safe_print("线程数:", THREAD_COUNT)
    thread_safe_print("=" * 60)

    # 获取Token地址
    token_data_list = get_token_addresses_from_db()

    if not token_data_list:
        thread_safe_print("未找到任何Token地址，请检查数据库")
        return

    # 多线程处理代币
    success_count = process_tokens_multi_thread(token_data_list)

    # 输出结果
    thread_safe_print("\n更新完成!")
    thread_safe_print(f"总计: {len(token_data_list)} 个Token")
    thread_safe_print(f"成功更新数据库: {success_count} 个")


if __name__ == "__main__":
    thread_safe_print("开始获取GMGN上Solana代币的TVL数据...")
    start_time = time.time()

    try:
        main()
    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        import traceback

        thread_safe_print(f"程序运行出错: {e}")
        thread_safe_print(traceback.format_exc())

    end_time = time.time()
    duration = end_time - start_time
    thread_safe_print(f"程序运行耗时: {duration:.2f} 秒")
    thread_safe_print("程序运行结束")