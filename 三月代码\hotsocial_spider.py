import requests
from loguru import logger


class HOTSOCIAL():

    def __init__(self):
        self.url = "https://dncapi.flink1.com/api/v3/coin/hotsocial"
        self.headers = {
            "authority": "dncapi.flink1.com",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "origin": "https://www.feixiaohao.com",
            "referer": "https://www.feixiaohao.com/",
            "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        }
        self.params = {
            "coincode": "ethereum-classic",
            "webp": "1"
        }


    def featch_data(self):
        try:
            response = requests.get(self.url, headers=self.headers, params=self.params)
            response.raise_for_status
            logger.info(f'请求状态码: {response.status_code}')
            return response.json()
        except Exception as e:
            logger.warning(f'获取数据时出错: {e}')


    def parse_data(self, data):
        data = data.get('data', {}).get('data', {})


    def run(self):
        pass


if __name__ == '__main__':
    activate = HOTSOCIAL()
    activate.run()