#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import hashlib
import base64
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import binascii

def preprocess_encrypted_data(raw_encrypted_data):
    """
    预处理从接口获取的加密字符串
    
    常见问题处理：
    1. 去除所有空格和换行符
    2. 去除可能的引号
    3. 确保格式正确
    
    Args:
        raw_encrypted_data (str): 从接口获取的原始加密字符串
        
    Returns:
        str: 处理后的干净加密字符串
    """
    if not raw_encrypted_data:
        return ""
    
    # 去除所有空白字符（空格、换行符、制表符等）
    cleaned_data = ''.join(raw_encrypted_data.split())
    
    # 去除可能的引号
    cleaned_data = cleaned_data.strip('"\'')
    
    print(f"🔧 预处理:")
    print(f"   原始长度: {len(raw_encrypted_data)}")
    print(f"   处理后长度: {len(cleaned_data)}")
    print(f"   去除的字符数: {len(raw_encrypted_data) - len(cleaned_data)}")
    
    return cleaned_data

def parse_ydsecret(ydsecret_url):
    """
    解析ydsecret://协议
    根据调试发现的规则进行字符替换
    """
    # 移除协议前缀
    if ydsecret_url.startswith("ydsecret://query/key/"):
        content = ydsecret_url.replace("ydsecret://query/key/", "")
        # key的替换规则
        content = content.replace("*", "+")
        content = content.replace("^", "/")
        content = content.replace("@", "=")
        content = content.replace("!", "I")
    elif ydsecret_url.startswith("ydsecret://query/iv/"):
        content = ydsecret_url.replace("ydsecret://query/iv/", "")
        # iv的替换规则（^保持不变）
        content = content.replace("*", "+")
        content = content.replace("@", "=")
        content = content.replace("!", "")  # 删除!而不是替换为I
        # 注意：iv中的^不替换
    else:
        content = ydsecret_url
    
    return content

def md5_hash(text):
    """
    计算MD5哈希，返回字节数组
    """
    return hashlib.md5(text.encode('utf-8')).digest()

def decrypt_youdao(encrypted_data, aes_key_ydsecret, aes_iv_ydsecret):
    """
    解密有道翻译数据
    """
    try:
        # 直接对原始ydsecret协议字符串进行MD5哈希（重要发现！）
        key_md5 = md5_hash(aes_key_ydsecret)
        iv_md5 = md5_hash(aes_iv_ydsecret)
        
        print(f"原始key: {aes_key_ydsecret}")
        print(f"原始iv: {aes_iv_ydsecret}")
        print(f"key MD5 (数组): {list(key_md5)}")
        print(f"iv MD5 (数组): {list(iv_md5)}")
        
        print(f"MD5处理后的key: {binascii.hexlify(key_md5)}")
        print(f"MD5处理后的iv: {binascii.hexlify(iv_md5)}")
        
        # 处理URL-safe base64格式（-和_需要替换为+和/）
        normalized_data = encrypted_data.replace('-', '+').replace('_', '/')
        
        print(f"原始数据长度: {len(encrypted_data)}")
        print(f"处理后的base64数据长度: {len(normalized_data)}")
        
        # 解密
        cipher = AES.new(key_md5, AES.MODE_CBC, iv_md5)
        decrypted = cipher.decrypt(base64.b64decode(normalized_data))
        
        # 去除PKCS7填充
        try:
            decrypted = unpad(decrypted, AES.block_size)
        except ValueError:
            # 如果自动去填充失败，尝试手动处理
            padding_length = decrypted[-1]
            if padding_length <= AES.block_size:
                decrypted = decrypted[:-padding_length]
        
        # 解析JSON并提取翻译结果
        result_text = decrypted.decode('utf-8')
        
        try:
            result_json = json.loads(result_text)
            
            # 提取翻译结果
            if result_json.get('code') == 0 and 'translateResult' in result_json:
                translation_parts = []
                for sentence_group in result_json['translateResult']:
                    for sentence in sentence_group:
                        if 'tgt' in sentence:
                            translation_parts.append(sentence['tgt'])
                
                final_translation = ''.join(translation_parts)
                return {
                    'success': True,
                    'translation': final_translation,
                    'raw_json': result_json
                }
            else:
                return {
                    'success': False,
                    'error': '解析翻译结果失败',
                    'raw_json': result_json
                }
                
        except json.JSONDecodeError:
            return {
                'success': False,
                'error': 'JSON解析失败',
                'raw_text': result_text
            }
        
    except Exception as e:
        print(f"解密失败: {e}")
        return None

# 测试数据
if __name__ == "__main__":
    # 密钥和IV（这些通常从GET请求获取）
    aes_key = "ydsecret://query/key/B*RGygVywfNBwpmBaZg*WT7SIOUP2T0C9WHMZN39j^DAdaZhAnxvGcCY6VYFwnHl"
    aes_iv = "ydsecret://query/iv/C@lZe2YzHtZ2CYgaXKSVfsb7Y4QWHjITPPZ0nQp87fBeJ!Iv6v^6fvi2WN@bYpJ4"
    
    # 模拟从接口获取的原始加密字符串（包含空格等需要清理的字符）
    raw_encrypted_from_api = """_jsUyA02rwkOJ4enKX7c4dhd7CjvGkcKfbRx0BjNGW-jMtdI8OOvfGuVGSgVDH5exzJtkwqt_3hBtsEwFtLLFFQXgz4QBUl4XOdoQR_RDli7uM36MexoeCaPkQ5Hyy5jNdhf44HZOX3ACqT5PIoS1muVNfWHVBaMhtBeU4kmhh_gIdvnmnSDUS3M0mxoU09gEuPHz9J34nqKO6vHtR66kIp2OzkiHtY9nD64vMW1s4sMwZ__CXlxTT_6QJPTNis-62 ZkxUPoXuxXqpdnedMZkkK-pwUyeJsCE8u4SdenDo17eLdAXaTeryLH5X_lzNYs9vp9V8mJMCVb9_EafE4GKKjMcT2sjNc4ukqBkFhpG3VwduKBnN_7kC7STRihPw_7h4g8LdzUCLQEfoi8zWGAbJq1Q9x7OWRj1iJQjgN_VQnoczcdkfvUEOzxuvLamQLtV-AOrxbtc1aMQWlM8DLymM_7UXLxffPl_1Zs9z0jwyTSTRCPVYKj7Ake3THoeGM-SnvriP2jkN19OP721d4o0QdhrrIjpKcwyYgWo9aTeMCIjtZEr2CZ6mxq2A58S-3 F41kMTsNZOkg6Azwqb6panRwKAlVEZucySUlTK90rHhwqaF2Jtd4hHnLxgC7JLrkSqJlA4n9KIQdGXidMJ2WLwcUc9hImVsEIKaLKhPxRWrO8sQd96llgfKMfUaXMp2Kn8zrOcGF0xrMGNdxeCGhXkhLBUNaZfsesiDZV__LF9NCQ-uu73fvwziWpz4OwgXdy88yzP_XcW0l3WJTKipUG4V0TEHIWy9Lj3XB_li5wP4QGfP4mgU5jUBsk7JRv0p9vORsXdVyYrK1VpekFCtcHc_WquOK-EB5tAKpBLZZglbz6N2QFXRPOsvpWp1BmKY2IlKF2jL6gYsBCstVWYoUFi2UevZXqS_ED1C_W96lq8CX0SH6nhtliRZvEVJm6yjBdAQBmPnl4IeJaNp-nQnixrUQahYYTdo03rD2oP86kAfuT2FEiMdaB_SKVeWRzqM7HmD8gFUQGsEX6IcmFf_tJ31VlMuDpJ9NmUZJYpJEX78omBKWhUaqES-QW9V0PDqLTa7uPW8ifRp4eOTFIbDTkpvT81TQZciVsVDWTUrszboOfAXCH804m8xA69MaqCc5Nj5r9mkMWQAbvAoSGbxfCSIhkrrUoxZoKrrqPYp758IljLKzh3Xe11kxl5O4Mdvz9AnY0SWn2dtiyIkWEu1rVXv7KSXmdf36yFcLAslwV9CcC93Ow9MekXAlUHHximuX7yBmhvkS2-9 fv0fMdbpJIgfQoazEq6sh1g3ujOxvemasB9Ju2r8sfJjQ5wTrsMD-Ff_sGs4u3JwwmPCbOfM2lMG8D_AeHUAMTh6522tc7xW0CJ03zUMCMv5qDH0puf3paMsU950jwf-QjTk8Ci6zhglY1MLpIriwS5NMaOPJQoA4MtZ9DHTLMcF26GvqzyMNohw4Hw2FeKw3A9ZMmpA4GBM4ZfIQ5Bt3wQ_VHUI7PyXcUljjW3WxIJiuLlrL7o7Qt1C2bkOrDDuWsVjlwt4icSc4UIpvjvIvloC9NpWFD5ntfC-qgEcDMdmlPcrttm9kT"""
    
    print("🚀 开始解密有道翻译数据...")
    print("=" * 60)
    
    # 步骤1: 预处理从接口获取的加密字符串
    print("📋 步骤1: 预处理加密字符串")
    cleaned_encrypted_data = preprocess_encrypted_data(raw_encrypted_from_api)
    
    print(f"\n📋 步骤2: 开始解密")
    # 步骤2: 使用清理后的字符串进行解密
    result = decrypt_youdao(cleaned_encrypted_data, aes_key, aes_iv)
    
    if result and result.get('success'):
        print("\n🎉 解密成功！")
        print("=" * 60)
        print("📝 最终翻译结果:")
        print(result['translation'])
        print("=" * 60)
        print("\n🔍 原始JSON数据:")
        print(json.dumps(result['raw_json'], indent=2, ensure_ascii=False))
    elif result:
        print(f"\n❌ 解密失败: {result.get('error', '未知错误')}")
        if 'raw_text' in result:
            print("原始文本:", result['raw_text'][:200] + "...")
        elif 'raw_json' in result:
            print("原始JSON:", result['raw_json'])
    else:
        print("\n❌ 解密失败")
    
    print("\n" + "=" * 60)
    print("💡 使用说明:")
    print("1. 从接口获取原始加密字符串后，先调用 preprocess_encrypted_data() 清理")
    print("2. 然后使用清理后的字符串调用 decrypt_youdao() 解密")
    print("3. 示例:")
    print("   raw_data = '从接口获取的字符串...'")
    print("   clean_data = preprocess_encrypted_data(raw_data)")
    print("   result = decrypt_youdao(clean_data, aes_key, aes_iv)")
    print("=" * 60) 