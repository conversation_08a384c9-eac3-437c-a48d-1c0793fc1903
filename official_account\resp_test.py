import requests

proxies = {
            "http": "socks5://192.168.224.75:30889",
            "https": "socks5://192.168.224.75:30889"
        }

headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "max-age=0",
    "Proxy-Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}
cookies = {
    "wdcid": "088c347420ebc1bd",
    "wdlast": "1746685792"
}
url = "http://www.xinhuanet.com/politics/news_politics.xml"
response = requests.get(url, headers=headers)

print(response.text)
print(response)