import csv
import json
from datetime import datetime
from curl_cffi import requests
from loguru import logger
import time
from datetime import datetime, timezone, timedelta
import sqlite3


proxy = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

# proxy = {
#     "http": "http://127.0.0.1:33210",
#     "https": "http://127.0.0.1:33210"
# }

class Trump():
    def __init__(self):
        self.headers = {
        "referer": "https://gmgn.ai/sol/token/8w2PY5u2C53tcoSPN4KCjjuZRDEqhyddBNpsyBBLbonk",
        }
        self.params = {
            "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
            "client_id": "gmgn_web_20250512-954-8c75333",
            "from_app": "gmgn",
            "app_ver": "20250512-954-8c75333",
            "tz_name": "Asia/Shanghai",
            "tz_offset": "28800",
            "app_lang": "zh-CN",
            "fp_did": "a0adc6758070914b5d3cd4679349eed1",
            "os": "web",
            "resolution": "1s",
            "from": "0",
            "to": "1747119542000",
            "limit": "180"
        }
        self.cookies = {
            "_ga": "GA1.1.1787124478.1747114971",
            "cf_clearance": "jUaJeiruCDwfmvBo24XH985VG5uYsdzXq42RgPbhOs8-1747815711-*******-ZMuuStJrEIXzFEfEnsQSlRLYi33VxM_NynJGSUTwKwaif6ha3Nt9dz0NUfZzfN_xB3hHWmkOGRGRJKvxg5w5wl3dSAzUNBJNQqqaFKFZYXN8rHWNOpMEpwq5grIPfYycT8AihN0nubKkiBGa2BImp0MTf3JtevdlAVna.LuVC1.ydEhz5VhKsjntJlx56eqDn4nUF_jj5tR4A3njNNk2COSXYl4D7ZyZ0X0ps9z7X0LsQc_KfzBBpKJe_sKAJkg8yc5VHn.FD02S8As_.Zq2_VNTQPwZIEUxVeaZvekb0DYwXB90cEF6lU_6mBHM0R7luVUq2l.SzZce2YMk7DNIEXdNckVAK1hipsg20rtRsew",
            "_ga_0XM0LYXGC8": "GS2.1.s1747815710$o4$g1$t1747816354$j0$l0$h0"
        }

    def parse_data(self):
        url = "https://gmgn.ai/api/v1/token_candles/sol/8w2PY5u2C53tcoSPN4KCjjuZRDEqhyddBNpsyBBLbonk"
        response = requests.get(url, headers=self.headers, cookies=self.cookies, params=self.params, proxies=proxy, impersonate='chrome110')

        if response.status_code == 200:
            print(response.text)
            data = response.json()
            if data.get("message") == "success":
                logger.info(f'访问成功,状态码:{response.status_code},正在获取数据')
                self.save_to_sqlite(data["data"]["list"])
        elif response.status_code == 403 and "Just a moment..." in response.text:
            logger.info(f'未通过校验,状态码:{response.status_code},请检查cookie')

    def save_to_sqlite(self, data_list):
        db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 创建表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS gmgn_data_top_1017 (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT COMMENT '时间（K线秒级）',
            token_symbol TEXT COMMENT '代币symbol',
            contract_address TEXT COMMENT '合约地址',
            ha_open_price REAL COMMENT '($)恒安价格-开',
            ha_high_price REAL COMMENT '($)恒安价格-高',
            ha_low_price REAL COMMENT '($)恒安价格-低',
            ha_close_price REAL COMMENT '($)恒安价格-收',
            okx_open_price REAL COMMENT '($)OKX价格-开',
            okx_high_price REAL COMMENT '($)OKX价格-高',
            okx_low_price REAL COMMENT '($)OKX价格-低',
            okx_close_price REAL COMMENT '($)OKX价格-收',
            gmgn_open_price REAL COMMENT '($)Gmgn价格-开',
            gmgn_high_price REAL COMMENT '($)Gmgn价格-高',
            gmgn_low_price REAL COMMENT '($)Gmgn价格-低',
            gmgn_close_price REAL COMMENT '($)Gmgn价格-收',
            tvl REAL COMMENT '($)TVL - 获取价格时的 TVL'
        )
        """
        cursor.execute(create_table_sql)

        # 插入数据
        insert_sql = """
        INSERT INTO gmgn_data_top_1017 (
            timestamp, token_symbol, contract_address,
            gmgn_open_price, gmgn_high_price, gmgn_low_price, gmgn_close_price
        )
        VALUES (?, 'SOL', '8w2PY5u2C53tcoSPN4KCjjuZRDEqhyddBNpsyBBLbonk', ?, ?, ?, ?)
        """

        # 创建Asia/Shanghai时区对象
        shanghai_tz = timezone(timedelta(hours=8))
        
        for item in data_list:
            open_price = item["open"]
            high = item["high"]
            low = item["low"]
            close = item["close"]
            time_stamp = int(item["time"]) / 1000
            # 先转换为UTC时间，然后转换为上海时间
            utc_time = datetime.fromtimestamp(time_stamp, timezone.utc)
            shanghai_time = utc_time.astimezone(shanghai_tz)
            time_str = shanghai_time.strftime('%Y-%m-%d %H:%M:%S')
            
            cursor.execute(insert_sql, (time_str, open_price, high, low, close))

        conn.commit()
        conn.close()
        logger.info('数据已成功保存到SQLite数据库')


if __name__ == '__main__':
    trump = Trump()
    trump.parse_data()