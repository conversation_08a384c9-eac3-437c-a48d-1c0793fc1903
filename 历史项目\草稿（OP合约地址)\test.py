import time
import random
from lxml import etree
from common import redis_sentry_connection, CommonFun
from loguru import logger
from datetime import date
import redis
import requests

def conn_redis(host='', port='', password='', db=''):
    while True:
        try:
            r = redis.Redis(host=host, port=port, password=password, db=db, decode_responses=True,
                            socket_timeout=120, socket_connect_timeout=10, retry_on_timeout=10)
            return r
        except Exception as e:
            logger.error(repr(e))
            logger.info('wait for connect sql')
            time.sleep(5)

class OptimisticSpider:
    def __init__(self):
        self.chain_name = 'Optimistic'
        self.redis_master = redis_sentry_connection()
        self.abi_addr = f'spider:abi:{self.chain_name}'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        self.common_fun = CommonFun()
        self.cawl_date =date.today()

    def run(self):
        pass

    def get_address(self):
        redis_master, _ = redis_sentry_connection()
        chain_name = self.chain_name
        abi_addr = f'spider:abi:{chain_name}'

        while True:
            address = redis_master.spop(abi_addr)
            if address:
                is_expire = self.common_fun.judge_expire(address, self.chain_name)
                if is_expire:
                    logger.info(f'获取到地址:{address}')
                    self.parse_data(address)
            else:
                logger.info('没有从数据库中获取到address')
                break

    def parse_data(self, address):
        url = f'https://optimistic.etherscan.io/address/{address}'
        time.sleep(1.2 + random.random())
        response = requests.get(url=url, headers=self.headers)
        
        if response is None or response.status_code != 200:
            logger.info(f'获取失败:{address}, status_code:{response.status_code}')
            return
        
        html = etree.HTML(response.text)
        c_abi = ''.join(html.xpath())
        c_name = ''.join(html.xpath())

        if c_abi:
            logger.info(f'获取到数据:{address}>{c_name}>{c_abi}')
            # save to sql...
            item = {
                'ADDRESS':address,
                'CHAIN_NAME':self.chain_name,
                'CONTRACT_NAME':c_name,
                'CONTRACT_ABI':c_abi
            }

            self.common_fun.save_to_phoenix(item)
            