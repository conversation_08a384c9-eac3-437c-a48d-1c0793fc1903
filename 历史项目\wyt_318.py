import requests
from lxml import html
import pandas as pd
from loguru import logger
import time

headers = {
    "authority": "www.ncsl.org",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "max-age=0",
    "content-type": "application/x-www-form-urlencoded",
    "origin": "https://www.ncsl.org",
    "referer": "https://www.ncsl.org/financial-services/cryptocurrency-digital-or-virtual-currency-and-digital-assets-2025-legislation?__cf_chl_tk=8HLlGtkUPTKHB3iwbnDXgGcSLpeLdUbWljuoPYcVgfA-1742289656-1.0.1.1-5fTixs1pfDT8L_rUmnJR5WClEF9EgDXT0KrebGkz79w",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-arch": "\"x86\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"114.0.5735.199\"",
    "sec-ch-ua-full-version-list": "\"Not.A/Brand\";v=\"8.0.0.0\", \"Chromium\";v=\"114.0.5735.199\", \"Google Chrome\";v=\"114.0.5735.199\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-ch-ua-platform-version": "\"15.0.0\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}
cookies = {
    ".ASPXANONYMOUS": "B_EBY9TyW4gHyakSQE4braoN5FKXGZSdM3UwKCJKuJ5Gd-WmkNCGWeWR5J6-WfyYuQODTgVBt_DgHZy0n8D6GRL2-goJ5nr0tUWc-FPQMrk4ndCv0",
    "_gcl_au": "1.1.1900414457.1742195194",
    "_fbp": "fb.1.1742195195890.618976060670522802",
    "wp46534": "\"XZYWXDDDDDDTVKJCJLY-THJC-XCVZ-CWMX-KZAVYZZXYXZTDgNssDgJls_hknDD\"",
    "cookieconsent_status": "dismiss",
    "dnn_IsMobile": "False",
    "language": "en-US",
    "__RequestVerificationToken": "HH5XcajMmZxIhLdoVU7RUA9gME9_Br9lsZXtx09-yL5aWRkHLlkOP9QHOVCM4B9wYDHjNg2",
    "_ga": "GA1.2.703699783.1742195196",
    "_gid": "GA1.2.996559705.1742289671",
    "_clck": "15hk0rm%7C2%7Cfub%7C0%7C1902",
    "_ga_L57VM439KX": "GS1.1.1742289669.3.0.1742289671.0.0.0",
    "_clsk": "1ea54rj%7C1742289673075%7C1%7C1%7Cq.clarity.ms%2Fcollect",
    "cf_clearance": "g3LNT9MypI6QPl8XjOh2LfgRK76_GXGuDUQnJqu0ITU-1742289672-*******-Us7uBO.fiNqi0Y7.MYnw4Epaawc07bkrEFErVjxUJvCR2vkolXp8znJYYssT0x7OgwvcNpedNRbvCd_pxhzM6NtJp9k4oRBAeJ.Qg11oj7iHZNmGxgfz4vlkMcSyWB5VHkqQbInx0jVY7a7wkpnfWma1UftD5w7yOmJh2Kg3EubC9CkCf14ZPU4fhcdzujtqv7mw.hfgSY.gi_NIsGlsW7io70zDSA6smwes30KufdhacP7x.pKmuD.k5H1t.AXimawZIG12E72h91gdNpzUAQherY6nmKUg1OhyUtwH1Pi1UCLpo.BKbPD1RbSynOXBw7vLPh79mRMdPxYEz144kwHje2EOWl_ANWdIMQ7TOJpzbHcAo8JBBwR_NnDWNgFzYqSmnPm2D1zS3e4uFDyq00DFHqLqw1W4l5ByaE9FRGs"
}

def fetch_data():
    """发送请求获取数据"""
    url = "https://www.ncsl.org/financial-services/cryptocurrency-digital-or-virtual-currency-and-digital-assets-2025-legislation"
    data = {
        "d6685880d3bad24c1d29c9abc422099bada2f55d1fe1bb02de360d6d5752c918": "lWTQEscYLXZYMPUSCfsxMgqwFPmVmQsn5AKsgUA..W4-1742289656-*******-aB7S1CBjo_Vv3geUcsSBLuhXbvBbOlT9Mqf6vrYTzfngUggwjySA06zHlz9nn9ztINV3R4s4HD374SXu11ETtStUGZdFCXuLCBJdXMRG5qNZpgoJnaWBpr0wqPW0R5zXT8_MGAi7KeSe.xhJhlIJCqsUyZenGqrcQhJ6fwowxoK3q0now1aHdD7mN8hLJzs24JrUzgNPtOl6PEBpmkW1zzRHSbRUfUm2WZJLotU2cm1IldSFDy5JAM8Aq5ycWTY8ID2xw8GmOlk7MBc.YkC.hQ1Whj0HJIr8JxJy7OD7gwEAVc.zBUhmoVs2.RY0vCynETwStQ85Dxbi.l59UK5X31JzTboXWKOt9Ec4yXFB7RQbm2pe1soFeCwd.L0c6XA6vlwKK5XXZ6Ea842r6WHSW49iPygRMl6j50pA.EedaSfnOgOhgKsJ6oSajshw_npocBOB3EOl.UBN4jQ5nL1ZZcU0zwC6PPNPadQMuLThYY4pnSna89w8EGg_Wz1h3.o2qmJ6cUgfG.*****************************.2_AKgTaeYOu8U5cyDvBRtsGAED96Kx20Ndi7OlcaQ.1d8WOA3KWBP1P.qg1Omy.xlJoDLt4HcfUeIxuU_fHspNmoS8.hCUotRwNfZnRAn.7cZJyJAyTUaYusKa.wX5uZF5M6ZNoZWJdFt11Cgltt2ZOb83Fesz7tOvVdPV7YZm96k4ctBxT8B.lMBM77YjzaVl7Z_7PPtEvkq0pGzS_gqvHS.YVwOehAgpL2YmRZq30mmo6MdQB2AVOgHE8pS85FEj_RUVvLAGjPkBqCpTSFItigyk4hT1JV6QPVTajFOpYd1glLn5qc0ZabpRknT_J63F32dyyC4M62ynE4pSgIY1qzhGi8tlNILq.CE.U1EbHBinjj4gAdlyPKjmxqjb5tYHoo6mPJN2M4UBw9IeQegD.FVV2uWTQVljdrHHlWcG3VO_UDbzbROmC9KL2RqmT3Fc6mQM5e631pVJRdQcXpahluI5NOviLs.MpvjEfopAton10lqMGAgAtN5aVkotbgxSnpcR7R8UHbNRpmnTwrjYsKFkyGxHeUgKdFCaWBld95awGAgoZreK3hx6aEq8xojc_1Ci3nm1izYabh0Jq2EavMo8ur4EpMzKkK._.TcbfNgjSF.zXc1iBzP0zol_SNVebqGnvNN33PtSXJks3uAMmX0_ACd5lgRkCSl2gH1W9chmZ0kIj8uWS02Q6AIQzpsIwzyZe8UYSyFN8PONgs5qTH3EomhjmvuxOaL8w6ilp9ZIkt7LC3g1DkCwQe2YNClwkqUnnaAWy_U.UIBl_GfsAxSxhF8q.qLGQiaGfjpHwgz0_TlOfVihmHYVVxTepJHccqvI5MYTtsHSPUgyekUPqm",
        "62b3e6cdfeb9f67cceeacd79cb52de6768865059efa1d647c4f8e5a7aa28ea29": "IKHLWeCuGBl9bcq8I_WShjuUA28CU7lFdMIiPEHdy2o-1742289656-*******-POOjl4MetY8JxjV26eXRN3wl.0AbOw7DQ3va.lbsddeODbyHP3HSaL8uNKNbq55LoJjpqd7FHTa131pOBDNMO0_EGUNffib6Qqj2i6aagR8W6AIxyLI1wUUVPgfposfXPY1Envzgo9KAetajfMOebhjWHL4XpSNy9SzXvLRbZeoCY77PiWiIPQG8cJdIvfIlslfW7HPIqnsyLxS5mLY6oeU5LzcIfK8EdHTcThIPRiGUi3Grv7TDls5SXJ3z4NapoUbe0fQilh9cUOaJuapbLH6loyOqU5h1_tCL3YqDEuwhIeA4cPIU6cCNYmPFrTruT6ERJchKAh5AFaIAfs9tRVoOryuGHjrG43kpgSV20qTX1zP5mE1qLTxIar05MV3cFYeQrFJkSTTV5lmrGRXYdLTVOSBdVlcCP.BI0qPxHXHiI6KfYUE.k7CnHRhVhzXdw3YFBIfBs_6t8McXTq6OMtsX8NeIR1LY288knSEQdpXfY01XAgdDbjX9TJNWZ6G83TxHlz_7BlNEF1QzotwOoCKHNfSENTWP7.G615PY1WwO96MJxB5aDtvxpBy7XGEU2KaEKum.xKGJ8Am6_PEsIGxin7CPjCaV5LKPYwpqsPOdg9NMzE2JwmnSiCcSaUWYE8rSoGmdnRjDF2Ju9CPr.WzHzcFVLcf9SYC7rKRHQmEhTspg.b316YfL9ak2Y3aSkuG6g8Vo40LnVFQ8ZhIf07rOhNjTS9HP8q6IhBpB8S1g2_AwySeI6Jx5NurmfPqqRtUQXqu21SxqFmZXnVc7T3s2a30RfUcvB6XieAnMbQR04Tw51pTPxjs6pOyYoOZEO_MdLnL3rgkxwjZOt1HqZHkQooIcwi_0jPOl5VmdUkudNysoB_qUDip7UDcM13XA80o4x7xrmt4B4Bh6IpANodcPhEfLll8m0RS.vWW8ZRDtJLjZW0ziBQLPb134JxQSJ1pwuEwQyctYBUcYCXsP1MTWWI3YJY1sFBvmXg5UpDPZ3wA5iTOYE_AZomq5OvignQF5DpuaIzrPdJwxhQCTpzWJCS9x6mnnuUtqE_bY1.Yvazv33d5qobimrkHSoqE3WQ7lgp50Bsc.qxpxd5ZrYuCmAOBsgotGCjR2u0sh4OWH0nnrHSgVNSgyAaD5SLrKsCwXp3LAmkEPeW2wzkSQNXyTlvuGJf6Bd8fXfTRRL4yelRhCtwDdf2ii3NQZQnhq5U6_qgZJM4qO19ef.bveI.xWLwDRJiFwwD83r0Y5yZh9BkUyGxm1eYXZZpi2zADvVtbGVaUK4ZsM5y0MfmKw2ZTnck9DbFOYrSCacs0FFl5E3.j6WWMZ4c6HuzpPodjWh6R4hRclrU5.KGFX7DdYQsKKHwpmOmzVtsZ5Hbx_aI9vhJB7KZhA3NHtQ2GkFSOFSPU16uXf.tVSD3K2HIb.eqWBOTZB3yaWWcOf_G1eX55XqR1qtRkVAYL6HFrkluDR7VrmuJoGFg.DLOXhiBSR_IZhab.Aoyo6jEwgJeg5Os8MibUf9g5puda4fwWzVuheZENeHjXKEaDf.knCaoePpIIoNU72n4oPe4y7fk8RkJQu3i8Q1ZYFqgz6u1zYXydJraei5f6EwfpTMIi4b8WvnO970eZl1Uyj5.tzOBiaGvvhEh7T8PyrP7ldVMqFl3FUfCVQLlX9.LRBG.R8FwOPskT0g_4AqrQLCMDXnXecgBhTSo5IeFxHipR3POnuV2uqngSrEjhZBk_zWMFHkyPDwuu9Syt.PnfjwOIM.moYypofnXvFoK4gld7eW.SadnH.zS4o6LUpsYf6j0wZzD52RxstomYH2tQg1ce5mE4FAUOtjX0J8OU.rPOSPfY.GQm818P.VeTh0gvea2Gg4EqGivc5wx06USSaeXFOqrWfxWwxBfs3ke1iDfUZx_W6v3CPvXFNbMFmYqoI69tNihKAx2_U4ZHnSRHQJXMS9W6XLtH..2S7GadyWnBLoQ1meeRBB2rPIvAcv4GpXOSQWUpe4ASLYedzIewb1GVS8Tmaf45FrfTQSd3xcO5MgOvchwGLmSjzY1k0bB5JIC_ZH2BItd8AI_L6.ZIB9r6WqajIjUG2BwLIsvd1fTbuEECFXSmb48X5UgJdq3nPjGa81Eq0GuIV_gst_Aj3VTRJR_rqfIi9x8aXiu42IvbDvQUs2uhdwUgXF_WA6cKH65AZlsGrZKkqkuV.Lod0s5grd3vLylMPxW3dzQbVrsHsej931KWSKTr50HXxPR1wNBdWHNNrtyIFU_rxj6taD0joL1zqg2FJm_OOnTpNCivr1y4er6lZjVHEOi7ZsYQBV5I6IJAx3BMVqJbKmTsnbaSUVMce4G2jknd88QLBJ7icUzH7QL5fHieYNcS6V9IZAiBrms.5g_Db2tx5KR.ldswa7y00PcHLps2klMRlSBX1sGjPr1evxyFuWNWb3wcOAV_OM2iweAZHbmBX2TZjFX5aUsac4MeOGqSUF5hqRKRhIWOxRC2zDSONk6RVtC1OT7mE2x47Bzx3EBWgzHeXjP577ojojbBiuuhDBCIADdOuoPrPiL.cQ.6zmCqvWHuaAyq40xia_8DbrtmUsuypLqIBkfRUjC3Njo3FzFbtKaAStLT2JdkH8cjHZrWqjivZx676EEgG5R.snBc.f.ZtwMIbUx8IXNHqxLzF0LV67fv_L__RzidHKvOV2SqZCuRJM6enkRrieqsq2Ah7J9IqU8scvGT7kQKNl0yg7uzQc3NSlRMQJnPP9vgINPXbuDZY95.a000RhQifFW5NL3sNdfy5lmpefSwUR2EnwOW31ofOhf5x1DwigYNTE06BlpgNgjvTZBuRyNTStq0iNYnnvlHnShpHUddg.TKUrlvQXd2hd_Y8lB_KyZLLQiChhuHhZriPH1sVpr5Zsjp17rXZTp1iq957Y1jcfbG794cwauIV3mthL7AISr3Fu7jhY9mGFDC65x0VQehBtjCRSrR4UVvPaGWMpJgmn0iKalgcyF8tPffeUNSwm6a3FBTDPXAw5iO.AeWO3t5eKu9jLATkViB_y4axLf.bVNLnFQEal7Pda2eHRIo2QbAusPQHdCLBgHkSY0QaLFDCqfu7mb0XYTc.rmSmbP8TBsQMB._wCQ.rnS1Jl_LA4eFJpl14jqJmwExZ5w2MJS6v3Eaa8bFxehOti64ySLuAIb8dRJBjgC5Fs8h1.L2p4wQWlzGG3w29V9W2vGF7FwbcSZ9ifSUX_rX5wGW3TqYYuGOi2QgYvuLWscaH5tcG_b40cLrubQn_w_9bT3PDyQJbQv.UPFh2qS5lkvO_mZSh42apYypNeOssVP6qSzTL.KXcUDDs232MumbSPtytBCoP.H720v.FQioW.RCxg373dTeJGSG84x5XR1er_amjEIvO.cquDKAoSBrc6HTY36BRjMMR7DqcxT5VG2aTYYGnGnnBFtuLYV8jx9P8WOmoq2XSU7YLM387LLM5qTtKMW3HyvoOKQ.K9Sbms0ZGRGmsNRKMNNmx1NiQcDt4oJtr86.TSla.b6fDt0lnhGLEfBIaAIot67cLa7BIFPuJjn3PfpywQ5OwPSGQqmR90ddq69DuaHA.9S_UwCtGjj3IinHKSLMyg6JoCem5gnDbyL..z0lCYaujmmrVEQzw9SHIlNj_1SjygkqyCUZXD6Xh2S8L.9aB4IH5J6ha9oUPDsYD929XJVHU1SdM6FYtCYqm4CYUdTE8uNmfNYTKEpKaB_LpXux4IMRZUWWQZqfFrZKV1wMW.TH1KFMHVtf5GZOiZ3598FLGsteqv5KqFQgx6NEnLRvpbMR5Q.7xHNwtW6dcN4ojYsQ92yuzONyk3UA0oHD_BSS2IeqCd4TxG6rUJv1Q4i7HhzbqhyZOSYsUGTY",
        "8e614e614b998e97187c584cd818b898d208888e0e34e33cd8de591746648c61": "bu9wKFp7PmjS.HJMlfE5fpG5wzmsScV8Lt_j_pRPA24-1742289662-*******-1wT63kzFjG3PFyhexRTc5J9mYcpM5E.nv3itTUOZZ_g3C_YjFhklz8oS8OAB4zpd0wc9VUG3C8zaZYh1tR7L6fzH55haPNmfaVIp2FlkyyE4tuzn3rjkSQ4fGqxLW48eZXeLaih2an4lEqnPElvSIjh5IMgKM0MXM8RUJyOMgvxLVeUFDOSQMKcPmfGvDP4H"
    }
    
    try:
        response = requests.post(url, headers=headers, cookies=cookies, data=data)
        if response.status_code == 200:
            logger.success("请求成功")
            return response.text
        else:
            logger.error(f"请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"请求发生错误: {e}")
        return None

def parse_data(html_content):
    """解析HTML数据"""
    try:
        tree = html.fromstring(html_content)
        
        # 获取所有行
        rows = tree.xpath('//*[@id="bills"]/tbody/tr')
        data_list = []
        
        for row in rows:
            try:
                # 提取每列数据
                jurisdiction = row.xpath('./td[1]/p/text()')
                jurisdiction = jurisdiction[0].strip() if jurisdiction else ""
                
                # 提取第五列的特殊数据
                summary = row.xpath('./td[5]//p/text()')
                summary = ''.join(summary).strip() if summary else ""

                bill_number = row.xpath('./td[2]//text()')
                bill_number = ''.join(bill_number).strip()
                
                bill_href = row.xpath('./td[2]/p/a/@href')
                bill_href = bill_href[0] if bill_href else ""
                
                bill_title = row.xpath('./td[3]//text()')
                bill_title = ''.join(bill_title).strip()
                
                bill_status = row.xpath('./td[4]/p/text()')
                bill_status = bill_status[0].strip() if bill_status else ""


                
                data_dict = {
                    'Jurisdiction': jurisdiction,
                    'Summary': summary,
                    'Bill_Number': bill_number,
                    'Bill_Href': bill_href,
                    'Bill_Title': bill_title,
                    'Bill_Status': bill_status
                }
                data_list.append(data_dict)
                logger.info(f"成功提取数据: {jurisdiction} - {bill_number}")
                
            except Exception as e:
                logger.warning(f"提取单行数据时出错: {e}")
                continue
        
        return data_list
    except Exception as e:
        logger.error(f"解析数据时出错: {e}")
        return []

def save_to_csv(data_list, filename="bills_data.csv"):
    """保存数据到CSV文件"""
    try:
        df = pd.DataFrame(data_list)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        logger.success(f"成功保存 {len(data_list)} 条数据到 {filename}")
    except Exception as e:
        logger.error(f"保存数据时出错: {e}")

def main():
    """主函数"""
    # 设置日志
    logger.add("spider.log", rotation="500 MB")
    
    # 获取数据
    html_content = fetch_data()
    if not html_content:
        return
    
    # 解析数据
    data_list = parse_data(html_content)
    if not data_list:
        return
    
    # 保存数据
    save_to_csv(data_list)

if __name__ == "__main__":
    main()