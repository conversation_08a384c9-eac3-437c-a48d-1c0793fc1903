import requests


headers = {
    "authority": "freemanlaw.com",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}
cookies = {
    "_gid": "GA1.2.23543285.1737600777",
    "_gat_gtag_UA_76777249_1": "1",
    "_ga_QM5D36JQKM": "GS1.1.1737600777.1.1.1737602592.0.0.0",
    "_ga": "GA1.1.1930919311.1737600777"
}
url = "https://freemanlaw.com/cryptocurrency/andorra/"
response = requests.get(url)

print(response.text)
print(response)