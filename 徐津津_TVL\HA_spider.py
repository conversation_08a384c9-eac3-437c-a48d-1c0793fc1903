#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取SOL链上代币的TVL（Total Value Locked）数据
接口: /api/v1/dex/market/coin-tvl
支持多线程并发处理
"""

import requests
import json
import sqlite3
import time
import sys
import threading
from queue import Queue
from datetime import datetime

if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库路径
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "okx_gmgn_ha_TVL"  # 表名

# API配置
API_URL = "https://valuescan.ai/api/v1/dex/market/coin-tvl"
HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

# 线程和请求配置
THREAD_COUNT = 2  # 线程数
REQUEST_INTERVAL = 1.0  # 请求间隔秒数

# 创建线程锁，用于同步输出和数据库操作
print_lock = threading.Lock()
db_lock = threading.Lock()

# 创建统计计数器
success_counter = 0
total_counter = 0


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with print_lock:
        print(*args, **kwargs)


def get_token_addresses_from_db():
    """从数据库中获取所有Token地址"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # 获取Token地址
        cursor.execute(f'SELECT rowid, "Token Address" FROM {DB_TABLE}')
        rows = cursor.fetchall()

        # 提取行ID和地址并过滤空值
        token_data = []
        for row_id, address in rows:
            if address:  # 确保Token Address不为空
                token_data.append({
                    "row_id": row_id,
                    "token_address": address
                })

    except sqlite3.Error as e:
        thread_safe_print(f"从数据库获取Token地址时出错: {e}")
        token_data = []

    conn.close()

    thread_safe_print(f"从数据库获取到 {len(token_data)} 个Token地址")
    return token_data


def update_tvl_in_db(row_id, token_address, tvl_value):
    """更新数据库中的TVL值"""
    # 使用线程锁确保数据库操作的线程安全
    with db_lock:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            # 更新数据库
            cursor.execute(
                f'''
                UPDATE {DB_TABLE}
                SET "data HA" = ?
                WHERE rowid = ?
                ''',
                (tvl_value, row_id)
            )

            if cursor.rowcount > 0:
                thread_safe_print(f"成功更新 {token_address} (rowid: {row_id}) 的TVL值: {tvl_value}")
                conn.commit()
                return True
            else:
                thread_safe_print(f"未找到记录 (rowid: {row_id})")
                conn.rollback()
                return False

        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库时出错: {e}")
            conn.rollback()
            return False

        finally:
            conn.close()


def get_tvl_for_token(token_address):
    """获取单个代币的TVL数据"""
    # 准备请求数据
    request_data = {
        "chainName": "SOL",
        "tokenContractAddress": token_address
    }

    thread_safe_print(f"请求代币 {token_address} 的TVL数据...")

    try:
        # 发送请求
        response = requests.post(
            API_URL,
            headers=HEADERS,
            json=request_data,
            timeout=30
        )

        # 检查响应状态码
        if response.status_code != 200:
            thread_safe_print(f"请求失败，状态码: {response.status_code}")
            thread_safe_print(f"响应内容: {response.text}")
            return None

        # 解析响应数据
        result = response.json()

        if result.get("code") == 200 and "data" in result:
            # 提取TVL值
            tvl = result["data"].get("tvl")
            if tvl is not None:
                thread_safe_print(f"获取到TVL值: {tvl}")
                return tvl
            else:
                thread_safe_print("响应中没有TVL数据")
                return None
        else:
            thread_safe_print(f"请求失败: {result.get('msg', '未知错误')}")
            return None

    except requests.exceptions.RequestException as e:
        thread_safe_print(f"请求异常: {e}")
        return None
    except json.JSONDecodeError:
        thread_safe_print(f"JSON解析失败: {response.text[:200]}...")
        return None
    except Exception as e:
        thread_safe_print(f"获取TVL数据时出错: {e}")
        return None


def worker(token_queue, thread_id):
    """工作线程函数，处理队列中的token"""
    global success_counter, total_counter

    thread_safe_print(f"线程 {thread_id} 开始运行")

    while not token_queue.empty():
        try:
            # 从队列获取一个token数据
            token_data = token_queue.get()
            row_id = token_data["row_id"]
            token_address = token_data["token_address"]

            # 更新计数器
            with print_lock:
                total_counter += 1
                current_count = total_counter

            thread_safe_print(
                f"\n线程 {thread_id} 处理第 {current_count}/{token_queue.qsize() + current_count} 个代币: {token_address}")

            # 获取TVL数据
            tvl_value = get_tvl_for_token(token_address)

            if tvl_value is not None:
                # 更新数据库
                if update_tvl_in_db(row_id, token_address, tvl_value):
                    with print_lock:
                        success_counter += 1
                        current_success = success_counter

                    thread_safe_print(f"线程 {thread_id} 已更新代币 {token_address} 的TVL值: {tvl_value}")
                    thread_safe_print(
                        f"当前进度: {current_success}/{current_count} ({current_success / current_count * 100:.2f}%)")

            # 标记任务完成
            token_queue.task_done()

            # 请求间隔，避免请求过于频繁
            time.sleep(REQUEST_INTERVAL)

        except Exception as e:
            thread_safe_print(f"线程 {thread_id} 处理任务时出错: {e}")
            token_queue.task_done()

    thread_safe_print(f"线程 {thread_id} 已完成所有任务")


def process_tokens_multi_thread(token_data_list):
    """使用多线程处理所有代币"""
    # 创建任务队列
    token_queue = Queue()

    # 将所有token数据添加到队列
    for token_data in token_data_list:
        token_queue.put(token_data)

    total_tokens = token_queue.qsize()
    thread_safe_print(f"总共 {total_tokens} 个Token将被处理，使用 {THREAD_COUNT} 个线程")

    # 创建并启动工作线程
    threads = []
    for i in range(THREAD_COUNT):
        t = threading.Thread(target=worker, args=(token_queue, i + 1))
        t.daemon = True  # 设置为守护线程，主线程结束时会自动结束
        threads.append(t)
        t.start()

    # 等待所有任务完成
    token_queue.join()

    # 等待所有线程结束
    for t in threads:
        t.join()

    return success_counter


def main():
    """主函数"""
    thread_safe_print("=" * 60)
    thread_safe_print("SOL代币TVL数据获取工具 (多线程版)")
    thread_safe_print("=" * 60)
    thread_safe_print("数据库路径:", DB_PATH)
    thread_safe_print("数据库表名:", DB_TABLE)
    thread_safe_print("API URL:", API_URL)
    thread_safe_print("线程数:", THREAD_COUNT)
    thread_safe_print("=" * 60)

    # 获取Token地址
    token_data_list = get_token_addresses_from_db()

    if not token_data_list:
        thread_safe_print("未找到任何Token地址，请检查数据库")
        return

    # 多线程处理代币
    success_count = process_tokens_multi_thread(token_data_list)

    # 输出结果
    thread_safe_print("\n更新完成!")
    thread_safe_print(f"总计: {len(token_data_list)} 个Token")
    thread_safe_print(f"成功更新数据库: {success_count} 个")


if __name__ == "__main__":
    thread_safe_print("开始获取SOL链上代币的TVL数据...")
    start_time = time.time()

    try:
        main()
    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        import traceback

        thread_safe_print(f"程序运行出错: {e}")
        thread_safe_print(traceback.format_exc())

    end_time = time.time()
    duration = end_time - start_time
    thread_safe_print(f"程序运行耗时: {duration:.2f} 秒")
    thread_safe_print("程序运行结束")
