#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GMGN代币价格变化WebSocket数据获取工具
通过WebSocket接口获取SOL链上代币的价格数据和涨跌幅
每个代币单独连接、获取数据、断开连接
"""

import asyncio
import json
import sqlite3
import time
import sys
import threading
import random
from datetime import datetime
import aiohttp
from aiohttp_socks import ProxyConnector

if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库配置
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "Price_Difference"

# WebSocket配置
WS_URL = "wss://ws.gmgn.ai/quotation"
WS_PARAMS = {
    "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
    "client_id": "gmgn_web_*************-f0a534f",  # 使用与volume爬虫相同的版本
    "from_app": "gmgn",
    "app_ver": "*************-f0a534f",  # 使用与volume爬虫相同的版本
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a0adc6758070914b5d3cd4679349eed1",
    "os": "web"
    # 移除uuid参数，与volume爬虫保持一致
}

# GMGN认证信息 - 更新为最新的有效cookies
GMGN_COOKIES = {
    "_ga": "GA1.1.1787124478.1747114971",
    "cf_clearance": "KSH4ihVUhG6VRO_Cz3qGB0qvvrSS4WL4z9aHTrPC6R8-1748582435-1.2.1.1-q45OStB4Y0n6lRvyTgpfemIRCUHDOWX31bPL.HUPhyFdwroZICoKdXKgc2OrbNvzQr6SmbjUOSW_S5U4adtJP.lhmP.3ZfVkoQU4qE.Xg0F.vHuYveQYNeKaMyQ3b9GVOB51W0BsqbHb1OQf4U9OgmWOuB6do3DSF8uQtw5wsKlTYsG58AAZ72jBfHTKPt2pCZH4FgYnGBpZl1wqDL6ITxhN5m0AcwA1_LnSHsoMnUR_I6D4l60L4dGrBtlRgDNnFUKdP2VgV0CZhqzx1sXF8WAm.TWF5BIYRmWzF41DTaK7_1GaiQ0ELDeiL_YDbq7At3FL0jNu05ue3pkt1ArzTSfAWJyvc1iFIe9FFvxUin8",
    "_ga_0XM0LYXGC8": "GS2.1.s1748581408$o25$g1$t1748582442$j49$l0$h0"
}

# WebSocket请求头
WS_HEADERS = {
    "Upgrade": "websocket",
    "Origin": "https://gmgn.ai",
    "Cache-Control": "no-cache",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Pragma": "no-cache",
    "Connection": "Upgrade",
    "Sec-WebSocket-Key": "2YwcU2ayRHPA4lDAmBj05A==",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
    "Sec-WebSocket-Version": "13",
    "Sec-WebSocket-Extensions": "permessage-deflate; client_max_window_bits"
}

# 代理配置
PRIMARY_PROXY = "http://127.0.0.1:33210"
BACKUP_PROXY = "socks5://**************:30889"

# 连接配置
CONNECTION_TIMEOUT = 30
MESSAGE_TIMEOUT = 10
MAX_RETRIES = 3
RETRY_DELAY = 2

# 线程锁
PRINT_LOCK = threading.Lock()
DB_LOCK = threading.Lock()

# 全局统计
processed_count = 0
success_count = 0


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with PRINT_LOCK:
        print(*args, **kwargs)


def get_token_addresses_from_db():
    """从数据库获取Token地址列表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(f'SELECT rowid, "Token Address" FROM {DB_TABLE}')
        rows = cursor.fetchall()

        token_data = []
        for row_id, address in rows:
            if address and address.strip():
                token_data.append({
                    "row_id": row_id,
                    "token_address": address.strip()
                })

        thread_safe_print(f"从数据库获取到 {len(token_data)} 个Token地址")
        return token_data

    except sqlite3.Error as e:
        thread_safe_print(f"从数据库获取Token地址时出错: {e}")
        return []
    finally:
        conn.close()


def update_price_change_in_db(row_id, token_address, price_change_percent):
    """更新数据库中的价格变化数据"""
    with DB_LOCK:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            # 更新数据库
            cursor.execute(f'''
                UPDATE {DB_TABLE}
                SET "data GMGN" = ?
                WHERE rowid = ?
            ''', (price_change_percent, row_id))

            if cursor.rowcount > 0:
                conn.commit()
                thread_safe_print(f"✅ 成功更新 {token_address} - 涨跌幅: {price_change_percent:.6f}")
                return True
            else:
                thread_safe_print(f"❌ 未找到记录 rowid: {row_id}")
                return False

        except sqlite3.Error as e:
            thread_safe_print(f"❌ 更新数据库时出错: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()


def calculate_price_change(current_price, price_24h):
    """计算价格变化百分比: (p24h - p) / p"""
    try:
        p = float(current_price)
        p24h = float(price_24h)

        if p == 0:
            return 0

        # 计算 (p24h - p) / p
        price_change = (p24h - p) / p
        return price_change

    except (ValueError, ZeroDivisionError) as e:
        thread_safe_print(f"❌ 价格计算错误: {e}")
        return 0


def build_ws_url():
    """构建WebSocket URL"""
    param_str = "&".join([f"{key}={value}" for key, value in WS_PARAMS.items()])
    return f"{WS_URL}?{param_str}"


async def connect_single_token(token_data, proxy_url=None):
    """
    连接单个代币的WebSocket，获取数据后立即断开
    """
    token_address = token_data["token_address"]
    row_id = token_data["row_id"]

    thread_safe_print(f"📡 开始连接代币: {token_address}")

    # 创建连接器
    connector = None
    if proxy_url:
        thread_safe_print(f"🔗 使用代理: {proxy_url}")
        try:
            if proxy_url.startswith("socks5://"):
                connector = ProxyConnector.from_url(proxy_url)
            else:
                connector = ProxyConnector.from_url(proxy_url)
        except Exception as e:
            thread_safe_print(f"❌ 创建代理连接器失败: {e}")
            return False
    else:
        thread_safe_print("🔗 直连模式")
        connector = aiohttp.TCPConnector()

    try:
        # 创建cookie jar并添加认证cookies
        cookie_jar = aiohttp.CookieJar()
        for name, value in GMGN_COOKIES.items():
            cookie_jar.update_cookies({name: value})

        async with aiohttp.ClientSession(connector=connector, cookie_jar=cookie_jar) as session:
            ws_url = build_ws_url()

            # 连接WebSocket
            async with session.ws_connect(
                    ws_url,
                    headers=WS_HEADERS,
                    timeout=aiohttp.ClientTimeout(total=CONNECTION_TIMEOUT)
            ) as ws:
                thread_safe_print(f"🔌 WebSocket连接建立成功: {token_address}")

                # 等待连接稳定
                await asyncio.sleep(1)

                # 构建订阅消息
                subscribe_message = {
                    "action": "subscribe",
                    "channel": "token_activity_info_all",
                    "id": f"req_{int(time.time())}_{random.randint(1000, 9999)}",
                    "data": [{
                        "chain": "sol",
                        "addresses": token_address
                    }]
                }

                # 发送订阅消息
                await ws.send_str(json.dumps(subscribe_message))
                thread_safe_print(f"📤 已发送订阅消息: {token_address}")

                # 等待并处理消息
                start_time = time.time()
                while time.time() - start_time < MESSAGE_TIMEOUT:
                    try:
                        msg = await asyncio.wait_for(ws.receive(), timeout=5.0)

                        if msg.type == aiohttp.WSMsgType.TEXT:
                            try:
                                message_data = json.loads(msg.data)
                                thread_safe_print(f"📥 收到消息: {token_address}")

                                # 处理消息数据
                                if await process_message(message_data, token_data):
                                    thread_safe_print(f"✅ 数据处理完成: {token_address}")
                                    return True

                            except json.JSONDecodeError as e:
                                thread_safe_print(f"❌ JSON解析错误: {e}")

                        elif msg.type == aiohttp.WSMsgType.ERROR:
                            thread_safe_print(f"❌ WebSocket错误: {ws.exception()}")
                            break

                    except asyncio.TimeoutError:
                        thread_safe_print(f"⏰ 等待消息超时: {token_address}")
                        continue

                thread_safe_print(f"⏰ 消息接收超时: {token_address}")
                return False

    except Exception as e:
        thread_safe_print(f"❌ WebSocket连接失败 {token_address}: {e}")
        return False


async def process_message(message_data, token_data):
    """处理接收到的WebSocket消息"""
    try:
        # 检查消息类型
        if message_data.get("channel") == "token_stat" and "data" in message_data:
            data_list = message_data["data"]

            for token_info in data_list:
                token_address = token_info.get("a")  # 代币地址
                current_price = token_info.get("p")  # 当前价格
                price_24h = token_info.get("p24h")  # 24小时前价格

                # 检查是否是目标代币
                if token_address == token_data["token_address"]:
                    if current_price and price_24h:
                        # 计算价格变化
                        price_change = calculate_price_change(current_price, price_24h)

                        thread_safe_print(f"📊 代币: {token_address}")
                        thread_safe_print(f"📊 当前价格: {current_price}")
                        thread_safe_print(f"📊 24h前价格: {price_24h}")
                        thread_safe_print(f"📊 涨跌幅: {price_change:.6f}")
                        thread_safe_print("-" * 50)

                        # 写入数据库
                        if update_price_change_in_db(token_data["row_id"], token_address, price_change):
                            global success_count
                            success_count += 1
                            return True
                        else:
                            return False
                    else:
                        thread_safe_print(f"❌ 价格数据不完整: {token_address}")
                        return False

        return False

    except Exception as e:
        thread_safe_print(f"❌ 消息处理错误: {e}")
        return False


async def process_token_with_retry(token_data):
    """带重试机制的代币处理"""
    global processed_count

    token_address = token_data["token_address"]

    # 尝试不同的连接方式
    proxy_configs = [
        PRIMARY_PROXY,  # 首先尝试主代理
        BACKUP_PROXY,  # 然后尝试备用代理
        None  # 最后尝试直连
    ]

    for attempt in range(MAX_RETRIES):
        thread_safe_print(f"🔄 第 {attempt + 1}/{MAX_RETRIES} 次尝试: {token_address}")

        for i, proxy_config in enumerate(proxy_configs):
            proxy_name = f"代理-{i + 1}" if proxy_config else "直连"
            thread_safe_print(f"🔄 尝试使用 {proxy_name}: {token_address}")

            try:
                success = await connect_single_token(token_data, proxy_config)
                if success:
                    thread_safe_print(f"✅ 使用 {proxy_name} 成功处理: {token_address}")
                    processed_count += 1
                    return True
                else:
                    thread_safe_print(f"❌ 使用 {proxy_name} 处理失败: {token_address}")

            except Exception as e:
                thread_safe_print(f"❌ 使用 {proxy_name} 连接异常 {token_address}: {e}")
                continue

        # 重试间隔
        if attempt < MAX_RETRIES - 1:
            thread_safe_print(f"⏳ 等待 {RETRY_DELAY} 秒后重试: {token_address}")
            await asyncio.sleep(RETRY_DELAY)

    thread_safe_print(f"❌ 所有尝试均失败: {token_address}")
    processed_count += 1
    return False


async def process_all_tokens():
    """处理所有代币"""
    # 获取所有代币地址
    all_tokens = get_token_addresses_from_db()

    if not all_tokens:
        thread_safe_print("❌ 未找到任何Token地址")
        return 0, 0

    total_count = len(all_tokens)
    thread_safe_print(f"📋 总共需要处理 {total_count} 个代币")

    # 逐个处理代币
    for i, token_data in enumerate(all_tokens):
        thread_safe_print(f"\n{'=' * 60}")
        thread_safe_print(f"📍 处理进度: {i + 1}/{total_count}")
        thread_safe_print(f"📍 当前代币: {token_data['token_address']}")
        thread_safe_print(f"{'=' * 60}")

        # 处理单个代币
        await process_token_with_retry(token_data)

        # 进度显示
        thread_safe_print(f"📊 已处理: {processed_count}/{total_count}, 成功: {success_count}")

        # 添加间隔，避免请求过于频繁
        if i < total_count - 1:
            delay = random.uniform(1, 3)
            thread_safe_print(f"⏳ 等待 {delay:.1f} 秒后处理下一个代币...")
            await asyncio.sleep(delay)

    return total_count, success_count


async def main():
    """主函数"""
    thread_safe_print("=" * 80)
    thread_safe_print("🚀 GMGN代币价格变化WebSocket数据获取工具")
    thread_safe_print("📡 通过WebSocket接口获取SOL链上代币的价格数据和涨跌幅")
    thread_safe_print("🔄 每个代币单独连接、获取数据、断开连接")
    thread_safe_print("=" * 80)
    thread_safe_print(f"🌐 WebSocket URL: {WS_URL}")
    thread_safe_print(f"💾 数据库: {DB_PATH}")
    thread_safe_print(f"📋 数据表: {DB_TABLE}")
    thread_safe_print(f"🔗 主代理: {PRIMARY_PROXY}")
    thread_safe_print(f"🔗 备用代理: {BACKUP_PROXY}")
    thread_safe_print("=" * 80)

    start_time = time.time()

    try:
        total_count, success_count = await process_all_tokens()

        thread_safe_print("\n" + "=" * 80)
        thread_safe_print("🎉 处理完成！")
        thread_safe_print(f"📊 总计处理: {total_count} 个代币")
        thread_safe_print(f"✅ 成功更新: {success_count} 个代币")
        thread_safe_print(f"📈 成功率: {success_count / total_count * 100:.2f}%" if total_count > 0 else "成功率: 0%")

    except KeyboardInterrupt:
        thread_safe_print("\n⛔ 程序被用户中断")
    except Exception as e:
        thread_safe_print(f"❌ 程序运行错误: {e}")
        import traceback
        thread_safe_print(traceback.format_exc())

    end_time = time.time()
    thread_safe_print(f"⏱️ 总耗时: {end_time - start_time:.2f} 秒")
    thread_safe_print("=" * 80)


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())