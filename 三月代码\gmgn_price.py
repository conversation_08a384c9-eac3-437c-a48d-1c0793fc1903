from curl_cffi import requests
import time
import random
import csv
import json
from loguru import logger


class GMGN_PRICE_SPIDER:

    def __init__(self):
        self.url = "https://gmgn.ai/vas/api/v1/token_trades/sol/3oMx1cnFNVuiK9D4QRB2igXvacYNRjVpdw5oEyHyDDDD"
        self.headers = {
            "authority": "gmgn.ai",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "referer": "https://gmgn.ai/sol/token/3oMx1cnFNVuiK9D4QRB2igXvacYNRjVpdw5oEyHyDDDD",
            "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
            "sec-ch-ua-arch": "\"x86\"",
            "sec-ch-ua-bitness": "\"64\"",
            "sec-ch-ua-full-version": "\"114.0.5735.199\"",
            "sec-ch-ua-full-version-list": "\"Not.A/Brand\";v=\"8.0.0.0\", \"Chromium\";v=\"114.0.5735.199\", \"Google Chrome\";v=\"114.0.5735.199\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-model": "\"\"",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-ch-ua-platform-version": "\"15.0.0\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        }
        self.params = {
            "device_id": "1713e771-6640-405d-8c71-fe483feeb742",
            "client_id": "gmgn_web_2025.0325.100758",
            "from_app": "gmgn",
            "app_ver": "2025.0325.100758",
            "tz_name": "Asia/Shanghai",
            "tz_offset": "28800",
            "app_lang": "zh-CN",
            "limit": "50",
            "maker": "",
            "cursor": "MzI4NzcxMzI2MTExNjAwMDM="
        }
        self.cookies = {
            "_ga": "GA1.1.566489715.1737427304",
            "__cf_bm": "BfYFmhpjWBoEZP57LNAyZ54UapozMRXNodbuSjkXoSQ-1742886711-*******-eMNjZTBVRmRz8di9lSDGypAiAsJWsFK4fw6Jn1gjRm7xMg6L9OG4BpRgOXTSmW0i6QighdZmcNeXnMnPgz3HcpKpFIA3V9WEv2EIpX2jhIo",
            "_ga_0XM0LYXGC8": "GS1.1.1742886712.16.0.1742886712.0.0.0",
            "cf_clearance": "VsNqwqo2WiYDD_HmerAfZh01g150Gu983CJAzbH3FKU-1742886713-*******-.WZSiXSZWKZCMH0SfVN9SrUAjMzBPKyCEBImSi9TlHVBtEOqO7Cm03CIP27v4LKoQLrVZoKoOgVTh9tmjRf.YbWe53IS_Y501.LAbgmoGV.Uvq6A6XBxNXcdOfiB1yc1m9.37tq18YlCiyEQ0SgZ1Ns9h1zmk5n2jDHBfIjA9q_eFtVqWTJbHUuGicKc2X6zUYzInZwQvZXQDp3X8nm1ykBtTKF36U1Ri26jLTcyrFThG9M47vNsNfnr_L5bLtuE8yxI2ls3gjY0HC48Mbs2u4QLVfwUhwcNHJ.Z8QoJK4RdG73zIeitsYJYRtusktMH41BeqE4TdwYwEnAeqPagrE9F7ijGTC5hLHhScdQzOqs"
        }
        self.proxies = {
            "http": "http://127.0.0.1:33210",
            "https": "http://127.0.0.1:33210"
        }



    def fetch_data(self, max_retries=3, retry_delay=5):
        """获取数据，添加重试机制
        Args:
            max_retries: 最大重试次数
            retry_delay: 重试等待时间(秒)
        """
        for attempt in range(max_retries):
            try:
                response = requests.get(
                    self.url, 
                    headers=self.headers, 
                    cookies=self.cookies, 
                    params=self.params, 
                    proxies=self.proxies,
                    timeout=30  # 添加超时设置
                )
                if response.status_code == 200:
                    logger.info(f"请求成功,状态码: {response.status_code}")
                    data = response.json()
                    return data
                else:
                    logger.error(f"请求失败,状态码: {response.status_code}")
            except Exception as e:
                if attempt < max_retries - 1:  # 如果不是最后一次尝试
                    wait_time = retry_delay * (attempt + 1)  # 递增等待时间
                    logger.warning(f"第 {attempt + 1} 次请求失败: {str(e)}, {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"已重试 {max_retries} 次仍然失败: {str(e)}")
        return None


    def parse_data(self, data):
        """解析数据"""
        data_parsed = []
        data_detail = data.get('data', {}).get('history', [])
        for item in data_detail:
            try:
                timestamp = item.get('timestamp', '')
                if timestamp:
                    if len(str(timestamp)) > 10:
                        timestamp = int(timestamp) / 1000
                    formatted_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
                else:
                    formatted_time = ''

                # 处理交易类型
                swap_type = item.get('event', '')
                if swap_type == 'buy':
                    swap_type_code = '1'
                elif swap_type == 'sell':
                    swap_type_code = '2'
                else:
                    swap_type_code = ''  # 处理异常情况
                    logger.warning(f"未知的交易类型: {swap_type}")

                data_parsed.append({
                    'gmgn_price_usd': item.get('price_usd', ''),
                    'gmgn_amount_usd': item.get('amount_usd', ''),
                    'gmgn_amount_base': item.get('base_amount', ''),
                    'gmgn_amount_quote': item.get('quote_amount', ''),
                    'gmgn_tx_hash': item.get('tx_hash', ''),
                    'gmgn_time': formatted_time,
                    'gmgn_swap_type': swap_type_code,
                })
            except Exception as e:
                logger.warning(f"处理单条数据时出错: {e}, 数据: {item}")
                continue
            
        logger.info(f"成功解析 {len(data_parsed)} 条数据")
        # 输出第一条数据作为样例
        if data_parsed:
            logger.debug(f"数据样例: {data_parsed[0]}")
        
        return data_parsed


    def get_next_page(self, data):
        """获取下一页数据"""
        next_page = data.get('data', {}).get('next', '')
        if next_page:
            self.params['cursor'] = next_page
            logger.info(f"获取下一页数据: {next_page}")
            return self.fetch_data()
        return None


    def save_to_csv(self, data_list):
        """保存数据到CSV文件"""
        try:
            if not data_list:
                logger.warning("没有数据需要保存")
                return
            
            # 按照parse_data中的顺序定义字段
            fieldnames = [
                'gmgn_price_usd',
                'gmgn_amount_usd',
                'gmgn_amount_base',
                'gmgn_amount_quote',
                'gmgn_tx_hash',
                'gmgn_time',
                'gmgn_swap_type'
            ]
            
            filename = f'gmgn_price_data_time_test_{int(time.time())}.csv'
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data_list)
                
            logger.success(f"成功保存 {len(data_list)} 条数据到文件: {filename}")
        except Exception as e:
            logger.error(f"保存数据失败: {e}")


    def run(self, max_pages=None):
        """运行爬虫
        Args:
            max_pages: 最大翻页次数，None表示不限制
        """
        try:
            current_page = 1
            total_records = 0
            filename = f'gmgn_price_data_{int(time.time())}.csv'
            
            # 按照parse_data中的顺序定义字段
            fieldnames = [
                'gmgn_price_usd',
                'gmgn_amount_usd',
                'gmgn_amount_base',
                'gmgn_amount_quote',
                'gmgn_tx_hash',
                'gmgn_time',
                'gmgn_swap_type'
            ]
            
            # 获取第一页数据
            response_data = self.fetch_data()
            if not response_data:
                logger.error("获取第一页数据失败")
                return

            # 创建CSV文件并写入表头
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

            while True:
                try:
                    # 解析当前页数据
                    parsed_data = self.parse_data(response_data)
                    if parsed_data:
                        # 追加模式打开文件并保存当前页数据
                        with open(filename, 'a', newline='', encoding='utf-8-sig') as f:
                            writer = csv.DictWriter(f, fieldnames=fieldnames)
                            writer.writerows(parsed_data)
                        
                        total_records += len(parsed_data)
                        logger.info(f"第 {current_page} 页: 成功提取并保存 {len(parsed_data)} 条数据")
                    else:
                        logger.warning(f"第 {current_page} 页: 没有解析到有效数据")

                    # 检查是否达到最大页数
                    if max_pages and current_page >= max_pages:
                        logger.info(f"已达到设定的最大页数 {max_pages}")
                        break

                    # 获取下一页数据
                    next_page_data = self.get_next_page(response_data)
                    if not next_page_data:
                        logger.info("没有下一页数据，爬取结束")
                        break

                    # 随机等待2-3秒
                    wait_time = random.uniform(2, 4)
                    logger.info(f"等待 {wait_time:.2f} 秒后获取下一页...")
                    time.sleep(wait_time)

                    # 更新数据和页数
                    response_data = next_page_data
                    current_page += 1

                except Exception as e:
                    logger.error(f"处理第 {current_page} 页时出错: {e}")
                    # 可以选择继续下一页或终止程序
                    if input("是否继续下一页？(y/n): ").lower() != 'y':
                        break

            # 输出总结信息
            logger.success(f"爬取完成，共获取并保存 {total_records} 条数据到文件: {filename}")

        except Exception as e:
            logger.error(f"运行出错: {e}")


if __name__ == "__main__":
    # 设置日志级别
    logger.add("gmgn_spider.log", rotation="500 MB")
    
    # 创建爬虫实例并运行，设置最大翻页数
    max_pages = int(input("请输入要爬取的页数（输入数字）: "))
    spider = GMGN_PRICE_SPIDER()
    spider.run(max_pages=max_pages)