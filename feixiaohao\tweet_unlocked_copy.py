import DrissionPage as dp
from loguru import logger
import pandas as pd
import time
import os
from datetime import datetime
import requests


class TweetUnlocke:
    def __init__(self):
        # 创建浏览器配置对象
        browser_options = dp.ChromiumOptions()
        # 设置无痕模式
        browser_options.set_argument('--incognito')
        # 设置窗口最大化
        browser_options.set_argument('--start-maximized')
        # 禁用扩展
        browser_options.set_argument('--disable-extensions')
        
        # 初始化无痕模式的浏览器
        self.browser = dp.ChromiumPage(browser_options)
        
        # 目标网址
        self.url = "https://x.com"
        self.email_url = "https://mail.tm/"
        self.code_url = "https://2fa.run/"
        
        # 创建标签页
        self.code_tab = self.browser.new_tab(self.code_url)
        time.sleep(2)
        self.email_tab = self.browser.new_tab(self.email_url)
        time.sleep(2)
        self.login_tab = self.browser.new_tab(self.url)
        time.sleep(2)
        logger.success("浏览器初始化成功")

    def open_page(self):
        """打开目标页面并点击登录按钮"""
        try:
            current_tab = self.browser.get_tab(self.login_tab.tab_id)
            time.sleep(5)
            
            login_button = current_tab.ele('xpath://*[@id="react-root"]/div/div/div[2]/main/div/div/div[1]/div/div/div[3]/div[4]/a/div')
            
            if login_button:
                login_button.click()
                logger.success("成功点击登录按钮")
                time.sleep(3)
                return True
            else:
                logger.error("未找到登录按钮")
                return False

        except Exception as e:
            logger.error(f"页面操作失败: {e}")
            return False

    def input_username(self):
        """输入用户名"""
        try:
            current_tab = self.browser.get_tab(self.login_tab.tab_id)
            username_input = current_tab.ele('tag:input')
            if username_input:
                username_input.click()
                username_input.clear()
                username_input.click()
                username_input.input('HaroldMusl56232')
                logger.success("成功输入账号")
                time.sleep(1)
                return True
            logger.error("未找到账号输入框")
            return False
        except Exception as e:
            logger.error(f"输入用户名失败: {e}")
            return False

    def click_next(self):
        """点击下一步按钮"""
        try:
            next_button = self.login_tab.ele('xpath://*[@id="layers"]/div[2]/div/div/div/div/div/div[2]/div[2]/div/div/div[2]/div[2]/div/div/div/button[2]/div')
            if next_button:
                next_button.click()
                logger.success("成功点击下一步按钮")
                time.sleep(2)
                return True
            logger.error("未找到下一步按钮")
            return False
        except Exception as e:
            logger.error(f"点击下一步失败: {e}")
            return False

    def input_password(self):
        """输入密码"""
        try:
            current_tab = self.browser.get_tab(self.login_tab.tab_id)
            password_input = current_tab.ele('tag:input')
            if password_input:
                password_input.clear()
                password_input.input('JYXJcdRdCeX')
                logger.success("成功输入密码")
                time.sleep(2)

                login_button = current_tab.ele('xpath://*[@id="layers"]/div[2]/div/div/div/div/div/div[2]/div[2]/div/div/div[2]/div[2]/div[2]/div/div[1]/div/div/button/div')
                if login_button:
                    login_button.click()
                    logger.success("成功点击登录按钮")
                    time.sleep(3)
                    return True
                else:
                    logger.error("未找到登录按钮")
                    return False

            logger.error("未找到密码输入框")
            return False
        except Exception as e:
            logger.error(f"输入密码失败: {e}")
            return False

    def get_2fa_code(self):
        """获取2FA验证码并在登录页面输入"""
        try:
            code_tab = self.browser.get_tab(self.code_tab.tab_id)
            time.sleep(2)

            secret_input = code_tab.ele('xpath://*[@id="secret-input-js"]')
            if not secret_input:
                logger.error("未找到验证码输入框")
                return False
            
            secret_input.click()
            secret_input.clear()
            secret_input.input('HA3JEUQ642M3GKM2')
            logger.success("成功输入验证码密钥")
            time.sleep(2)

            get_code_button = code_tab.ele('xpath://*[@id="btn-js"]')
            if not get_code_button:
                logger.error("未找到获取验证码按钮")
                return False
            
            get_code_button.click()
            logger.success("成功点击获取验证码按钮")
            time.sleep(2)

            code_element = code_tab.ele('xpath://*[@id="code_js"]')
            if not code_element:
                logger.error("未找到验证码元素")
                return False
            
            verification_code = code_element.text
            logger.success(f"成功获取验证码: {verification_code}")
            
            login_tab = self.browser.get_tab(self.login_tab.tab_id)
            time.sleep(2)
            
            code_input = login_tab.ele('xpath://*[@id="layers"]/div[2]/div/div/div/div/div/div[2]/div[2]/div/div/div[2]/div[2]/div[1]/div/div[2]/label/div/div[2]/div/input')
            if not code_input:
                logger.error("未找到验证码输入框")
                return False
            
            code_input.clear()
            code_input.input(verification_code)
            logger.success("成功输入验证码")
            time.sleep(2)
            
            next_button = login_tab.ele('xpath://*[@id="layers"]/div[2]/div/div/div/div/div/div[2]/div[2]/div/div/div[2]/div[2]/div[2]/div/div/div/button/div')
            if not next_button:
                logger.error("未找到下一步按钮")
                return False
            
            next_button.click()
            logger.success("成功点击下一步按钮")
            time.sleep(3)

            # 点击start按钮
            start_button = login_tab.ele('xpath:/html/body/div[2]/div/form/input[6]')
            if not start_button:
                logger.error("未找到start按钮")
                return False
            
            start_button.click()
            logger.success("成功点击start按钮")
            time.sleep(2)

            # 点击send email按钮
            send_email_button = login_tab.ele('xpath:/html/body/div[2]/div/form/input[5]')
            if not send_email_button:
                logger.error("未找到send email按钮")
                return False
            
            send_email_button.click()
            logger.success("成功点击send email按钮")
            time.sleep(2)
            
            return True

        except Exception as e:
            logger.error(f"获取2FA验证码失败: {e}")
            return False

    def email_login(self):
        """邮箱登录操作"""
        try:
            # API基础URL
            base_url = "https://api.mail.tm"
            
            # 登录信息
            login_data = {
                "address": "<EMAIL>",
                "password": "XY5WOfukpM"
            }
            
            # 获取token
            login_response = requests.post(
                f"{base_url}/token",
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            if login_response.status_code != 200:
                logger.error(f"邮箱登录失败: {login_response.text}")
                return False
                
            token = login_response.json()["token"]
            logger.success("成功获取邮箱token")

            # 获取消息列表
            messages_response = requests.get(
                f"{base_url}/messages",
                headers={
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
            )
            
            if messages_response.status_code != 200:
                logger.error(f"获取消息列表失败: {messages_response.text}")
                return False
                
            messages = messages_response.json()
            if not messages["hydra:member"]:
                logger.error("没有找到任何消息")
                return False
                
            # 获取最新消息的ID
            latest_message_id = messages["hydra:member"][0]["id"]
            logger.success("成功获取最新消息ID")

            # 获取消息详情
            message_response = requests.get(
                f"{base_url}/messages/{latest_message_id}",
                headers={
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
            )
            
            if message_response.status_code != 200:
                logger.error(f"获取消息详情失败: {message_response.text}")
                return False
                
            message_content = message_response.json()
            
            # 从消息内容中提取验证码
            verification_code = ''.join(filter(str.isdigit, message_content["text"]))[:6]
            logger.success(f"成功获取邮箱验证码: {verification_code}")

            # 切换到登录标签页并输入验证码
            login_tab = self.browser.get_tab(self.login_tab.tab_id)
            time.sleep(2)

            # 点击验证码输入框
            code_input = login_tab.ele('xpath:/html/body/div[2]/div/form/input[5]')
            if not code_input:
                logger.error("未找到验证码输入框")
                return False
            code_input.click()
            code_input.input(verification_code)
            logger.success("成功输入邮箱验证码")
            time.sleep(2)

            # 点击verify按钮
            verify_button = login_tab.ele('xpath:/html/body/div[2]/div/form/input[6]')
            if not verify_button:
                logger.error("未找到verify按钮")
                return False
            verify_button.click()
            logger.success("成功点击verify按钮")
            time.sleep(3)

            # 点击continue to x按钮
            continue_button = login_tab.ele('xpath:/html/body/div[2]/div/form/input[6]')
            if not continue_button:
                logger.error("未找到continue to x按钮")
                return False
            continue_button.click()
            logger.success("成功点击continue to x按钮")
            time.sleep(5)

            return True

        except Exception as e:
            logger.error(f"邮箱登录失败: {e}")
            return False

    def run(self):
        """运行主程序"""
        try:
            steps = [
                (self.open_page, "打开页面并点击登录"),
                (self.input_username, "输入用户名"),
                (self.click_next, "点击下一步"),
                (self.input_password, "输入密码"),
                (self.get_2fa_code, "获取2FA验证码"),
                (self.email_login, "邮箱登录")
            ]

            for step_func, step_name in steps:
                if not step_func():
                    logger.error(f"{step_name}失败")
                    return False
                logger.success(f"{step_name}成功")

            return True
            
        except Exception as e:
            logger.error(f"程序运行出错: {e}")
            return False
        finally:
            self.browser.quit()


if __name__ == '__main__':
    
    bot = TweetUnlocke()
    try:
        if bot.run():
            logger.success("程序执行完成")
        else:
            logger.error("程序执行失败")
    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {e}")