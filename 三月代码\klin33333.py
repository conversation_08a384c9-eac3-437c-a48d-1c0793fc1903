import cloudscraper
import time
import random
from fake_useragent import UserAgent
import requests
import cfscrape

class CloudflareBypass:
    def __init__(self):
        self.user_agent = UserAgent().random
        self.session = requests.Session()
        self.scraper = None
        self.cf_scraper = None
        
    def init_scrapers(self):
        """初始化多个scraper以提高成功率"""
        # 初始化 cloudscraper
        self.scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'mobile': False
            },
            delay=10
        )
        
        # 初始化 cfscrape
        self.cf_scraper = cfscrape.create_scraper()
        
        # 设置通用headers
        self.base_headers = {
            "authority": "gmgn.ai",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "referer": "https://gmgn.ai/sol/token/So11111111111111111111111111111111111111112",
            "sec-ch-ua": '"Google Chrome";v="114", "Chromium";v="114", "Not?A_Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": self.user_agent,
            "accept-encoding": "gzip, deflate, br",
            "dnt": "1",
            "upgrade-insecure-requests": "1"
        }

def get_k_line_data(retries=5, delay=5):
    cf_bypass = CloudflareBypass()
    cf_bypass.init_scrapers()
    
    params = {
        "client_id": "gmgn_web_2024.0319.175759",
        "from_app": "gmgn",
        "tz_name": "Asia/Shanghai",
        "tz_offset": "28800",
        "app_lang": "zh-CN",
        "resolution": "1m",
        "from": "",
        "to": "1734486573000",
        "limit": "357"
    }

    url = "https://gmgn.ai/api/v1/token_candles/sol/So11111111111111111111111111111111111111112"
    
    for attempt in range(retries):
        try:
            # 随机延迟
            time.sleep(random.uniform(2, 5))
            
            # 随机选择一个scraper
            if random.choice([True, False]):
                scraper = cf_bypass.scraper
                scraper_name = "cloudscraper"
            else:
                scraper = cf_bypass.cf_scraper
                scraper_name = "cfscrape"
            
            print(f"尝试使用 {scraper_name} 发送请求...")
            
            # 添加随机User-Agent
            current_headers = cf_bypass.base_headers.copy()
            current_headers['user-agent'] = UserAgent().random
            
            # 发送请求
            response = scraper.get(
                url,
                headers=current_headers,
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"使用 {scraper_name} 成功获取数据")
                return response.json()
            else:
                print(f"尝试 {attempt + 1}/{retries} 失败: 状态码 {response.status_code}")
                
                # 如果是403错误，尝试重新初始化scraper
                if response.status_code == 403:
                    print("检测到403错误，重新初始化scraper...")
                    cf_bypass.init_scrapers()
                    time.sleep(random.uniform(5, 10))
                
        except Exception as e:
            print(f"尝试 {attempt + 1}/{retries} 失败: {str(e)}")
            if attempt < retries - 1:
                wait_time = delay * (attempt + 1) + random.uniform(1, 3)
                print(f"等待 {wait_time:.2f} 秒后重试...")
                time.sleep(wait_time)
            else:
                raise
    
    return None

def test_connection(url="https://gmgn.ai"):
    """测试网站连接性"""
    try:
        response = requests.get(url, timeout=10)
        print(f"网站响应状态码: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"连接测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    try:
        # 首先测试连接
        if test_connection():
            print("网站连接正常，开始获取数据...")
            data = get_k_line_data()
            if data:
                print("成功获取数据")
                print("响应数据:", data)
            else:
                print("未能获取数据")
        else:
            print("网站连接异常，请检查网络或者网站状态")
    except Exception as e:
        print(f"获取数据失败: {str(e)}")