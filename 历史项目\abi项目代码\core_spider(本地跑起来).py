import time
import json
from loguru import logger

import requests
import random

from common import CommonFun, del_expire_hash, redis_sentry_connection

class Core_test_Spider:
    def __init__(self):
        self.chain_name = 'Core'
        self.cf: CommonFun = CommonFun()
        self.abi_addr = f'spider:abi:{self.chain_name}'
        self.headers = {
            "content-type": "application/json;charset=UTF-8",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

    def run(self):
        redis_master, redis_slave = redis_sentry_connection()
        while True:
            address = redis_master.spop(self.abi_addr)
            if not address:
                # logger.info('暂无地址，等待...')
                time.sleep(5)
                continue

            is_expire = self.cf.judge_expire(address, self.chain_name)
            logger.info(f'从 {self.chain_name} 链获取到address: {address}')
            if is_expire:
                self.parse_data(address)

    def parse_data(self, address):
        url = "https://scan.coredao.org/api/chain/abi"
        data = {
            'contractAddress': f'{address}'
        }
        logger.info(data)
        logger.info('开始获取contract_abi')
        data = json.dumps(data)
        time.sleep(1.2 + random.random())
        response = requests.post(url, headers=self.headers, data=data)
        if not response:
            return

        response_data = response.json()
        contract_addr = address
        chain_name = self.chain_name
        contract_name = response_data['data']['contractName']
        contract_abi = response_data['data']['abi']

        '''拿代理合约'''
        '''isProxy=2时 存在代理合约'''
        isProxy = response_data['data']['isProxy']
        proxy_contract = response_data['data']['logicAddress']


        if contract_abi:
            logger.info(f'获取到 {chain_name} 的数据:{contract_addr}:{contract_name}: {contract_abi}')
            item = {
                'ADDRESS': address,
                'CHAIN_NAME': chain_name,
                'CONTRACT_NAME': contract_name,
                'CONTRACT_ABI': contract_abi
            }

            # 如果是代理合约，则把代理合约的abi也拿到
            if isProxy == 2:
                item['IMP_CONTRACT'] = proxy_contract
                self.cf.save_to_phoenix(item)

                existed = self.cf.check_exist(proxy_contract, self.chain_name)
                if not existed:  # 没有的话则爬
                    self.parse_data(proxy_contract)
            else:
                self.cf.save_to_phoenix(item)
                logger.info('保存成功')

            # 已爬取到abi，则将过期记录删除（防止冗余）
            del_expire_hash(address, self.chain_name)
        else:
            logger.info('no abi')
            self.cf.set_expire_hash(address, self.chain_name)

if __name__ == '__main__':
    spider = Core_test_Spider()
    spider.run()