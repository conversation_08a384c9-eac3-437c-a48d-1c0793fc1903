#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TVL数据统一调度器 - 同步运行HA、OKX和GMGN三个平台的数据获取脚本
确保每次请求都是同时发送的，保证数据一致性
"""

import requests
import sqlite3
import json
import datetime
import time
import sys
from curl_cffi import requests as cf_requests

# 设置控制台输出编码
if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库配置
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "test_data_bsc"

# 重试配置
MAX_RETRIES = 10
RETRY_INTERVAL = 3

# HA API配置
HA_API_URL = "http://**************/api/v1/dex/market/coin-tvl"
HA_HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

# OKX API配置
OKX_API_URL = "https://web3.okx.com/priapi/v1/dx/market/v2/token/overview"
OKX_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

def get_okx_headers(token_address):
    return {
        "accept": "application/json",
        "accept-language": "zh-CN,zh;q=0.9",
        "app-type": "web",
        "devid": "ed72f275-eb0a-418a-be47-91f58cf52649",
        "ok-timestamp": str(int(time.time() * 1000)),
        "ok-verify-sign": "XXSV6AD5NGJvfC8yez/Krd/al+dy2lvKLVbGaK631tA=",
        "ok-verify-token": "571bda7e-6293-47b7-b0a0-fc95dbba4f72",
        "priority": "u=1, i",
        "referer": f"https://web3.okx.com/zh-hans/token/solana/{token_address}",
        "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-cdn": "https://web3.okx.com",
        "x-fptoken": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "x-fptoken-signature": "{P1363}zW65av6lMPqFCS1DCJ4kQstrNxihzjDg++7GM1KRO4TWvvLdSaiewIaJo71C/tDNRSvsYYxK0BeSYvSzvGzxKw==",
        "x-id-group": "2121185015799960003-c-8",
        "x-locale": "zh_CN",
        "x-request-timestamp": str(int(time.time() * 1000) - 100),
        "x-simulated-trading": "undefined",
        "x-site-info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOi42bpdWZyJye",
        "x-utc": "8",
        "x-zkdex-env": "0"
    }

OKX_COOKIES = {
    "_gcl_gs": "2.1.k1$i1747121212$u3801134",
    "intercom-id-ny9cf50h": "59b1a03b-f342-4a22-9469-ca04541cfc98",
    "intercom-device-id-ny9cf50h": "e3eb8110-ee9e-4eec-8777-a8840b95118e",
    "_ym_uid": "1747121226975031991",
    "_ym_d": "1747121226",
    "devId": "ed72f275-eb0a-418a-be47-91f58cf52649",
    "locale": "zh_CN",
    "ok_prefer_udColor": "0",
    "ok_prefer_udTimeZone": "0",
    "first_ref": "https%3A%2F%2Fwww.okx.com%2F",
    "ok_login_type": "OKX_GLOBAL",
    "fingerprint_id": "286c47e1-4945-45bb-88e9-98ddc1deff0f",
    "_gid": "GA1.2.1941574504.1748224542",
    "amp_21c676": "sxXY9NatdSUAkrG8DFaDSs.ZDVtenN3VUNMVkowT3d6K1hZU0RnQT09..1is8n1mkg.1is8pnra8.i.f.11",
    "intercom-session-ny9cf50h": "WURMYmNvY3RXTFhLVmd4eUpjM3lTRVNacElwdmpWQTlTbEdvSjd5T0pGdzE0aUVycFI4N2YrY082WmlpMVMyZElBVVc3L3Nndmd0bG9MWDlyYWs1WWNHaXNRd3Bya3VrYko5OG4vcDJTWjQ9LS1qQmlyYkVkb1JENDJQTS8wMnN3SjNBPT0=--df77abdb800d3811db2579189fb0bd5a0d1c13f7",
    "connected": "1",
    "connectedWallet": "1",
    "_ym_isad": "2",
    "OptanonAlertBoxClosed": "2025-05-29T05:32:26.912Z",
    "ok-exp-time": "1748497227575",
    "tmx_session_id": "q6dk4519osm_1748497236053",
    "fp_s": "0",
    "OptanonConsent": "isGpcEnabled=0&datestamp=Thu+May+29+2025+13%3A59%3A17+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202405.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&landingPath=NotLandingPage&groups=C0004%3A0%2CC0002%3A0%2CC0003%3A0%2CC0001%3A1&geolocation=US%3B&AwaitingReconsent=false",
    "ok_site_info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOi42bpdWZyJye",
    "__cf_bm": "wDt2o1MPM6SUyIwcWwBcNKqiTFgFX65r31e4vQIlQq0-1748501270-*******-.W94uVnYR3N7Fq1OerSDtzG72E2OVN6O4BiXiZQbmYcizR3p7dwpwHh83KMFwr0yHSQ.oXy.MZZjeNLujVmxxxYFe8RY2HMHlnXOyg.1smI",
    "okg.currentMedia": "xl",
    "ok_prefer_currency": "0%7C0%7Cfalse%7CUSD%7C2%7C%24%7C1%7C1%7C%E7%BE%8E%E5%85%83",
    "_gat_UA-35324627-3": "1",
    "traceId": "2121185015799960003",
    "ok_prefer_exp": "1",
    "_ga": "GA1.1.2028963803.1747121216",
    "_ga_G0EKWWQGTZ": "GS2.1.s1748501419$o41$g1$t1748501580$j26$l0$h0",
    "ok-ses-id": "Ot3r8sCJruak1aDq2WdL0uvdABXHc3V/y23K68zaV4TLENyozv9yyvFoocWMHxMuXEKTd6XS5QB+faq1BgwotOJ03lvvuCaqxNOHcpkmKvLuQRiBFmKJh9gOJq80WgZQ",
    "_monitor_extras": "{\"deviceId\":\"OWTCklx6Hw5J0KFQPhe8Aq\",\"eventId\":1116,\"sequenceNumber\":1116}"
}

# GMGN API配置
GMGN_API_URL = "https://gmgn.ai/api/v1/mutil_window_token_info"
GMGN_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

def get_gmgn_headers(token_address):
    return {
        "referer": f"https://gmgn.ai/bsc/token/{token_address}"
    }

GMGN_COOKIES = {
    "_ga": "GA1.1.1787124478.1747114971",
    "cf_clearance": "uBGoNKbju0gJKlK8kYbfibSg6U5EzUtuftEIySzY3cs-1749797746-*******-Sp4GRUUsOknzXfKNy5C8ajBOCitbdfHrc7yPkk8.CPFklkfwt8hjCRfPJk.DJ1vReQ_y61ILaAx3KaFZJIqgJtpOFycmM6M5Pk9aLmYwceQrqKVJl_hsAB7q.Oljo1cVKzat45qxXyZTBJAwLiujok_dOkeXhRppBwYVFxx7FtpjSIUIxFx45PQPDLGjiGq69sBp1jDlMv4yyxxs7jpcNq8gpV0Jx7IEhkNzrRT8DTO6iNxrMpbgdHu6fsc5gCXY_1I.GvUxonEwl3esoUeMyFkmPp7E_H6YcNL8VMOYpPlsumwwyqX_h4HbxIfWs3qkUU_mxPeZW_6Cdg04k278FcCX8mOxCzNUC_QOGsxzFAI",
    "_ga_0XM0LYXGC8": "GS2.1.s1749797734$o46$g1$t1749797748$j46$l0$h0"
}

GMGN_PARAMS = {
    "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
    "client_id": "gmgn_web_20250613-2203-ef1b00b",
    "from_app": "gmgn",
    "app_ver": "20250613-2203-ef1b00b",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a0adc6758070914b5d3cd4679349eed1",
    "os": "web"
}


def print_log(message):
    """统一的日志打印函数"""
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")


def get_all_token_addresses():
    """从数据库中获取所有Token Address"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'SELECT rowid, "code_address" FROM {DB_TABLE} WHERE "code_address" IS NOT NULL')
        return cursor.fetchall()
    except sqlite3.Error as e:
        print_log(f"数据库错误: {e}")
        return []
    finally:
        if conn:
            conn.close()


def fetch_ha_tvl(token_address):
    """获取HA平台TVL数据"""
    request_data = {
        "chainName": "BSC",
        "tokenContractAddress": token_address
    }
    
    for attempt in range(MAX_RETRIES):
        try:
            print_log(f"HA - 请求代币 {token_address} (尝试 {attempt + 1}/{MAX_RETRIES})")
            
            response = requests.post(
                HA_API_URL,
                headers=HA_HEADERS,
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200 and "data" in result:
                    tvl = result["data"].get("tvl")
                    if tvl is not None:
                        print_log(f"HA - 成功获取TVL: {tvl}")
                        return {"tvl": tvl, "success": True}
                
                print_log(f"HA - 响应数据不完整: {result}")
                return {"success": False, "error": "数据不完整"}
            else:
                print_log(f"HA - 请求失败: 状态码 {response.status_code}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_INTERVAL)
                    continue
                    
        except Exception as e:
            print_log(f"HA - 请求异常: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_INTERVAL)
                continue
    
    return {"success": False, "error": "达到最大重试次数"}


def fetch_okx_tvl(token_address):
    """获取OKX平台TVL数据"""
    params = {
        "chainId": "56",  # Solana的链ID
        "tokenContractAddress": token_address
    }
    
    headers = get_okx_headers(token_address)
    
    for attempt in range(MAX_RETRIES):
        try:
            print_log(f"OKX - 请求代币 {token_address} (尝试 {attempt + 1}/{MAX_RETRIES})")
            
            response = requests.get(
                OKX_API_URL,
                headers=headers,
                cookies=OKX_COOKIES,
                params=params,
                proxies=OKX_PROXY,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0 and 'data' in data:
                    market_info = data['data'].get('marketInfo', {})
                    total_liquidity = market_info.get('totalLiquidity')
                    
                    if total_liquidity:
                        tvl_value = float(total_liquidity)
                        print_log(f"OKX - 成功获取TVL: {tvl_value}")
                        return {"tvl": tvl_value, "success": True}
                
                print_log(f"OKX - 响应数据不完整: {data}")
                return {"success": False, "error": "数据不完整"}
            else:
                print_log(f"OKX - 请求失败: 状态码 {response.status_code}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_INTERVAL)
                    continue
                    
        except Exception as e:
            print_log(f"OKX - 请求异常: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_INTERVAL)
                continue
    
    return {"success": False, "error": "达到最大重试次数"}


def fetch_gmgn_tvl(token_address):
    """获取GMGN平台TVL数据"""
    headers = get_gmgn_headers(token_address)
    
    # 构建请求数据
    data = {
        "chain": "bsc",
        "addresses": [token_address]
    }
    json_data = json.dumps(data, separators=(',', ':'))
    
    for attempt in range(MAX_RETRIES):
        try:
            print_log(f"GMGN - 请求代币 {token_address} (尝试 {attempt + 1}/{MAX_RETRIES})")
            
            response = cf_requests.post(
                GMGN_API_URL,
                headers=headers,
                cookies=GMGN_COOKIES,
                params=GMGN_PARAMS,
                data=json_data,
                impersonate='chrome116',
                proxies=GMGN_PROXY,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0 and 'data' in data and len(data['data']) > 0:
                    token_data = data['data'][0]
                    liquidity = token_data.get('liquidity')
                    
                    if liquidity:
                        tvl_value = float(liquidity)
                        print_log(f"GMGN - 成功获取TVL: {tvl_value}")
                        return {"tvl": tvl_value, "success": True}
                
                print_log(f"GMGN - 响应数据不完整: {data}")
                return {"success": False, "error": "数据不完整"}
            else:
                print_log(f"GMGN - 请求失败: 状态码 {response.status_code}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_INTERVAL)
                    continue
                    
        except Exception as e:
            print_log(f"GMGN - 请求异常: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_INTERVAL)
                continue
    
    return {"success": False, "error": "达到最大重试次数"}


def update_ha_database(row_id, tvl_value):
    """更新HA TVL到数据库"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'''
            UPDATE {DB_TABLE}
            SET "ha_tvl" = ?
            WHERE rowid = ?
        ''', (tvl_value, row_id))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print_log(f"HA - 更新数据库错误: {e}")
        return False
    finally:
        if conn:
            conn.close()


def update_okx_database(row_id, tvl_value):
    """更新OKX TVL到数据库"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'''
            UPDATE {DB_TABLE}
            SET "okx_tvl" = ?
            WHERE rowid = ?
        ''', (tvl_value, row_id))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print_log(f"OKX - 更新数据库错误: {e}")
        return False
    finally:
        if conn:
            conn.close()


def update_gmgn_database(row_id, tvl_value):
    """更新GMGN TVL到数据库"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'''
            UPDATE {DB_TABLE}
            SET "gmgn_tvl" = ?
            WHERE rowid = ?
        ''', (tvl_value, row_id))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print_log(f"GMGN - 更新数据库错误: {e}")
        return False
    finally:
        if conn:
            conn.close()


def process_single_token(row_id, token_address):
    """同步处理单个代币的三个平台TVL数据"""
    print_log(f"开始同步处理代币: {token_address} (rowid: {row_id})")
    print_log("=" * 80)
    
    # 同时发送三个平台的请求
    print_log("同时向三个平台发送请求...")
    start_time = time.time()
    
    # 按顺序调用，确保严格同步
    ha_result = fetch_ha_tvl(token_address)
    okx_result = fetch_okx_tvl(token_address)
    gmgn_result = fetch_gmgn_tvl(token_address)
    
    end_time = time.time()
    print_log(f"三个平台请求完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 统计成功情况
    success_count = 0
    
    # 更新HA数据
    if ha_result["success"]:
        if update_ha_database(row_id, ha_result["tvl"]):
            print_log(f"HA - 成功更新数据库: TVL={ha_result['tvl']}")
            success_count += 1
        else:
            print_log("HA - 数据库更新失败")
    else:
        print_log(f"HA - 获取TVL失败: {ha_result.get('error', '未知错误')}")
    
    # 更新OKX数据
    if okx_result["success"]:
        if update_okx_database(row_id, okx_result["tvl"]):
            print_log(f"OKX - 成功更新数据库: TVL={okx_result['tvl']}")
            success_count += 1
        else:
            print_log("OKX - 数据库更新失败")
    else:
        print_log(f"OKX - 获取TVL失败: {okx_result.get('error', '未知错误')}")
    
    # 更新GMGN数据
    if gmgn_result["success"]:
        if update_gmgn_database(row_id, gmgn_result["tvl"]):
            print_log(f"GMGN - 成功更新数据库: TVL={gmgn_result['tvl']}")
            success_count += 1
        else:
            print_log("GMGN - 数据库更新失败")
    else:
        print_log(f"GMGN - 获取TVL失败: {gmgn_result.get('error', '未知错误')}")
    
    print_log(f"代币 {token_address} 处理完成: {success_count}/3 个平台成功")
    print_log("=" * 80)
    
    return success_count


def main():
    """主函数"""
    print_log("=" * 60)
    print_log("三平台同步TVL数据获取工具")
    print_log("=" * 60)
    print_log(f"数据库路径: {DB_PATH}")
    print_log(f"数据库表名: {DB_TABLE}")
    print_log(f"最大重试次数: {MAX_RETRIES}")
    print_log("=" * 60)
    
    # 获取所有token地址
    all_tokens = get_all_token_addresses()
    total_count = len(all_tokens)
    
    if not all_tokens:
        print_log("未找到任何Token地址，请检查数据库")
        return
    
    print_log(f"总共需要处理 {total_count} 个代币")
    print_log("开始逐个同步处理...")
    
    total_success = 0
    
    start_time = time.time()
    
    for i, (row_id, token_address) in enumerate(all_tokens, 1):
        print_log(f"\n处理进度: {i}/{total_count} ({i/total_count*100:.1f}%)")
        
        # 处理单个代币
        success_count = process_single_token(row_id, token_address)
        total_success += success_count
        
        # 如果不是最后一个，等待一段时间再处理下一个
        if i < total_count:
            delay = 4  # 每个代币处理完后等待2秒
            print_log(f"等待 {delay} 秒后处理下一个代币...")
            time.sleep(delay)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 输出最终统计
    print_log("\n" + "=" * 60)
    print_log("处理完成!")
    print_log(f"总计处理: {total_count} 个代币")
    print_log(f"总成功次数: {total_success} (满分: {total_count * 3})")
    print_log(f"总耗时: {duration:.2f} 秒")
    print_log(f"平均每个代币耗时: {duration/total_count:.2f} 秒")
    print_log("=" * 60)


if __name__ == "__main__":
    print_log("开始运行三平台同步TVL数据获取工具...")
    
    try:
        main()
    except KeyboardInterrupt:
        print_log("\n程序被用户中断")
    except Exception as e:
        import traceback
        print_log(f"程序运行出错: {e}")
        print_log(traceback.format_exc())
    
    print_log("程序运行结束")
