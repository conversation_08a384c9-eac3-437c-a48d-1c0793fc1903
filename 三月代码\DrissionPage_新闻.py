import DrissionPage as dp
from loguru import logger
import pandas as pd
import time
import os
from datetime import datetime


class NewsSpider:
    def __init__(self):
        # 初始化浏览器页面
        self.page = dp.ChromiumPage()
        # 目标网址
        self.url = "https://www.congress.gov/"
        # 数据保存路径
        self.save_path = "digital asset.csv"
        
    def open_page(self):
        """打开目标页面并执行搜索"""
        try:
            self.page.get(self.url)
            logger.success("页面加载成功")
            time.sleep(3)  # 增加等待时间

            try:
                # 使用JavaScript执行搜索框操作
                self.page.run_js('''
                    document.querySelector("input#search").click();
                    document.querySelector("input#search").value = "digital asset";
                ''')
                logger.info("输入搜索关键词")

                # 使用JavaScript点击搜索按钮
                self.page.run_js('''
                    document.querySelector("#search-submit").click();
                ''')
                logger.success("点击搜索按钮")

                # 等待搜索结果加载
                time.sleep(5)

                # 设置显示所有数据
                try:
                    # 点击下拉框
                    self.page.run_js('''
                        let selectElement = document.querySelector('#main div div:nth-child(2) div select');
                        if (selectElement) {
                            selectElement.click();
                        }
                    ''')
                    logger.success("点击下拉框成功")
                    time.sleep(2)

                    # 选择显示所有数据的选项
                    self.page.run_js('''
                        let optionElement = document.querySelector('#main div div:nth-child(2) div select option[value="100"]');
                        if (optionElement) {
                            optionElement.selected = true;
                            let event = new Event('change', { bubbles: true });
                            optionElement.parentElement.dispatchEvent(event);
                        }
                    ''')
                    logger.success("选择显示所有数据成功")
                    time.sleep(5)  # 等待页面重新加载
                except Exception as e:
                    logger.error(f"设置显示所有数据失败: {e}")
                    return False

                return True
            except Exception as e:
                logger.error(f"元素交互失败: {e}")
                return False

        except Exception as e:
            logger.error(f"页面操作失败: {e}")
            return False

    def extract_data(self):
        """提取页面数据"""
        try:
            # 等待页面加载完成
            time.sleep(3)
            
            # 使用JavaScript提取数据
            js_script = ''' 
            let items = document.querySelectorAll('#main ol li');
            let data = [];
            
            for (let item of items) {
                try {
                    let titleSpan = item.querySelector('span:first-child');
                    let titleLink = titleSpan.querySelector('a');
                    let dateSpan = item.querySelector('span:nth-child(2)');
                    
                    if (titleSpan && dateSpan) {
                        data.push({
                            title: titleSpan.textContent.trim(),
                            date: dateSpan.textContent.trim(),
                            url: titleLink ? titleLink.href : ''
                        });
                    }
                } catch (e) {
                    console.error('单条数据提取失败:', e);
                }
            }
            return JSON.stringify(data);
            '''
            
            # 执行JavaScript并获取结果
            results = self.page.run_js(js_script)
            results = eval(results)  # 将字符串转换为Python对象
            logger.info(f"找到 {len(results)} 条列表项")
            
            news_list = []
            for index, item in enumerate(results, 1):
                try:
                    news_data = {
                        'title': item['title'],
                        'date': item['date'],
                        'url': item['url'],
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    news_list.append(news_data)
                    logger.info(f"第 {index} 条数据: 标题={item['title']}, 日期={item['date']}, 链接={item['url']}")
                except Exception as e:
                    logger.warning(f"第 {index} 条数据处理失败: {e}")
                    logger.debug(f"详细错误: {str(e)}")
                    continue
            
            logger.info(f"总共提取到 {len(news_list)} 条数据")
            return news_list
        except Exception as e:
            logger.error(f"数据提取失败: {e}")
            return []

    def next_page(self):
        """翻到下一页"""
        try:
            # 查找下一页按钮 (需要根据实际网页调整xpath)
            next_button = self.page.ele('//a[contains(text(), "Next")]')
            
            if next_button and next_button.is_displayed():
                next_button.click()
                time.sleep(3)  # 等待页面加载
                logger.success("成功翻到下一页")
                return True
            else:
                logger.info("已经是最后一页")
                return False
        except Exception as e:
            logger.error(f"翻页失败: {e}")
            return False

    def save_to_csv(self, data):
        """保存数据到CSV"""
        if not data:
            logger.warning("没有数据需要保存")
            return
        
        try:
            df = pd.DataFrame(data)
            logger.info(f"准备保存数据到CSV，数据框大小: {df.shape}")  # 添加日志：显示数据框大小
            
            file_exists = os.path.exists(self.save_path)
            save_path = os.path.abspath(self.save_path)  # 获取文件的绝对路径
            
            df.to_csv(
                self.save_path,
                mode='a' if file_exists else 'w',
                header=not file_exists,
                index=False,
                encoding='utf-8-sig'
            )
            logger.success(f"成功保存 {len(data)} 条数据到 {save_path}")  # 显示保存路径
            
            # 验证文件是否成功创建
            if os.path.exists(self.save_path):
                file_size = os.path.getsize(self.save_path)
                logger.info(f"CSV文件已创建，大小: {file_size} 字节")
            else:
                logger.warning("CSV文件未能成功创建")
            
        except Exception as e:
            logger.error(f"数据保存失败: {e}")

    def run(self, max_pages=None):
        """运行爬虫"""
        if not self.open_page():
            return
        
        page_num = 1
        while True:
            logger.info(f"正在爬取第 {page_num} 页")
            
            # 提取数据
            news_data = self.extract_data()
            if news_data:
                self.save_to_csv(news_data)
            
            # 检查是否达到最大页数
            if max_pages and page_num >= max_pages:
                logger.info(f"已达到设定的最大页数 {max_pages}")
                break
            
            # 尝试翻页
            if not self.next_page():
                break
                
            page_num += 1
            time.sleep(2)  # 防止频繁请求
        
        # 关闭浏览器
        self.page.quit()
        logger.success("爬虫任务完成")


if __name__ == "__main__":
    # 设置日志级别
    # logger.add("spider.log", rotation="500 MB")
    
    # 创建爬虫实例并运行
    spider = NewsSpider()
    spider.run(max_pages=10)
