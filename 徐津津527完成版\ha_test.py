import requests

PRIMARY_PROXY = {
    "http": "socks5://**************:30889",
    "https": "socks5://**************:30889"
}

headers = {
    "Content-Type": "application/json"
}
url = "https://www.valuescan.ai/api/v1/dex/market/current-price"
data = {
    "chainName": "SOL",
    "tokenContractAddress": "5zKZyG8HfM42Ekj68sBUj1j9hyKwcYCNsU5DRwU3pump"
}
response = requests.post(url, headers=headers, data=data)

print(response.text)
print(response)