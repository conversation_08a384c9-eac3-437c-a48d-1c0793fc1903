"""
添加账户到redis
"""

import json

from settings import *
from db import RedisClient

account_db = RedisClient(REDIS_ACCOUNTS_NAME)


def read_account(sp='----'):
    with open(r"C:\Users\<USER>\Desktop\account.txt", "r") as f:
        lines = f.readlines()
        for line in lines:
            data_list = line.strip('\n').split(sp)
            account = data_list[0]
            password = data_list[1]
            secret_key = data_list[2]

            print("导入-->账号:%s, 密码:%s, 密钥:%s" % (account, password, secret_key))
            account_db.set(account, json.dumps({'password': password, 'secret_key': secret_key}))


def get_account():
    aa = account_db.get('GuglerH62022')
    print(aa)
    print(type(aa))


if __name__ == "__main__":
    read_account()
