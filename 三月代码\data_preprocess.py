#!/usr/bin/env python
# coding: utf-8

# In[1]:


import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import OneHotEncoder
from sklearn.preprocessing import LabelEncoder, StandardScaler  # 导入 LabelEncoder
from torch.utils.data import DataLoader, Dataset
import numpy as np
from sklearn.decomposition import TruncatedSVD


# In[2]:


import dask.dataframe as dd
# 设置较小的 blocksize 以减少每次读取的数据量
user_data = dd.read_json("E:\yelp\yelp_academic_dataset_user.json", lines=True, blocksize="10MB")
business_data = dd.read_json("E:\yelp\yelp_academic_dataset_business.json", lines=True, blocksize="10MB")
review_data = dd.read_json("E:\yelp\yelp_academic_dataset_review.json", lines=True, blocksize="10MB")
checkin_data=dd.read_json("E:\yelp\yelp_academic_dataset_checkin.json", lines=True, blocksize="10MB")
print(user_data.head())


# In[3]:


print(business_data.head())


# In[4]:


print(review_data.head())


# In[5]:


print(checkin_data.head())


# yelp_academic_dataset_business.json：包含了Yelp商家信息的数据
# 每个商家的信息都以JSON格式存储，包括商家的名称、地址、经纬度坐标、类别（如餐厅、咖啡馆等）、营业时间、星级评分以及其他相关信息。这个文件用于描述Yelp平台上注册的商家的基本信息。  
# yelp_academic_dataset_review.json：包含了Yelp用户对商家的评论数据
# 每条评论都以JSON格式存储，包括用户ID、商家ID、评分、评论文本、评论时间等信息。这个文件是Yelp数据集中最重要的部分，用于进行情感分析、自然语言处理和用户行为分析等任务。  
# yelp_academic_dataset_user.json: 包含了Yelp平台上用户的基本信息
# 每个用户的数据都以JSON格式存储，包括用户ID、姓名、注册时间、好友列表、评分分布、评论数量等信息。这个文件提供了关于Yelp用户的一些统计信息和行为模式。  
# yelp_academic_dataset_checkin.json：包含用户在Yelp平台上进行签到（Check-in）的数据
# 签到是指用户在实体店面（如餐厅、商店等）实际到达的时间点记录。这个文件存储了用户签到的时间和商家的信息，可以用于分析用户活动模式和商家受欢迎程度。

# In[6]:


from transformers import AutoTokenizer

# 指定你的本地模型路径
model_path = "D:\\models/distilbert-base-uncased-finetuned-sst-2-english"

# 加载 tokenizer
tokenizer = AutoTokenizer.from_pretrained(model_path)

# 保存 tokenizer 配置文件
tokenizer.save_pretrained(model_path)

print("special_tokens_map.json 和 tokenizer_config.json 已成功生成！")


# In[ ]:


import pandas as pd
import time
from transformers import pipeline, AutoTokenizer
from multiprocessing import Pool
import tqdm
from concurrent.futures import ThreadPoolExecutor, TimeoutError

# 加载数据
review_data = review_data.head(5000).compute()
print(f"加载数据条数:{len(review_data)}")

# 模型和权重参数
model_name = "distilbert-base-uncased-finetuned-sst-2-english"
revision = "714eb0f"
local_model_path = "D:\\models/distilbert-base-uncased-finetuned-sst-2-english"

weight_original = 0.7
weight_adjusted = 1 - weight_original

# 预加载模型
max_retries = 3
for attempt in range(max_retries):
    try:
        tokenizer = AutoTokenizer.from_pretrained(local_model_path)
        sentiment_analyzer = pipeline("sentiment-analysis", model=local_model_path)
        print(f"✅ 成功加载本地模型: {local_model_path}")
        break
    except Exception as e:
        print(f"❌ 失败: {e}")
        if attempt < max_retries - 1:
            print(f"🔄 重试 {attempt + 1}/{max_retries}...")
            time.sleep(3)
        else:
            print("⚠️ 无法加载模型，退出程序")
            exit()

# 文本预处理
def preprocess_text(text):
    tokens = tokenizer.tokenize(text)
    if len(tokens) > 512:
        return tokenizer.convert_tokens_to_string(tokens[:500])
    return text

# 带超时的分析函数
def analyze_sentiment_with_timeout(text, timeout=10):
    try:
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(lambda: sentiment_analyzer(preprocess_text(text))[0])
            result = future.result(timeout=timeout)
            return result
    except TimeoutError:
        print(f"⚠️ 超时：{text[:50]}...")
        return None
    except Exception as e:
        print(f"❌ 处理失败：{text[:50]}... 错误：{e}")
        return None

# 评分调整逻辑
def adjust_rating_by_sentiment(text, original_stars):
    sentiment_result = analyze_sentiment_with_timeout(text)
    if not sentiment_result:
        return original_stars
    label = sentiment_result.get('label', 'NEUTRAL')
    score = sentiment_result.get('score', 0.5)
    if label == 'POSITIVE':
        adjusted_stars = min(original_stars + score * 0.5, 5)
    elif label == 'NEGATIVE':
        adjusted_stars = max(original_stars - score * 0.5, 1)
    else:
        adjusted_stars = original_stars
    return round(weight_original * original_stars + weight_adjusted * adjusted_stars, 2)

# 多进程处理
def process_row(row):
    return adjust_rating_by_sentiment(row['text'], row['stars'])



if __name__ == '__main__':
    print("🚀 正在处理数据，请稍候...")
    print(f"待处理数据条数：{len(review_data)}")
    
    with Pool(processes=4) as pool:
        adjusted_stars_list = list(tqdm.tqdm(
            pool.imap(process_row, [row for _, row in review_data.iterrows()]), 
            total=len(review_data)
        ))
    
    review_data['adjusted_stars'] = adjusted_stars_list
    review_data.to_csv('review_data_adjusted.csv', index=False)
    print(f"✅ 数据处理完成，处理条数：{len(adjusted_stars_list)}")
    print("结果已保存到 review_data_adjusted.csv！")



# In[13]:


# 数据量太大，对每个 DataFrame 进行随机采样
user_data = user_data.sample(frac=0.3, random_state=42).reset_index(drop=True)  # 保留 30% 数据
business_data = business_data.sample(frac=0.3, random_state=42).reset_index(drop=True)
review_data = review_data.sample(frac=0.3, random_state=42).reset_index(drop=True)
checkin_data = checkin_data.sample(frac=0.3, random_state=42).reset_index(drop=True)


# In[10]:


import pandas as pd
from transformers import pipeline
import time
from requests.exceptions import ConnectTimeout, ConnectionError

# 明确指定使用的模型及其版本
model_name = "distilbert-base-uncased-finetuned-sst-2-english"
revision = "714eb0f"
local_model_path = "D:\\models/distilbert-base-uncased-finetuned-sst-2-english"  # 本地模型路径

# 权重参数，控制原始评分和调整评分的比例
weight_original = 0.7
weight_adjusted = 0.3  # 直接指定，避免误解


# 定义评分调整函数
def adjust_rating_by_sentiment(text, original_stars):
    try:
        result = sentiment_analyzer(text)[0]
        label = result.get('label', 'NEUTRAL')  # 避免KeyError
        score = result.get('score', 0.5)  # 取默认值 0.5，避免 None

        if label == 'POSITIVE':
            adjusted_stars = min(original_stars + score * 0.5, 5)  # 不能超过 5
        elif label == 'NEGATIVE':
            adjusted_stars = max(original_stars - score * 0.5, 1)  # 不能低于 1
        else:
            adjusted_stars = original_stars

        # 计算加权平均值
        final_stars = weight_original * original_stars + weight_adjusted * adjusted_stars
        return round(final_stars, 2)  # 保留两位小数
    except Exception as e:
        print(f"Error processing text: {text}, error: {e}")
        return original_stars  # 返回原始评分作为默认值

# 先尝试加载模型
max_retries = 3
for attempt in range(max_retries):
    try:
        sentiment_analyzer = pipeline("sentiment-analysis", model=local_model_path)
        print(f"✅ 成功加载本地模型: {local_model_path}")
        break
    except (OSError, ValueError) as e:
        print(f"❌ 失败: {e}")
        if attempt < max_retries - 1:
            print(f"🔄 重试 {attempt + 1}/{max_retries}...")
            time.sleep(3)
        else:
            print("⚠️ 无法加载模型，退出程序")
            exit()

# 对整个数据集应用调整
adjusted_stars_list = []
for index, row in review_data.iterrows():
    text = row['text']
    original_stars = row['stars']
    
    # 重试机制
    retries = 3
    for attempt in range(retries):
        try:
            adjusted_stars = adjust_rating_by_sentiment(text, original_stars)
            break
        except (ConnectTimeout, ConnectionError) as e:
            if attempt < retries - 1:
                print(f"🔄 请求超时，重试 {attempt + 1}/{retries}...")
                time.sleep(1)
            else:
                print(f"❌ 失败: {e}")
                adjusted_stars = original_stars
    
    adjusted_stars_list.append(adjusted_stars)

# 添加修正后的评分到数据框
review_data['adjusted_stars'] = adjusted_stars_list

# 保存调整后的数据
output_file = 'review_data_adjusted.csv'
try:
    review_data.to_csv(output_file, index=False)
    print(f"✅ 处理完成，数据已保存至 {output_file}")
except Exception as e:
    print(f"❌ 保存文件失败: {e}")


# In[7]:


# 启动 Dask 集群
from dask.distributed import Client
client = Client(n_workers=4, threads_per_worker=2, memory_limit='2GB')
print("Dask 集群已启动")

# ========== 数据预处理 ==========
def preprocess_data():
    try:
        # 用户特征（确保列存在）
        user_features = user_data[['user_id', 'review_count', 'average_stars']]
        user_features = user_features.astype({'user_id': 'category'}).compute()
        
        # 商家特征（过滤无效数据）
        business_processed = business_data[['business_id', 'categories']]
        business_processed = business_processed.compute()
        business_processed['category'] = business_processed['categories'].str.split(',').str[0].str.strip()
        le = LabelEncoder()
        business_processed['category_id'] = le.fit_transform(business_processed['category'].fillna('Other'))
        
        # 评论数据（分块处理）
        review_processed = review_data[['user_id', 'business_id', 'stars']]
        review_processed = review_processed.compute()
        
        # 签到数据（修复日期解析）
        checkin_processed = checkin_data.compute()
        checkin_processed['checkin_dates'] = checkin_processed['date'].str.split(',')
        checkin_exploded = checkin_processed.explode('checkin_dates')
        checkin_exploded['checkin_time'] = pd.to_datetime(
            checkin_exploded['checkin_dates'], 
            format='%Y-%m-%d %H:%M:%S', 
            errors='coerce'
        )
        checkin_exploded = checkin_exploded.dropna(subset=['checkin_time'])
        # 数据一致性检查：过滤掉不在 user_df 中的 user_id
        valid_user_ids = user_features['user_id'].unique()
        review_processed = review_processed[review_processed['user_id'].isin(valid_user_ids)]
        
        return user_features, business_processed, review_processed, checkin_exploded, le
    
    except KeyError as e:
        print(f"关键列缺失: {str(e)}")
        client.close()
        raise


# 调整特征工程逻辑：  
# 因为签到数据中没有 'user_id'，我们改为基于 business_id 进行特征工程。计算每个商家的签到次数、评分统计等特征。
# 商家长期行为特征:
# 使用 business_id 代替 user_id 来计算签到频率和评分统计。  
# 上下文特征：
# 提取最近签到时间和地理位置特征，这些特征仍然可以基于 business_id 进行计算。  
# 交互矩阵：
# 构建基于 business_id 和 category_id 的交互矩阵，用于后续分析

# In[8]:


from scipy.sparse import lil_matrix, csr_matrix
from tqdm import tqdm
import scipy.sparse
# 假设 user_df 包含用户的 DataFrame


class MemorySafeFeatureEngineer:
    def __init__(self, user_df, business_df, review_df, checkin_df):
        """初始化方法"""
    
        self.user_df = user_df
        self.business_df = business_df
        self.review_df = review_df
        self.checkin_df = checkin_df
        user_id_map = {user_id: idx for idx, user_id in enumerate(user_df['user_id'].unique())}
        self.user_id_map = None  # 用于存储用户ID到索引的映射

    def build_sparse_interaction_matrix(self, chunk_size=100000):
        """分块构建稀疏交互矩阵"""
        # 获取所有类别ID
        all_categories = self.business_df['category_id'].unique()
        num_categories = len(all_categories)
        
        # 初始化稀疏矩阵
        user_ids = self.user_df['user_id'].unique()
        num_users = len(user_ids)
        self.user_id_map = {uid: i for i, uid in enumerate(user_ids)}  # 存储为类属性

        
        # 使用LIL格式便于逐步填充
        count_matrix = lil_matrix((num_users, num_categories), dtype=np.int32)
        mean_matrix = lil_matrix((num_users, num_categories), dtype=np.float32)
        
        # 分块处理评论数据
        merged = pd.merge(
            self.review_df[['user_id', 'business_id', 'stars']],
            self.business_df[['business_id', 'category_id']],
            on='business_id'
        )
        
        # 按用户分块处理
        user_chunks = np.array_split(merged['user_id'].unique(), 
                                   len(merged) // chunk_size + 1)
        for chunk_users in tqdm(user_chunks, desc="Processing chunks"):
            chunk_data = merged[merged['user_id'].isin(chunk_users)]
            for user_id, group in chunk_data.groupby('user_id'):
                user_idx = self.user_id_map[user_id]  # 使用类属性
                if user_id not in user_id_map:
                    continue  # 跳过无效的 user_id
                user_idx = user_id_map[user_id]
                cats = group['category_id'].value_counts()
                means = group.groupby('category_id')['stars'].mean()
                for cat_id, count in cats.items():
                    count_matrix[user_idx, cat_id] = count
                    mean_matrix[user_idx, cat_id] = means[cat_id]
        return csr_matrix(count_matrix), csr_matrix(mean_matrix)


# In[9]:



from sklearn.decomposition import TruncatedSVD
import scipy.sparse
from torch.utils.data import Dataset  # 如果使用 PyTorch

class SparseDataset(Dataset):
    def __init__(self, features):
        """初始化稀疏数据集"""
        self.features = features

    def __len__(self):
        """返回样本数量"""
        return self.features.shape[0]

    def __getitem__(self, idx):
        """获取指定索引的样本"""
        return self.features[idx]


# In[10]:



class OptimizedFeatureEngineer(MemorySafeFeatureEngineer):
    def __init__(self, user_df, business_df, review_df, checkin_df):
        """初始化方法"""
        super().__init__(user_df, business_df, review_df, checkin_df)
    
    def _build_user_features(self):
        """用户基础特征"""
        # 移除 'user_id' 列，仅保留数值型特征
        numerical_columns = ['review_count', 'average_stars']
        features = self.user_df[numerical_columns]
        
        # 标准化处理
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(features)
        return scaled_features
    
    
    def build_sparse_interaction_matrix(self):
        """
        构建稀疏交互矩阵
        返回值：
        - count_matrix: 用户-商家交互次数矩阵
        - mean_matrix: 用户-商家评分均值矩阵
        """
        # 示例逻辑：根据 review_df 构建交互矩阵
        interaction_df = self.review_df.groupby(['user_id', 'business_id']).agg(
            review_count=('stars', 'count'),
            avg_rating=('stars', 'mean')
        ).reset_index()

        # 构建稀疏矩阵
        user_ids = self.user_df['user_id'].unique()
        business_ids = self.business_df['business_id'].unique()
        user_map = {user_id: idx for idx, user_id in enumerate(user_ids)}
        business_map = {business_id: idx for idx, business_id in enumerate(business_ids)}

        rows, cols, data_count, data_mean = [], [], [], []
        for _, row in interaction_df.iterrows():
            user_idx = user_map.get(row['user_id'], -1)
            business_idx = business_map.get(row['business_id'], -1)
            if user_idx != -1 and business_idx != -1:
                rows.append(user_idx)
                cols.append(business_idx)
                data_count.append(row['review_count'])
                data_mean.append(row['avg_rating'])

        count_matrix = scipy.sparse.coo_matrix((data_count, (rows, cols)), shape=(len(user_ids), len(business_ids)))
        mean_matrix = scipy.sparse.coo_matrix((data_mean, (rows, cols)), shape=(len(user_ids), len(business_ids)))

        return count_matrix, mean_matrix
    
    def filter_low_frequency_features(self, sparse_matrix, min_count=100):
        """
        过滤低频特征
        参数：
        - sparse_matrix: 输入的稀疏矩阵
        - min_count: 最小交互次数阈值
        返回值：
        - 过滤后的稀疏矩阵
        """
        # 计算每列的交互次数
        col_counts = np.array(sparse_matrix.sum(axis=0)).squeeze()
        mask = col_counts >= min_count

        # 如果过滤后列数过少，降低阈值
        if mask.sum() < 10:  # 至少保留 10 列
            min_count = max(1, min_count // 2)
            mask = col_counts >= min_count

        # 过滤低频列
        filtered_matrix = sparse_matrix[:, mask]
        return filtered_matrix
    
    def embed_features(self, sparse_matrix, n_components=64):
        """
        对交互特征进行降维
        参数：
        - sparse_matrix: 输入的稀疏矩阵
        - n_components: 降维后的维度
        返回值：
        - 降维后的稠密矩阵
        """
        # 动态调整 n_components，确保不超过输入矩阵的列数
        n_features = sparse_matrix.shape[1]
        n_components = min(n_components, n_features)

        if n_features < 2:
            raise ValueError("输入矩阵的列数不足，无法进行降维操作。")

        # 使用 TruncatedSVD 进行降维
        svd = TruncatedSVD(n_components=n_components, random_state=42)
        embedded_matrix = svd.fit_transform(sparse_matrix)
        return embedded_matrix
    
    def build_features(self):
        """生成优化后的分层特征"""
        # 1. 构建基础特征（非稀疏）
        user_features = self._build_user_features()
        print(f"用户基础特征矩阵尺寸: {user_features.shape}")
        
        # 2. 构建稠密交互特征
        count_matrix, mean_matrix = self.build_sparse_interaction_matrix()
        interaction_features = scipy.sparse.hstack([count_matrix, mean_matrix], format='csr')
        print(f"交互特征矩阵尺寸: {interaction_features.shape}")

        # 3. 过滤低频特征
        filtered_features = self.filter_low_frequency_features(interaction_features, min_count=100)
        print(f"过滤后特征矩阵尺寸: {filtered_features.shape}")

        # 4. 对交互特征降维
        embedded_features = self.embed_features(filtered_features, n_components=64)
        print(f"降维后特征矩阵尺寸: {embedded_features.shape}")

        # 5. 合并特征
        combined_features = np.hstack([user_features, embedded_features])
        print(f"最终合并特征矩阵尺寸: {combined_features.shape}")
        return combined_features


# In[11]:


import gc

# 在完成某些操作后释放内存
gc.collect()


# In[12]:


from sklearn.model_selection import train_test_split
if __name__ == "__main__":
    try:
        # 数据预处理
        user_df, business_df, review_df, checkin_df, encoder = preprocess_data()
        
        # 初始化特征工程
        engineer = OptimizedFeatureEngineer(user_df, business_df, review_df, checkin_df)
        features = engineer.build_features()
        
        # 模拟生成标签数据
        labels = np.random.randint(0, 2, size=features.shape[0])

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(features, labels, test_size=0.2, random_state=42)

        # 数据标准化
        scaler = StandardScaler(with_mean=False)  # 对稀疏矩阵使用 with_mean=False
        train_features = scaler.fit_transform(X_train)
        test_features = scaler.transform(X_test)

        # 保存处理后的数据
        if isinstance(train_features, np.ndarray):
            # 如果已经是 numpy 数组，直接保存
            np.save("train_features.npy", train_features)
        else:
            # 如果是稀疏矩阵，先转换为密集矩阵再保存
            np.save("train_features.npy", train_features.toarray())
        np.save("test_features.npy", test_features)
        np.save("train_labels.npy", y_train)
        np.save("test_labels.npy", y_test)
        print("数据已保存到当前工作目录！")
        
        # 转换为PyTorch Dataset
        dataset = SparseDataset(features)
        train_loader = DataLoader(dataset, batch_size=256, shuffle=True)
        def calculate_sparsity(matrix):
            if scipy.sparse.issparse(matrix):
                return 1 - matrix.nnz / np.prod(matrix.shape)
            else:
                return 1 - np.count_nonzero(matrix) / np.prod(matrix.shape)
            # 修改后的输出
        print(f"特征矩阵尺寸: {features.shape} (稀疏度: {calculate_sparsity(features):.2%})")
        
    
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        client.close()


# 原因：类别型特征（如商家类别）直接展开为稀疏矩阵、用户-物品交互矩阵中存在大量低频交互、特征工程未进行有效降维  
# 下一步：过滤低频特征、调整特征工程降维

# In[14]:


from typing import Tuple, Dict, List
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
# 修改 HRLReplayBuffer 的 state 构建（增加更多特征）
class HRLReplayBuffer(Dataset):
    def __init__(self, processed_data: Dict, args):
        self.processed_data = processed_data
        self.args = args
        
    def __len__(self):
        return len(self.processed_data['user_df'])

    def __getitem__(self, idx):
        user_row = self.processed_data['user_df'].iloc[idx]
        business_row = self.processed_data['business_df'].iloc[idx]
        interaction = self.processed_data['business_interacts'].loc[(user_row['user_id'], business_row['business_id'])]

        # 构建更丰富的特征（示例）
        state = np.concatenate([
            user_row[['age', 'avg_rating']].values.astype(np.float32),
            business_row[['popularity', 'category_embedding']].values.astype(np.float32),
            [interaction['rating']]
        ])
        
        action = np.array([interaction['business_id']], dtype=np.long)
        reward = np.array([interaction['business_rating']], dtype=np.float32)
        next_state = state.copy()  # 根据实际场景修改

        return state, action, reward, next_state


# 改进的 Dueling DQN 算法：  
# 增加网络宽度：将隐藏层的神经元数量从标准的 64 扩展到 256 和 128，增强了模型的表达能力。  
# 归一化与 Dropout：在网络中引入 LayerNorm 和 Dropout 层，以缓解过拟合问题并加速训练。  
# 深度扩展：通过增加网络层数，模型能够更好地捕捉复杂的数据模式。  
# 损失函数改进：使用 Huber 损失代替传统的均方误差损失，以减少异常值对训练的影响。  在高层策略和低层策略的损失计算中，分别采用均方误差损失和 Huber 损失的组合形式，确保模型在不同层次上的稳定性和鲁棒性。  
# 学习率调度：引入学习率调度器（StepLR），在训练过程中逐步降低学习率，从而帮助模型收敛到更优解。

# In[46]:


import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim.lr_scheduler import ReduceLROnPlateau
from torch.utils.data import Dataset, DataLoader

# 定义 DuelingDQN 模型
class DuelingDQN(nn.Module):
    def __init__(self, input_dim, action_dim):
        super().__init__()
        self.feature_layer = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        self.value_stream = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        self.advantage_stream = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, action_dim)
        )

    def forward(self, state):
        features = self.feature_layer(state)
        value = self.value_stream(features)
        advantage = self.advantage_stream(features)
        q_values = value + (advantage - advantage.mean(dim=-1, keepdim=True))
        return q_values


# 定义分层强化学习模型
class HierarchicalRL(nn.Module):
    def __init__(self, input_dim, num_categories, num_business_per_cat):
        super().__init__()
        self.num_categories = num_categories
        self.top_policy = DuelingDQN(input_dim, num_categories)
        self.sub_policies = nn.ModuleList([
            DuelingDQN(input_dim + num_categories, num_business_per_cat)
            for _ in range(num_categories)
        ])

    def forward(self, state, category_id=None):
        top_q_values = self.top_policy(state)
        if category_id is None:
            selected_category = torch.argmax(top_q_values, dim=1)
        else:
            selected_category = category_id

        goal = F.one_hot(selected_category, num_classes=self.num_categories).float()
        state_with_goal = torch.cat([state, goal], dim=1)

        mask = F.one_hot(selected_category, num_classes=self.num_categories).bool()
        sub_q_values = torch.stack([
            policy(state_with_goal) for policy in self.sub_policies
        ], dim=1)
        sub_q_values = sub_q_values[mask]
        return top_q_values, sub_q_values, selected_category


# In[47]:


# 定义 Focal Loss
class FocalLoss(nn.Module):
    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        return focal_loss

# 定义损失函数
def compute_loss(top_q_values, sub_q_values, actions, rewards, next_states, gamma=0.99):
    with torch.no_grad():
        _, next_sub_q, _ = model(next_states)
        target = rewards + gamma * next_sub_q.max(dim=1)[0]

    focal_loss_fn = FocalLoss()
    sub_loss = focal_loss_fn(sub_q_values.gather(1, actions), target.unsqueeze(1))
    top_loss = F.mse_loss(top_q_values, target.unsqueeze(1).expand(-1, top_q_values.size(1)))
    category_loss = F.cross_entropy(top_q_values, actions.squeeze())
    return sub_loss + top_loss + 0.1 * category_loss


# In[50]:


import numpy as np
from torch.utils.data import DataLoader, TensorDataset

# 加载数据
train_features = np.load("train_features.npy")  # 训练集特征
test_features = np.load("test_features.npy")    # 测试集特征
train_labels = np.load("train_labels.npy")      # 训练集标签
test_labels = np.load("test_labels.npy")        # 测试集标签

# 转换为PyTorch Dataset
train_dataset = TensorDataset(
    torch.FloatTensor(train_features), 
    torch.FloatTensor(train_labels))
test_dataset = TensorDataset(
    torch.FloatTensor(test_features), 
    torch.FloatTensor(test_labels))


# 创建DataLoader
train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=256, shuffle=False)


# In[51]:


input_dim = train_features.shape[1]  # 特征维度
num_categories = 50                  # 类别数量
num_business_per_cat = 100           # 每个类别下的商家数量

model = HierarchicalRL(
    input_dim=input_dim,
    num_categories=num_categories,
    num_business_per_cat=num_business_per_cat
)


# In[61]:


import torch.optim as optim
# 定义训练函数
def train_model(model, train_loader, optimizer, num_epochs=15, gamma=0.99):
    global best_ndcg, patience_counter
    scheduler = ReduceLROnPlateau(optimizer, mode='max', factor=0.1, patience=3, verbose=True)
    best_ndcg = 0.0
    patience_counter = 0
    early_stop_patience = 10
    
    model.train()
    for epoch in range(num_epochs):
        total_loss = 0
        for batch_features, batch_labels in train_loader:
            optimizer.zero_grad()
            
            # 前向传播
            top_q_values, sub_q_values, _ = model(batch_features)
            
            # 调整标签形状
            batch_labels = batch_labels.unsqueeze(1).expand(-1, num_business_per_cat)
            
            # 计算损失
            loss = F.binary_cross_entropy_with_logits(sub_q_values, batch_labels.float())
            
            # 反向传播
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        
        print(f"Epoch {epoch+1}/{num_epochs} | Loss: {total_loss/len(train_loader):.4f}")


# In[65]:


input_dim = 66  # 合并后的特征维度
num_categories = 50
num_business_per_cat = 100
# 定义模型和优化器
input_dim = train_features.shape[1]
num_categories = 50
num_business_per_cat = 100
model = HierarchicalRL(input_dim, num_categories, num_business_per_cat)
optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)


# In[66]:


# train_loader = DataLoader(...)

train_model(model, train_loader, optimizer, num_epochs=15)


# In[67]:


def ndcg_at_k(preds, labels, k=10):
    """计算 NDCG@K（假设 labels 是二维张量）"""
    # 确保 labels 是二维
    if labels.dim() == 1:
        labels = labels.unsqueeze(1).expand(-1, preds.size(1))
    
    # 获取 top-k 索引
    _, indices = torch.topk(preds, k=k, dim=1)
    # 收集相关分数
    rel = labels.gather(1, indices)
    
    # 计算 DCG
    dcg = (rel / torch.log2(torch.arange(2, k+2, device=rel.device))).sum(dim=1)
    
    # 计算 IDCG
    sorted_labels, _ = torch.sort(labels, dim=1, descending=True)
    idcg = (sorted_labels[:, :k] / torch.log2(torch.arange(2, k+2, device=labels.device))).sum(dim=1)
    
    # 避免除以零
    idcg[idcg == 0] = 1e-10  # 将 idcg 为 0 的情况设置为一个很小的值
    
    return (dcg / idcg).mean().item()


# In[68]:


def evaluate_model(model, test_loader, k=10):
    model.eval()
    ndcg_scores = []
    with torch.no_grad():
        for batch_features, batch_labels in test_loader:
            # 前向传播
            _, sub_q_values, _ = model(batch_features)
            
            # 调整标签形状
            if batch_labels.dim() == 1:
                batch_labels = batch_labels.unsqueeze(1).expand(-1, sub_q_values.size(1))
            
            # 计算 NDCG@K
            ndcg = ndcg_at_k(sub_q_values, batch_labels, k=k)
            ndcg_scores.append(ndcg)
    
    print(f"NDCG@{k}: {np.mean(ndcg_scores):.4f}")

# 开始评估
evaluate_model(model, test_loader, k=10)


# In[1]:


#改进前NDCG@10: 0.4984，第一次改进NDCG@10: 0.5004，第二次改进0.5006,第三次改进后NDCG@10: 0.5006


# In[ ]:


class DuelingDQN(nn.Module):
    def __init__(self, input_dim, action_dim):
        super().__init__()
        self.feature_layer = nn.Sequential(
            nn.Linear(input_dim, 256),  # 扩大网络宽度
            nn.LayerNorm(256),          # 添加归一化
            nn.ReLU(),
            nn.Dropout(0.2),            # 添加 Dropout
            nn.Linear(256, 128),
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        self.value_stream = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        self.advantage_stream = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, action_dim)
        )

    def forward(self, state):
        # 提取共享特征
        features = self.feature_layer(state)

        # 计算价值流和优势流
        value = self.value_stream(features)  # [batch_size, 1]
        advantage = self.advantage_stream(features)  # [batch_size, action_dim]

        # 组合 Q 值
        q_values = value + (advantage - advantage.mean(dim=-1, keepdim=True))
        return q_values
def compute_loss(top_q_values, sub_q_values, actions, rewards, next_states, gamma=0.99):
    # DQN 目标计算
    with torch.no_grad():
        _, next_sub_q, _ = model(next_states)
        target = rewards + gamma * next_sub_q.max(dim=1)[0]
    # 改进优化器配置
optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)  # 使用 AdamW
scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)  # 学习率调度

# 改进训练循环
def train_model(model, train_loader, optimizer, num_epochs=50):  # 增加训练轮次
    model.train()
    for epoch in range(num_epochs):
        total_loss = 0
        for batch_features, batch_labels in train_loader:
            optimizer.zero_grad()
            
            # 添加数据增强
            batch_features = add_gaussian_noise(batch_features, std=0.01)
            
            # 前向传播
            top_q, sub_q, selected_cat = model(batch_features)
            
            # 计算损失
            loss = compute_loss(top_q, sub_q, batch_labels)  # 需适配实际动作选择
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        
        scheduler.step()
        print(f"Epoch {epoch+1} | Loss: {total_loss/len(train_loader):.4f} | LR: {scheduler.get_last_lr()[0]:.2e}")

    # 使用 Huber 损失
    sub_loss = F.smooth_l1_loss(sub_q_values.gather(1, actions), target.unsqueeze(1))
    top_loss = F.mse_loss(top_q_values, target.unsqueeze(1).expand(-1, num_categories))
    return top_loss + sub_lossmodel = HierarchicalRL(input_dim, num_categories, num_business_per_cat)
optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)

def train_model(model, train_loader, optimizer, num_epochs=10):
    model.train()
    for epoch in range(num_epochs):
        total_loss = 0
        for batch_features, batch_labels in train_loader:
            optimizer.zero_grad()
            
            # 前向传播
            top_q_values, sub_q_values, _ = model(batch_features)
            
            # 调整标签形状
            batch_labels = batch_labels.unsqueeze(1).expand(-1, num_business_per_cat)
            
            # 计算损失
            loss = F.binary_cross_entropy_with_logits(sub_q_values, batch_labels.float())
            
            # 反向传播
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        
        print(f"Epoch {epoch+1}/{num_epochs} | Loss: {total_loss/len(train_loader):.4f}")

