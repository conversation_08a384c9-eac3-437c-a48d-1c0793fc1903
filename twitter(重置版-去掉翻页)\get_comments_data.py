# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2025/2/11 13:45
@Python  :  Python3.7
@Desc    :  根据推文地址获取推文评论数据
"""

import requests
import json
import time
import random
import atexit
import signal
import sys
import threading
from loguru import logger
from datetime import datetime
from db import RedisClient
from Spiders.utils import DB_BASE
from Spiders.settings import POLL_DB


class Tweet_detial_Spider(DB_BASE):

    def __init__(self):
        super().__init__()
        self.mysql_conn = POLL_DB.connection()

        self.redis_client = RedisClient(redis_name='cookies:twitter')
        self.redis_is_using = RedisClient(redis_name='cookies:is_using')
        self.current_cookie_account = None
        self.cookie_data = self.get_cookie_from_redis()

        self.progress_redis = RedisClient(redis_name='twitter:crawler:progress')
        self.progress_key = "current_position"

        self.api_url = "https://x.com/i/api/graphql/4b8_JHYvPYebY8PpwfUdIg/TweetDetail"
        self.headers = {
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": self.cookie_data.get('csrf_token', '')
        }
        self.cookies = {
            "auth_token": self.cookie_data.get('auth_token', ''),
            "ct0": self.cookie_data.get('csrf_token', ''),
        }
        self.base_params = {
            "variables": "",
            "features": "{\"rweb_video_screen_enabled\":false,\"payments_enabled\":false,\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_show_grok_translated_post\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":true,\"responsive_web_enhance_cards_enabled\":false}",
            "fieldToggles": "{\"withArticleRichContentState\":true,\"withArticlePlainText\":false,\"withGrokAnalyze\":false,\"withDisallowedReplyControls\":false}"
        }
        proxy_ip = self.cookie_data.get('proxy_ip', '')
        self.proxy = {
            "http": proxy_ip,
            "https": proxy_ip
        } if proxy_ip else {}
        
        # 注册程序退出时的清理函数
        atexit.register(self.cleanup_on_exit)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def get_cookie_from_redis(self):
        """从Redis中获取cookie信息"""
        try:
            # 获取正在使用的账号名列表
            using_cookies = self.redis_is_using.all()
            using_account_names = set(using_cookies.values()) if using_cookies else set()
            logger.info(f"当前正在使用的账号: {using_account_names}")
            
            # 获取所有可用的账号
            all_cookies = self.redis_client.all()
            if not all_cookies:
                logger.error("Redis中没有可用的cookie")
                return {}
            
            available_accounts = [account for account in all_cookies.keys() if account not in using_account_names]
            
            if not available_accounts:
                logger.warning("所有cookie都在使用中，使用随机cookie")
                account_name, cookie_json = self.redis_client.get_random_account()
            else:
                # 随机选择一个可用账号
                selected_account = random.choice(available_accounts)
                cookie_json = all_cookies[selected_account]
                account_name = selected_account
            
            if not account_name or not cookie_json:
                logger.error("无法从Redis获取cookie信息:'cookies:twitter'哈希表为空")
                return {}
            
            logger.success(f"成功提取账号 {account_name} 的cookie信息")
            
            # 将选择的账号标记为正在使用 - 统一使用Krickliu前缀
            self.redis_is_using.set("Krickliu_single", account_name)
            self.current_cookie_account = account_name
            logger.success(f"已将账号 {account_name} 标记为正在使用")

            try:
                cookie_data = json.loads(cookie_json)
                if not isinstance(cookie_data, dict):
                    logger.error("从Redis获取的cookie信息不是有效的字典格式")
                    return {}
                return cookie_data
            except json.JSONDecodeError as e:
                logger.error(f"从Redis获取的cookie信息不是有效的JSON格式: {e}")
                return {}
        except Exception as e:
            logger.error(f"从Redis获取cookie信息失败: {e}")
            return {}

    def cleanup_on_exit(self):
        """程序退出时清理cookies:is_using中的记录"""
        try:
            if self.current_cookie_account:
                # 从cookies:is_using中移除自己的记录 - 统一使用Krickliu前缀
                self.redis_is_using.delete("Krickliu_single")
                logger.success(f"已从cookies:is_using中移除记录: Krickliu_single -> {self.current_cookie_account}")
                self.current_cookie_account = None
        except Exception as e:
            logger.error(f"清理cookies:is_using记录失败: {e}")

    def get_tweets_and_users_from_mysql(self):
        """从MySQL获取推文ID和用户名信息，并过滤最近20天内的数据"""
        try:
            cursor = self.mysql_conn.cursor()

            last_processed_id = self.progress_redis.get(self.progress_key)
            logger.info(f"从Redis获取的上次处理位置: {last_processed_id}")

            if last_processed_id:
                last_processed_id = int(last_processed_id)
                logger.info(f"从上次处理位置继续：tweet_id > {last_processed_id}")
                sql = """
                SELECT t.tweet_id, u.username, t.tweet_time
                FROM twitter_tweets t 
                JOIN twitter_users u ON t.user_id = u.user_id 
                WHERE t.tweet_id IS NOT NULL 
                AND u.username IS NOT NULL
                AND t.tweet_time IS NOT NULL
                AND t.tweet_time >= DATE_SUB(NOW(), INTERVAL 20 DAY)
                AND t.tweet_id > %s
                ORDER BY t.tweet_id ASC
                """
                cursor.execute(sql, (last_processed_id,))
            else:
                logger.info("从头开始处理数据")
                sql = """
                SELECT t.tweet_id, u.username, t.tweet_time
                FROM twitter_tweets t 
                JOIN twitter_users u ON t.user_id = u.user_id 
                WHERE t.tweet_id IS NOT NULL 
                AND u.username IS NOT NULL
                AND t.tweet_time IS NOT NULL
                AND t.tweet_time >= DATE_SUB(NOW(), INTERVAL 20 DAY)
                ORDER BY t.tweet_id ASC
                """
                cursor.execute(sql)

            results = cursor.fetchall()
            cursor.close()

            if not results:
                logger.warning("未从MySQL获取到有效数据（可能没有最近20天内的推文）")
                return []

            data_list = []
            for result in results:
                username = result[1].lstrip('@') if result[1] else ''
                tweet_time = result[2]
                data_list.append({
                    'tweet_id': result[0],
                    'username': username,
                    'tweet_time': tweet_time
                })

            logger.success(f"成功从MySQL获取 {len(data_list)} 条数据（最近20天内）")
            return data_list
        except Exception as e:
            logger.error(f"从MySQL获取数据失败: {e}")
            return []

    def update_progress(self, tweet_id):
        """更新处理进度"""
        try:
            progress_key = "twitter:crawler:progress:current_position"
            self.progress_redis.set(progress_key, str(tweet_id))
            logger.info(f"已更新处理进度：tweet_id = {tweet_id}")
        except Exception as e:
            logger.error(f"更新处理进度失败: {e}")

    def reset_progress(self):
        """重置处理进度（需要重新开始时使用）"""
        try:
            self.progress_redis.delete(self.progress_key)
            logger.info("已重置处理进度")
        except Exception as e:
            logger.error(f"重置处理进度失败: {e}")

    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间格式到标准格式"""
        try:
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}")
            return None

    def get_available_cookie_for_thread(self, thread_id):
        """为指定线程获取可用的cookie，避开正在使用的cookie"""
        while True:
            try:
                # 获取正在使用的账号名列表
                using_cookies = self.redis_is_using.all()
                using_account_names = set(using_cookies.values()) if using_cookies else set()
                logger.info(f"线程{thread_id} - 当前正在使用的账号: {using_account_names}")
                
                # 获取所有可用的账号
                all_cookies = self.redis_client.all()
                if not all_cookies:
                    logger.error(f"线程{thread_id} - Redis中没有可用的cookie")
                    return {}
                
                available_accounts = [account for account in all_cookies.keys() if account not in using_account_names]
                
                if not available_accounts:
                    logger.warning(f"线程{thread_id} - 所有cookie都在使用中，等待30秒后重试...")
                    time.sleep(30)
                    continue
                
                # 随机选择一个可用账号
                selected_account = random.choice(available_accounts)
                cookie_json = all_cookies[selected_account]
                
                logger.info(f"线程{thread_id} - 选择账号: {selected_account}")
                
                try:
                    cookie_data = json.loads(cookie_json)
                    if not isinstance(cookie_data, dict):
                        logger.error(f"线程{thread_id} - cookie信息不是有效的字典格式")
                        continue
                    
                    # 将选择的账号标记为正在使用 - 统一使用Krickliu前缀
                    self.redis_is_using.set(f"Krickliu_thread_{thread_id}", selected_account)
                    logger.success(f"线程{thread_id} - 已将账号 {selected_account} 标记为正在使用")
                    
                    return cookie_data
                    
                except json.JSONDecodeError as e:
                    logger.error(f"线程{thread_id} - cookie信息不是有效的JSON格式: {e}")
                    continue
                    
            except Exception as e:
                logger.error(f"线程{thread_id} - 获取cookie信息失败: {e}")
                time.sleep(10)
                continue

    def setup_thread_environment(self, thread_id):
        """为线程设置独立的环境（cookie、headers、代理等）"""
        cookie_data = self.get_available_cookie_for_thread(thread_id)
        if not cookie_data:
            return None, None, None, None

        # 创建线程独立的数据库连接
        mysql_conn = POLL_DB.connection()

        headers = {
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": cookie_data.get('csrf_token', '')
        }
        cookies = {
            "auth_token": cookie_data.get('auth_token', ''),
            "ct0": cookie_data.get('csrf_token', ''),
        }
        # 修复代理IP问题：直接使用proxy_ip的值
        proxy_ip = cookie_data.get('proxy_ip', '')
        proxy = {
            "http": proxy_ip,
            "https": proxy_ip
        } if proxy_ip else {}

        return mysql_conn, headers, cookies, proxy

    def process_tweets_thread(self, tweet_chunk, thread_id):
        """线程处理推文评论的方法"""
        mysql_conn, headers, cookies, proxy = self.setup_thread_environment(thread_id)
        if not mysql_conn:
            logger.error(f"线程{thread_id} - 环境设置失败，线程退出")
            return

        try:
            logger.info(f"线程{thread_id} - 开始处理 {len(tweet_chunk)} 条推文")
            
            for tweet_user in tweet_chunk:
                tweet_id = tweet_user['tweet_id']
                username = tweet_user['username']

                try:
                    logger.info(f"线程{thread_id} - 开始处理推文: {tweet_id} ({username})")
                    self.send_req_get_data_thread(tweet_id, username, headers, cookies, proxy, thread_id)
                    self.update_progress(tweet_id)
                    logger.success(f"线程{thread_id} - 完成推文 {tweet_id} 的处理")

                except Exception as e:
                    logger.error(f"线程{thread_id} - 处理推文失败 tweet_id={tweet_id}: {e}")
                    self.update_progress(tweet_id)
                    continue

                time.sleep(random.uniform(8, 15))

        except Exception as e:
            logger.error(f"线程{thread_id} - 线程执行失败: {e}")
        finally:
            # 清理线程资源
            try:
                mysql_conn.close()
                self.redis_is_using.delete(f"Krickliu_thread_{thread_id}")
                logger.info(f"线程{thread_id} - 已清理资源和cookie使用记录")
            except Exception as e:
                logger.error(f"线程{thread_id} - 清理资源失败: {e}")

    def send_req_get_data_thread(self, tweet_id, username, headers, cookies, proxy, thread_id):
        """线程版本的数据获取方法"""
        # 第一次 第一层
        headers["referer"] = f"https://x.com/{username}/status/{tweet_id}"
        variables_dict = {
            "focalTweetId": tweet_id,
            "with_rux_injections": False,
            "rankingMode": "Relevance",
            "includePromotedContent": True,
            "withCommunity": True,
            "withQuickPromoteEligibilityTweetFields": True,
            "withBirdwatchNotes": True,
            "withVoice": True
        }
        cursor = None
        all_comments_data = []

        while True:
            if cursor:
                variables_dict["cursor"] = cursor
                variables_dict["referrer"] = "tweet"
                # 翻页请求不设置referer（根据抓包结果）
            else:
                # 只在第一次请求时设置referer
                headers["referer"] = f"https://x.com/{username}/status/{tweet_id}"
            
            request_params = self.base_params.copy()
            request_params["variables"] = json.dumps(variables_dict)
            logger.info(f"线程{thread_id} - 已更新请求参数: tweet_id={tweet_id}, username={username}, cursor={cursor}")

            time.sleep(random.uniform(8, 15))
            response = requests.get(
                self.api_url,
                headers=headers,
                cookies=cookies,
                params=request_params,
                proxies=proxy
            )

            if response.status_code != 200:
                logger.error(f"线程{thread_id} - 请求失败 状态码: {response.status_code}")
                response.raise_for_status()

            data = response.json()
            comments_data = self.parse_comments_data(data)
            all_comments_data.extend(comments_data)
            self.save_comments_to_mysql(comments_data, tweet_id)

            logger.info(f"线程{thread_id} - 开始获取下一页游标...")
            new_cursor = self.get_next_cursor(data)
            logger.info(f"线程{thread_id} - 获取游标结果: {new_cursor[:30] if new_cursor else 'None'}...")
            
            if new_cursor == cursor or not new_cursor:
                logger.info(f"线程{thread_id} - 翻页结束，原因: {'游标相同' if new_cursor == cursor else '未找到游标'}")
                break
            cursor = new_cursor
            logger.info(f"线程{thread_id} - 设置新游标，准备翻页请求")

            time.sleep(random.uniform(8, 15))
            logger.info(f"线程{thread_id} - 等待完成，开始下一轮翻页循环")

            # 层级评论数据
            logger.info(f"线程{thread_id} - 开始检查是否有深层评论...")
            next_control = self.get_next_control(data)
            logger.info(f"线程{thread_id} - 深层评论检查结果: {'有' if next_control else '无'}")
            processed_controls = set()  # 防止死循环
            while next_control:
                controller_data, id_data, screen_name = next_control
                
                # 检查是否已经处理过这个controllerData，防止死循环
                control_key = f"{controller_data}_{id_data}"
                if control_key in processed_controls:
                    logger.warning(f"线程{thread_id} - 检测到重复的controllerData，退出深层循环")
                    break
                processed_controls.add(control_key)
                
                logger.info(f"线程{thread_id} - 开始深层评论请求: {id_data} ({screen_name})")
                
                variables = json.dumps({
                    "focalTweetId": id_data,
                    "referrer": "search_tweet",
                    "controller_data": controller_data,
                    "with_rux_injections": False,
                    "rankingMode": "Relevance",
                    "includePromotedContent": True,
                    "withCommunity": True,
                    "withQuickPromoteEligibilityTweetFields": True,
                    "withBirdwatchNotes": True,
                    "withVoice": True
                })
                variables_control = self.base_params.copy()
                variables_control["variables"] = variables

                # 深层请求
                deep_headers = headers.copy()
                deep_headers["referer"] = f"https://twitter.com/{screen_name}/status/{id_data}"
                
                time.sleep(random.uniform(8, 15))
                response_deep = requests.get(
                    self.api_url,
                    headers=deep_headers,
                    cookies=cookies,
                    params=variables_control,
                    proxies=proxy
                )
                if response_deep.status_code != 200:
                    logger.error(f"线程{thread_id} - 深层请求失败 状态码: {response_deep.status_code}")
                    break  # 出错时退出循环，而不是抛异常

                additional_data = response_deep.json()
                additional_comments_data = self.parse_deep_comments_data(additional_data)
                all_comments_data.extend(additional_comments_data)
                self.save_deep_comments_to_mysql(additional_comments_data, tweet_id)
                
                logger.info(f"线程{thread_id} - 深层评论请求完成，获取{len(additional_comments_data)}条评论")

                next_control = self.get_next_control(additional_data)

            logger.info(f"线程{thread_id} - 深层评论循环结束")

        logger.success(f"线程{thread_id} - 请求完成，共获取 {len(all_comments_data)} 条评论数据")
        return all_comments_data


    def parse_comments_data(self, data):
        comments_data = []
        try:
            instructions = data.get('data', {}).get('threaded_conversation_with_injections_v2', {}).get('instructions',[])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                    logger.info(f"总条目数: {len(entries)}")

                    for entry in entries:
                        items = entry.get('content', {}).get('items', [])
                        for item in items:
                            tweet_results = item.get('item', {}).get('itemContent', {}).get('tweet_results', {})
                            result = tweet_results.get('result', {})
                            legacy = result.get('legacy', {})
                            screen_name = result.get('core', {}).get('user_results', {}).get('result', {}).get('legacy', {}).get('screen_name')
                            views_count = result.get('views', {}).get('count', 0)

                            if legacy:
                                editable_time = None
                                edit_control = result.get('edit_control', {})
                                editable_until_msecs = edit_control.get('editable_until_msecs')
                                if editable_until_msecs:
                                    try:
                                        editable_time = datetime.fromtimestamp(
                                            int(editable_until_msecs) / 1000
                                        ).strftime('%Y-%m-%d %H:%M:%S')
                                    except Exception as e:
                                        logger.warning(f"时间转换失败: {e}")

                                comment_data = {
                                    'comment_id': legacy.get('id_str'),
                                    'comment_text': legacy.get('full_text'),
                                    'comment_time': editable_time,
                                    'views': views_count,
                                    'replies': legacy.get('reply_count', 0),
                                    'retweets': legacy.get('retweet_count', 0),
                                    'favorites': legacy.get('favorite_count', 0),
                                    'bookmarks': legacy.get('bookmark_count', 0)
                                }
                                comments_data.append(comment_data)

            logger.success(f"成功解析 {len(comments_data)} 条评论数据")
            return comments_data
        except Exception as e:
            logger.error(f"评论数据解析失败: {e}")
            return []

    def parse_deep_comments_data(self, data):
        comments_data = []
        try:
            instructions = data.get('data', {}).get('threaded_conversation_with_injections_v2', {}).get('instructions',[])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                    logger.info(f"总条目数: {len(entries)}")

                    for entry in entries:
                        items = entry.get('content', {}).get('items', [])
                        for item in items:
                            tweet_results = item.get('item', {}).get('itemContent', {}).get('tweet_results', {})
                            result = tweet_results.get('result', {})
                            legacy = result.get('legacy', {})
                            screen_name = result.get('core', {}).get('user_results', {}).get('result', {}).get('legacy', {}).get('screen_name')
                            views_count = result.get('views', {}).get('count', 0)

                            if legacy:
                                editable_time = None
                                edit_control = result.get('edit_control', {})
                                editable_until_msecs = edit_control.get('editable_until_msecs')
                                if editable_until_msecs:
                                    try:
                                        editable_time = datetime.fromtimestamp(
                                            int(editable_until_msecs) / 1000
                                        ).strftime('%Y-%m-%d %H:%M:%S')
                                    except Exception as e:
                                        logger.warning(f"时间转换失败: {e}")

                                comment_data = {
                                    'comment_id': legacy.get('id_str'),
                                    'comment_text': legacy.get('full_text'),
                                    'comment_time': editable_time,
                                    'views': views_count,
                                    'replies': legacy.get('reply_count', 0),
                                    'retweets': legacy.get('retweet_count', 0),
                                    'favorites': legacy.get('favorite_count', 0),
                                    'bookmarks': legacy.get('bookmark_count', 0),
                                    'parent_comment_id': legacy.get('in_reply_to_status_id_str', '')
                                }
                                comments_data.append(comment_data)

            logger.success(f"成功解析 {len(comments_data)} 条评论数据")
            return comments_data
        except Exception as e:
            logger.error(f"评论数据解析失败: {e}")
            return []

    def get_next_cursor(self, data):
        """翻页逻辑"""
        try:
            instructions = data.get('data', {}).get('threaded_conversation_with_injections_v2', {}).get('instructions',[])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])

                    for entry in entries:
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineItem':
                            itemContent = content.get('itemContent', {})
                            if itemContent.get('itemType') == 'TimelineTimelineCursor':
                                cursor_value = itemContent.get('value')
                                if cursor_value:
                                    logger.success(f"成功提取游标: {cursor_value[:30]}...")
                                    return cursor_value

            logger.warning("未找到有效游标")
            return None
        except Exception as e:
            logger.error(f"游标解析异常: {str(e)}")
            return None

    def get_next_control(self, data):
        """下一层评论逻辑"""
        try:
            instructions = data.get('data', {}).get('threaded_conversation_with_injections_v2', {}).get('instructions',[])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])

                    for entry in entries:
                        items = entry.get('content', {}).get('items', [])
                        for item in items:
                            controllerData = item.get('item', {}).get('clientEventInfo', {}).get('details', {}).get('timelinesDetails', {}).get('controllerData', '')
                            id_data = item.get('item', {}).get('itemContent', {}).get('tweet_results', {}).get('result', {}).get('legacy', {}).get('id_str', '')
                            screen_name = item.get('item', {}).get('itemContent', {}).get('tweet_results', {}).get('result', {}).get('core', {}).get('user_results', {}).get('result', {}).get('legacy', {}).get('screen_name', '')
                            if controllerData:
                                logger.success(f"成功提取controllerData: {controllerData[:10]}..., id_data: {id_data}, screen_name: {screen_name}")
                                return controllerData, id_data, screen_name

            logger.warning("未找到有效controllerData")
            return None

        except Exception as e:
            logger.error(f"controllerData解析异常: {str(e)}")
            return None

    def save_comments_to_mysql(self, comments_data, tweet_id):
        """将评论数据保存到MySQL"""
        try:
            if not comments_data:
                logger.warning("没有评论数据需要保存")
                return

            sql = """
            INSERT INTO twitter_comments (comment_id, tweet_id, comment_text, comment_time,
                            views, replies, retweets, favorites, bookmarks)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            comment_id=VALUES(comment_id),
            tweet_id=VALUES(tweet_id),
            comment_text=VALUES(comment_text),
            comment_time=VALUES(comment_time),
            views=VALUES(views),
            replies=VALUES(replies),
            retweets=VALUES(retweets),
            favorites=VALUES(favorites),
            bookmarks=VALUES(bookmarks)
            """

            values_list = []
            for comment in comments_data:
                values = (
                    comment.get('comment_id'),
                    tweet_id,
                    comment.get('comment_text', ''),
                    comment.get('comment_time'),
                    comment.get('views', 0),
                    comment.get('replies', 0),
                    comment.get('retweets', 0),
                    comment.get('favorites', 0),
                    comment.get('bookmarks', 0)
                )
                values_list.append(values)

            logger.debug(f"保存评论数据 - 批量插入 {len(values_list)} 条记录")
            super().insert_mysql(sql, values_list)
            logger.success(f"成功保存 {len(comments_data)} 条评论到数据库")
        except Exception as e:
            logger.error(f"保存评论数据失败: {e}")

    def save_deep_comments_to_mysql(self, comments_data, tweet_id):
        """将深层评论数据保存到MySQL"""
        try:
            if not comments_data:
                logger.warning("没有评论数据需要保存")
                return

            sql = """
            INSERT INTO twitter_comments (comment_id, tweet_id, comment_text, comment_time,
                            views, replies, retweets, favorites, bookmarks, parent_comment_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            comment_id=VALUES(comment_id),
            tweet_id=VALUES(tweet_id),
            comment_text=VALUES(comment_text),
            comment_time=VALUES(comment_time),
            views=VALUES(views),
            replies=VALUES(replies),
            retweets=VALUES(retweets),
            favorites=VALUES(favorites),
            bookmarks=VALUES(bookmarks),
            parent_comment_id=VALUES(parent_comment_id)
            """

            values_list = []
            for comment in comments_data:
                values = (
                    comment.get('comment_id'),
                    tweet_id,
                    comment.get('comment_text', ''),
                    comment.get('comment_time'),
                    comment.get('views', 0),
                    comment.get('replies', 0),
                    comment.get('retweets', 0),
                    comment.get('favorites', 0),
                    comment.get('bookmarks', 0),
                    comment.get('parent_comment_id', '')
                )
                values_list.append(values)

            logger.debug(f"保存深层评论数据 - 批量插入 {len(values_list)} 条记录")
            super().insert_mysql(sql, values_list)
            logger.success(f"成功保存 {len(comments_data)} 条深层评论到数据库")
        except Exception as e:
            logger.error(f"保存深层评论数据失败: {e}")

    def cleanup_all_thread_cookies(self):
        """清理所有线程的cookie使用记录"""
        try:
            using_cookies = self.redis_is_using.all()
            if using_cookies:
                for field_name in using_cookies.keys():
                    if field_name.startswith("Krickliu_thread_"):
                        self.redis_is_using.delete(field_name)
                        logger.info(f"已清理线程cookie记录: {field_name}")
        except Exception as e:
            logger.error(f"清理线程cookie记录失败: {e}")

    def run_with_threads(self, num_threads=5):
        """多线程运行方法"""
        try:
            # 清理之前可能残留的线程cookie记录
            self.cleanup_all_thread_cookies()

            tweet_users = self.get_tweets_and_users_from_mysql()
            if not tweet_users:
                logger.warning("没有需要处理的推文数据")
                return

            # 将推文数据分配给不同线程
            tweets_per_thread = len(tweet_users) // num_threads
            tweet_chunks = []

            for i in range(num_threads):
                start_idx = i * tweets_per_thread
                if i == num_threads - 1:  # 最后一个线程处理剩余的推文
                    end_idx = len(tweet_users)
                else:
                    end_idx = (i + 1) * tweets_per_thread
                tweet_chunks.append(tweet_users[start_idx:end_idx])

            logger.info(f"开始运行 {num_threads} 个线程，推文分配: {[len(chunk) for chunk in tweet_chunks]}")

            # 创建并启动线程
            threads = []
            for i, tweet_chunk in enumerate(tweet_chunks):
                if tweet_chunk:  # 只为非空的推文列表创建线程
                    thread = threading.Thread(
                        target=self.process_tweets_thread,
                        args=(tweet_chunk, i + 1),
                        name=f"CommentsThread-{i + 1}"
                    )
                    threads.append(thread)
                    thread.start()
                    logger.info(f"启动线程 {i + 1}，处理 {len(tweet_chunk)} 条推文")

            # 等待所有线程完成
            for i, thread in enumerate(threads):
                thread.join()
                logger.info(f"线程 {i + 1} 已完成")

            logger.success("所有线程已完成推文评论处理")
                
        except Exception as e:
            logger.error(f"多线程运行失败: {e}")
        finally:
            # 确保程序结束时清理cookie使用记录
            self.cleanup_on_exit()

    def send_req_get_data(self, tweet_id, username):
        """发送请求获取数据，并处理翻页 - 单线程版本"""
        # 第一次 第一层
        self.headers["referer"] = f"https://x.com/{username}/status/{tweet_id}"
        variables_dict = {
            "focalTweetId": tweet_id,
            "with_rux_injections": False,
            "rankingMode": "Relevance",
            "includePromotedContent": True,
            "withCommunity": True,
            "withQuickPromoteEligibilityTweetFields": True,
            "withBirdwatchNotes": True,
            "withVoice": True
        }
        cursor = None
        all_comments_data = []

        while True:
            if cursor:
                variables_dict["cursor"] = cursor
                variables_dict["referrer"] = "tweet"
                # 翻页请求不设置referer（根据抓包结果）
            else:
                # 只在第一次请求时设置referer
                self.headers["referer"] = f"https://x.com/{username}/status/{tweet_id}"
            
            self.base_params["variables"] = json.dumps(variables_dict)
            logger.info(f"已更新请求参数: tweet_id={tweet_id}, username={username}, cursor={cursor}")

            time.sleep(random.uniform(15, 25))
            response = requests.get(
                self.api_url,
                headers=self.headers,
                cookies=self.cookies,
                params=self.base_params,
                proxies=self.proxy
            )

            if response.status_code != 200:
                logger.error(f"请求失败 状态码: {response.status_code}")
                response.raise_for_status()

            data = response.json()
            comments_data = self.parse_comments_data(data)
            all_comments_data.extend(comments_data)
            self.save_comments_to_mysql(comments_data, tweet_id)

            logger.info("开始获取下一页游标...")
            new_cursor = self.get_next_cursor(data)
            logger.info(f"获取游标结果: {new_cursor[:30] if new_cursor else 'None'}...")
            
            if new_cursor == cursor or not new_cursor:
                logger.info(f"翻页结束，原因: {'游标相同' if new_cursor == cursor else '未找到游标'}")
                break
            cursor = new_cursor
            logger.info("设置新游标，准备翻页请求")

            time.sleep(random.uniform(15, 25))
            logger.info("等待完成，开始下一轮翻页循环")

            # 层级评论数据
            logger.info("开始检查是否有深层评论...")
            next_control = self.get_next_control(data)
            logger.info(f"深层评论检查结果: {'有' if next_control else '无'}")
            processed_controls = set()  # 防止死循环
            while next_control:
                controller_data, id_data, screen_name = next_control
                
                # 检查是否已经处理过这个controllerData，防止死循环
                control_key = f"{controller_data}_{id_data}"
                if control_key in processed_controls:
                    logger.warning("检测到重复的controllerData，退出深层循环")
                    break
                processed_controls.add(control_key)
                
                logger.info(f"开始深层评论请求: {id_data} ({screen_name})")
                
                variables = json.dumps({
                    "focalTweetId": id_data,
                    "referrer": "search_tweet",
                    "controller_data": controller_data,
                    "with_rux_injections": False,
                    "rankingMode": "Relevance",
                    "includePromotedContent": True,
                    "withCommunity": True,
                    "withQuickPromoteEligibilityTweetFields": True,
                    "withBirdwatchNotes": True,
                    "withVoice": True
                })
                variables_control = self.base_params.copy()
                variables_control["variables"] = variables

                # 深层请求
                deep_headers = self.headers.copy()
                deep_headers["referer"] = f"https://twitter.com/{screen_name}/status/{id_data}"
                
                time.sleep(random.uniform(15, 25))
                response_deep = requests.get(
                    self.api_url,
                    headers=deep_headers,
                    cookies=self.cookies,
                    params=variables_control,
                    proxies=self.proxy
                )
                if response_deep.status_code != 200:
                    logger.error(f"深层请求失败 状态码: {response_deep.status_code}")
                    logger.error(f"错误响应: {response_deep.text}")
                    logger.error(f"请求URL: {self.api_url}")
                    logger.error(f"请求头: {json.dumps(self.headers, indent=2)}")
                    logger.error(f"请求参数: {json.dumps(variables_control, indent=2)}")
                    break  # 出错时退出循环，而不是抛异常

                additional_data = response_deep.json()
                additional_comments_data = self.parse_deep_comments_data(additional_data)
                all_comments_data.extend(additional_comments_data)
                self.save_deep_comments_to_mysql(additional_comments_data, tweet_id)
                
                logger.info(f"深层评论请求完成，获取{len(additional_comments_data)}条评论")

                next_control = self.get_next_control(additional_data)

            logger.info("深层评论循环结束")

        logger.success(f"请求完成，共获取 {len(all_comments_data)} 条评论数据")
        return all_comments_data

    def run(self):
        """单线程运行方法"""
        try:
            tweet_users = self.get_tweets_and_users_from_mysql()

            for tweet_user in tweet_users:
                tweet_id = tweet_user['tweet_id']
                username = tweet_user['username']

                try:
                    self.send_req_get_data(tweet_id, username)
                    self.update_progress(tweet_id)

                except Exception as e:
                    logger.error(f"处理推文失败 tweet_id={tweet_id}: {e}")
                    self.update_progress(tweet_id)
                    continue

                time.sleep(random.uniform(15, 25))
                
        except Exception as e:
            logger.error(f"程序运行失败: {e}")
        finally:
            # 确保程序结束时清理cookie使用记录
            self.cleanup_on_exit()

    def signal_handler(self, signum, frame):
        """处理程序中断信号"""
        logger.info("接收到退出信号，正在清理...")
        self.cleanup_on_exit()
        sys.exit(0)


if __name__ == '__main__':
    spider = Tweet_detial_Spider()
    
    # spider.run()  # 单线程运行
    spider.run_with_threads(num_threads=5)
