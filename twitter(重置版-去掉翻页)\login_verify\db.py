# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2025/2/19 14:39
@Python  :  Python3.7
@Desc    :  None
"""
import random

import redis

from settings import *

class RedisClient:
    def __init__(
            self,
            redis_name,
            host=utils.REDIS_HOST,
            password=utils.REDIS_PASSWORD,
            port=utils.REDIS_PORT,
            db=utils.REDIS_DB_U
    ):

        self.db = redis.StrictRedis(host=host, port=port, password=password, db=db, decode_responses=True)
        self.redis_name = redis_name

    def set(self, username, value):
        """
        设置键值对
        :param username: 用户名
        :param value: 密码+密钥 或 Cookies
        :return:
        """
        return self.db.hset(self.redis_name, username, value)

    def get(self, username):
        """
        根据键名获取键值
        :param username: 用户名
        :return:
        """
        return self.db.hget(self.redis_name, username)

    def delete(self, username):
        """
        根据键名删除键值对
        :param username: 用户名
        :return:
        """
        return self.db.hdel(self.redis_name, username)

    def random(self):
        """
        随机Cookies获取
        :return:
        """
        return random.choice(self.db.hvals(self.redis_name))

    def all(self):
        """
        获取所有键值对
        :return: 用户名和密码 或 用户名和Cookies
        """
        return self.db.hgetall(self.redis_name)

    def set_proxy_ip(self, proxy_ip, bound_cookies_count):
        """
        设置键值对
        :param proxy_ip: 代理IP
        :param bound_cookies_count: 代理IP绑定的cookie数
        :return:
        """
        return self.db.hset(self.redis_name, proxy_ip, bound_cookies_count)

    def get_proxy_ip(self):
        all_proxy_ip = self.db.hgetall(self.redis_name)
        if not all_proxy_ip:
            raise ValueError('No proxy IP')

        can_used_proxy_ip = None
        for proxy_ip, bound_cookies_count in all_proxy_ip.items():
            if int(bound_cookies_count) < MAX_PROXY_IP_BOUND_COOKIES_COUNT:
                can_used_proxy_ip = proxy_ip
                break

        if can_used_proxy_ip is None:
            raise ValueError('No proxy IP is available')

        return can_used_proxy_ip

    def incr_bound_cookies_count(self, proxy_ip, count=1):
        """
        增加代理IP绑定的cookie数量
        :param proxy_ip:
        :param count:
        :return:
        """
        return self.db.hincrby(self.redis_name, proxy_ip, count)

    def get_random_account(self):
        """从Redis中随机获取一个账号，返回 (key, value) 元组"""
        keys = self.db.hkeys(self.redis_name)
        if keys:
            random_key = random.choice(keys)
            value = self.db.hget(self.redis_name, random_key)
            return random_key, value
        return None, None


    def get_account_from_redis(self):
        """按顺序从Redis获取账号，优先获取没有对应cookie的账号"""
        accounts = self.db.hgetall(self.redis_name)
        if not accounts:
            return None

        cookie_pool = RedisClient(redis_name='cookies:twitter')
        cookie_accounts = cookie_pool.all()

        for username, value in accounts.items():
            if username not in cookie_accounts:
                # 顺序重置
                self.db.hdel(self.redis_name, username)
                self.db.hset(self.redis_name, username, value)
                return username, value

        username = list(accounts.keys())[0]
        value = accounts[username]

        self.db.hdel(self.redis_name, username)
        self.db.hset(self.redis_name, username, value)

        return username, value

    def get_account_from_redis222(self):
        accounts = self.db.hkeys('accounts:twitter')

        username = None
        value = None
        for account in accounts:
            if self.db.hexists('cookies:twitter', account):
                continue
            else:
                username = account
                value = self.db.hget('accounts:twitter', account)

        return username, value