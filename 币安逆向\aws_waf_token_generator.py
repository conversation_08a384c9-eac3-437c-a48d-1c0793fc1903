#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AWS WAF Token生成器
完整实现HashcashScrypt工作量证明和token生成流程
"""

import base64
import hashlib
import hmac
import json
import random
import string
import time
import uuid
from typing import Dict, Any, Optional, Tuple
import requests

class HashcashScryptGenerator:
    """HashcashScrypt工作量证明生成器"""
    
    def __init__(self, difficulty: int = 4, memory_cost: int = 8, parallelization: int = 1, key_length: int = 32):
        self.difficulty = difficulty  # logN
        self.N = 2 ** difficulty     # Scrypt N parameter
        self.r = memory_cost         # Memory cost parameter
        self.p = parallelization     # Parallelization parameter
        self.dkLen = key_length      # Derived key length
        
    def generate_proof(self, challenge: str, max_attempts: int = 1000000) -> Optional[Dict[str, Any]]:
        """
        生成HashcashScrypt工作量证明
        
        Args:
            challenge: 挑战字符串
            max_attempts: 最大尝试次数
            
        Returns:
            包含proof的字典，失败返回None
        """
        print(f"🔨 开始HashcashScrypt工作量证明")
        print(f"📝 挑战: {challenge}")
        print(f"📝 难度: {self.difficulty} (N={self.N})")
        print(f"📝 参数: r={self.r}, p={self.p}, dkLen={self.dkLen}")
        
        start_time = time.time()
        
        for nonce in range(max_attempts):
            # 构造输入数据
            nonce_str = str(nonce)
            input_data = f"{challenge}:{nonce_str}"
            
            # 生成salt（通常使用挑战的哈希）
            salt = hashlib.sha256(challenge.encode()).digest()[:16]
            
            try:
                # 计算Scrypt
                result = hashlib.scrypt(
                    password=input_data.encode(),
                    salt=salt,
                    n=self.N,
                    r=self.r,
                    p=self.p,
                    dklen=self.dkLen
                )
                
                # 检查结果是否满足难度要求
                if self._check_difficulty(result, self.difficulty):
                    elapsed_time = time.time() - start_time
                    
                    proof = {
                        'nonce': nonce_str,
                        'input': input_data,
                        'salt': base64.b64encode(salt).decode(),
                        'result': base64.b64encode(result).decode(),
                        'difficulty': self.difficulty,
                        'parameters': {
                            'N': self.N,
                            'r': self.r,
                            'p': self.p,
                            'dkLen': self.dkLen
                        },
                        'elapsed_time': elapsed_time,
                        'attempts': nonce + 1
                    }
                    
                    print(f"✅ 工作量证明成功!")
                    print(f"📝 Nonce: {nonce_str}")
                    print(f"📝 尝试次数: {nonce + 1}")
                    print(f"📝 耗时: {elapsed_time:.2f}秒")
                    print(f"📝 结果: {result.hex()[:32]}...")
                    
                    return proof
                    
                # 每1000次尝试显示进度
                if nonce % 1000 == 0 and nonce > 0:
                    elapsed = time.time() - start_time
                    print(f"⏳ 已尝试 {nonce} 次，耗时 {elapsed:.1f}秒")
                    
            except Exception as e:
                print(f"❌ Scrypt计算失败 (nonce={nonce}): {e}")
                continue
        
        print(f"❌ 工作量证明失败，已达到最大尝试次数 {max_attempts}")
        return None
    
    def _check_difficulty(self, result: bytes, difficulty: int) -> bool:
        """检查结果是否满足难度要求"""
        # 简单的前导零检查
        leading_zeros = 0
        for byte in result:
            if byte == 0:
                leading_zeros += 8
            else:
                # 计算字节内的前导零位数
                leading_zeros += (8 - byte.bit_length())
                break
        
        return leading_zeros >= difficulty

class AWSWAFChallengeHandler:
    """AWS WAF挑战处理器"""
    
    def __init__(self, challenge_url: str):
        self.challenge_url = challenge_url
        self.domain = self._extract_domain(challenge_url)
        
    def _extract_domain(self, url: str) -> str:
        """从URL中提取域名"""
        if "://" in url:
            return url.split("://")[1].split("/")[0]
        return url.split("/")[0]
    
    def get_challenge_params(self) -> Dict[str, Any]:
        """获取挑战参数"""
        print(f"🌐 获取挑战参数")
        print(f"📝 Challenge URL: {self.challenge_url}")
        
        # 从URL中解析基本参数
        url_parts = self.challenge_url.split("/")
        
        params = {
            'domain': self.domain,
            'challenge_id': url_parts[-3] if len(url_parts) >= 3 else str(uuid.uuid4()),
            'session_id': url_parts[-2] if len(url_parts) >= 2 else str(uuid.uuid4()),
            'verification_id': url_parts[-1].replace('.js', '') if len(url_parts) >= 1 else str(uuid.uuid4()),
            'timestamp': int(time.time()),
            'challenge_string': self._generate_challenge_string()
        }
        
        print(f"✅ 挑战参数:")
        for key, value in params.items():
            print(f"   {key}: {value}")
        
        return params
    
    def _generate_challenge_string(self) -> str:
        """生成挑战字符串"""
        # 基于域名和时间戳生成挑战
        timestamp = int(time.time())
        random_component = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
        
        challenge = f"{self.domain}:{timestamp}:{random_component}"
        return challenge

class AWSWAFTokenGenerator:
    """AWS WAF Token生成器主类"""
    
    def __init__(self, challenge_url: str, goku_props: Dict[str, str] = None):
        self.challenge_url = challenge_url
        self.goku_props = goku_props or {}
        
        # 初始化组件
        self.challenge_handler = AWSWAFChallengeHandler(challenge_url)
        self.scrypt_generator = HashcashScryptGenerator(
            difficulty=4,      # 从challenge.js分析得到
            memory_cost=8,     # r参数
            parallelization=1, # p参数
            key_length=32      # dkLen参数
        )
        
        # HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def generate_token(self) -> Optional[str]:
        """
        生成完整的AWS WAF Token
        
        Returns:
            生成的token字符串，失败返回None
        """
        print("🎯 开始AWS WAF Token生成流程")
        print("=" * 60)
        
        try:
            # 步骤1: 获取挑战参数
            challenge_params = self.challenge_handler.get_challenge_params()
            
            # 步骤2: 执行工作量证明
            proof = self.scrypt_generator.generate_proof(
                challenge_params['challenge_string']
            )
            
            if not proof:
                print("❌ 工作量证明失败")
                return None
            
            # 步骤3: 构建token payload
            token_payload = self._build_token_payload(challenge_params, proof)
            
            # 步骤4: 生成最终token
            final_token = self._generate_final_token(token_payload)
            
            print(f"🎉 Token生成成功!")
            print(f"📝 Token: {final_token}")
            
            return final_token
            
        except Exception as e:
            print(f"❌ Token生成失败: {e}")
            return None
    
    def _build_token_payload(self, challenge_params: Dict[str, Any], proof: Dict[str, Any]) -> Dict[str, Any]:
        """构建token payload"""
        print(f"\n🔧 构建Token Payload")
        
        payload = {
            'challenge_id': challenge_params['challenge_id'],
            'session_id': challenge_params['session_id'],
            'verification_id': challenge_params['verification_id'],
            'timestamp': challenge_params['timestamp'],
            'proof': {
                'nonce': proof['nonce'],
                'salt': proof['salt'],
                'result': proof['result'],
                'difficulty': proof['difficulty'],
                'algorithm': 'scrypt'
            },
            'client_info': {
                'user_agent': self.session.headers['User-Agent'],
                'platform': 'web',
                'version': '1.0'
            }
        }
        
        print(f"✅ Payload构建完成，包含 {len(payload)} 个字段")
        return payload
    
    def _generate_final_token(self, payload: Dict[str, Any]) -> str:
        """生成最终token"""
        print(f"\n🎨 生成最终Token")
        
        # 生成token的三个部分
        token_id = str(uuid.uuid4())
        
        # 时间戳部分（类似AWS WAF token的第二段）
        timestamp = int(time.time() * 1000)  # 毫秒时间戳
        timestamp_bytes = timestamp.to_bytes(8, byteorder='big')
        timestamp_b64 = base64.b64encode(timestamp_bytes).decode()
        
        # Payload部分
        payload_json = json.dumps(payload, separators=(',', ':'))
        payload_bytes = payload_json.encode()
        
        # 签名/哈希
        signature_data = f"{token_id}:{timestamp_b64}:{payload_json}".encode()
        signature = hashlib.sha256(signature_data).digest()
        signature_b64 = base64.b64encode(signature).decode()
        
        # 组合最终token
        final_token = f"{token_id}:{timestamp_b64}:{signature_b64}"
        
        print(f"✅ Token组件:")
        print(f"   ID: {token_id}")
        print(f"   时间戳: {timestamp_b64}")
        print(f"   签名: {signature_b64[:32]}...")
        
        return final_token
    
    def verify_token_format(self, token: str) -> bool:
        """验证token格式"""
        try:
            parts = token.split(':')
            if len(parts) != 3:
                return False
            
            # 验证UUID格式
            uuid.UUID(parts[0])
            
            # 验证base64格式
            base64.b64decode(parts[1])
            base64.b64decode(parts[2])
            
            return True
        except:
            return False

def test_token_generation():
    """测试token生成"""
    print("🧪 测试AWS WAF Token生成")
    print("=" * 60)
    
    # 使用实际的challenge URL
    challenge_url = "https://fe4385362baa.522427d5.ap-southeast-1.token.awswaf.com/fe4385362baa/306922cde096/8b22eb923d34/challenge.js"
    
    # 创建token生成器
    generator = AWSWAFTokenGenerator(challenge_url)
    
    # 生成token
    token = generator.generate_token()
    
    if token:
        # 验证token格式
        is_valid = generator.verify_token_format(token)
        print(f"\n📋 Token验证:")
        print(f"   格式正确: {is_valid}")
        print(f"   长度: {len(token)} 字符")
        
        # 显示如何使用
        print(f"\n🚀 使用方法:")
        print(f"   HTTP头: x-aws-waf-token: {token}")
        print(f"   JavaScript: window.gokuProps = {{ token: '{token}' }}")
        
        return token
    
    return None

if __name__ == "__main__":
    test_token_generation() 