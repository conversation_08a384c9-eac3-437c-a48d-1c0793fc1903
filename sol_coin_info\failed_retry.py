"""失败的写入redis中的地址重试"""

import pymysql
import boto3
import time
import json
import redis
import re
import sys
import os
from loguru import logger
from datetime import datetime
from curl_cffi import requests as cf_requests
from urllib.parse import urlparse

# 添加项目根目录到Python路径，以便导入utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils import DB_BASE

class FailedRetrySpider:
    
    def __init__(self):
        """初始化，复用原有配置"""
        self.db_config = {
            'host': '**********',
            'port': 6000,
            'user': 'root',
            'password': 'iAn7*+154-j9r3_dcm',
            'database': 'solana'
        }
        self.aws_config = {
            'aws_access_key_id': '********************',
            'aws_secret_access_key': 'Jax967rCDcH8uVdp3EQzd+wRR1NEm56IJgJUrWRn',
            'region_name': 'ap-northeast-1'
        }
        self.bucket_name = 'chainsight-dex-spider'
        self.s3 = boto3.client('s3', **self.aws_config)
        self.redis_config = {
            'host': '**************',
            'password': 123456,
            'port': 6379,
            'db': 4,
            'decode_responses': True
        }
        self.redis_client = redis.Redis(**self.redis_config)
        self.failed_url_key = 'sol_url:failed'
        self.db_base = DB_BASE()
        self.max_retry = 3
        self.timeout = 10
        
        # 代理配置
        self.proxy = {
            "http": "http://9bc99966eb7353:<EMAIL>:5001",
            "https": "http://9bc99966eb7353:<EMAIL>:5001"
        }
        
        # 重试统计
        self.retry_stats = {
            'total_failed': 0,
            'success_count': 0,
            'skip_count': 0,  # 非IPFS URL请求失败的跳过数量
            'still_failed': 0  # 重试后仍然失败的数量
        }
    
    def run(self):
        """主执行方法"""
        try:
            logger.info("=== 开始处理失败的URL重试 ===")
            
            # 获取失败的URL列表
            failed_data_list = self.get_failed_urls_from_redis()
            
            if not failed_data_list:
                logger.info("Redis中没有失败的URL数据")
                return
            
            self.retry_stats['total_failed'] = len(failed_data_list)
            logger.info(f"从Redis中获取到 {len(failed_data_list)} 条失败数据")
            
            # 遍历处理每个失败的URL
            for index, failed_item in enumerate(failed_data_list, 1):
                logger.info(f"[{index}/{len(failed_data_list)}] 处理失败URL重试")
                
                try:
                    # 解析失败数据
                    failed_data = json.loads(failed_item) if isinstance(failed_item, str) else failed_item
                    original_url = failed_data.get('url', '')
                    
                    if not original_url:
                        logger.warning(f"[{index}] 失败数据中没有URL字段，跳过")
                        continue
                    
                    # 检查是否是IPFS相关的URL
                    if self.is_ipfs_url(original_url):
                        # 处理IPFS URL，按优先级尝试不同方法
                        ipfs_result = self.process_ipfs_url(original_url)
                        if not ipfs_result:
                            logger.error(f"[{index}] 无法处理IPFS URL: {original_url}")
                            self.retry_stats['still_failed'] += 1
                            continue
                        
                        new_url = ipfs_result['new_url']
                        address = ipfs_result['address']
                        
                        logger.info(f"[{index}] URL替换: {original_url} -> {new_url}")
                        logger.info(f"[{index}] 提取到address: {address}")
                        
                        # 处理该条数据
                        success = self.process_single_failed_record(address, new_url, index)
                    else:
                        # 非IPFS URL，直接尝试请求一次
                        # logger.info(f"[{index}] URL不包含IPFS，尝试直接请求: {original_url}")
                        success = self.try_non_ipfs_url(original_url, index)
                        
                        if not success:
                            logger.warning(f"[{index}] 非IPFS URL请求失败，跳过处理: {original_url}")
                            self.retry_stats['skip_count'] += 1
                            continue
                    
                    if success:
                        self.retry_stats['success_count'] += 1
                        # 从Redis中删除已成功处理的失败记录
                        self.remove_failed_record_from_redis(failed_item)
                        logger.info(f"[{index}] 重试成功，已从Redis中删除失败记录")
                    else:
                        self.retry_stats['still_failed'] += 1
                        logger.error(f"[{index}] 重试后仍然失败")
                    
                    # 添加处理间隔，避免请求过快
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"[{index}] 处理失败数据时出错: {e}")
                    self.retry_stats['still_failed'] += 1
                    continue
            
            # 显示最终统计
            self.show_retry_stats()
            
        except Exception as e:
            logger.error(f"执行失败URL重试时出错: {e}")
            raise
    
    def get_failed_urls_from_redis(self):
        """从Redis中获取所有失败的URL数据"""
        try:
            # 获取所有失败的URL数据
            failed_data_list = self.redis_client.lrange(self.failed_url_key, 0, -1)
            return failed_data_list
        except Exception as e:
            logger.error(f"从Redis获取失败URL数据时出错: {e}")
            return []
    
    def is_ipfs_url(self, url):
        """检查URL是否包含ipfs相关域名"""
        # 检查是否包含ipfs关键词
        url_lower = url.lower()
        return 'ipfs' in url_lower
    
    def process_ipfs_url(self, url):
        """处理IPFS URL，按优先级尝试不同的方法提取address和替换URL"""
        try:
            logger.info(f"开始处理IPFS URL: {url}")
            
            # 方法1：处理 cf-ipfs.com
            result = self.try_cf_ipfs_method(url)
            if result:
                logger.info(f"使用cf-ipfs方法成功处理")
                return result
            
            # 方法2：处理 cloudflare-ipfs.com
            result = self.try_cloudflare_ipfs_method(url)
            if result:
                logger.info(f"使用cloudflare-ipfs方法成功处理")
                return result
            
            # 方法3：处理 quicknode-ipfs.com
            result = self.try_quicknode_ipfs_method(url)
            if result:
                logger.info(f"使用quicknode-ipfs方法成功处理")
                return result
            
            # 方法4：万能方法 - 处理所有包含ipfs的域名
            result = self.try_universal_ipfs_method(url)
            if result:
                logger.info(f"使用万能IPFS方法成功处理")
                return result
            
            logger.warning(f"所有方法都无法处理该URL: {url}")
            return None
            
        except Exception as e:
            logger.error(f"处理IPFS URL时出错: {url} - 错误: {e}")
            return None
    
    def try_cf_ipfs_method(self, url):
        """方法1：处理cf-ipfs.com格式的URL"""
        try:
            if 'cf-ipfs.com' not in url.lower():
                return None
            
            # 提取IPFS哈希
            pattern = r'cf-ipfs\.com/ipfs/([a-zA-Z0-9]+)'
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                address = match.group(1)
                if self.is_valid_ipfs_hash(address):
                    new_url = f"https://ipfs.io/ipfs/{address}"
                    return {'new_url': new_url, 'address': address}
            
            return None
        except Exception as e:
            logger.error(f"cf-ipfs方法处理失败: {e}")
            return None
    
    def try_cloudflare_ipfs_method(self, url):
        """方法2：处理cloudflare-ipfs.com格式的URL"""
        try:
            if 'cloudflare-ipfs.com' not in url.lower():
                return None
            
            # 提取IPFS哈希
            pattern = r'cloudflare-ipfs\.com/ipfs/([a-zA-Z0-9]+)'
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                address = match.group(1)
                if self.is_valid_ipfs_hash(address):
                    new_url = f"https://ipfs.io/ipfs/{address}"
                    return {'new_url': new_url, 'address': address}
            
            return None
        except Exception as e:
            logger.error(f"cloudflare-ipfs方法处理失败: {e}")
            return None
    
    def try_quicknode_ipfs_method(self, url):
        """方法3：处理quicknode-ipfs.com格式的URL"""
        try:
            if 'quicknode-ipfs.com' not in url.lower():
                return None
            
            # 提取IPFS哈希 - 支持各种quicknode子域名格式
            pattern = r'quicknode-ipfs\.com/ipfs/([a-zA-Z0-9]+)'
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                address = match.group(1)
                if self.is_valid_ipfs_hash(address):
                    new_url = f"https://ipfs.io/ipfs/{address}"
                    return {'new_url': new_url, 'address': address}
            
            return None
        except Exception as e:
            logger.error(f"quicknode-ipfs方法处理失败: {e}")
            return None
    
    def try_universal_ipfs_method(self, url):
        """方法4：万能方法 - 处理所有包含ipfs的域名"""
        try:
            if 'ipfs' not in url.lower():
                return None
            
            # 常见的IPFS URL模式
            patterns = [
                r'/ipfs/([a-zA-Z0-9]+)',  # 匹配 /ipfs/hash 格式
                r'ipfs\.io/ipfs/([a-zA-Z0-9]+)',  # 匹配 ipfs.io/ipfs/hash
                r'gateway\.ipfs\.io/ipfs/([a-zA-Z0-9]+)',  # 匹配 gateway.ipfs.io/ipfs/hash
                r'dweb\.link/ipfs/([a-zA-Z0-9]+)',  # 匹配 dweb.link/ipfs/hash
                r'ipfs\.infura\.io/ipfs/([a-zA-Z0-9]+)',  # 匹配 ipfs.infura.io/ipfs/hash
                r'[a-zA-Z0-9\-\.]+\.ipfs\.com/ipfs/([a-zA-Z0-9]+)',  # 匹配各种*.ipfs.com域名
            ]
            
            for pattern in patterns:
                match = re.search(pattern, url, re.IGNORECASE)
                if match:
                    address = match.group(1)
                    if self.is_valid_ipfs_hash(address):
                        new_url = f"https://ipfs.io/ipfs/{address}"
                        return {'new_url': new_url, 'address': address}
            
            # 如果正则都不匹配，尝试从URL路径的最后一段提取
            parsed = urlparse(url)
            path_parts = parsed.path.strip('/').split('/')
            
            # 寻找可能的IPFS哈希
            for part in reversed(path_parts):  # 从后往前查找
                if self.is_valid_ipfs_hash(part):
                    new_url = f"https://ipfs.io/ipfs/{part}"
                    return {'new_url': new_url, 'address': part}
            
            return None
            
        except Exception as e:
            logger.error(f"万能IPFS方法处理失败: {e}")
            return None
    
    def is_valid_ipfs_hash(self, hash_str):
        """验证是否是有效的IPFS哈希"""
        if not hash_str:
            return False
        
        # IPFS哈希通常满足以下条件之一：
        # 1. 以Qm开头，长度46个字符（CIDv0）
        # 2. 以b开头，长度较长（CIDv1 base32）
        # 3. 以z开头，长度较长（CIDv1 base58btc）
        # 4. 长度在40-60个字符之间的字母数字组合
        
        if len(hash_str) < 40:
            return False
            
        # CIDv0格式：以Qm开头，46个字符
        if hash_str.startswith('Qm') and len(hash_str) == 46:
            return True
        
        # CIDv1格式：以b或z开头
        if (hash_str.startswith('b') or hash_str.startswith('z')) and len(hash_str) > 45:
            return True
        
        # 通用验证：长度在合理范围内的字母数字组合
        if 40 <= len(hash_str) <= 65 and hash_str.isalnum():
            return True
            
        return False
    
    def try_non_ipfs_url(self, original_url, index):
        """处理非IPFS URL，直接尝试请求一次"""
        try:
            # logger.info(f"[{index}] 尝试请求非IPFS URL: {original_url}")
            
            # 发送请求
            response_data = self.make_request(original_url)
            if not response_data:
                # logger.warning(f"[{index}] 非IPFS URL请求失败: {original_url}")
                return False
            
            # 解析响应数据
            parsed_data = self.parse_data(response_data)
            if not parsed_data:
                logger.warning(f"[{index}] 非IPFS URL数据解析失败: {original_url}")
                return False
            
            # 从原始URL中尝试提取可能的address（用于命名）
            address = self.extract_address_from_non_ipfs_url(original_url)
            if not address:
                logger.warning(f"[{index}] 无法从非IPFS URL提取address: {original_url}")
                return False
            
            image_url = parsed_data['image_url']
            extra_json = parsed_data['extra_json']
            
            # 下载图片并上传到S3
            s3_info = self.download_and_upload_image(image_url, address)
            
            # 更新数据库extra字段
            db_updated = self.update_database_extra(address, extra_json, s3_info)
            
            # 更新icon字段
            icon_updated = False
            if s3_info and s3_info.get('s3_key'):
                icon_updated = self.update_database_icon(address, s3_info['s3_key'])
            
            logger.info(f"[{index}] 非IPFS URL处理成功: {original_url}")
            return True
            
        except Exception as e:
            logger.error(f"[{index}] 处理非IPFS URL失败: {original_url} - 错误: {e}")
            return False
    
    def extract_address_from_non_ipfs_url(self, url):
        """从非IPFS URL中提取可能的address"""
        try:
            # 解析URL，获取路径部分
            parsed = urlparse(url)
            path = parsed.path.strip('/')
            
            # 分割路径，获取最后一段作为address
            path_parts = path.split('/')
            if path_parts:
                # 取最后一个非空的部分
                for part in reversed(path_parts):
                    if part and len(part) > 10:  # 简单过滤，address通常比较长
                        return part
            
            # 如果路径中没有找到合适的，尝试从查询参数中找
            from urllib.parse import parse_qs
            query_params = parse_qs(parsed.query)
            
            # 常见的参数名
            possible_keys = ['id', 'hash', 'token', 'address', 'cid']
            for key in possible_keys:
                if key in query_params and query_params[key]:
                    value = query_params[key][0]
                    if len(value) > 10:
                        return value
            
            # 如果都没找到，使用URL的哈希值作为address
            import hashlib
            url_hash = hashlib.md5(url.encode()).hexdigest()
            return f"non_ipfs_{url_hash[:16]}"
            
        except Exception as e:
            logger.error(f"从非IPFS URL提取address失败: {e}")
            # 返回一个基于URL的唯一标识
            import hashlib
            url_hash = hashlib.md5(url.encode()).hexdigest()
            return f"non_ipfs_{url_hash[:16]}"

    def process_single_failed_record(self, address, uri, index):
        """处理单条失败记录，复用原有的完整流程"""
        try:
            logger.info(f"[{index}] 开始处理地址: {address}")
            
            # 发送请求
            response_data = self.make_request(uri)
            if not response_data:
                # logger.error(f"[{index}] 请求失败: {uri}")
                return False
                
            # 解析响应数据
            parsed_data = self.parse_data(response_data)
            if not parsed_data:
                logger.error(f"[{index}] 数据解析失败")
                return False
                
            image_url = parsed_data['image_url']
            extra_json = parsed_data['extra_json']
            
            # 下载图片并上传到S3
            s3_info = self.download_and_upload_image(image_url, address)
            
            # 更新数据库extra字段
            db_updated = self.update_database_extra(address, extra_json, s3_info)
            
            # 更新icon字段
            icon_updated = False
            if s3_info and s3_info.get('s3_key'):
                icon_updated = self.update_database_icon(address, s3_info['s3_key'])
            
            logger.info(f"[{index}] 处理成功: {address}")
            return True
            
        except Exception as e:
            logger.error(f"[{index}] 处理失败 {address}: {e}")
            return False
    
    def remove_failed_record_from_redis(self, failed_item):
        """从Redis中删除已成功处理的失败记录"""
        try:
            # 使用LREM命令删除特定的记录
            self.redis_client.lrem(self.failed_url_key, 1, failed_item)
        except Exception as e:
            logger.error(f"从Redis删除失败记录时出错: {e}")
    
    def show_retry_stats(self):
        """显示重试统计信息"""
        logger.info("=== 失败URL重试统计 ===")
        logger.info(f"总失败数量: {self.retry_stats['total_failed']}")
        logger.info(f"重试成功: {self.retry_stats['success_count']}")
        logger.info(f"跳过处理: {self.retry_stats['skip_count']} (非IPFS URL请求失败)")
        logger.info(f"仍然失败: {self.retry_stats['still_failed']}")
        
        if self.retry_stats['total_failed'] > 0:
            success_rate = (self.retry_stats['success_count'] / self.retry_stats['total_failed']) * 100
            logger.info(f"重试成功率: {success_rate:.2f}%")
        
        logger.info("=====================")
    
    # 以下方法直接复用原代码的逻辑
    
    def get_database_connection(self):
        """连接数据库"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def make_request(self, url):
        """万能请求方法 - 使用curl_cffi统一处理所有类型的URL，支持重试"""
        retry_count = 0
        
        while retry_count < self.max_retry:
            try:
                retry_count += 1
                logger.info(f"正在请求URL (第{retry_count}次尝试): {url}")
                
                response = cf_requests.get(url, proxies=self.proxy, impersonate="chrome116", timeout=self.timeout)
                response.raise_for_status()

                try:
                    return response.json()
                except:
                    return response
                    
            except Exception as e:
                if retry_count < self.max_retry:
                    time.sleep(2)
                else:
                    # logger.info(f"请求失败，已重试{self.max_retry}次: {url} - 错误: {e}")
                    return None

        return None

    def parse_data(self, response_data):
        """解析数据，提取image URL和社交媒体字段"""
        if not response_data:
            return None
            
        try:
            if hasattr(response_data, 'json'):
                try:
                    data = response_data.json()
                except:
                    return None
            elif isinstance(response_data, dict):
                data = response_data
            else:
                return None

            if 'error' in data:
                return None

            image_url = data.get('image', '')
            if not image_url:
                return None

            # 对image URL也进行IPFS域名转换处理
            if self.is_ipfs_url(image_url):
                logger.info(f"检测到image字段也是IPFS URL，进行转换: {image_url}")
                ipfs_result = self.process_ipfs_url(image_url)
                if ipfs_result:
                    image_url = ipfs_result['new_url']
                    logger.info(f"image URL转换成功: {image_url}")
                else:
                    logger.warning(f"image URL转换失败，使用原URL: {image_url}")

            extra_data = {}
            
            # 第一步：常规字段提取
            if 'twitter' in data and data['twitter']:
                extra_data['twitter'] = data['twitter']
            if 'telegram' in data and data['telegram']:
                extra_data['telegram'] = data['telegram']
            if 'createdOn' in data and data['createdOn']:
                extra_data['createdOn'] = data['createdOn']
            
            # 从extensions对象提取
            if 'extensions' in data and isinstance(data['extensions'], dict):
                extensions = data['extensions']
                if 'twitter' in extensions and extensions['twitter'] and 'twitter' not in extra_data:
                    extra_data['twitter'] = extensions['twitter']
                if 'telegram' in extensions and extensions['telegram'] and 'telegram' not in extra_data:
                    extra_data['telegram'] = extensions['telegram']
            
            # 第二步：如果还没有提取到twitter或telegram，使用正则表达式作为保底方法
            if 'twitter' not in extra_data or 'telegram' not in extra_data:
                logger.info("使用正则表达式作为保底方法提取社交媒体链接")
                regex_extracted = self._extract_social_links_with_regex(data)
                
                # 只添加还没有提取到的字段
                if 'twitter' not in extra_data and 'twitter' in regex_extracted:
                    extra_data['twitter'] = regex_extracted['twitter']
                
                if 'telegram' not in extra_data and 'telegram' in regex_extracted:
                    extra_data['telegram'] = regex_extracted['telegram']
            
            # 转换为JSON字符串，只要有任意一个字段就生成
            extra_json = json.dumps(extra_data, ensure_ascii=False) if extra_data else None
            
            result = {
                'image_url': image_url,
                'extra_json': extra_json,
                'raw_data': data
            }
            
            return result
            
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return None

    def download_and_upload_image(self, image_url, address):
        """下载图片并上传到S3"""
        try:
            retry_count = 0
            image_data = None
            
            while retry_count < self.max_retry:
                try:
                    retry_count += 1
                    
                    response = cf_requests.get(image_url, proxies=self.proxy, impersonate="chrome116", timeout=self.timeout)
                    response.raise_for_status()

                    content_type = response.headers.get('content-type', '').lower()
                    if not any(img_type in content_type for img_type in ['image/', 'application/octet-stream']):
                        pass  # 但仍然尝试上传，有些图片服务器不返回正确的content-type
                    
                    image_data = response.content
                    break
                    
                except Exception as e:
                    if retry_count < self.max_retry:
                        time.sleep(2)
                    else:
                        logger.error(f"图片下载失败: {image_url} - 错误: {e}")
                        return None
            
            if not image_data:
                return None
            
            # 生成S3对象名称：logo/address_101.png格式
            s3_object_name = f"logo/101-{address}.png"
            
            try:
                self.s3.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_object_name,
                    Body=image_data,
                    ContentType=self._get_content_type(s3_object_name),
                    ACL='public-read'
                )

                # 构建S3 URL
                s3_url = f"https://{self.bucket_name}.s3.{self.aws_config['region_name']}.amazonaws.com/{s3_object_name}"
                logger.info(f"图片上传成功: {s3_url}")
                
                return {
                    's3_url': s3_url,
                    's3_key': s3_object_name,
                    'file_size': len(image_data),
                    'original_url': image_url
                }
                
            except Exception as e:
                logger.error(f"图片上传到S3失败: {e}")
                return None
                
        except Exception as e:
            logger.error(f"下载和上传图片过程出错: {e}")
            return None
    
    def _get_content_type(self, file_path):
        """根据文件路径或扩展名获取Content-Type"""
        # 如果是完整路径，提取扩展名
        if '/' in file_path:
            file_extension = '.' + file_path.split('.')[-1].lower()
        else:
            file_extension = file_path.lower()
            
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.svg': 'image/svg+xml'
        }
        return content_types.get(file_extension, 'image/png')  # 默认使用PNG

    def _extract_social_links_with_regex(self, data):
        """使用正则表达式作为保底方法提取社交媒体链接"""
        extracted_data = {}
        
        # 将整个数据转换为字符串进行正则匹配
        data_str = json.dumps(data, ensure_ascii=False).lower()
        
        # Twitter正则表达式模式
        twitter_patterns = [
            r'https?://(?:www\.)?(?:twitter\.com|x\.com)/[^\s"\'<>]+',
            r'https?://t\.co/[^\s"\'<>]+',
        ]
        
        # Telegram正则表达式模式
        telegram_patterns = [
            r'https?://(?:www\.)?t\.me/[^\s"\'<>]+',
            r'https?://(?:www\.)?telegram\.me/[^\s"\'<>]+',
        ]
        
        # 提取Twitter链接
        for pattern in twitter_patterns:
            matches = re.findall(pattern, data_str, re.IGNORECASE)
            if matches:
                # 选择第一个匹配的链接
                extracted_data['twitter'] = matches[0]
                break
        
        # 提取Telegram链接
        for pattern in telegram_patterns:
            matches = re.findall(pattern, data_str, re.IGNORECASE)
            if matches:
                # 选择第一个匹配的链接
                extracted_data['telegram'] = matches[0]
                break
        
        return extracted_data

    def update_database_icon(self, address, s3_key):
        """更新数据库中的icon字段"""
        if not s3_key:
            return False
            
        # 从s3_key提取路径，格式：logo/101-address.png -> /logo/101-address.png
        icon_path = f"/{s3_key}"
        
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                sql = """UPDATE coin_info SET icon = %s WHERE address = %s"""
                cursor.execute(sql, (icon_path, address))
                connection.commit()
                
                logger.info(f"Icon字段更新成功: {address} - 路径: {icon_path}")
                return True
                
        except Exception as e:
            logger.error(f"Icon字段更新失败: {address} - 错误: {e}")
            connection.rollback()
            return False
        finally:
            connection.close()

    def update_database_extra(self, address, extra_json, s3_info=None):
        """更新数据库中的extra字段，只写入twitter、telegram、createdOn字段"""
        # 如果没有有效的社交媒体字段，则不更新数据库
        if not extra_json:
            return False
            
        connection = self.get_database_connection()
        try:
            with connection.cursor() as cursor:
                # 只写入用户需要的字段：twitter、telegram、createdOn
                # 不包含s3_image信息
                final_extra_json = extra_json
                
                # 更新数据库
                sql = """UPDATE coin_info SET extra = %s WHERE address = %s"""
                cursor.execute(sql, (final_extra_json, address))
                connection.commit()
                
                # 解析字段用于日志显示
                update_data = json.loads(extra_json) if extra_json else {}
                logger.info(f"数据库更新成功: {address} - 更新内容: {list(update_data.keys())}")
                return True
                
        except Exception as e:
            logger.error(f"数据库更新失败: {address} - 错误: {e}")
            connection.rollback()
            return False
        finally:
            connection.close()


if __name__ == '__main__':
    """
    使用示例：
    python failed_retry.py
    """
    retry_spider = FailedRetrySpider()
    
    try:
        # 执行失败URL重试
        retry_spider.run()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
    finally:
        # 显示最终统计
        retry_spider.show_retry_stats()
