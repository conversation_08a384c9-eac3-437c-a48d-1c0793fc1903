#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
涨跌幅和交易量数据统一调度器 - 同步运行HA、OKX和GMGN三个平台的数据获取脚本
确保每次请求都是同时发送的，保证数据一致性
"""

import requests
import sqlite3
import json
import datetime
import time
import hmac
import base64
from hashlib import sha256
from datetime import timezone
import sys
from curl_cffi import requests as cf_requests

# 设置控制台输出编码
if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库配置
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "test_data_bsc"

# 重试配置
MAX_RETRIES = 5
RETRY_INTERVAL = 2

# HA API配置
HA_API_URL = "http://**************/api/v1/dex/market/stats"
HA_TIMEOUT = 10
HA_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

# OKX API配置
OKX_API_HOST = "https://web3.okx.com"
OKX_PRICE_API = "/api/v5/dex/market/price-info"
OKX_API_KEY = "d87d0a5f-a4df-4209-a3b7-573dad862d25"
OKX_API_SECRET = "8807594F0F5B6A15F2B223638B8537D0"
OKX_API_PASSPHRASE = "Dh112211!"
OKX_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

# GMGN API配置
GMGN_API_URL = "https://gmgn.ai/api/v1/mutil_window_token_info"

GMGN_COOKIES = {
    "_ga": "GA1.1.1787124478.1747114971",
    "cf_clearance": "uBGoNKbju0gJKlK8kYbfibSg6U5EzUtuftEIySzY3cs-1749797746-*******-Sp4GRUUsOknzXfKNy5C8ajBOCitbdfHrc7yPkk8.CPFklkfwt8hjCRfPJk.DJ1vReQ_y61ILaAx3KaFZJIqgJtpOFycmM6M5Pk9aLmYwceQrqKVJl_hsAB7q.Oljo1cVKzat45qxXyZTBJAwLiujok_dOkeXhRppBwYVFxx7FtpjSIUIxFx45PQPDLGjiGq69sBp1jDlMv4yyxxs7jpcNq8gpV0Jx7IEhkNzrRT8DTO6iNxrMpbgdHu6fsc5gCXY_1I.GvUxonEwl3esoUeMyFkmPp7E_H6YcNL8VMOYpPlsumwwyqX_h4HbxIfWs3qkUU_mxPeZW_6Cdg04k278FcCX8mOxCzNUC_QOGsxzFAI",
    "_ga_0XM0LYXGC8": "GS2.1.s1749797734$o46$g1$t1749797748$j46$l0$h0"
}

GMGN_PARAMS = {
    "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
    "client_id": "gmgn_web_20250613-2203-ef1b00b",
    "from_app": "gmgn",
    "app_ver": "20250613-2203-ef1b00b",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a0adc6758070914b5d3cd4679349eed1",
    "os": "web"
}

GMGN_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}


def print_log(message):
    """统一的日志打印函数"""
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")


def get_all_token_addresses():
    """从数据库中获取所有Token Address"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'SELECT rowid, "code_address" FROM {DB_TABLE} WHERE "code_address" IS NOT NULL')
        return cursor.fetchall()
    except sqlite3.Error as e:
        print_log(f"数据库错误: {e}")
        return []
    finally:
        if conn:
            conn.close()


def generate_okx_signature(timestamp, method, request_path, body=''):
    """生成OKX API签名"""
    message = timestamp + method + request_path + body
    mac = hmac.new(
        bytes(OKX_API_SECRET, encoding='utf8'),
        bytes(message, encoding='utf-8'),
        digestmod=sha256
    )
    return base64.b64encode(mac.digest()).decode()


def fetch_ha_data(token_address):
    """获取HA平台涨跌幅和交易量数据"""
    # 构造请求数据
    coin_keys = [{
        "chainName": "BSC",
        "tokenContractAddress": token_address
    }]
    
    request_data = {
        "bar": "24h",
        "coinKeys": coin_keys
    }
    
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    
    for attempt in range(MAX_RETRIES):
        try:
            print_log(f"HA - 请求代币 {token_address} (尝试 {attempt + 1}/{MAX_RETRIES})")
            
            response = requests.post(
                HA_API_URL,
                headers=headers,
                json=request_data,
                proxies=HA_PROXY,
                timeout=HA_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200 and "data" in result:
                    stats_data = result["data"]
                    if len(stats_data) > 0:
                        stats = stats_data[0]
                        price_change = stats.get("priceChange")
                        volume = stats.get("volUsd")
                        
                        if price_change is not None:
                            print_log(f"HA - 成功获取数据: 涨跌幅={price_change}, 交易量={volume}")
                            return {"price_change": price_change, "volume": volume, "success": True}
                
                print_log(f"HA - 响应数据不完整: {result}")
                return {"success": False, "error": "数据不完整"}
            else:
                print_log(f"HA - 请求失败: 状态码 {response.status_code}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_INTERVAL)
                    continue
                    
        except Exception as e:
            print_log(f"HA - 请求异常: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_INTERVAL)
                continue
    
    return {"success": False, "error": "达到最大重试次数"}


def fetch_okx_data(token_address):
    """获取OKX平台涨跌幅和交易量数据"""
    # 构造请求数据
    request_data = [{
        "chainIndex": "56",  # 链ID
        "tokenContractAddress": token_address
    }]
    
    request_body = json.dumps(request_data)
    url = f"{OKX_API_HOST}{OKX_PRICE_API}"
    
    for attempt in range(MAX_RETRIES):
        try:
            print_log(f"OKX - 请求代币 {token_address} (尝试 {attempt + 1}/{MAX_RETRIES})")
            
            # 生成时间戳和签名
            timestamp = datetime.datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
            signature = generate_okx_signature(timestamp, "POST", OKX_PRICE_API, request_body)
            
            headers = {
                "Content-Type": "application/json",
                "OK-ACCESS-KEY": OKX_API_KEY,
                "OK-ACCESS-SIGN": signature,
                "OK-ACCESS-PASSPHRASE": OKX_API_PASSPHRASE,
                "OK-ACCESS-TIMESTAMP": timestamp
            }
            
            response = requests.post(
                url,
                headers=headers,
                data=request_body,
                proxies=OKX_PROXY,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "0" and "data" in data:
                    data_list = data["data"]
                    if len(data_list) > 0:
                        token_data = data_list[0]
                        price_change_24h = token_data.get("priceChange24H")
                        volume_24h = token_data.get("volume24H")
                        
                        if price_change_24h is not None and volume_24h is not None:
                            print_log(f"OKX - 成功获取数据: 涨跌幅={price_change_24h}, 交易量={volume_24h}")
                            return {"price_change": price_change_24h, "volume": volume_24h, "success": True}
                
                print_log(f"OKX - 响应数据不完整: {data}")
                return {"success": False, "error": "数据不完整"}
            else:
                print_log(f"OKX - 请求失败: 状态码 {response.status_code}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_INTERVAL)
                    continue
                    
        except Exception as e:
            print_log(f"OKX - 请求异常: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_INTERVAL)
                continue
    
    return {"success": False, "error": "达到最大重试次数"}


def get_gmgn_headers(token_address):
    """生成GMGN请求头，动态设置referer"""
    headers = {
        "referer": f"https://gmgn.ai/bsc/token/{token_address}",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    return headers


def fetch_gmgn_data(token_address):
    """获取GMGN平台交易量数据"""
    # 构造请求数据
    request_data = {
        "chain": "bsc",
        "addresses": [token_address]
    }
    
    request_body = json.dumps(request_data, separators=(',', ':'))
    
    for attempt in range(MAX_RETRIES):
        try:
            print_log(f"GMGN - 请求代币 {token_address} (尝试 {attempt + 1}/{MAX_RETRIES})")
            
            # 生成动态请求头
            headers = get_gmgn_headers(token_address)
            
            response = cf_requests.post(
                GMGN_API_URL,
                headers=headers,
                cookies=GMGN_COOKIES,
                params=GMGN_PARAMS,
                data=request_body,
                impersonate='chrome116',
                proxies=GMGN_PROXY,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and "data" in data and len(data["data"]) > 0:
                    token_info = data["data"][0]
                    
                    # 提取volume_24h数据
                    if "price" in token_info and "volume_24h" in token_info["price"]:
                        volume_24h = token_info["price"]["volume_24h"]
                        
                        print_log(f"GMGN - 成功获取数据: 交易量={volume_24h}")
                        return {"volume": volume_24h, "success": True}
                
                print_log(f"GMGN - 响应数据不完整: {data}")
                return {"success": False, "error": "数据不完整"}
            else:
                print_log(f"GMGN - 请求失败: 状态码 {response.status_code}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_INTERVAL)
                    continue
                    
        except Exception as e:
            print_log(f"GMGN - 请求异常: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_INTERVAL)
                continue
    
    return {"success": False, "error": "达到最大重试次数"}


def update_ha_database(row_id, price_change, volume):
    """更新HA涨跌幅和交易量到数据库"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'''
            UPDATE {DB_TABLE}
            SET "ha_change" = ?, "ha_volume" = ?
            WHERE rowid = ?
        ''', (price_change, volume, row_id))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print_log(f"HA - 更新数据库错误: {e}")
        return False
    finally:
        if conn:
            conn.close()


def update_okx_database(row_id, price_change, volume):
    """更新OKX涨跌幅和交易量到数据库"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'''
            UPDATE {DB_TABLE}
            SET "okx_change" = ?, "okx_volume" = ?
            WHERE rowid = ?
        ''', (price_change, volume, row_id))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print_log(f"OKX - 更新数据库错误: {e}")
        return False
    finally:
        if conn:
            conn.close()


def update_gmgn_database(row_id, volume):
    """更新GMGN交易量到数据库"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'''
            UPDATE {DB_TABLE}
            SET "gmgn_volume" = ?
            WHERE rowid = ?
        ''', (volume, row_id))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print_log(f"GMGN - 更新数据库错误: {e}")
        return False
    finally:
        if conn:
            conn.close()


def process_single_token(row_id, token_address):
    """同步处理单个代币的三个平台涨跌幅和交易量数据"""
    print_log(f"开始同步处理代币: {token_address} (rowid: {row_id})")
    print_log("=" * 80)
    
    # 同时发送三个平台的请求
    print_log("同时向三个平台发送请求...")
    start_time = time.time()
    
    # 按顺序调用，确保严格同步
    ha_result = fetch_ha_data(token_address)
    okx_result = fetch_okx_data(token_address)
    gmgn_result = fetch_gmgn_data(token_address)
    
    end_time = time.time()
    print_log(f"三个平台请求完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 统计成功情况
    success_count = 0
    
    # 更新HA数据
    if ha_result["success"]:
        if update_ha_database(row_id, ha_result["price_change"], ha_result["volume"]):
            print_log(f"HA - 成功更新数据库: 涨跌幅={ha_result['price_change']}, 交易量={ha_result['volume']}")
            success_count += 1
        else:
            print_log("HA - 数据库更新失败")
    else:
        print_log(f"HA - 获取数据失败: {ha_result.get('error', '未知错误')}")
    
    # 更新OKX数据
    if okx_result["success"]:
        if update_okx_database(row_id, okx_result["price_change"], okx_result["volume"]):
            print_log(f"OKX - 成功更新数据库: 涨跌幅={okx_result['price_change']}, 交易量={okx_result['volume']}")
            success_count += 1
        else:
            print_log("OKX - 数据库更新失败")
    else:
        print_log(f"OKX - 获取数据失败: {okx_result.get('error', '未知错误')}")
    
    # 更新GMGN数据
    if gmgn_result["success"]:
        if update_gmgn_database(row_id, gmgn_result["volume"]):
            print_log(f"GMGN - 成功更新数据库: 交易量={gmgn_result['volume']}")
            success_count += 1
        else:
            print_log("GMGN - 数据库更新失败")
    else:
        print_log(f"GMGN - 获取数据失败: {gmgn_result.get('error', '未知错误')}")
    
    print_log(f"代币 {token_address} 处理完成: {success_count}/3 个平台成功")
    print_log("=" * 80)
    
    return success_count


def main():
    """主函数"""
    print_log("=" * 60)
    print_log("三平台同步涨跌幅和交易量数据获取工具")
    print_log("=" * 60)
    print_log(f"数据库路径: {DB_PATH}")
    print_log(f"数据库表名: {DB_TABLE}")
    print_log(f"最大重试次数: {MAX_RETRIES}")
    print_log("=" * 60)
    
    # 获取所有token地址
    all_tokens = get_all_token_addresses()
    total_count = len(all_tokens)
    
    if not all_tokens:
        print_log("未找到任何Token地址，请检查数据库")
        return
    
    print_log(f"总共需要处理 {total_count} 个代币")
    print_log("开始逐个同步处理...")
    
    total_success = 0
    
    start_time = time.time()
    
    for i, (row_id, token_address) in enumerate(all_tokens, 1):
        print_log(f"\n处理进度: {i}/{total_count} ({i/total_count*100:.1f}%)")
        
        # 处理单个代币
        success_count = process_single_token(row_id, token_address)
        total_success += success_count
        
        # 如果不是最后一个，等待一段时间再处理下一个
        if i < total_count:
            delay = 2  # 每个代币处理完后等待2秒
            print_log(f"等待 {delay} 秒后处理下一个代币...")
            time.sleep(delay)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 输出最终统计
    print_log("\n" + "=" * 60)
    print_log("处理完成!")
    print_log(f"总计处理: {total_count} 个代币")
    print_log(f"总成功次数: {total_success} (满分: {total_count * 3})")
    print_log(f"总耗时: {duration:.2f} 秒")
    print_log(f"平均每个代币耗时: {duration/total_count:.2f} 秒")
    print_log("=" * 60)


if __name__ == "__main__":
    print_log("开始运行三平台同步涨跌幅和交易量数据获取工具...")
    
    try:
        main()
    except KeyboardInterrupt:
        print_log("\n程序被用户中断")
    except Exception as e:
        import traceback
        print_log(f"程序运行出错: {e}")
        print_log(traceback.format_exc())
    
    print_log("程序运行结束")
