import requests
import re
import time
import redis
import random
from loguru import logger
from pymysql.cursors import DictCursor
from html import unescape
import pymysql
import os
import threading


class INVESTMENTTEAM_SPIDER():
    def __init__(self):
        self.url = "https://dncapi.flink1.com/api/v3/coin/team"
        self.headers = {
            "authority": "dncapi.flink1.com",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "origin": "https://www.feixiaohao.com",
            "referer": "https://www.feixiaohao.com/",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.params = {
            "webp": "1"
        }
        
        self.mysql_config = {
            'host': '***************',
            'port': 3306,
            'user': 'spider',
            'password': 'ha13579.',
            'db': 'spider',
            'charset': 'utf8',
            'cursorclass': DictCursor
        }
        
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            password='123456',
            db=2,
            decode_responses=True
        )
        
        self.proxies = {
            "http": "http://127.0.0.1:33210",
            "https": "http://127.0.0.1:33210"
        }
        # self.proxies = {
        #     'http': 'socks5://**************:30889',
        #     'https': 'socks5://**************:30889'
        # }
        # 使用当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.team_logo_dir = os.path.join(current_dir, 'team_logo')
        self.agency_logo_dir = os.path.join(current_dir, 'agency_logo')
        # self.team_logo_dir = os.path.join(current_dir, '/data/spider_icons/TOKEN_ICON/token_basic_info/team_agency_logo/team_logo')
        # self.agency_logo_dir = os.path.join(current_dir, '/data/spider_icons/TOKEN_ICON/token_basic_info/team_agency_logo/agency_logo')

        logger.info(f"当前工作目录: {os.getcwd()}")
        logger.info(f"团队logo目录: {self.team_logo_dir}")
        logger.info(f"机构logo目录: {self.agency_logo_dir}")

        # 检查目录
        for directory in [self.team_logo_dir, self.agency_logo_dir]:
            if os.path.exists(directory):
                logger.info(f"目录已存在: {directory}")
            else:
                try:
                    os.makedirs(directory, exist_ok=True)
                    logger.info(f"创建目录: {directory}")
                except Exception as e:
                    logger.error(f"创建目录失败: {directory}, 错误: {e}")

        # 添加一个程序终止标志
        self.should_stop = False
        self.conn = pymysql.connect(**self.mysql_config)
        self.request_count = 0

    def clean_text(self, text):
        if not text:
            return ''
        text = unescape(text)
        text = re.sub(r'<[^>]+>', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        text = re.sub(r'[\s\u3000]+', ' ', text)
        text = re.sub(r'\(\s*\)', '', text)
        return text.strip()

    def clean_filename(self, filename):
        if not filename:
            return ''
        filename = re.sub(r'[\\/*?:"<>|]', '', filename)
        filename = re.sub(r'\（.*?\）', '', filename)
        filename = re.sub(r'\(.*?\)', '', filename)
        filename = ' '.join(filename.split())
        return filename.strip()

    def download_logo(self, url, code, name, is_team=True):
        try:
            if not url:
                logger.warning(f"URL为空，跳过下载: {name}")
                return None

            clean_name = self.clean_filename(name)
            if not clean_name:
                logger.warning(f"清理后的名称为空，跳过下载: {name}")
                return None

            filename = f"{code}_{clean_name}.png"
            save_dir = self.team_logo_dir if is_team else self.agency_logo_dir
            save_path = os.path.join(save_dir, filename)
            
            # 在这里定义relative_path
            relative_path = f"/icon/token_basic_info/team_agency_logo/{'team_logo' if is_team else 'agency_logo'}/{filename}"
            
            logger.info(f"准备下载图片 - URL: {url}")
            logger.info(f"保存路径: {save_path}")

            if os.path.exists(save_path):
                logger.info(f"文件已存在，跳过下载: {save_path}")
                return relative_path  # 这里使用已定义的relative_path

            try:
                response = requests.get(
                    url,
                    headers=self.headers,
                    proxies=self.proxies,
                    timeout=10
                )
                logger.info(f"请求状态码: {response.status_code}, URL: {url}")

                if response.status_code == 200:
                    try:
                        with open(save_path, 'wb') as f:
                            f.write(response.content)
                        
                        # 验证文件是否成功保存
                        if os.path.exists(save_path):
                            file_size = os.path.getsize(save_path)
                            logger.success(f"成功保存文件: {save_path}, 大小: {file_size} 字节")
                            return relative_path  # 这里使用已定义的relative_path
                        else:
                            logger.error(f"文件未能成功保存: {save_path}")
                            return None
                            
                    except Exception as e:
                        logger.error(f"保存文件时出错: {save_path}, 错误: {e}")
                        return None
                else:
                    logger.error(f"下载失败 {name}, 状态码: {response.status_code}")
                    return None

            except requests.exceptions.RequestException as e:
                logger.error(f"请求出错 {url}: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"下载过程出错 {name}: {str(e)}")
            return None

    def get_code_from_redis(self):
        try:
            code = self.redis_client.spop('feixiaohao:coin_codes')
            if code:
                logger.info(f'从Redis获取到code: {code}')
                return code
            return None
        except Exception as e:
            logger.error(f'从Redis获取code失败: {e}')
            return None

    def fetch_data(self):
        try:
            response = requests.get(self.url, headers=self.headers, params=self.params, proxies=self.proxies)
            logger.info(f'请求URL: {self.url}')
            return response.json()
        except Exception as e:
            logger.error(f'获取数据时出错: {e}')
            return None

    def process_data(self, data, code):
        if not data:
            logger.error("没有接收到数据")
            return False

        team_data = data.get('data', {}).get('team', [])
        agency_data = data.get('data', {}).get('agency', [])
        
        logger.info(f"获取到数据 - 团队数量: {len(team_data)}, 机构数量: {len(agency_data)}")

        # 存储成功处理的数据
        parsed_team_data = []
        parsed_agency_data = []

        # 处理团队数据
        for team in team_data:
            team_name = self.clean_text(team.get('name', ''))
            team_logo_url = team.get('logo', '')
            
            if team_logo_url and team_name:
                logger.info(f"处理团队logo - 名称: {team_name}, URL: {team_logo_url}")
                team_route = self.download_logo(team_logo_url, code, team_name, is_team=True)
                if team_route:
                    logger.success(f"团队logo下载成功: {team_name}")
                    # 添加到待保存数据列表
                    parsed_team_data.append({
                        'team_name': team_name,
                        'team_logo': team_logo_url,
                        'team_route': team_route
                    })
                else:
                    logger.error(f"团队logo下载失败: {team_name}")

        # 处理机构数据
        for agency in agency_data:
            agency_name = self.clean_text(agency.get('name', ''))
            agency_logo_url = agency.get('logo', '')
            
            if agency_logo_url and agency_name:
                logger.info(f"处理机构logo - 名称: {agency_name}, URL: {agency_logo_url}")
                agency_route = self.download_logo(agency_logo_url, code, agency_name, is_team=False)
                if agency_route:
                    logger.success(f"机构logo下载成功: {agency_name}")
                    # 添加到待保存数据列表
                    parsed_agency_data.append({
                        'agency_name': agency_name,
                        'agency_logo': agency_logo_url,
                        'agency_route': agency_route
                    })
                else:
                    logger.error(f"机构logo下载失败: {agency_name}")

        # 保存数据到数据库
        try:
            if parsed_team_data:
                logger.info(f"开始保存{len(parsed_team_data)}条团队数据到数据库")
                self.insert_team_data_to_mysql(parsed_team_data, code)
                
            if parsed_agency_data:
                logger.info(f"开始保存{len(parsed_agency_data)}条机构数据到数据库")
                self.insert_agency_data_to_mysql(parsed_agency_data, code)
                
            return True
        except Exception as e:
            logger.warning(f"保存数据到数据库失败: {e}")
            return False

    def insert_team_data_to_mysql(self, parsed_team_data, code):
        if not parsed_team_data:
            logger.warning(f"没有团队数据需要保存，code: {code}")
            return

        logger.info(f"准备保存团队数据，数量: {len(parsed_team_data)}")
        try:
            with self.conn.cursor() as cursor:
                check_sql = "SELECT id FROM fxh_team_agency_data WHERE code = %s AND team_name = %s"
                insert_sql = """
                    INSERT INTO fxh_team_agency_data 
                    (code, team_name, team_logo, team_route, created_at)
                    VALUES (%s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    team_logo = VALUES(team_logo),
                    team_route = VALUES(team_route),
                    created_at = VALUES(created_at)
                """
                
                for team in parsed_team_data:
                    team_name = team['team_name']
                    team_logo = team['team_logo']
                    team_route = team['team_route']
                    current_time = time.strftime('%Y-%m-%d %H:%M:%S')  # 获取当前时间
                    
                    logger.info(f"准备插入团队数据 - code: {code}, name: {team_name}, route: {team_route}")
                    
                    try:
                        cursor.execute(check_sql, (code, team_name))
                        result = cursor.fetchone()
                        
                        if result:
                            update_sql = """
                                UPDATE fxh_team_agency_data 
                                SET team_logo = %s, team_route = %s, created_at = %s
                                WHERE code = %s AND team_name = %s
                            """
                            cursor.execute(update_sql, (team_logo, team_route, current_time, code, team_name))
                            logger.info(f"更新团队数据 - code: {code}, name: {team_name}")
                        else:
                            cursor.execute(insert_sql, (code, team_name, team_logo, team_route, current_time))
                            logger.info(f"插入新团队数据 - code: {code}, name: {team_name}")
                    except pymysql.err.IntegrityError as e:
                        if e.args[0] == 1452:  # 外键约束错误
                            logger.warning(f"外键约束错误，跳过该记录 - code: {code}, name: {team_name}")
                            continue
                        else:
                            logger.warning(f"数据库操作失败: {e}")
                            self.should_stop = True
                            raise Exception("数据库操作失败，程序将终止")
                    except Exception as e:
                        logger.warning(f"数据库操作失败: {e}")
                        self.should_stop = True
                        raise Exception("数据库操作失败，程序将终止")
                
            self.conn.commit()
            logger.success(f"成功处理团队数据，code: {code}")
        except Exception as e:
            if isinstance(e, pymysql.err.IntegrityError) and e.args[0] == 1452:
                logger.warning(f"外键约束错误，跳过该记录 - code: {code}")
                self.conn.rollback()
            else:
                logger.warning(f"处理团队数据时出错: {e}")
                self.conn.rollback()
                self.should_stop = True
                raise Exception("处理团队数据失败，程序将终止")

    def insert_agency_data_to_mysql(self, parsed_agency_data, code):
        if not parsed_agency_data:
            logger.warning(f"没有机构数据需要保存，code: {code}")
            return

        logger.info(f"准备保存机构数据，数量: {len(parsed_agency_data)}")
        try:
            with self.conn.cursor() as cursor:
                check_sql = "SELECT id FROM fxh_team_agency_data WHERE code = %s AND agency_name = %s"
                insert_sql = """
                    INSERT INTO fxh_team_agency_data 
                    (code, agency_name, agency_logo, agency_route, created_at)
                    VALUES (%s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    agency_logo = VALUES(agency_logo),
                    agency_route = VALUES(agency_route),
                    created_at = VALUES(created_at)
                """
                
                for agency in parsed_agency_data:
                    agency_name = agency['agency_name']
                    agency_logo = agency['agency_logo']
                    agency_route = agency['agency_route']
                    current_time = time.strftime('%Y-%m-%d %H:%M:%S')  # 获取当前时间
                    
                    logger.info(f"准备插入机构数据 - code: {code}, name: {agency_name}, route: {agency_route}")
                    
                    try:
                        cursor.execute(check_sql, (code, agency_name))
                        result = cursor.fetchone()
                        
                        if result:
                            update_sql = """
                                UPDATE fxh_team_agency_data 
                                SET agency_logo = %s, agency_route = %s, created_at = %s
                                WHERE code = %s AND agency_name = %s
                            """
                            cursor.execute(update_sql, (agency_logo, agency_route, current_time, code, agency_name))
                            logger.info(f"更新机构数据 - code: {code}, name: {agency_name}")
                        else:
                            cursor.execute(insert_sql, (code, agency_name, agency_logo, agency_route, current_time))
                            logger.info(f"插入新机构数据 - code: {code}, name: {agency_name}")
                    except pymysql.err.IntegrityError as e:
                        if e.args[0] == 1452:  # 外键约束错误
                            logger.warning(f"外键约束错误，跳过该记录 - code: {code}, name: {agency_name}")
                            continue
                        else:
                            logger.warning(f"数据库操作失败: {e}")
                            self.should_stop = True
                            raise Exception("数据库操作失败，程序将终止")
                    except Exception as e:
                        logger.warning(f"数据库操作失败: {e}")
                        self.should_stop = True
                        raise Exception("数据库操作失败，程序将终止")
                
            self.conn.commit()
            logger.success(f"成功处理机构数据，code: {code}")
        except Exception as e:
            if isinstance(e, pymysql.err.IntegrityError) and e.args[0] == 1452:
                logger.warning(f"外键约束错误，跳过该记录 - code: {code}")
                self.conn.rollback()
            else:
                logger.warning(f"处理机构数据时出错: {e}")
                self.conn.rollback()
                self.should_stop = True
                raise Exception("处理机构数据失败，程序将终止")

    def process_single_thread(self, thread_id):
        """单个线程的处理逻辑"""
        logger.info(f'线程 {thread_id} 开始运行')
        
        while not self.should_stop:
            with self.thread_lock:
                code = self.get_code_from_redis()
                if not code:
                    logger.info(f"线程 {thread_id} 完成所有code处理")
                    break
                if code in self.processed_codes:
                    continue
                self.processed_codes.add(code)

            try:
                self.params['code'] = code
                data = self.fetch_data()
                if data:
                    self.process_data(data, code)
                else:
                    logger.error(f"线程 {thread_id} 未获取到数据，程序将终止")
                    self.should_stop = True
                    break

                # 检查是否应该停止
                if self.should_stop:
                    logger.warning(f"线程 {thread_id} 检测到停止信号，正在退出")
                    break

                self.request_count += 1
                if self.request_count >= 8:
                    sleep_time = random.uniform(10, 15)
                    logger.info(f"线程 {thread_id} 已完成8次请求，休息 {sleep_time:.2f} 秒")
                    time.sleep(sleep_time)
                    self.request_count = 0
                else:
                    wait_time = random.uniform(3, 7)
                    logger.info(f"线程 {thread_id} 单次请求后休息 {wait_time:.2f} 秒")
                    time.sleep(wait_time)

            except Exception as e:
                logger.error(f"线程 {thread_id} 处理code {code}时发生错误: {e}")
                self.should_stop = True
                break

    def run(self):
        """使用多线程运行爬虫"""
        try:
            self.thread_lock = threading.Lock()
            self.processed_codes = set()
            self.should_stop = False

            threads = []
            for i in range(6):
                thread = threading.Thread(
                    target=self.process_single_thread,
                    args=(i+1,),
                    name=f"Thread-{i+1}"
                )
                threads.append(thread)
                thread.start()
                time.sleep(0.5)
                logger.info(f"线程 {i+1} 已启动")

            for thread in threads:
                thread.join()

            if self.should_stop:
                logger.warning("程序因错误终止")
            else:
                logger.success("所有线程正常完成")

        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
        finally:
            self.conn.close()

    def test_file_write(self):
        """测试文件写入功能"""
        test_dirs = [self.team_logo_dir, self.agency_logo_dir]
        for directory in test_dirs:
            try:
                test_file = os.path.join(directory, 'test_write.txt')
                with open(test_file, 'w') as f:
                    f.write('test')
                if os.path.exists(test_file):
                    logger.success(f"测试文件写入成功: {test_file}")
                    os.remove(test_file)
                else:
                    logger.error(f"测试文件写入失败: {test_file}")
            except Exception as e:
                logger.error(f"测试文件写入出错: {directory}, 错误: {e}")


if __name__ == '__main__':
    spider = INVESTMENTTEAM_SPIDER()
    # 先测试文件写入
    spider.test_file_write()
    spider.run()