import requests
import pymysql
from pymysql.cursors import DictCursor
import json
from loguru import logger
from datetime import datetime


class ChaininsightHotlistSpider:

    def __init__(self):
            self.headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9",
                "authorization;": "",
                "content-type": "application/json",
                "priority": "u=1, i",
                "referer": "https://chaininsight.vip/",
                "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }

            self.url = "https://chaininsight.vip/api/v0/ai_summary/query_aggregated_daily_summary"
            self.params = {
                "pageSize": "20",
                "pageNum": "1",
                "keyword": ""
            }

            self.db_config = {
                'host': '***************',
                'port': 3306,
                'user': 'spider',
                'password': 'ha13579.',
                'db': 'spider',
                'charset': 'utf8mb4',
                'cursorclass': DictCursor
            }
            self.table_name = "chaininsight_hot"
            self.proxies = {
            "http": "socks5://**************:30889",
            "https": "socks5://**************:30889"
        }


    def get_data(self):
        try:
            response = requests.get(self.url, headers=self.headers, params=self.params, proxies=self.proxies)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None


    def convert_timestamp_to_datetime(self, timestamp_ms):
        """将毫秒级时间戳转换为标准时间格式"""
        try:
            if timestamp_ms:
                timestamp_s = int(timestamp_ms) / 1000
                dt = datetime.fromtimestamp(timestamp_s)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            return None
        except (ValueError, TypeError) as e:
            logger.error(f"时间戳转换失败: {e}")
            return None


    def check_data_exists(self, cursor, code_address, time):
        """检查数据库中是否已存在相同的code_address和time的记录"""
        try:
            cursor.execute(f"""
                SELECT COUNT(*) as count FROM {self.table_name} 
                WHERE code_address = %s AND time = %s
            """, (code_address, time))
            count = cursor.fetchone()['count']
            return count > 0
        except pymysql.Error as e:
            logger.error(f"检查重复数据失败: {e}")
            return False


    def insert_to_database(self, data_list):
        """将数据插入MySQL数据库"""
        try:
            conn = pymysql.connect(**self.db_config)
            cursor = conn.cursor()
            
            insert_count = 0
            skip_count = 0
            
            for item_data in data_list:
                formatted_time = self.convert_timestamp_to_datetime(item_data['timestamp'])
                information_sources = ', '.join(item_data['sources']) if item_data['sources'] else ''
                
                if item_data['relatedTokens']:
                    for token in item_data['relatedTokens']:
                        if self.check_data_exists(cursor, token['ca'], formatted_time):
                            logger.debug(f"跳过重复数据: CA={token['ca']}, Time={formatted_time}")
                            skip_count += 1
                            continue

                        try:
                            cursor.execute(f"""
                                INSERT INTO {self.table_name} 
                                (code_address, chain, code_name, information_sources, time, content)
                                VALUES (%s, %s, %s, %s, %s, %s)
                            """, (
                                token['ca'],
                                token['chain'],
                                item_data['projectName'],
                                information_sources,
                                formatted_time,
                                item_data['projectAnalysis']
                            ))
                            insert_count += 1
                            logger.debug(f"成功插入数据: CA={token['ca']}, Time={formatted_time}")
                        except pymysql.Error as e:
                            logger.error(f"插入数据失败: {e}")
                else:
                    if self.check_data_exists(cursor, '', formatted_time):
                        logger.debug(f"跳过重复数据: CA='', Time={formatted_time}")
                        skip_count += 1
                        continue

                    try:
                        cursor.execute(f"""
                            INSERT INTO {self.table_name} 
                            (code_address, chain, code_name, information_sources, time, content)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """, (
                            '',
                            '',
                            item_data['projectName'],
                            information_sources,
                            formatted_time,
                            item_data['projectAnalysis']
                        ))
                        insert_count += 1
                        logger.debug(f"成功插入数据: CA='', Time={formatted_time}")
                    except pymysql.Error as e:
                        logger.error(f"插入数据失败: {e}")
            
            conn.commit()
            conn.close()

            logger.info(f"数据库操作完成: 新插入 {insert_count} 条数据，跳过重复数据 {skip_count} 条")
            return insert_count
            
        except pymysql.Error as e:
            logger.error(f"数据库操作失败: {e}")
            return 0

    def parse_data(self, data):
        """解析JSON数据，提取所需字段"""
        if not data or data.get('code') != 0:
            logger.error("数据格式错误或请求失败")
            return []
        
        result_list = []
        
        # 遍历data.list中的每个日期块
        for date_block in data.get('data', {}).get('list', []):
            for item in date_block.get('items', []):
                item_data = {
                    'timestamp': item.get('timestamp'),
                    'projectName': item.get('projectName'),
                    'projectAnalysis': item.get('projectAnalysis'),
                    'relatedTokens': [],
                    'sources': []
                }
                
                for token in item.get('relatedTokens', []):
                    token_info = {
                        'ca': token.get('ca'),
                        'chain': token.get('chain'),
                        'name': token.get('name')
                    }
                    item_data['relatedTokens'].append(token_info)
                
                for source in item.get('source', []):
                    source_name = source.get('name')
                    if source_name:
                        item_data['sources'].append(source_name)
                
                result_list.append(item_data)
                
                logger.info(f"项目名称: {item_data['projectName']}")
                logger.info(f"时间戳: {item_data['timestamp']}")
                logger.info(f"项目分析: {item_data['projectAnalysis'][:100]}...")
                
                for i, token in enumerate(item_data['relatedTokens'], 1):
                    logger.info(f"代币{i} - CA: {token['ca']}")
                    logger.info(f"代币{i} - 链: {token['chain']}")
                    logger.info(f"代币{i} - 名称: {token['name']}")
                
                logger.info(f"信息来源: {', '.join(item_data['sources'])}")
                logger.info("-" * 80)

        return result_list


if __name__ == "__main__":
    spider = ChaininsightHotlistSpider()
    logger.info("开始获取热门数据...")
    json_data = spider.get_data()
    
    if json_data:
        logger.info("数据获取成功，开始解析...")
        parsed_data = spider.parse_data(json_data)
        logger.info(f"解析完成，共处理了 {len(parsed_data)} 条记录")
        
        if parsed_data:
            logger.info("开始写入数据库...")
            insert_count = spider.insert_to_database(parsed_data)
            logger.info(f"数据库写入完成，共插入 {insert_count} 条记录")
        else:
            logger.warning("没有数据需要写入数据库")
    else:
        logger.error("数据获取失败")