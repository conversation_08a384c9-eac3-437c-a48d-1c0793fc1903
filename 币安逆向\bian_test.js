// 币安逆向分析脚本
console.log('=== 币安 AWS WAF Token 逆向分析 ===');

// 解码 device-info 数据
const deviceInfo1 = "eyJzY3JlZW5fcmVzb2x1dGlvbiI6IjE5MjAsMTA4MCIsImF2YWlsYWJsZV9zY3JlZW5fcmVzb2x1dGlvbiI6IjE5MjAsMTAzMiIsInN5c3RlbV92ZXJzaW9uIjoiV2luZG93cyAxMCIsImJyYW5kX21vZGVsIjoidW5rbm93biIsInN5c3RlbV9sYW5nIjoiemgtQ04iLCJ0aW1lem9uZSI6IkdNVCswODowMCIsInRpbWV6b25lT2Zmc2V0IjotNDgwLCJ1c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzOC4wLjAuMCBTYWZhcmkvNTM3LjM2IiwibGlzdF9wbHVnaW4iOiJQREYgVmlld2VyLENocm9tZSBQREYgVmlld2VyLENocm9taXVtIFBERiBWaWV3ZXIsTWljcm9zb2Z0IEVkZ2UgUERGIFZpZXdlcixXZWJLaXQgYnVpbHQtaW4gUERGIiwiY2FudmFzX2NvZGUiOiIzOWNkMTMyZSIsIndlYmdsX3ZlbmRvciI6Ikdvb2dsZSBJbmMuIChJbnRlbCkiLCJ3ZWJnbF9yZW5kZXJlciI6IkFOR0xFIChJbnRlbCwgSW50ZWwoUikgVUhEIEdyYXBoaWNzIDYzMCAoMHgwMDAwOUJDOCkgRGlyZWN0M0QxMSB2c181XzAgcHNfNV8wLCBEM0QxMSkiLCJhdWRpbyI6IjEyNC4wNDM0NzUyNzUxNjA3NCIsInBsYXRmb3JtIjoiV2luMzIiLCJ3ZWJfdGltZXpvbmUiOiJBc2lhL1NoYW5naGFpIiwiZGV2aWNlX25hbWUiOiJDaHJvbWUgVjEzOC4wLjAuMCAoV2luZG93cykiLCJmaW5nZXJwcmludCI6IjNlZjk0NWE3Njc5ODkzZDk0OTJkMDU2NmJiMzQ1MDFlIiwiZGV2aWNlX2lkIjoiIiwicmVsYXRlZF9kZXZpY2VfaWRzIjoiIn0=";

// Node.js 环境解码
console.log('=== Device Info 解码结果 ===');
try {
    const decoded = Buffer.from(deviceInfo1, 'base64').toString('utf-8');
    const parsed = JSON.parse(decoded);
    console.log('解码后的设备信息：');
    console.log(JSON.stringify(parsed, null, 2));
} catch (e) {
    console.log('解码失败：', e.message);
}

// 关键发现记录
console.log('\n=== 关键发现 ===');
console.log('1. BNC-UUID: 6208334d-cc3d-4455-a42e-52531d072aeb');
console.log('2. X-TRACE-ID: 19218b91-04d7-434b-95c4-31c5e9feff9b');
console.log('3. AWS WAF Token 第一段: 54f88a70-c77d-4b8a-90f4-299048b561e1');
console.log('4. 设备指纹包含: canvas_code, fingerprint, audio 等');

// 分析 AWS WAF Token 结构
const awsWafToken = "54f88a70-c77d-4b8a-90f4-299048b561e1:BgoAk4w+N98pAAAA:gQEg0SfC80QqmYQtkMM6l3z9rS7xvupRgMj0J6ldUqm5VmXInpTqdB15ZrLpo09aYJ8aNwhCDYejFj4zd+Hl1d/G2gIyqs//J0PLw1zkTlpImqV7uFhF6LcIplcwvp+cD9vyQum3druK4Mk90Ba+P4/JdQY/hvpFvTdubokLj2IHxEovodwd3PIbNMFtEcQM0y0=";

const parts = awsWafToken.split(':');
console.log('\n=== AWS WAF Token 结构分析 ===');
console.log('第一段 (UUID):', parts[0]);
console.log('第二段 (Base64):', parts[1]);
console.log('第三段 (Base64 长串):', parts[2]);

// 尝试解码第二段
try {
    const secondPart = Buffer.from(parts[1], 'base64');
    console.log('第二段解码 (hex):', secondPart.toString('hex'));
    console.log('第二段解码 (buffer):', Array.from(secondPart));
} catch (e) {
    console.log('第二段解码失败：', e.message);
}

// 新增：时间戳分析
console.log('\n=== 时间戳分析 ===');
const secondPartHex = '060a00938c3e37df29000000';
console.log('第二段完整hex:', secondPartHex);

// 尝试不同的时间戳解析方式
const hexBytes = secondPartHex.match(/.{2}/g).map(hex => parseInt(hex, 16));
console.log('字节数组:', hexBytes);

// 检查可能的时间戳位置
// 方式1: 取中间6字节作为时间戳 (938c3e37df29)
const timestampHex1 = secondPartHex.substring(6, 18); // 938c3e37df29
console.log('\n可能的时间戳 (6字节):', timestampHex1);

// 尝试转换为时间戳
const timestampInt1 = parseInt(timestampHex1, 16);
console.log('转换为整数:', timestampInt1);
console.log('作为毫秒时间戳:', new Date(timestampInt1));
console.log('作为秒时间戳:', new Date(timestampInt1 * 1000));

// 方式2: 取后4字节作为时间戳 (37df29)
const timestampHex2 = secondPartHex.substring(10, 16); // 37df29
console.log('\n可能的时间戳 (3字节):', timestampHex2);
const timestampInt2 = parseInt(timestampHex2, 16);
console.log('转换为整数:', timestampInt2);
console.log('作为秒时间戳:', new Date(timestampInt2 * 1000));

// 当前时间对比
console.log('\n当前时间:', new Date());
console.log('当前时间戳 (毫秒):', Date.now());
console.log('当前时间戳 (秒):', Math.floor(Date.now() / 1000));

// UUID分析
console.log('\n=== UUID 对比分析 ===');
console.log('BNC-UUID 和 AWS WAF Token 第一段 是不同的 UUID');
console.log('这意味着 AWS WAF Token 有独立的生成逻辑');
console.log('需要寻找 AWS WAF Token 的具体生成函数');

// 新增：AWS WAF Challenge 分析
console.log('\n=== AWS WAF Challenge 机制分析 ===');

// Challenge URL 分析
const challengeUrl = "https://fe4385362baa.522427d5.ap-southeast-1.token.awswaf.com/fe4385362baa/306922cde096/8b22eb923d34/challenge.js";
console.log('Challenge JS URL:', challengeUrl);

// 解析URL结构
const urlParts = new URL(challengeUrl);
console.log('域名:', urlParts.hostname);
console.log('路径:', urlParts.pathname);

// gokuProps 分析
const gokuProps = {
    "key": "AQIDAHjcYu/GjX+QlghicBgQ/7bFaQZ+m5FKCMDnO+vTbNg96AFe5qPVJjlBcLicGIp1DWcnAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMXBD3MVVKhU9DfptbAgEQgDtqRRs/hlECu5ia7EQndp8CCQn0BXGkrAH3dt0k9RjxGjbFCmNvMvdy43NHyZa8WtnabLfOHaP9LIwSWQ==",
    "iv": "A6weaADBywAAoIx+",
    "context": "smGqEk+mXPIYng9xMpRWcjYS85r913+PYsfMrU1/Vaz0m761PVPuRv45DTZ7Y253iOzD37+u8a3hQ8VgsJrTi+qDtPzI5wEfiJf4vp9LPmHrKpm0d5X+mvj/96BfEed3TSgieuACnp15YO9O2jo3Du00NtHje1kSeNaq3hu+m0aL58RpRu0Xfzkn2MmmrYJjwf5skmefEwlVhYUVeArwXYsPQ6zAEli3rF+WWqBdAXCl51T6hk5+yTBxPZ3sae9kCQ6TPBMTlmTYo8hPS7veUCZITQnmdZMjX5AeSXaixUKPiXRWOqa3eFIw9Q2evaGQw1qAK5tzEwk0oWIoaBUdZkdwD3ZHfJfe55oL0p43SMXS8Y1G0L/t/eb3rf7B75Z/BnX5NLB1hai1d1GwlNNeZ88AOYI="
};

console.log('\n=== gokuProps 分析 ===');
console.log('Key (AWS KMS加密数据):', gokuProps.key.substring(0, 50) + '...');
console.log('IV (初始化向量):', gokuProps.iv);
console.log('Context长度:', gokuProps.context.length);

// IV解码分析
try {
    const ivDecoded = Buffer.from(gokuProps.iv, 'base64');
    console.log('IV解码 (hex):', ivDecoded.toString('hex'));
    console.log('IV解码 (bytes):', Array.from(ivDecoded));
} catch (e) {
    console.log('IV解码失败:', e.message);
}

// AWS WAF Integration 分析
console.log('\n=== AwsWafIntegration 流程分析 ===');
console.log('1. AwsWafIntegration.saveReferrer() - 保存来源页面');
console.log('2. AwsWafIntegration.checkForceRefresh() - 检查强制刷新');
console.log('3. AwsWafIntegration.forceRefreshToken() - 强制刷新token');
console.log('4. AwsWafIntegration.getToken() - 获取token');
console.log('5. window.location.reload(true) - 重新加载页面');

console.log('\n=== 下一步分析重点 ===');
console.log('1. 获取 challenge.js 文件内容');
console.log('2. 分析 AwsWafIntegration 实现');
console.log('3. 理解 gokuProps 加密数据的用途');
console.log('4. 复现 token 生成算法');
