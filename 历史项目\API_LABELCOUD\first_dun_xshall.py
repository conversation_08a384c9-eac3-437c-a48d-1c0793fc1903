import redis
import requests
from curl_cffi import requests as curl_requests
import time
import json
from datetime import date
from loguru import logger
import re

crawl_date = date.today()

redis_client = redis.Redis(
    host='**************',
    port=6379,
    db=14,
    password='123456',
    decode_responses=True
)

proxy = "*************************************************"
proxies = {
    'http': proxy, 
    'https': proxy,
}

def create_task_for_cookie():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}
    data_task = {
        "clientKey": client_key,
        "task": {
            "type": "CloudFlareTaskS2",
            "websiteURL": "https://optimistic.etherscan.io/login",
            "userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
            "proxy": proxy,
            "waitLoad": True,
        }
    }
    response = requests.post(url, headers=headers, json=data_task)
    response_json = response.json()
    if response_json['errorId'] == 0:
        task_id_for_cookie = response_json['taskId']
        logger.info(f"Task ID: {task_id_for_cookie}")
        return task_id_for_cookie
    else:
        logger.info(f"Error: {response_json['errorDescription']}")
        return None

def get_task_result_for_cookie(task_id_for_cookie):
    logger.info('正在获取cookie等信息')
    url_result = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data_result = {
        "clientKey": client_key,
        "taskId": task_id_for_cookie
    }

    response = requests.post(url_result, headers=headers, json=data_result)
    result = response.json()
    for i in range(40):
        if result['errorId'] == 0:
            if result['status'] == 'ready':
                logger.info("Task is ready. Solution:", result['solution'])

                cookies = result['solution'].get('cookies', {})
                cf_clearance_value = cookies.get("cf_clearance")
                asp_net_session_id = cookies.get("ASP.NET_SessionId")
                required_cookies = {'cf_clearance': cf_clearance_value,
                                    'ASP.NET_SessionId': asp_net_session_id
                                    }
                logger.info(f'{required_cookies}')
                cookies_json = json.dumps(required_cookies())
                redis_client.set("API_COOKIES", cookies_json)
                # logger.info('================================================================================================')
                # user_agent = result['solution'].get('user_agent')
                # if user_agent:
                #     headers = {'User-Agent': user_agent}
                # else:
                #     logger.warning("User agent not found in the solution.")
                #     headers = None
                # logger.info(f'{headers}')
                # logger.info('================================================================================================')
                # return required_cookies, headers

            elif result['status'] == 'processing':
                logger.info(f'retry to get cookie, num: {i}')
                time.sleep(3)
                response = requests.post(url_result, headers=headers, json=data_result)
                result = response.json()
                continue
            else:
                logger.info("Task is not ready yet. Status:", result['status'])
                return result
        else:
            logger.info("Error in getting task result:", result['errorDescription'])
            return result

    logger.info("Max retries reached. Task is still processing.")
    return None


def create_task_for_token():
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": client_key,
        "task": {
            "type": "TurnstileTaskProxyless",
            "websiteURL": "https://optimistic.etherscan.io/myaccount",
            "websiteKey": "0x4AAAAAAAEa1DD36OluMD6w"
        }
    }
    response = requests.post(url, headers=headers, json=data)
    response_json = response.json()
    if response_json['errorId'] == 0:
        task_id_for_token = response_json['taskId']
        logger.info(f"Task ID: {task_id_for_token}")
        logger.info('已获取到task_id_for_token')
        return task_id_for_token
    else:
        logger.info(f"Error: {response_json['errorDescription']}")
        return None


def get_task_result_for_token(task_id_for_token):
    time.sleep(3)
    logger.info('开始获取token')
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": client_key,
        "taskId": task_id_for_token
    }

    response = requests.post(url, headers=headers, json=data)
    token_result = response.json()
    for i in range(40):
        if token_result['errorId'] == 0:
            if token_result['status'] == 'ready':
                token = token_result['solution']['token']
                logger.info(f'token:{token}')
                return token
            
            elif token_result['status'] == 'processing':
                logger.info(f'retry to get token, num: {i}')
                time.sleep(3)
                response = requests.post(url, headers=headers, json=data)
                token_result = response.json()
                continue
            else:
                logger.info("Task is not ready yet. Status:", token_result['status'])
                return token_result
        else:
            logger.info("Error in getting task result:", token_result['errorDescription'])
            return token_result

    logger.info("Max retries reached. Task is still processing.")
    return None


# @staticmethod
def check_login_status(text):
    """ 检查登录状态 """

    login_status: bool
    error_msg: str = ''
    if 'Sign In for Continued Access' in text:
        login_status = False
        error_msg = '登录失败, 已过掉cloudflare检测, 但未登录成功!'
    elif 'OP Mainnet Top Accounts by ETH Balance' in text:
        login_status = True
        logger.info('检测登录-成功')
    else:
        login_status = False
        error_msg = '登录失败, 没有过掉cloudflare检测'
    return login_status, error_msg


def login_for_5s(required_cookies, headers, token):
    
    logger.info(f'cookie:{required_cookies}')
    logger.info(f'headers:{headers}')
    logger.info(f'token: {token}')
    logger.info('尝试拿取载荷参数')
    login_url = "https://optimistic.etherscan.io/login"
    response1 = requests.get(login_url, headers=headers , cookies=required_cookies, proxies=proxies)
    time.sleep(1)

    if response1.status_code != 200:
        logger.info(f'获取登录页面失败，状态码：{response1.status_code}')
        logger.info(f'{response1.text}')
        return
    
    if response1.status_code == 200:
        logger.info('过盾成功,开始拿取载荷参数')

    viewstate = re.search(r'__VIEWSTATE" value="(.*?)"', response1.text)
    viewstate_generator = re.search(r'__VIEWSTATEGENERATOR" value="(.*?)"', response1.text)
    event_validation = re.search(r'__EVENTVALIDATION" value="(.*?)"', response1.text)

    if viewstate and viewstate_generator and event_validation:

        # logger.info(f'viewstate_generator:{viewstate_generator.group(1)}')
        # logger.info(f'event_validation:{event_validation.group(1)}')
        logger.info('成功拿到载荷参数')
        logger.info('开始打码尝试登陆')
        time.sleep(2)

    post_data = {
        "__VIEWSTATE": viewstate.group(1),
        "__VIEWSTATEGENERATOR": viewstate_generator.group(1),
        "__EVENTVALIDATION": event_validation.group(1),
        "ctl00$ContentPlaceHolder1$txtUserName": "Krickliu",
        "ctl00$ContentPlaceHolder1$txtPassword": "DOMYBEST0922",
        "cf-turnstile-response": token,
        "ctl00$ContentPlaceHolder1$btnLogin": "LOGIN"
    }
    url = "https://optimistic.etherscan.io/login"
    logger.info('尝试登录....')

    for i in range(5):
            response2 = requests.post(url=url, headers=headers, data=post_data, cookies=required_cookies, proxies=proxies)
            logger.info(response2.status_code)
            time.sleep(2)
            if response2.status_code == 200:
                logger.info(f'登录页面状态：{response2.status_code}')
                if response2.cookies:
                    logger.info(f'登录成功后的cookies: {response2.cookies.get_dict()}')
                    cookies_json = json.dumps(response2.cookies.get_dict())
                    redis_client.set("API_COOKIES", cookies_json)
                    logger.info('cookie已经保存到redis')
                    break
                else:
                    logger.info('登录成功,但未获取到任何cookie信息。')
                    logger.info(f'开始重试,第{i}次')
            else:
                logger.info(f'登录请求失败，状态码：{response2.status_code}，退出程序。')
                exit()

    # response2 = requests.post(url=url, headers=headers , data=post_data, cookies=required_cookies,
    #                         proxies=proxies)

    # logger.info(response2.status_code)
    # time.sleep(2)
    # if response2.status_code != 200:
    #     logger.info(f'登录请求失败，状态码：{response2.status_code}，退出程序。')
    #     exit()

    # elif response2.status_code == 200:
    #     logger.info(f'登录页面状态：{response2.status_code}')
    #     # logger.info(f'{response2.text}')
    #     logger.info('登录成功！')
    #     time.sleep(3)
                
    #     if response2.cookies:
    #         logger.info(f'登录成功后的cookies: {response2.cookies.get_dict()}')
    #         logger.info('已经成功获取到cookie!')
    #         cookies_json = json.dumps(response2.cookies.get_dict())
    #         redis_client.set("API_COOKIES", cookies_json)
    #     else:
    #         logger.info('登录成功,但未获取到任何cookie信息。')
    #         logger.info('开始重试')



if __name__ == '__main__':

    client_key = "a4eb359df78983d82fdbe816f4c699829cddf67856298"
    task_id_for_cookie = create_task_for_cookie()
    required_cookies = None
    headers = None
    if task_id_for_cookie:
        required_cookies, headers = get_task_result_for_cookie(task_id_for_cookie)

    task_id_for_token = create_task_for_token()

    token = None
    if task_id_for_token:
        token = get_task_result_for_token(task_id_for_token)

    session = requests.Session()
    login_for_5s(required_cookies , headers,  token)