#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取代币当前价格脚本
接口: /api/v1/dex/market/current-pricechange_volume
"""

from curl_cffi import requests
import json
import sqlite3
import time
import os
from datetime import datetime


class CoinPriceSpider:
    """代币价格爬虫"""
    
    def __init__(self, db_path='coin_prices.db'):
        """初始化爬虫"""
        self.base_url = "https://valuescan.ai/api/v1/dex/market/current-price"
        self.headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建表，如果不存在
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS coin_prices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            chain_name TEXT NOT NULL,
            token_contract_address TEXT NOT NULL,
            pricechange_volume TEXT NOT NULL,
            time TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建索引，提高查询效率
        cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_token_time 
        ON coin_prices (chain_name, token_contract_address, timestamp)
        ''')
        
        conn.commit()
        conn.close()
        print(f"数据库初始化完成: {self.db_path}")
    
    def get_current_price(self, chain_name, token_contract_address):
        """
        获取代币当前价格
        
        Args:
            chain_name: 链名称，如 "SOL", "ETH" 等
            token_contract_address: 代币合约地址
            
        Returns:
            dict: 响应数据，包含价格信息
        """
        data = {
            "chainName": chain_name,
            "tokenContractAddress": token_contract_address
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=data,
                timeout=10
            )
            
            # 检查请求是否成功
            response.raise_for_status()
            
            # 解析响应数据
            result = response.json()
            
            if result.get("code") == 200:
                return result
            else:
                print(f"请求失败: {result.get('msg')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None
        except json.JSONDecodeError:
            print(f"JSON解析失败: {response.text}")
            return None
    
    def save_to_db(self, chain_name, token_contract_address, price_data):
        """
        将价格数据保存到数据库
        
        Args:
            chain_name: 链名称
            token_contract_address: 代币合约地址
            price_data: 价格数据，API返回的json
        """
        if not price_data or "data" not in price_data:
            print("没有有效数据可保存")
            return False
        
        try:
            data_item = None
            # 查找对应的数据项
            for item in price_data["data"]:
                if (item.get("chainName") == chain_name and 
                    item.get("tokenContractAddress") == token_contract_address):
                    data_item = item
                    break
            
            if not data_item:
                print(f"未找到匹配的数据: {chain_name}/{token_contract_address}")
                return False
            
            # 提取需要的字段
            price = data_item.get("pricechange_volume", "0")
            time_str = data_item.get("time", "0")
            
            # 将时间戳字符串转换为整数
            timestamp = int(time_str) if time_str.isdigit() else 0
            
            # 将数据保存到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                """
                INSERT INTO coin_prices 
                (chain_name, token_contract_address, pricechange_volume, time, timestamp) 
                VALUES (?, ?, ?, ?, ?)
                """,
                (chain_name, token_contract_address, price, time_str, timestamp)
            )
            
            conn.commit()
            conn.close()
            
            print(f"数据保存成功: {chain_name}/{token_contract_address} - 价格: {price}")
            return True
            
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def fetch_and_save(self, chain_name, token_contract_address):
        """
        获取并保存代币价格
        
        Args:
            chain_name: 链名称
            token_contract_address: 代币合约地址
            
        Returns:
            tuple: (成功与否, 价格数据)
        """
        price_data = self.get_current_price(chain_name, token_contract_address)
        
        if price_data:
            success = self.save_to_db(chain_name, token_contract_address, price_data)
            return success, price_data
        
        return False, None
    
    def get_latest_price(self, chain_name, token_contract_address):
        """
        从数据库获取最新价格
        
        Args:
            chain_name: 链名称
            token_contract_address: 代币合约地址
            
        Returns:
            dict: 最新价格信息
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            """
            SELECT pricechange_volume, time, timestamp, created_at 
            FROM coin_prices 
            WHERE chain_name = ? AND token_contract_address = ? 
            ORDER BY timestamp DESC LIMIT 1
            """,
            (chain_name, token_contract_address)
        )
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                "pricechange_volume": result[0],
                "time": result[1],
                "timestamp": result[2],
                "db_time": result[3]
            }
        
        return None
    
    def get_price_history(self, chain_name, token_contract_address, limit=100):
        """
        获取价格历史记录
        
        Args:
            chain_name: 链名称
            token_contract_address: 代币合约地址
            limit: 返回的记录数量限制
            
        Returns:
            list: 价格历史记录列表
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使用字典形式返回结果
        cursor = conn.cursor()
        
        cursor.execute(
            """
            SELECT id, chain_name, token_contract_address, pricechange_volume, time, timestamp, created_at 
            FROM coin_prices 
            WHERE chain_name = ? AND token_contract_address = ? 
            ORDER BY timestamp DESC LIMIT ?
            """,
            (chain_name, token_contract_address, limit)
        )
        
        results = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return results


def main():
    """主函数"""
    # 设置数据库路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(script_dir, 'coin_prices.db')
    
    # 创建爬虫实例
    spider = CoinPriceSpider(db_path)
    
    # 定义要获取的代币
    tokens = [
        # 链名称, 代币合约地址
        ("SOL", "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"),  # USDC on Solana
        ("ETH", "******************************************"),     # USDT on Ethereum
        # 可以添加更多代币
    ]
    
    # 获取并保存价格
    for chain_name, token_address in tokens:
        print(f"\n开始获取 {chain_name}/{token_address} 的当前价格...")
        success, price_data = spider.fetch_and_save(chain_name, token_address)
        
        if success:
            # 获取并打印最新价格
            latest = spider.get_latest_price(chain_name, token_address)
            if latest:
                print(f"最新价格: {latest['pricechange_volume']}")
                print(f"价格时间: {latest['time']}")
                print(f"入库时间: {latest['db_time']}")
        else:
            print(f"获取 {chain_name}/{token_address} 价格失败")


if __name__ == "__main__":
    main() 