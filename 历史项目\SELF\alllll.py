import requests
import csv
from bs4 import BeautifulSoup
import json

def load_cookies():
    cookies = {}
    with open('1_login_cookies.csv', 'r', newline='', encoding='utf-8') as cookiefile:
        reader = csv.reader(cookiefile)
        for row in reader:
            if row:
                cookies[row[0]] = row[1]
    return cookies

def load_categories():
    categories = []
    with open('2_url_data.csv', 'r', newline='', encoding='utf-8') as file:
        reader = csv.reader(file)
        next(reader)
        for row in reader:
            categories.append(row[0].strip())
    return categories

def fetch_data(category, draw, length):
    url = "https://optimistic.etherscan.io/accounts.aspx/GetTableEntriesBySubLabel"
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    payload = {
        "dataTableModel": {
            "draw": draw,
            "columns": [
                {"data": "address", "searchable": True, "orderable": False},
                {"data": "nameTag", "searchable": True, "orderable": False},
                {"data": "balance", "searchable": True, "orderable": True},
                {"data": "txnCount", "searchable": True, "orderable": True}
            ],
            "order": [{"column": 1, "dir": "asc"}],
            "start": 0,
            "length": length,
            "search": {"value": "", "regex": False}
        },
        "labelModel": {"label": category}
    }
    response = requests.post(url, headers=headers, cookies=load_cookies(), data=json.dumps(payload))
    if response.status_code == 200:
        return response.text
    else:
        print(f"Failed to fetch data for category {category} with draw {draw} and length {length}")
        return None

def parse_webpage(html):
    soup = BeautifulSoup(html, 'html.parser')
    data_rows = soup.find_all('tr', class_='odd')
    results = []
    for row in data_rows:
        cells = row.find_all('td')
        if len(cells) >= 4:
            address_span = cells[0].find('span', {'data-highlight-target': True})
            address = address_span['data-highlight-target'] if address_span else "地址信息缺失"
            name_tag = cells[1].text.strip()
            balance = cells[2].text.strip()
            txn_count = cells[3].text.strip()
            results.append([address, name_tag, balance, txn_count])
    return results

def save_results_to_csv(results, category):
    with open(f'DATA/{category}_data.csv', 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['Address', 'Name Tag', 'Balance', 'Txn Count'])
        writer.writerows(results)

def main():
    categories = load_categories()
    for category in categories:
        all_results = []
        for draw, length in zip(range(1, 6), [10, 25, 50, 100, 150]):
            html = fetch_data(category, draw, length)
            if html:
                results = parse_webpage(html)
                all_results.extend(results)
        save_results_to_csv(all_results, category)

if __name__ == "__main__":
    main()