from curl_cffi import requests
import json
import sqlite3
import os
from datetime import datetime

# proxy = {
#     "http": "http://127.0.0.1:33210",
#     "https": "http://127.0.0.1:33210"
# }

proxies = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

headers = {
    "authority": "gmgn.ai",
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "baggage": "sentry-environment=production,sentry-release=20250510-918-0737323,sentry-public_key=93c25bab7246077dc3eb85b59d6e7d40,sentry-trace_id=bc1801049e9b4b7695d8304e7a8aff5a,sentry-sample_rate=0.01,sentry-sampled=false",
    "referer": "https://gmgn.ai/sol/token/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "bc1801049e9b4b7695d8304e7a8aff5a-a749685224b19fdc-0",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
cookies = {
    "_ga": "GA1.1.1784117668.1746768932",
    "GMGN_LOCALE": "zh-CN",
    "GMGN_THEME": "dark",
    "GMGN_CHAIN": "sol",
    "__cf_bm": "BW5M3r8qSKhT15ZPCC8NDPohAcBrafb4oIpvvNVewT4-1747049932-*******-mGIM5FIAL5tIf7CH7u05AqeExFw5HGAfn_srbm00_sNHw2MJ8oUoGog4OF4Om_RJ2P4ht5oEiQC7nUE1376firpQaOEZUaKzosHAfV.bfZU",
    "cf_clearance": "j50rW_C3yvKC5kuskypQIssn3OmcmJ0JS97wUvYEa.c-1747049933-*******-Z6Ioq96XsMaIUbM2fDk.ozrZ5g.FXGQgmAk42o5sbZqCBKy3lQj9gjoGU06Ts2qGyN54fS.5W3eEtA3seAP12d7SQvrFNG2f0J643gwctcmq6Ci157hy2Db.gygWBgbVGCFwVKR2WZl_CnsTwQl55XahzyWo5aWnanSILVNOaw4KOPNMTeMAnYBtJmeDCM1vxiXFtsFNx32YpCbJyVd7B_i6gdKAuNtW6phU3MsySi9zOkdlldVH3u6qDF2xv0NZHRArL11CXgCHRFbEtmaHGzox17GcDJGVSl0vUS7cBnUwucOiv2vygqZwjeN1JpYvV.ewqBFTdqRKK4zvoJkGLFglhCABocxXVS9SHjX6bYM",
    "_ga_0XM0LYXGC8": "GS2.1.s1747047756$o6$g1$t1747049939$j0$l0$h0"
}

def init_database():
    """初始化数据库，创建表"""
    db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建表，如果表不存在
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS transaction_spider (
        timestamp INTEGER,
        tx_hash TEXT PRIMARY KEY,
        token_symbol TEXT,
        token_address TEXT,
        ha_price REAL,
        okx_price REAL,
        gmgn_price REAL,
        tvl REAL
    )
    ''')
    
    conn.commit()
    conn.close()
    print(f"数据库初始化完成，表 transaction_spider 已创建")
    return db_path

def parse_and_save_data(response_data, db_path):
    """解析响应数据并保存到数据库"""
    if not response_data or 'data' not in response_data or 'history' not in response_data['data']:
        print("没有找到有效的交易历史数据")
        return

    transactions = response_data['data']['history']
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    inserted_count = 0
    for tx in transactions:
        try:
            # 获取Unix时间戳并转换为标准日期时间格式
            unix_timestamp = tx.get('timestamp')
            # 转换时间戳为可读格式 (YYYY-MM-DD HH:MM:SS)
            formatted_timestamp = datetime.fromtimestamp(unix_timestamp).strftime('%Y-%m-%d %H:%M:%S')
            
            tx_hash = tx.get('tx_hash')
            token_address = tx.get('token_address')
            price_usd = tx.get('price_usd')
            
            # 获取token_symbol - 在响应中可能没有直接提供，这里使用空字符串
            token_symbol = tx.get('quote_symbol', '')
            
            # 检查交易是否已存在
            cursor.execute("SELECT tx_hash FROM transaction_spider WHERE tx_hash = ?", (tx_hash,))
            if cursor.fetchone() is None:
                # 插入新交易记录，使用格式化后的时间戳
                cursor.execute('''
                INSERT INTO transaction_spider 
                (timestamp, tx_hash, token_symbol, token_address, ha_price, okx_price, gmgn_price, tvl) 
                VALUES (?, ?, ?, ?, NULL, NULL, ?, NULL)
                ''', (formatted_timestamp, tx_hash, token_symbol, token_address, price_usd))
                inserted_count += 1
        except Exception as e:
            print(f"处理交易记录时出错: {e}")
    
    conn.commit()
    print(f"成功插入 {inserted_count} 条交易记录")
    conn.close()

def fetch_transaction_data():
    """获取交易数据"""
    url = "https://gmgn.ai/vas/api/v1/token_trades/sol/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"
    params = {
        "device_id": "e17ccaad-5aa6-43a9-a219-ae220bc8a6e4",
        "client_id": "gmgn_web_20250510-918-0737323",
        "from_app": "gmgn",
        "app_ver": "20250510-918-0737323",
        "tz_name": "Asia/Shanghai",
        "tz_offset": "28800",
        "app_lang": "zh-CN",
        "fp_did": "a799e4cfe3ca887eb253de175a8cc8bf",
        "os": "web",
        "limit": "50",
        "maker": "",
        "from": "1747030920",
        "to": "1747031400"
    }
    
    try:
        response = requests.get(url, headers=headers, cookies=cookies, params=params, proxies=proxies, impersonate="chrome120")
        if response.status_code == 200:
            return json.loads(response.text)
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"请求出错: {e}")
        return None

def main():
    # 初始化数据库
    db_path = init_database()
    
    # 获取交易数据
    response_data = fetch_transaction_data()
    
    if response_data:
        # 解析并保存数据
        parse_and_save_data(response_data, db_path)
    else:
        print("未获取到交易数据")

if __name__ == "__main__":
    main()