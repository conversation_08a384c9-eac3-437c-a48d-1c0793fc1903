# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2023/11/23 16:49
@Python  :  Python3.7
@Desc    :  None
"""
import re
# from loguru import logger
from lxml import etree
from settings import *
from Spiders.Spider import Spider_Crawler_Request
import redis
import json

redis_client = redis.Redis(
    host='**************',
    port=6379,
    db=14,
    password='123456',
    decode_responses=True
)


class Schedule(Spider_Crawler_Request):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # self.headers = {
        #     'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36'
        # }
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            db=14,
            password='123456',
            decode_responses=True
        )
        api_headers = self.redis_client.get('API_headers')
        self.headers = {
            'User-Agent': api_headers
        }
        logger.info(f'已从redis中获取到headers信息:{self.headers}')

    def list_page(self):
        url = 'https://optimistic.etherscan.io/labelcloud'

        response = self.get(url, headers=self.headers, proxies=proxies_api)
        if response is False:
            raise 'list_page response fail'

        html = etree.HTML(response.text)
        divs = html.xpath('//*[@id="content"]/div[2]/div/div/div[3]/div')
        for div in divs:
            lis = div.xpath('.//ul/li')
            for li in lis:
                href = ''.join(li.xpath('.//a/@href'))
                text = ''.join(li.xpath('.//a//text()'))
                if href.startswith('/accounts'):
                    logger.info(f'{href}, {text}')
                    try:
                        # count = re.findall('Accounts \((.*?)\)', text)[0]
                        count = re.findall('Accounts \\((.*?)\\)', text)[0]
                        if int(count) > 50000:  # 数量过大会请求超时，并且会影响后续的请求
                            continue
                        else:
                            self.set_seed(seed_key, {'detail_href': href})
                    except Exception as e:
                        logger.error(repr(e))
                        self.send_to_fs({'Program': 'eth_label_cloud_schedule', 'Error': '正则匹配出错, 请检查'})


if __name__ == '__main__':
    clear_log()
    logger.info('开始调度...')
    Schedule().list_page()
    logger.info('调度完成')
