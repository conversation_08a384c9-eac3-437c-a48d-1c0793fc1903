import sqlite3
import pandas as pd
import os
from loguru import logger

def import_csv_to_sqlite():
    try:
        # CSV文件路径
        csv_file = "gmgn_price_data2222.csv"
        
        # 数据库路径
        db_path = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 读取CSV文件
        logger.info("开始读取CSV文件...")
        df = pd.read_csv(csv_file)
        
        # 连接到SQLite数据库
        logger.info("连接到SQLite数据库...")
        conn = sqlite3.connect(db_path)
        
        # 创建表
        # 根据CSV的列名和数据类型创建表结构
        create_table_sql = '''
        CREATE TABLE IF NOT EXISTS gmgn_price (
            gmgn_price_usd REAL,
            gmgn_amount_usd REAL,
            gmgn_amount_base REAL,
            gmgn_amount_quote REAL,
            gmgn_tx_hash TEXT,
            gmgn_time DATETIME,
            gmgn_swap_type INTEGER
        )
        '''
        
        logger.info("创建表结构...")
        conn.execute(create_table_sql)
        
        # 将DataFrame写入SQLite数据库
        logger.info("开始导入数据...")
        df.to_sql('gmgn_price', conn, if_exists='replace', index=False)
        
        # 获取导入的记录数
        count = conn.execute("SELECT COUNT(*) FROM gmgn_price").fetchone()[0]
        logger.success(f"数据导入完成，共导入 {count} 条记录")
        
        # 关闭连接
        conn.close()
        logger.info("数据库连接已关闭")
        
    except Exception as e:
        logger.error(f"导入过程中出错: {str(e)}")
        if 'conn' in locals():
            conn.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    # 设置日志
    # logger.add("csv_to_sqlite.log", rotation="500 MB")
    
    # 执行导入
    import_csv_to_sqlite()