import requests
from loguru import logger
from datetime import date
crawl_date = date.today()
import re
import time

proxy = "http://185.248.186.24:38136"
proxies = {
    'http': proxy, 
    'https': proxy,
}


headers = {
    "authority": "optimistic.etherscan.io",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "referer": "https://optimistic.etherscan.io/labelcloud",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-arch": "\"x86\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"114.0.5735.199\"",
    "sec-ch-ua-full-version-list": "\"Not.A/Brand\";v=\"8.0.0.0\", \"Chromium\";v=\"114.0.5735.199\", \"Google Chrome\";v=\"114.0.5735.199\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-ch-ua-platform-version": "\"15.0.0\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
cookies = {
    "op mainnet etherscan_offset_datetime": "+8",
    "__stripe_mid": "32ff405f-7d86-4d09-89ba-c1341f1a08989e51c6",
    "op mainnet etherscan_switch_token_amount_value": "value",
    "_gid": "GA1.2.1179451399.1734277079",
    "_ga_T1JC9RNQXV": "GS1.1.1734523781.25.1.1734523962.60.0.0",
    "ASP.NET_SessionId": "ezybkebo0oguia5lokpwfon2",
    "__cflb": "02DiuJ7NLBYKjsZtxjRR4QggQcq1CaL9QGXcSDF7uJvsN",
    "__stripe_sid": "808b13ad-6bdf-4704-9d41-66817bf8f6654b0de6",
    "cf_clearance": "y1odmg4HMLYYtAkVF5SISqmNG7pZS.FE5lx9HoVtwt4-1734687868-*******-0y0zgXJJoKMVjYvNlmc3RUymgDDUwjCdXM_NoJ5nMCu.R8n.5UysGSiJ3CvGnODqyNBSyzCeQ7q2qBF4Vk_qpbSgWkICsBgwZcFujrWEjztv.oOPoSZIFfQZNeBfCkfezWqxZlSOdcPf.W2e7lLBh2DJCCOozQ8FYTdeCTCnOMG2lXbFrMJQZeyCJQQPyO1voUGWLYWAVYQCuYLxWhaI1fYJqg85cCoT25x0OEcf_w7uPfHyALjdEUPbzyp5z_c8DIUzGUrQLePzqdfkUkSfKC9igT3w2bEuUphiD2nZ7bo8UCXJtC7iksFAvA0N05dmOLu5_QfwZnZ4KUzsvqrQIcgtob8RClfXAaiMhAr9Fmgt5eJ212ei29rFVnwqtnJYw4pC9b6IbzesI4hDAxRes92G7Dx0KPpXK1z63b1Io3JyTojLUdMj0g2VzLN_GFVJ",
    "_ga": "GA1.2.********.**********",
    "_ga_XPR6BMZXSN": "GS1.1.**********.40.1.**********.0.0.0"
}
url = "https://optimistic.etherscan.io/accounts/label/contract-deployer"
params = {
    "subcatid": "undefined",
    "size": "100",
    "start": "0",
    "col": "1",
    "order": "asc"
}
response = requests.post(url, headers=headers, cookies=cookies)
logger.info(response.status_code)
time.sleep(2)
if response.status_code != 200:
    logger.info(f'登录请求失败，状态码：{response.status_code}，退出程序。')
    exit()
elif response.status_code == 200:
    logger.info(f'登录页面状态：{response.status_code}')
    # logger.info(f'{response.text}')
    logger.info('登录成功！')
    if response.cookies:
        logger.info(f'登录成功后的cookies: {response.cookies.get_dict()}')
        logger.info('已经成功获取到cookie!')
    else:
        logger.info('登录成功,但未获取到任何cookie信息。')
        # exit()
    logger.info(response.text)