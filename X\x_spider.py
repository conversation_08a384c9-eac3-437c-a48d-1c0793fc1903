import requests
import pandas as pd
import json
import os
import time
import random
import redis
from loguru import logger
from datetime import datetime

class XSpider:
    def __init__(self, max_pages=None):
        self.max_pages = max_pages
        self.redis_client = redis.Redis(
            host='**************',
            port=6379,
            db=14,
            password='123456',
            decode_responses=True
        )
        
        twitter_cookie_api = self.redis_client.hgetall('twitter_cookie_api')
        x_csrf_token = twitter_cookie_api.get('ct0')
        auth_token = twitter_cookie_api.get('auth_token')
        ct0 = twitter_cookie_api.get('ct0')

        self.headers = {
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "referer": "",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-csrf-token": x_csrf_token
        }
        self.cookies = {
            "auth_token": auth_token,
            "ct0": ct0
        }
        self.base_params = {
        "variables": "{\"rawQuery\":\"\",\"count\":20,\"querySource\":\"typed_query\",\"product\":\"Top\"}",
        "features": "{\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"responsive_web_graphql_exclude_directive_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"rweb_video_timestamps_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":true,\"responsive_web_enhance_cards_enabled\":false}"
        }

    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间格式到标准格式"""
        try:
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}")
            return None

    def fetch_data(self, keyword, cursor=None):
        """发送请求获取数据"""
        url = "https://x.com/i/api/graphql/QGMTWxm841rbDndB-yQhIw/SearchTimeline"
        variables = {
            "rawQuery": keyword,
            "count": 20,
            "querySource": "typed_query",
            "product": "Top"
        }
        if cursor:
            variables["cursor"] = cursor

        params = self.base_params.copy()
        params["variables"] = json.dumps(variables)
        self.headers["referer"] = f"https://x.com/search?q={keyword}&src=typed_query"

        try:
            response = requests.get(
                url,
                headers=self.headers,
                cookies=self.cookies,
                params=params
            )
            response.raise_for_status()
            logger.success(f"请求成功 [{response.status_code}]")
        
            return response.json()
        except Exception as e:
            logger.error(f"请求失败: {e}")
            return None

    def extract_entries(self, data):
        """从响应数据中提取目标字段"""
        extracted = []
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    for entry in instruction.get('entries', []):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineItem':
                            item_content = content.get('itemContent', {})
                            tweet_result = item_content.get('tweet_results', {}).get('result', {})
                            user_result = tweet_result.get('core', {}).get('user_results', {}).get('result', {})
                            legacy_user = user_result.get('legacy', {})
                            legacy_tweet = tweet_result.get('legacy', {})

                            # 获取editable_until_msecs并转换为标准时间格式
                            edit_control = tweet_result.get('edit_control', {})
                            editable_until_msecs = edit_control.get('editable_until_msecs')
                            if editable_until_msecs:
                                try:
                                    # 将毫秒时间戳转换为标准时间格式
                                    editable_time = datetime.fromtimestamp(int(editable_until_msecs) / 1000).strftime('%Y-%m-%d %H:%M:%S')
                                except Exception as e:
                                    logger.warning(f"时间转换失败: {e}")
                                    editable_time = None
                            else:
                                editable_time = None

                            # 处理媒体数据
                            media = legacy_tweet.get('entities', {}).get('media', [{}])
                            media_info = media[0] if media else {}

                            extracted.append({
                                'name': legacy_user.get('name'),
                                'rest_id': user_result.get('rest_id'),
                                'is_blue_verified': user_result.get('is_blue_verified', False),
                                'verified_type': user_result.get('verified_type'),
                                'normal_followers_count': legacy_user.get('normal_followers_count', 0),
                                'editable_until': editable_time,
                                'description': legacy_user.get('description'),
                                'default_profile_image': legacy_user.get('default_profile_image', False),
                                'followers_count': legacy_user.get('followers_count', 0),
                                'media_url_https': media_info.get('media_url_https')
                            })
            return extracted
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return []

    def get_next_cursor(self, data):
        """翻页逻辑"""
        try:
            # 深度遍历所有可能的路径
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [])
            
            for instruction in instructions:
                # 处理 TimelineAddEntries 和 TimelineReplaceEntry 两种类型
                entries = []
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                elif instruction.get('type') == 'TimelineReplaceEntry':
                    entry = instruction.get('entry')
                    if entry:
                        entries = [entry]
                
                # 调试日志：打印当前处理的条目
                logger.debug(f"处理 {instruction.get('type')} 指令，发现 {len(entries)} 个条目")
                
                for entry in entries:
                    entry_id = entry.get('entryId')
                    # 关键修改：同时检查 cursor-bottom-0 和替换条目
                    if entry_id == 'cursor-bottom-0' or (isinstance(entry_id, str) and 'cursor-bottom' in entry_id):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineCursor':
                            cursor_value = content.get('value')
                            if cursor_value:
                                logger.success(f"成功提取游标: {cursor_value[:30]}...")
                                return cursor_value
            logger.warning("未找到有效游标")
            return None
        except Exception as e:
            logger.error(f"游标解析异常: {str(e)}")
            return None

    def save_to_csv(self, data, filename):
        """保存数据到CSV文件"""
        if not data:
            return

        try:
            df = pd.DataFrame(data)
            df.replace({pd.NA: None}, inplace=True)
            
            # 处理文件存在性
            file_exists = os.path.exists(filename)
            df.to_csv(
                filename,
                mode='a' if file_exists else 'w',
                header=not file_exists,
                index=False,
                encoding='utf-8-sig'
            )
            logger.success(f"成功保存 {len(data)} 条数据到 {filename}")
        except Exception as e:
            logger.error(f"保存失败: {e}")

    def run(self):
        """主运行方法"""
        with open('X/x_keyword/keywords.txt', 'r', encoding='utf-8') as f:
            keywords = [k.strip() for k in f if k.strip()]

        for keyword in keywords:
            logger.info(f"开始处理关键词: {keyword}")
            next_cursor = None
            page = 1
            max_retry = 3

            while True:
                # 页数限制检查
                if self.max_pages and page > self.max_pages:
                    logger.info(f"已达到设置的最大页数 {self.max_pages}，停止翻页")
                    break

                logger.info(f"正在爬取第 {page} 页...")
                data = None
                
                # 带重试的请求
                for attempt in range(max_retry):
                    data = self.fetch_data(keyword, next_cursor)
                    if data:
                        break
                    logger.warning(f"第 {attempt+1} 次重试...")
                    time.sleep(5)

                if not data:
                    logger.error("请求失败，停止翻页")
                    break

                # 数据提取和保存
                extracted = self.extract_entries(data)
                if extracted:
                    self.save_to_csv(extracted, f'X/x_output/{keyword}.csv')
                else:
                    logger.warning("未提取到有效数据")
                
                # 获取下一页游标
                new_cursor = self.get_next_cursor(data)

                # 更新循环终止条件
                if not new_cursor or (self.max_pages and page >= self.max_pages):
                    logger.info("已到达最后一页或达到设置的最大页数") 
                    break                
                
                # 更新游标并继续
                next_cursor = new_cursor
                page += 1
                
                # 随机等待
                wait_time = random.uniform(5, 7)
                logger.info(f"等待 {wait_time:.1f} 秒后继续")
                time.sleep(wait_time)

            logger.success(f"完成关键词 {keyword} 的采集")

if __name__ == "__main__":
    spider = XSpider(max_pages=100)
    spider.run()

# 自动登录保存模块
# 登录失败的还要删除 或者保存到另外一个队列里去
# 主爬虫程序跑起时新增关键词
# txt文件中的关键词应该放在redis中