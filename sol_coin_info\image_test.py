# from curl_cffi import requests
#
# proxy = {
#     "http": "http://127.0.0.1:33210",
#     "https": "http://127.0.0.1:33210"
# }
#
# headers = {
#     "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
#     "accept-language": "zh-CN,zh;q=0.9",
#     "cache-control": "max-age=0",
#     "if-none-match": "\"Qme3rREc6n6v2VvttstXSJKZ53cumfh5Wf3cfCxjLRtmjZ\"",
#     "priority": "u=0, i",
#     "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
#     "sec-ch-ua-mobile": "?0",
#     "sec-ch-ua-platform": "\"Windows\"",
#     "sec-fetch-dest": "document",
#     "sec-fetch-mode": "navigate",
#     "sec-fetch-site": "none",
#     "sec-fetch-user": "?1",
#     "upgrade-insecure-requests": "1",
#     "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
# }
#
# url = "https://ipfs.io/ipfs/QmZ8W3ByMmPcC5mzxDDLYX8CtyjfwEtM858riAzSV553s3"
# response = requests.get(url, impersonate="chrome116", proxies=proxy)
#
# binary_content = response.content
#
#
# # print("\n前100个字节的十六进制:")
# print(binary_content[:300].hex())
#
# print("二进制:")
# print(binary_content[:100])
#
# print("=======================")
# print(response.text)

from curl_cffi import requests

proxy = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

url = "https://gold-historic-alpaca-737.mypinata.cloud/ipfs/bafybeihrremo5f45x3mqr2kshydh45kbgrf5obusp24u22tr3pcnvkxelu/trumppump.json"
response = requests.get(url, proxies=proxy, impersonate="chrome116")

print(response.text)
print(response)