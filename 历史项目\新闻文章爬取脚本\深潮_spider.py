import requests
import pandas as pd
import os
import time
import random
from loguru import logger


def read_existing_titles(excel_path, sheet_name='web3热点信息'):
    if os.path.exists(excel_path):
        try:
            df = pd.read_excel(excel_path, sheet_name=sheet_name, usecols=['文章标题'])
            print("Existing titles read successfully:", df.head())
            return df
        except Exception as e:
            print("Failed to read existing titles:", e)
            return pd.DataFrame()
    else:
        print("Excel file does not exist.")
        return pd.DataFrame()

def SHENCHAO_spider(keywords_file, output_folder, excel_file):
    with open(keywords_file, 'r', encoding='utf-8') as file:
        keywords = file.read().splitlines()

    headers = {"user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"}
    cookies = {"ASP.NET_SessionId": "3gtgxdx3munmizuwr1kpozz3", "zh_choose": "s"}
    base_url = "https://www.techflowpost.com/ashx/search_index.ashx"

    os.makedirs(output_folder, exist_ok=True)
    output_file = os.path.join(output_folder, excel_file)

    all_data = []

    for keyword in keywords:
        data = {
            "pageindex": "1",
            "pagesize": "6",
            "type": "快讯",
            "wd": keyword
        }
        response = requests.post(base_url, headers=headers, cookies=cookies, data=data)
        logger.info(f'Response Status Code for keyword "{keyword}": {response.status_code}')

        if response.status_code == 200:
            json_data = response.json()
            content = json_data.get('content', [])
            if content:
                extracted_data = [{
                    '文章标题': item['stitle'],
                    '文章描述': item['sabstract'],
                    '文章时间': item['dcreate_time'],
                    '文章地址': item['surl']
                } for item in content]

                all_data.extend(extracted_data)
                logger.info(f'已经获取到关于：{keyword} 的信息')
            else:
                logger.info(f'没有关于：{keyword} 的信息')
        else:
            logger.error(f'获取：{keyword} 相关信息失败，状态码: {response.status_code}')

        time.sleep(2.3 + random.random())

    if all_data:
        new_df = pd.DataFrame(all_data)
        new_df['文章时间'] = pd.to_datetime(new_df['文章时间'])
        new_df.sort_values('文章时间', ascending=False, inplace=True)

        # 读取现有数据中的“文章标题”
        existing_titles = read_existing_titles(output_file)

        # 如果现有数据不为空，进行去重操作
        if not existing_titles.empty:
            new_df = new_df[~new_df['文章标题'].isin(existing_titles['文章标题'])]

        # 追加数据到 Excel 文件
        if not new_df.empty:
            with pd.ExcelWriter(output_file, engine='openpyxl', mode='a', if_sheet_exists='overlay') as writer:
                if 'web3热点信息' not in writer.book.sheetnames:
                    pd.DataFrame().to_excel(writer, sheet_name='web3热点信息')
                new_df.to_excel(writer, index=False, sheet_name='web3热点信息', startrow=writer.sheets['web3热点信息'].max_row, header=False)
            logger.info(f'所有数据已经保存到: {output_file}')
        else:
            logger.info('没有要追加的新数据 或所有新数据都是重复的')
            logger.info("数据列表是空的:", new_df)

if __name__ == '__main__':
    keywords_file = r'D:\python码云\关键词\web3热点信息.txt'
    output_folder = r'D:\python_区块链数据爬取'
    excel_file = r'D:\python_区块链数据爬取\web3热点信息.xlsx'
    SHENCHAO_spider(keywords_file, output_folder, excel_file)