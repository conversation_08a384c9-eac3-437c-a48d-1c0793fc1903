#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试版本 - 获取SOL链上指定代币的价格
"""

import requests
import json
import traceback


def get_sol_token_price():
    """获取SOL链上代币价格并打印详细信息"""
    
    # 设置请求信息
    url = "https://valuescan.ai/api/v1/dex/market/current-price"
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    
    # SOL链上的测试代币
    token_address = "HNg5PYJmtqcmzXrv6S9zP1CDKk5BgDuyFBxbvNApump"

    # 请求数据
    data = [{
        "chainName": "SOL",
        "tokenContractAddress": token_address
    }]

    print("=" * 50)
    print("开始请求代币价格")
    print(f"请求URL: {url}")
    print(f"请求头: {headers}")
    print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
    print("=" * 50)

    try:
        # 发送请求
        print("\n发送POST请求...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        # 打印响应状态
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        # 打印响应内容
        print("\n原始响应内容:")
        print(response.text)
        
        # 解析JSON
        if response.text:
            try:
                result = response.json()
                print("\nJSON解析结果:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 提取价格信息
                if result.get("code") == 200 and "data" in result:
                    for item in result["data"]:
                        if (item.get("chainName") == "SOL" and 
                            item.get("tokenContractAddress") == token_address):
                            
                            print("\n价格信息提取成功:")
                            print(f"代币地址: {token_address}")
                            print(f"价格: {item.get('pricechange_volume')}")
                            print(f"时间戳: {item.get('time')}")
                            break
                    else:
                        print(f"\n未在响应中找到匹配的数据: SOL/{token_address}")
                else:
                    print(f"\n请求失败或响应格式异常: {result.get('msg', '未知错误')}")
            
            except json.JSONDecodeError:
                print("\n响应内容不是有效的JSON格式")
        else:
            print("\n响应内容为空")
    
    except Exception as e:
        print(f"\n发生异常: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()


if __name__ == "__main__":
    print("脚本开始执行...")
    get_sol_token_price()
    print("\n脚本执行完毕") 