"""
框架运行需要的一些工具方法
"""
import sys
import time
import json
import phoenixdb
import requests
import traceback
from threading import Lock
from loguru import logger
# redis的配置
REDIS_DB_U = 14 # 14为测试库，原库为6
REDIS_POLL_SIZE_U = 10
REDIS_HOST = '**************'
REDIS_PASSWORD = 123456
REDIS_PORT = 6379
RETRY_NUM_U = 10
# MYSQL的配置
HOST_U = '**************'
PORT_U = 33060
USER_U = "root"
POSS_WORD_U = '12345678'
MYSQL_BASE_U = "spider"
CHAR_U = "utf8"
# phoenixdb的配置
PHOENIX_PORT = 8765
PHOENIX_HOST = '**************'
LOCK = Lock()


class DB_BASE(object):

    # def __init__(self):
    #     # print(database)
    #     super(DB_BASE, self).__init__()
    #
    # def __new__(cls, *args, **kwargs):
    #     if not DB_BASE._instance:
    #         DB_BASE._instance = object.__new__(cls)
    #     return DB_BASE._instance

    def send_to_fs(self, text):
        """
        发送程序异常消息到飞书
        :param text:
        :return:
        """
        headers = {
            'User-Agent': 'Apipost client Runtime/+https://www.apipost.cn/',
        }
        text = f'<at user_id="all">全体成员</at>{text}'
        # "text": "<at user_id = \"ou_f43d7bf0bxxxxxxxxxxxxxxx\">Tom</at> text content"
        params = (
            ('msg_type', 'text'),
            ('content', json.dumps({"text": text}, ensure_ascii=False)),
        )

        requests.post('https://open.feishu.cn/open-apis/bot/v2/hook/a87e07bd-3d88-4e96-b21c-39411423f5b6',
                      headers=headers, params=params)

    def select_mysql(self, sql: str) -> list:
        """
        查询MySQL数据库，需要传入合法的SQL语句
        :param sql: 传入的SQL语句
        :return result_list: 查询的结果，返回类型是列表
        """
        from Spiders.settings import REDIS_CONN, POLL_DB
        REDIS_CONN.close()
        if not isinstance(sql, str):
            raise "sql 必须是字符串"
        conn = POLL_DB.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql)
            # conn.commit()
            conn.close()
        except Exception as e:
            conn.close()
            raise e
        else:
            result_list = cursor.fetchall()
            return result_list  # 返回查询结果

    def insert_mysql(self, sql: str, v_list: list = None):
        """
        插入数据到MySQL，需要传入合法的SQL语句
        :param sql: 传入的SQL语句
        :param v_list:
        :return:
        """
        from Spiders.settings import REDIS_CONN, POLL_DB
        REDIS_CONN.close()
        if not isinstance(sql, str):
            raise "sql 必须是字符串"
        conn = POLL_DB.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql, v_list)
            conn.commit()
            conn.close()
        except Exception as e:
            conn.close()
            raise e

    def update_mysql(self, sql: str):
        """
        更新数据到MySQL，需要传入合法的SQL语句
        :param sql: 传入的SQL语句
        :return:
        """
        from Spiders.settings import REDIS_CONN, POLL_DB
        REDIS_CONN.close()
        if not isinstance(sql, str):
            raise "sql 必须是字符串"
        conn = POLL_DB.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql)
            conn.commit()
            conn.close()
        except Exception as e:
            conn.close()
            raise e

    def del_mysql(self, sql: str):
        """
        删除MySQL里面的数据，需要传入合法的SQL语句
        :param sql: 传入的SQL语句
        :return:
        """
        from Spiders.settings import REDIS_CONN, POLL_DB
        REDIS_CONN.close()
        if not isinstance(sql, str):
            raise "sql 必须是字符串"
        conn = POLL_DB.connection()
        cursor = conn.cursor()
        try:
            cursor.execute(sql)
            conn.commit()
            conn.close()
        except Exception as e:
            conn.close()
            raise e

    def insert_into_phoenix(self, phoenixdb_conn: str, commit: bool, cache_list: str, commit_num: int, table_name: str):
        """
        数据插入phoenixdb
        :param table_name:
        :param commit_num:
        :param cache_list:
        :param phoenixdb_conn:
        :param commit:
        :return:
        """
        LOCK.acquire()
        if not isinstance(commit_num, int):
            raise "commit_num must be int"
        if commit_num < 0:
            raise "commit_num must > 0"
        if commit_num == 0:
            commit = True
        with phoenixdb.connect(phoenixdb_conn, autocommit=commit) as conn:
            with conn.cursor() as cursor:
                while True:
                    if commit_num == 0:
                        break
                    item = self.get_seed(seed_list=cache_list)
                    if not item:
                        break
                    sql = f"UPSERT INTO {table_name}({','.join(list(item.keys()))}) VALUES({','.join(list(item.values()))})"
                    cursor.execute(sql)
                    commit_num -= 1
            conn.commit()
        LOCK.release()

    def select_of_phoenix(self, sql: str, phoenixdb_conn: str):
        """
        查询phoenix的数据
        :param sql:
        :param phoenixdb_conn:
        :return:
        """
        with phoenixdb.connect(phoenixdb_conn, autocommit=True) as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql)
                result_list = cursor.fetchall()
        return result_list

    def set_seed(self, seed_list: str, seed: dict):
        """
        向种子队列压入种子
        :param seed_list:
        :param seed:
        :return:
        """
        from Spiders.settings import REDIS_CONN, POLL_DB
        POLL_DB.close()
        seed.update({
            "err_num": 0
        })
        REDIS_CONN.sadd(seed_list, json.dumps(seed, ensure_ascii=False))
        REDIS_CONN.close()

    def get_seed(self, seed_list: str):
        """
        获取种子队列的种子
        :param seed_list:
        :return:
        """
        from Spiders.settings import REDIS_CONN, POLL_DB
        POLL_DB.close()
        if REDIS_CONN.scard(seed_list) == 0:
            return None
        try:
            seed = json.loads(REDIS_CONN.spop(seed_list))
        except:
            return None
        if not seed:
            return None
        REDIS_CONN.close()
        return seed

    def get_scard(self, seed_list: str):
        """
        获取队列长度
        :param seed_list:
        :return:
        """
        from Spiders.settings import REDIS_CONN, POLL_DB
        POLL_DB.close()
        REDIS_CONN.close()
        return REDIS_CONN.scard(seed_list)

    def scheduler_err_seed(self, seed_list: str, error_retry=True, err_request_num=1):
        from Spiders.settings import REDIS_CONN, POLL_DB
        POLL_DB.close()
        if not isinstance(error_retry, bool):
            raise "是否对错误种子重新调度的参数类型必须是bool"
        if (REDIS_CONN.scard(seed_list) == 0) and error_retry:
            # 如果种子队列为空，则把请求失败的种子重新爬一次
            if not isinstance(err_request_num, int) and err_request_num < 1:
                raise "错误种子调度次数必须是大于等于1的整数"
            err_seed_list = "err_" + seed_list
            while True:
                err_seed = REDIS_CONN.spop(err_seed_list)
                if not err_seed:
                    break
                err_seed = json.loads(err_seed)
                err_num = err_seed['err_num']
                if err_num <= err_request_num:
                    # err_seed = json.loads(err_seed)
                    err_seed['err_num'] = err_num + 1
                    REDIS_CONN.sadd(seed_list, json.dumps(err_seed, ensure_ascii=False))
                else:
                    REDIS_CONN.sadd("final:" + err_seed_list, json.dumps(err_seed, ensure_ascii=False))
                REDIS_CONN.close()
    def get_ip_remaining_quantity(self):
        """
        获取IP剩余IP
        :return:
        """
        url = "https://proxy.qg.net/info/quota?Pool=1&Key=STHB9U24"
        ip_headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Connection": "close"
        }
        response = requests.get(url=url, headers=ip_headers, timeout=10)
        remaining_quantity = response.json()['Available']
        return remaining_quantity

    def set_ip(self, ip_list_name: str, ip_key: str, is_internal=True):
        """
        将IP压入IP队列, 采用哈希表缓存
        :param is_internal: 判断IP地区，默认是国内
        :param ip_list_name: ip队列
        :param ip_key: IP的键
        :return:
        """
        from Spiders.settings import REDIS_CONN, POLL_DB
        POLL_DB.close()

        def send_error(proxy_ip, error_msg):
            for extract_stack in traceback.extract_stack():
                program_name = extract_stack.filename
                if 'threading.py' in program_name:
                    continue
                else:
                    self.send_to_fs(
                        {"Program": program_name, "Proxy_IP": proxy_ip, "Error:": error_msg})
                    return

        if not isinstance(is_internal, bool):
            raise "必须是布尔类型"
        if is_internal:
            # 国内代理
            ip_url = "https://share.proxy.qg.net/get?key=89BCSYPQ&num=1&area=&isp=&format=json&seq=&distinct=false&pool=1"
            ip_headers = {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Connection": "close"
            }
            while True:
                try:
                    response = requests.get(url=ip_url, headers=ip_headers, timeout=10)
                    json_data = response.json()
                    break
                    # json_data['']
                except Exception as e:
                    logger.info("IP请求失败\n")
                    pass
            code = json_data.get('code')
            if not code:
                return

            if code == 'BALANCE_INSUFFICIENT':
                send_error(proxy_ip="国内代理", error_msg="代理IP余额不足")
                sys.exit(0)

            ip_list = json_data['data']
            if len(ip_list) == 0:
                return
            if code == "SUCCESS":
                host_and_time = {}
                host = ip_list[0]['server']  # 代理IP和端口号
                host_and_time['host'] = "89BCSYPQ:5074FD629809@" + host
                host_and_time['time'] = time.time()
                # print(host_and_time)
                REDIS_CONN.hset(ip_list_name, ip_key, json.dumps(host_and_time, ensure_ascii=False))  # 将代理IP压入IP_LIST
                REDIS_CONN.close()
            else:
                # raise "IP获取失败,请登录青果IP网站查看原因"
                send_error(proxy_ip="国内代理", error_msg=json_data)
                sys.exit(0)
        else:
            # 海外代理
            ip_url = "https://overseas.proxy.qg.net/get?key=A620LQXY&num=1&area=&isp=&format=json&seq="
            ip_headers = {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Connection": "close"
            }
            while True:
                try:
                    response = requests.get(url=ip_url, headers=ip_headers, timeout=10)
                    json_data = response.json()
                    break
                except Exception as e:
                    pass
            if json_data.get('code', '') == 'SUCCESS':
                host_and_time = {}
                try:
                    host = json_data['data'][0]['server']
                except:
                    return
                host_and_time['host'] = "A620LQXY:DFA3FE8EF467@" + host
                host_and_time['time'] = time.time()
                REDIS_CONN.hset(ip_list_name, ip_key, json.dumps(host_and_time, ensure_ascii=False))  # 将代理IP压入IP_LIST
                REDIS_CONN.close()
            else:
                if json_data.get('code', '') == 'FAILED_OPERATION':
                    logger.warning('海外代理IP提取失败')
                    return
                else:
                    send_error(proxy_ip="海外代理", error_msg=json_data)
                    sys.exit(0)

    def get_ip(self, ip_list_name: str, ip_key: str, is_internal=True):
        """
        按照键值获取IP
        :param is_internal: 代理地区选择，默认是国内
        :param ip_list_name: ip队列
        :param ip_key: IP的键
        :return host_and_time: ip和获取IP的时间
        """
        from Spiders.settings import REDIS_CONN, POLL_DB
        POLL_DB.close()
        if not isinstance(is_internal, bool):
            raise "必须是布尔类型"
        while True:
            host_and_time = REDIS_CONN.hget(ip_list_name, ip_key)  # 读取IP
            if not host_and_time:
                # 如果IP队列为空，则设置IP到IP队列
                self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key, is_internal=is_internal)
            else:
                break
        host_and_time = json.loads(host_and_time)
        REDIS_CONN.close()
        return host_and_time  # 返回IP及获取IP的时间

    def set_hash(self, hash_name: str, hash_key: str, hash_values):
        """
        设置hash表
        :param hash_name:
        :param hash_key:
        :param hash_values:
        :return:
        """
        from Spiders.settings import REDIS_CONN
        REDIS_CONN.hset(hash_name, hash_key, hash_values)
        REDIS_CONN.close()

    def get_hash(self, hash_name: str, hash_key: str):
        """
        获取hash表的值
        :param hash_name:
        :param hash_key:
        :return:
        """
        from Spiders.settings import REDIS_CONN
        hash_values = REDIS_CONN.hget(hash_name, hash_key)
        if not hash_values:
            return None
        return hash_values.decode()
