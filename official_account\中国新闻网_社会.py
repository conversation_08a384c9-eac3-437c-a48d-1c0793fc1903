import requests
import xml.etree.ElementTree as ET
from datetime import datetime
from loguru import logger
import re

headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "cross-site",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}

def convert_time_format(time_str):
    """转换时间格式
    Args:
        time_str: 原始时间字符串，格式如：'Fri, 9 May 2025 13:26:40 +0800'
    Returns:
        转换后的时间字符串，格式：YYYY-MM月-DD日
    """
    try:
        # 根据中国新闻网的时间格式进行解析
        dt = datetime.strptime(time_str, '%a, %d %b %Y %H:%M:%S %z')
        return dt.strftime('%Y-%m月-%d日')
    except Exception as e:
        logger.error(f"时间转换失败: {str(e)}")
        return time_str

def parse_news_data(xml_content):
    """解析新闻数据
    Args:
        xml_content: XML格式的内容
    Returns:
        解析后的新闻列表
    """
    try:
        news_list = []
        root = ET.fromstring(xml_content)
        
        # 遍历所有新闻条目
        for item in root.findall('.//item'):
            # 提取标题
            title = item.find('title').text if item.find('title') is not None else ''
            
            # 提取描述（需要处理HTML标签和空白字符）
            description = item.find('description').text if item.find('description') is not None else ''
            # 移除HTML标签
            description = re.sub(r'<[^>]+>', '', description)
            # 移除多余的空白字符和换行符
            description = ' '.join(description.split())
            # 移除开头的空白字符
            description = description.lstrip()
            
            # 提取发布时间并转换格式
            pub_date = item.find('pubDate').text if item.find('pubDate') is not None else ''
            formatted_date = convert_time_format(pub_date.strip()) if pub_date else ''
            
            # 提取链接
            link = item.find('link').text if item.find('link') is not None else ''
            
            # 构建新闻字典
            news = {
                'title': title.strip(),
                'description': description,
                'pub_date': formatted_date,
                'link': link.strip()
            }
            
            # 使用logger输出每条新闻的详细信息
            logger.info(f"\n{'='*50}\n新闻详情:")
            logger.info(f"标题: {news['title']}")
            logger.info(f"发布时间: {news['pub_date']}")
            logger.info(f"链接: {news['link']}")
            logger.info(f"描述: {news['description'][:200]}...")  # 只显示描述的前200个字符
            logger.info(f"{'='*50}")
            
            news_list.append(news)
            
        logger.info(f"成功解析 {len(news_list)} 条新闻")
        return news_list
        
    except ET.ParseError as e:
        logger.error(f"XML解析错误: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"数据解析过程出错: {str(e)}")
        return None

def main():
    url = "https://www.chinanews.com.cn/rss/society.xml"
    try:
        # 发送请求获取数据
        response = requests.get(url, headers=headers)
        response.encoding = 'utf-8'
        
        # 解析数据
        news_list = parse_news_data(response.text)
        
        if not news_list:
            logger.error("没有解析到任何新闻数据")
            
    except requests.RequestException as e:
        logger.error(f"请求失败: {str(e)}")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()