'''
从数据库中获取钱包logo的url并下载保存到本地，使用name字段作为文件名前缀
'''

import requests
import pymysql
from pymysql.cursors import DictCursor
from loguru import logger
import os
import time
import random
import re


class WalletLogoDownloader:
    def __init__(self):
        self.mysql_config = {
            'host': '***************',
            'port': 3306,
            'user': 'spider',
            'password': 'ha13579.',
            'db': 'spider',
            'charset': 'utf8',
            'cursorclass': DictCursor
        }

        self.save_dir = '/data/spider_icons/TOKEN_ICON/token_basic_info/wallet_logo'
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
            logger.info(f"创建目录: {self.save_dir}")

        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        
        self.proxies = {
            'http': 'socks5://**************:30889',
            'https': 'socks5://**************:30889'
        }
        
        self.conn = pymysql.connect(**self.mysql_config)
        self.request_count = 0

    def clean_filename(self, filename):
        filename = re.sub(r'[\\/*?:"<>|]', '', filename)
        filename = re.sub(r'\（.*?\）', '', filename)
        filename = re.sub(r'\(.*?\)', '', filename)
        filename = ' '.join(filename.split())
        return filename.strip()

    def get_all_logo_urls(self):
        try:
            with self.conn.cursor() as cursor:
                sql = """
                    SELECT wallet_name, logo_url 
                    FROM fxh_wallet_info 
                    WHERE logo_url IS NOT NULL 
                    AND logo_url != ''
                """
                cursor.execute(sql)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取logo URL失败: {e}")
            return []

    def update_logo_route(self, wallet_name, logo_route):
        try:
            with self.conn.cursor() as cursor:
                sql = """
                    UPDATE fxh_wallet_info 
                    SET logo_route = %s 
                    WHERE wallet_name = %s
                """
                cursor.execute(sql, (logo_route, wallet_name))
                self.conn.commit()
                logger.success(f"成功更新 {wallet_name} 的logo_route: {logo_route}")
        except Exception as e:
            logger.error(f"更新logo_route失败 {wallet_name}: {e}")
            self.conn.rollback()

    def download_logo(self, wallet_info):
        name = wallet_info['wallet_name']
        url = wallet_info['logo_url']

        try:
            clean_name = self.clean_filename(name)
            file_ext = url.split('.')[-1].split('?')[0]
            if file_ext.lower() not in ['png', 'jpg', 'jpeg', 'webp']:
                file_ext = 'png'

            filename = f"{clean_name}.{file_ext}"
            save_path = os.path.join(self.save_dir, filename)
            
            full_path = f"/icon/token_basic_info/wallet_logo/{filename}"

            if os.path.exists(save_path):
                logger.info(f"文件已存在，跳过下载: {filename}")
                self.update_logo_route(name, full_path)
                return True

            response = requests.get(
                url, 
                headers=self.headers, 
                proxies=self.proxies,
                timeout=10
            )
            
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                logger.success(f"成功下载: {filename}")

                self.update_logo_route(name, full_path)
                logger.success(f"成功更新数据库路径: {full_path}")

                self.request_count += 1

                if self.request_count >= 8:
                    sleep_time = random.uniform(5, 7)
                    logger.info(f"已完成8次请求，休息 {sleep_time:.2f} 秒")
                    time.sleep(sleep_time)
                    self.request_count = 0
                else:
                    time.sleep(random.uniform(1, 2))

                return True
            else:
                logger.error(f"下载失败 {name}, 状态码: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"下载出错 {name}: {str(e)}")
            return False

    def run(self):
        try:
            wallet_infos = self.get_all_logo_urls()
            if not wallet_infos:
                logger.warning("没有找到需要下载的logo")
                return

            logger.info(f"总共找到 {len(wallet_infos)} 个logo需要下载")

            for wallet_info in wallet_infos:
                self.download_logo(wallet_info)

            logger.success("所有logo下载完成")

        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
        finally:
            self.conn.close()


if __name__ == '__main__':
    downloader = WalletLogoDownloader()
    downloader.run()