import requests
import pymysql
import json
import time
import signal
import sys
from loguru import logger
from datetime import datetime
from utils import DB_BASE

proxies = {
    "http": "***************************************************",
    "https": "***************************************************"
}

headers = {
    "Host": "www.ynhf1jp.com",
    "Accept": "*/*",
    "x-utc": "+08:00",
    "Referer": "https://www.ynhf1jp.com/priapi/v1/dx/market/v2/advanced/ranking/content",
    "fingerprint-id": "57E28747-EEAD-4D63-A80D-D19D16842ED4",
    "User-Agent": "OKEx/6.119.0 (iPhone;U;iOS 18.3;zh-CN/zh-CN) locale=zh-CN",
    "x-cdn": "https://static.coinall.ltd",
    "x-site-info": "9JCTBJ0TMd0XYt0TiojIlR2bjJCL0EjOikHdpRvblJye",
    # "OK-VERIFY-SIGN": "hJll5Jiqr27jPgO0TRMcW1b3d5tgM58kPjCAj/RpWvs=",
    "x-simulated-trading": "0",
    "BuildVersion": "20250511006001",
    "app_web_mode": "web3",
    "Subdomain-Strategy": "2",
    # "risk-params": "fingerprint-id=57E28747-EEAD-4D63-A80D-D19D16842ED4&session-id=57E28747-EEAD-4D63-A80D-D19D16842ED4_txn_start_1751526574073&fp-status=3",
    "OK-VERIFY-TOKEN": "3885a5cb9d59e049ad70a65a32883125",
    "BundleId": "com.okex.OKExAppstoreFull",
    "platform": "iOS",
    "real-app-version": "6.119.0",
    "Accept-Language": "zh-CN",
    "x-id": "f41b708ce14bb663c7ffac25db4533b7",
    "devid": "57E28747-EEAD-4D63-A80D-D19D16842ED4",
    "lua-version": "6.123.1"
}

url = "https://***********/priapi/v1/dx/market/v2/advanced/ranking/content"
params = {
    # "accountId": "BC64A46A-A06D-4D6E-B729-D42D1FDD1F7C",
    "chainIds": "all",
    "changeMax": "",
    "changeMin": "",
    "changePeriod": "2",
    "desc": "true",
    "fdvMax": "",
    "fdvMin": "",
    "holdersMax": "",
    "holdersMin": "",
    "liquidityMax": "",
    "liquidityMin": "5000",
    "marketCapMax": "",
    "marketCapMin": "",
    "page": "1",
    "pageSize": "200",
    "periodType": "2",
    "rankBy": "9",
    "riskFilter": "true",
    "stableTokenFilter": "true",
    "tags": "0",
    "tokenAgeMax": "",
    "tokenAgeMin": "",
    "tokenAgeType": "2",
    "tradeNumMax": "",
    "tradeNumMin": "",
    "tradeNumPeriod": "2",
    "txsMax": "",
    "txsMin": "",
    "txsPeriod": "2",
    "uniqueTraderMax": "",
    "uniqueTraderMin": "10",
    "uniqueTraderPeriod": "2",
    "volumeMax": "",
    "volumeMin": "10000",
    "volumePeriod": "2",
    # "walletAddress": "******************************************"
}

# 数据库配置
db_config = {
    'host': '**********',
    'port': 6000,
    'user': 'root',
    'password': 'iAn7*+154-j9r3_dcm',
    'database': 'solana'
}

db_base = DB_BASE()

def get_database_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**db_config)
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise


def extract_data_from_response(response_data):
    """从响应数据中提取所需字段"""
    extracted_records = []

    try:
        # 解析JSON数据
        if isinstance(response_data, str):
            data = json.loads(response_data)
        else:
            data = response_data

        # 检查数据结构
        if data.get('code') != 0:
            logger.error(f"API返回错误: {data}")
            return []

        # 获取代币信息列表
        market_list = data.get('data', {}).get('marketListsTokenInfos', [])

        if not market_list:
            logger.warning("未找到代币信息列表")
            return []

        logger.info(f"获取到 {len(market_list)} 条代币数据")

        # 提取每条记录的数据
        for token_info in market_list:
            try:
                # 提取基础字段
                record = {
                    'address': token_info.get('tokenContractAddress', ''),
                    'tokenSymbol': token_info.get('tokenSymbol', ''),
                    'chainId': token_info.get('chainId', ''),
                    'price(usd)': token_info.get('price', ''),
                    'mcap': token_info.get('marketCap', ''),
                    'liquid': token_info.get('liquidity', ''),
                    'price_chage': token_info.get('change', ''),
                    'type': ''
                }

                # 提取tagType (从tagList中获取第一个有效的tag)
                tag_list = token_info.get('tagList', [])
                if tag_list and len(tag_list) > 0:
                    # 取第一个标签组的第一个标签作为type
                    first_tag_group = tag_list[0]
                    if isinstance(first_tag_group, list) and len(first_tag_group) > 0:
                        record['type'] = first_tag_group[0]

                # 验证必要字段
                if not record['address'] or not record['tokenSymbol']:
                    # db_base.send_to_fs("okx本次请求缺少关键字段:address")
                    logger.warning(f"跳过无效记录，缺少关键字段: {record}")

                extracted_records.append(record)

            except Exception as e:
                logger.error(f"提取单条记录失败: {e}, 原始数据: {token_info}")
                continue

        # 去重处理：基于address字段去重，保留最后一条
        unique_records = {}
        for record in extracted_records:
            unique_records[record['address']] = record

        final_records = list(unique_records.values())

        logger.info(f"成功提取 {len(extracted_records)} 条记录，去重后 {len(final_records)} 条有效记录")
        return final_records

    except Exception as e:
        logger.error(f"数据提取失败: {e}")
        return []


def save_to_database(records):
    """将提取的数据保存到数据库（先清空旧数据）"""
    if not records:
        logger.warning("没有数据需要保存")
        return False

    connection = get_database_connection()
    try:
        with connection.cursor() as cursor:
            # 先清空表中的旧数据
            logger.info("正在清空旧数据...")
            cursor.execute("DELETE FROM coin_ecology_list")
            logger.info("旧数据已清空")

            # 构建批量插入SQL
            sql = """
            INSERT INTO coin_ecology_list 
            (address, tokenSymbol, chainId, type, `price(usd)`, mcap, liquid, price_chage, created_at) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            batch_data = []
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            for record in records:
                batch_data.append((
                    record['address'],
                    record['tokenSymbol'],
                    record['chainId'],
                    record['type'],
                    record['price(usd)'],
                    record['mcap'],
                    record['liquid'],
                    record['price_chage'],
                    current_time
                ))

            # 执行批量插入
            cursor.executemany(sql, batch_data)
            connection.commit()

            logger.info(f"成功保存 {len(records)} 条记录到数据库")
            return True

    except Exception as e:
        logger.error(f"数据库操作失败: {e}")
        connection.rollback()
        return False
    finally:
        connection.close()


def main():
    """主函数：发送请求、提取数据、保存到数据库"""
    try:
        logger.info("开始发送API请求...")
        # 发送请求
        response = requests.get(url, headers=headers, params=params, proxies=proxies, verify=False)
        logger.info(f"请求状态码: {response.status_code}")
        # print(response.text)

        if response.status_code != 200:
            db_base.send_to_fs(f"okx请求失败，状态码: {response.status_code}")
            logger.error(f"请求失败，状态码: {response.status_code}")
            return False

        # 提取数据
        logger.info("开始提取数据...")
        extracted_records = extract_data_from_response(response.text)

        if not extracted_records:
            logger.warning("未提取到有效数据")
            return False

        # 显示提取的数据样例
        logger.info("数据提取完成，样例数据:")
        for i, record in enumerate(extracted_records[:3]):  # 显示前3条
            logger.info(f"记录 {i + 1}: {record}")

        # 保存到数据库
        logger.info("开始保存数据到数据库...")
        success = save_to_database(extracted_records)

        if success:
            logger.info("数据处理完成！")
            return True
        else:
            logger.error("数据保存失败！")
            return False

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return False


def signal_handler(signum, frame):
    """处理中断信号"""
    logger.info(f"接收到信号 {signum}，程序正在安全退出...")
    sys.exit(0)


def run_scheduler():
    """定时任务调度器：每小时运行一次"""
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 程序终止

    logger.info("=== OKX代币数据定时采集程序启动 ===")
    logger.info("程序将每小时运行一次，按 Ctrl+C 可安全退出")

    run_count = 0

    while True:
        try:
            run_count += 1
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            logger.info(f"=== 第 {run_count} 次执行开始 ({current_time}) ===")

            # 执行主要任务
            success = main()

            if success:
                logger.info(f"第 {run_count} 次执行完成，数据采集成功")
            else:
                logger.error(f"第 {run_count} 次执行失败，数据采集失败")

            # 计算下次运行时间
            next_run_time = datetime.now().replace(minute=0, second=0, microsecond=0)
            next_run_time = next_run_time.replace(hour=next_run_time.hour + 1)
            # logger.info(f"下次运行时间: {next_run_time.strftime('%Y-%m-%d %H:%M:%S')}")

            # 等待一小时（3600秒）
            logger.info("等待中...（5分钟后执行下次采集）")
            time.sleep(300)  # 3600秒 = 1小时

        except KeyboardInterrupt:
            logger.info("接收到键盘中断信号，程序退出")
            break
        except Exception as e:
            logger.error(f"定时任务执行过程中发生错误: {e}")
            logger.info("等待1小时后重试...")
            time.sleep(3600)  # 即使出错也等待1小时再重试

    logger.info("定时采集程序已退出")


if __name__ == '__main__':
    # 如果需要只运行一次，可以直接调用 main()
    # main()

    # 定时运行模式
    run_scheduler()