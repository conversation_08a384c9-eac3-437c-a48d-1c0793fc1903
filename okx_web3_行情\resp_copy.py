import requests
from rsa.cli import verify

proxies = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

headers = {
    "Host": "www.ynhf1jp.com",
    "Accept": "*/*",
    "x-utc": "+08:00",
    "Referer": "https://www.ynhf1jp.com/priapi/v1/dx/market/v2/advanced/ranking/content",
    # "fingerprint-id": "57E28747-EEAD-4D63-A80D-D19D16842ED4",
    "User-Agent": "OKEx/6.119.0 (iPhone;U;iOS 18.3;zh-CN/zh-CN) locale=zh-CN",
    "x-cdn": "https://static.coinall.ltd",
    # "x-site-info": "9JCTBJ0TMd0XYt0TiojIlR2bjJCL0EjOikHdpRnblJye",
    # "OK-VERIFY-SIGN": "hJll5Jiqr27jPgO0TRMcW1b3d5tgM58kPjCAj/RpWvs=",
    "x-simulated-trading": "0",
    # "BuildVersion": "20250511006001",
    "app_web_mode": "web3",
    "Subdomain-Strategy": "2",
    # "risk-params": "fingerprint-id=57E28747-EEAD-4D63-A80D-D19D16842ED4&session-id=57E28747-EEAD-4D63-A80D-D19D16842ED4_txn_start_1751526574073&fp-status=3",
    # "OK-VERIFY-TOKEN": "3885a5cb9d59e049ad70a65a32883125",
    "BundleId": "com.okex.OKExAppstoreFull",
    "platform": "iOS",
    "real-app-version": "6.119.0",
    "Accept-Language": "zh-CN",
    # "x-id": "f41b708ce14bb663c7ffac25db4533b7",
    # "devid": "57E28747-EEAD-4D63-A80D-D19D16842ED4",
    # "lua-version": "6.123.1"
}
url = "https://***********/priapi/v1/dx/market/v2/advanced/ranking/content"
params = {
    # "accountId": "BC64A46A-A06D-4D6E-B729-D42D1FDD1F7C",
    "chainIds": "all",
    "changeMax": "",
    "changeMin": "",
    "changePeriod": "2",
    "desc": "true",
    "fdvMax": "",
    "fdvMin": "",
    "holdersMax": "",
    "holdersMin": "",
    "liquidityMax": "",
    "liquidityMin": "5000",
    "marketCapMax": "",
    "marketCapMin": "",
    "page": "1",
    "pageSize": "200",
    "periodType": "2",
    "rankBy": "9",
    "riskFilter": "true",
    "stableTokenFilter": "true",
    "tags": "0",
    "tokenAgeMax": "",
    "tokenAgeMin": "",
    "tokenAgeType": "2",
    "tradeNumMax": "",
    "tradeNumMin": "",
    "tradeNumPeriod": "2",
    "txsMax": "",
    "txsMin": "",
    "txsPeriod": "2",
    "uniqueTraderMax": "",
    "uniqueTraderMin": "10",
    "uniqueTraderPeriod": "2",
    "volumeMax": "",
    "volumeMin": "10000",
    "volumePeriod": "2",
    # "walletAddress": "******************************************"
}
response = requests.get(url, headers=headers, params=params, proxies=proxies, verify=False)

print(response.text)
print(response)