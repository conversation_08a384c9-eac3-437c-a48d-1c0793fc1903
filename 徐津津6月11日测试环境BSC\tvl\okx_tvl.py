#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取OKX上Solana代币的TVL数据
使用OKX API获取代币的总流动性数据作为TVL
支持超时重试、IP切换和多线程并发
"""

import requests
import json
import sqlite3
import time
import sys
import traceback
import threading
from queue import Queue
from datetime import datetime

# 设置控制台输出编码
if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库路径
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "test_data_sol"  # 表名

# 代理配置
PRIMARY_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}
BACKUP_PROXY = {
    "http": "socks5://**************:30889",
    "https": "socks5://**************:30889"
}

# 请求配置
REQUEST_TIMEOUT = 30  # 秒
MAX_RETRIES = 5
RETRY_DELAY = 2  # 秒
REQUEST_INTERVAL = 1  # 秒

# 线程配置
THREAD_COUNT = 1  # 线程数

# 创建线程锁，用于同步输出和数据库操作
print_lock = threading.Lock()
db_lock = threading.Lock()

# 创建统计计数器
success_counter = 0
total_counter = 0


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with print_lock:
        print(*args, **kwargs)


# OKX API请求头和cookies
def get_headers(token_address):
    return {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "app-type": "web",
    "devid": "ed72f275-eb0a-418a-be47-91f58cf52649",
        "ok-timestamp": str(int(time.time() * 1000)),
    "ok-verify-sign": "XXSV6AD5NGJvfC8yez/Krd/al+dy2lvKLVbGaK631tA=",
    "ok-verify-token": "571bda7e-6293-47b7-b0a0-fc95dbba4f72",
    "priority": "u=1, i",
        "referer": f"https://web3.okx.com/zh-hans/token/solana/{token_address}",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-cdn": "https://web3.okx.com",
    "x-fptoken": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "x-fptoken-signature": "{P1363}zW65av6lMPqFCS1DCJ4kQstrNxihzjDg++7GM1KRO4TWvvLdSaiewIaJo71C/tDNRSvsYYxK0BeSYvSzvGzxKw==",
    "x-id-group": "2121185015799960003-c-8",
    "x-locale": "zh_CN",
        "x-request-timestamp": str(int(time.time() * 1000) - 100),
    "x-simulated-trading": "undefined",
    "x-site-info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIySIJiOi42bpdWZyJye",
    "x-utc": "8",
    "x-zkdex-env": "0"
}


cookies = {
    "_gcl_gs": "2.1.k1$i1747121212$u3801134",
    "intercom-id-ny9cf50h": "59b1a03b-f342-4a22-9469-ca04541cfc98",
    "intercom-device-id-ny9cf50h": "e3eb8110-ee9e-4eec-8777-a8840b95118e",
    "_ym_uid": "1747121226975031991",
    "_ym_d": "1747121226",
    "devId": "ed72f275-eb0a-418a-be47-91f58cf52649",
    "locale": "zh_CN",
    "ok_prefer_udColor": "0",
    "ok_prefer_udTimeZone": "0",
    "first_ref": "https%3A%2F%2Fwww.okx.com%2F",
    "ok_login_type": "OKX_GLOBAL",
    "fingerprint_id": "286c47e1-4945-45bb-88e9-98ddc1deff0f",
    "_gid": "GA1.2.1941574504.1748224542",
    "amp_21c676": "sxXY9NatdSUAkrG8DFaDSs.ZDVtenN3VUNMVkowT3d6K1hZU0RnQT09..1is8n1mkg.1is8pnra8.i.f.11",
    "intercom-session-ny9cf50h": "WURMYmNvY3RXTFhLVmd4eUpjM3lTRVNacElwdmpWQTlTbEdvSjd5T0pGdzE0aUVycFI4N2YrY082WmlpMVMyZElBVVc3L3Nndmd0bG9MWDlyYWs1WWNHaXNRd3Bya3VrYko5OG4vcDJTWjQ9LS1qQmlyYkVkb1JENDJQTS8wMnN3SjNBPT0=--df77abdb800d3811db2579189fb0bd5a0d1c13f7",
    "connected": "1",
    "connectedWallet": "1",
    "_ym_isad": "2",
    "OptanonAlertBoxClosed": "2025-05-29T05:32:26.912Z",
    "ok-exp-time": "1748497227575",
    "tmx_session_id": "q6dk4519osm_1748497236053",
    "fp_s": "0",
    "OptanonConsent": "isGpcEnabled=0&datestamp=Thu+May+29+2025+13%3A59%3A17+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202405.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&landingPath=NotLandingPage&groups=C0004%3A0%2CC0002%3A0%2CC0003%3A0%2CC0001%3A1&geolocation=US%3B&AwaitingReconsent=false",
    "ok_site_info": "==QfzojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIySIJiOi42bpdWZyJye",
    "__cf_bm": "wDt2o1MPM6SUyIwcWwBcNKqiTFgFX65r31e4vQIlQq0-1748501270-*******-.W94uVnYR3N7Fq1OerSDtzG72E2OVN6O4BiXiZQbmYcizR3p7dwpwHh83KMFwr0yHSQ.oXy.MZZjeNLujVmxxxYFe8RY2HMHlnXOyg.1smI",
    "okg.currentMedia": "xl",
    "ok_prefer_currency": "0%7C0%7Cfalse%7CUSD%7C2%7C%24%7C1%7C1%7C%E7%BE%8E%E5%85%83",
    "_gat_UA-35324627-3": "1",
    "traceId": "2121185015799960003",
    "ok_prefer_exp": "1",
    "_ga": "GA1.1.2028963803.1747121216",
    "_ga_G0EKWWQGTZ": "GS2.1.s1748501419$o41$g1$t1748501580$j26$l0$h0",
    "ok-ses-id": "Ot3r8sCJruak1aDq2WdL0uvdABXHc3V/y23K68zaV4TLENyozv9yyvFoocWMHxMuXEKTd6XS5QB+faq1BgwotOJ03lvvuCaqxNOHcpkmKvLuQRiBFmKJh9gOJq80WgZQ",
    "_monitor_extras": "{\"deviceId\":\"OWTCklx6Hw5J0KFQPhe8Aq\",\"eventId\":1116,\"sequenceNumber\":1116}"
}


def get_token_addresses_from_db():
    """从数据库中获取Token地址列表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # 获取Token地址
        cursor.execute(f'SELECT rowid, "code_address" FROM {DB_TABLE}')
        rows = cursor.fetchall()

        # 提取行ID和地址并过滤空值
        token_data = []
        for row_id, address in rows:
            if address:  # 确保Token Address不为空
                token_data.append({
                    "row_id": row_id,
                    "token_address": address
                })

        thread_safe_print(f"从数据库获取到 {len(token_data)} 个Token地址")
        return token_data

    except sqlite3.Error as e:
        thread_safe_print(f"从数据库获取Token地址时出错: {e}")
        return []

    finally:
        conn.close()


def update_tvl_in_db(row_id, token_address, tvl_value):
    """更新数据库中的TVL值"""
    # 使用线程锁确保数据库操作的线程安全
    with db_lock:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            # 更新数据库
            cursor.execute(
                f'''
                UPDATE {DB_TABLE}
                SET "okx_tvl" = ?
                WHERE rowid = ?
                ''',
                (tvl_value, row_id)
            )

            if cursor.rowcount > 0:
                thread_safe_print(f"成功更新 {token_address} (rowid: {row_id}) 的TVL值: {tvl_value}")
                conn.commit()
                return True
            else:
                thread_safe_print(f"未找到记录 (rowid: {row_id})")
                conn.rollback()
                return False

        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库时出错: {e}")
            conn.rollback()
            return False

        finally:
            conn.close()


def get_token_tvl(token_address, use_backup_proxy=False):
    """
    获取代币的TVL数据
    返回：TVL值或None（如果获取失败）
    """
    url = "https://web3.okx.com/priapi/v1/dx/market/v2/token/overview"
    params = {
        "chainId": "501",  # Solana的链ID
        "tokenContractAddress": token_address
    }

    # 选择使用的代理
    current_proxy = BACKUP_PROXY if use_backup_proxy else PRIMARY_PROXY
    proxy_name = "备用代理" if use_backup_proxy else "主要代理"

    # 生成headers（包含当前token_address的referer）
    headers = get_headers(token_address)

    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            thread_safe_print(
                f"请求代币 {token_address} 的TVL信息... (使用{proxy_name}, 尝试 {retry_count + 1}/{MAX_RETRIES})")

            response = requests.get(
                url,
                headers=headers,
                cookies=cookies,
                params=params,
                proxies=current_proxy,
                timeout=REQUEST_TIMEOUT
            )

            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('code') == 0 and 'data' in data:
                        market_info = data['data'].get('marketInfo', {})
                        total_liquidity = market_info.get('totalLiquidity')

                        if total_liquidity:
                            thread_safe_print(f"获取到代币 {token_address} 的TVL: {total_liquidity}")
                            return float(total_liquidity)
                        else:
                            thread_safe_print(f"代币 {token_address} 的TVL数据不存在")
                    else:
                        thread_safe_print(f"API返回错误: {data.get('error_message', '未知错误')}")
                except json.JSONDecodeError:
                    thread_safe_print(f"无法解析JSON响应: {response.text[:200]}...")
            else:
                thread_safe_print(f"请求失败，状态码: {response.status_code}")
                thread_safe_print(f"响应内容: {response.text[:200]}...")

                # 如果是API限制类错误，等待更长时间
                if response.status_code == 429:
                    thread_safe_print(f"API请求频率限制，等待时间延长")
                    time.sleep(RETRY_DELAY * 2)
                else:
                    time.sleep(RETRY_DELAY)

            retry_count += 1

        except requests.exceptions.Timeout:
            thread_safe_print(f"请求超时")
            retry_count += 1
            time.sleep(RETRY_DELAY)

        except requests.exceptions.RequestException as e:
            thread_safe_print(f"请求异常: {e}")
            retry_count += 1
            time.sleep(RETRY_DELAY)

        except Exception as e:
            thread_safe_print(f"获取TVL数据时出错: {e}")
            retry_count += 1
            time.sleep(RETRY_DELAY)

    thread_safe_print(f"已达到最大重试次数 {MAX_RETRIES}，放弃获取此代币的数据")
    return None


def worker(token_queue, thread_id):
    """工作线程函数，处理队列中的token"""
    global success_counter, total_counter

    thread_safe_print(f"线程 {thread_id} 开始运行")

    while not token_queue.empty():
        try:
            # 从队列获取一个token数据
            token_data = token_queue.get()
            row_id = token_data["row_id"]
            token_address = token_data["token_address"]

            # 更新计数器
            with print_lock:
                total_counter += 1
                current_count = total_counter

            thread_safe_print(
                f"\n线程 {thread_id} 处理第 {current_count}/{token_queue.qsize() + current_count} 个代币: {token_address}")

            # 尝试使用主要代理获取TVL
            tvl = get_token_tvl(token_address, use_backup_proxy=False)

            # 如果主要代理失败，尝试使用备用代理
            if tvl is None:
                thread_safe_print(f"使用主要代理获取失败，切换到备用代理...")
                tvl = get_token_tvl(token_address, use_backup_proxy=True)

            # 如果获取到TVL，更新数据库
            if tvl is not None:
                if update_tvl_in_db(row_id, token_address, tvl):
                    with print_lock:
                        success_counter += 1
                        current_success = success_counter

                    thread_safe_print(f"线程 {thread_id} 成功更新代币 {token_address} 的TVL: {tvl}")
                    thread_safe_print(
                        f"当前进度: {current_success}/{current_count} ({current_success / current_count * 100:.2f}%)")
                else:
                    thread_safe_print(f"线程 {thread_id} 更新代币 {token_address} 的TVL到数据库失败")
            else:
                thread_safe_print(f"线程 {thread_id} 无法获取代币 {token_address} 的TVL数据")

            # 标记任务完成
            token_queue.task_done()

            # 请求间隔
            time.sleep(REQUEST_INTERVAL)

        except Exception as e:
            thread_safe_print(f"线程 {thread_id} 处理任务时出错: {e}")
            token_queue.task_done()

    thread_safe_print(f"线程 {thread_id} 已完成所有任务")


def process_tokens_multi_thread(token_data_list):
    """使用多线程处理所有代币"""
    # 创建任务队列
    token_queue = Queue()

    # 将所有token数据添加到队列
    for token_data in token_data_list:
        token_queue.put(token_data)

    total_tokens = token_queue.qsize()
    thread_safe_print(f"总共 {total_tokens} 个Token将被处理，使用 {THREAD_COUNT} 个线程")

    # 创建并启动工作线程
    threads = []
    for i in range(THREAD_COUNT):
        t = threading.Thread(target=worker, args=(token_queue, i + 1))
        t.daemon = True  # 设置为守护线程，主线程结束时会自动结束
        threads.append(t)
        t.start()

    # 等待所有任务完成
    token_queue.join()

    # 等待所有线程结束
    for t in threads:
        t.join()

    return success_counter


def main():
    """主函数"""
    thread_safe_print("=" * 60)
    thread_safe_print("OKX Solana代币TVL数据获取工具 (多线程版)")
    thread_safe_print("=" * 60)
    thread_safe_print("数据库路径:", DB_PATH)
    thread_safe_print("数据库表名:", DB_TABLE)
    thread_safe_print("最大重试次数:", MAX_RETRIES)
    thread_safe_print("线程数:", THREAD_COUNT)
    thread_safe_print("=" * 60)

    # 获取Token地址
    token_data_list = get_token_addresses_from_db()

    if not token_data_list:
        thread_safe_print("未找到任何Token地址，请检查数据库")
        return

    # 多线程处理代币
    success_count = process_tokens_multi_thread(token_data_list)

    # 输出结果
    thread_safe_print("\n更新完成!")
    thread_safe_print(f"总计: {len(token_data_list)} 个Token")
    thread_safe_print(f"成功更新数据库: {success_count} 个")


if __name__ == "__main__":
    thread_safe_print("开始获取OKX上Solana代币的TVL数据...")
    start_time = time.time()

    try:
        main()
    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        import traceback

        thread_safe_print(f"程序运行出错: {e}")
        thread_safe_print(traceback.format_exc())

    end_time = time.time()
    duration = end_time - start_time
    thread_safe_print(f"程序运行耗时: {duration:.2f} 秒")
    thread_safe_print("程序运行结束")