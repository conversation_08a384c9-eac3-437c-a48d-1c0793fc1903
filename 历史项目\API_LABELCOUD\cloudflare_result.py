import time

import requests

def get_task_result(client_key, task_id):
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {'Content-Type': 'application/json'}
    data = {
        "clientKey": client_key,
        "taskId": task_id
    }

    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    for i in range(40):
        if result['errorId'] == 0:
            if result['status'] == 'ready':
                print("Task is ready. Solution:", result['solution'])
                return result
            elif result['status'] == 'processing':
                print(f'retrying {i}')
                time.sleep(3)

                response = requests.post(url, headers=headers, json=data)
                result = response.json()
                continue
            else:
                print("Task is not ready yet. Status:", result['status'])
                return result
        else:
            print("Error in getting task result:", result['errorDescription'])
            return result

    print("Max retries reached. Task is still processing.")
    return None


client_key = '86529fa1d01339f34ff2b5f77f5ff0d1622065ed56034'
task_id = '491b98c8-bce6-11ef-b78c-9669990674f1'


if __name__ == '__main__':
    get_task_result(client_key, task_id)