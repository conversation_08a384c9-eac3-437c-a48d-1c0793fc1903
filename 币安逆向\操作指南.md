# AWS WAF Token生成器 - 详细操作指南

## 🎯 两种使用方式

### 🌐 方式一：网页界面操作（推荐 - 最简单）

#### 第1步：直接打开HTML文件
```
双击打开：币安逆向/test_aws_waf_js.html
```

#### 第2步：等待自动加载
- 页面会自动加载JavaScript代码
- 看到"✅ AWS WAF Token生成器初始化成功！"表示准备就绪

#### 第3步：点击按钮测试
- 点击 **"生成AWS WAF Token"** 开始生成
- 点击 **"测试设备指纹"** 查看设备信息
- 点击 **"性能基准测试"** 进行性能测试

#### 第4步：查看结果
- 在页面的"执行日志"区域查看详细过程
- 在"统计数据"区域查看性能指标

---

### 💻 方式二：浏览器控制台操作

#### 第1步：打开任意网页
```
建议：https://www.binance.com （或任何网站）
```

#### 第2步：打开开发者工具
```
按 F12 或 右键 → 检查 → Console
```

#### 第3步：复制粘贴JS代码
```javascript
// 复制 complete_js_aws_waf_decoder.js 中的所有内容
// 粘贴到控制台并按回车
```

#### 第4步：运行测试命令
```javascript
// 方法1：运行内置测试
await testAWSWAFTokenGeneration();

// 方法2：手动创建生成器
const generator = new AWSWAFTokenGenerator();
const result = await generator.generateToken("https://api.binance.com");
console.log("生成的Token:", result.token);
```

---

## 🔍 具体操作演示

### 网页界面方式演示

1. **文件位置**
   ```
   D:\莫福瑞\python_files\python码云\币安逆向\test_aws_waf_js.html
   ```

2. **打开方式**
   - 方法A：双击HTML文件
   - 方法B：右键 → 打开方式 → 浏览器
   - 方法C：拖拽到浏览器窗口

3. **界面说明**
   ```
   🔐 AWS WAF Token生成器 - JavaScript完整版
   
   ┌─ 🎯 项目概述
   ├─ 🚀 控制面板
   │   ├─ [生成AWS WAF Token]
   │   ├─ [测试设备指纹]  
   │   ├─ [性能基准测试]
   │   └─ [清空结果]
   ├─ 📊 统计数据（生成后显示）
   ├─ 📋 执行日志
   ├─ 💡 使用说明
   └─ ⚠️ 重要提醒
   ```

4. **预期结果**
   ```
   [14:30:25] ✅ AWS WAF Token生成器初始化成功！
   [14:30:27] 🚀 开始生成AWS WAF Token...
   [14:30:27] 📍 目标域名: https://api.binance.com
   [14:30:27] 🎉 Token生成成功！
   [14:30:27] 📊 详细信息:
   [14:30:27]   - 解决方案: 128
   [14:30:27]   - 哈希值: 00a1b2c3d4e5f6...
   [14:30:27]   - 尝试次数: 129
   [14:30:27]   - 算法耗时: 15.42ms
   [14:30:27]   - 总耗时: 18.75ms
   [14:30:27]   - Token长度: 347 字符
   [14:30:27]   - Token预览: 54f88a70-c77d-4b8a-90f4-299048b561e1:BgoAk4w...
   ```

### 控制台方式演示

1. **复制代码**
   ```javascript
   // 完整复制 complete_js_aws_waf_decoder.js 的内容
   // 从第1行到最后一行（617行）
   ```

2. **粘贴执行**
   ```
   1. 打开控制台（F12 → Console）
   2. Ctrl+V 粘贴代码
   3. 按回车执行
   4. 看到初始化信息表示成功
   ```

3. **运行测试**
   ```javascript
   // 运行完整测试
   await testAWSWAFTokenGeneration();
   
   // 或手动操作
   const generator = new AWSWAFTokenGenerator();
   const result = await generator.generateToken();
   ```

---

## 📝 操作注意事项

### ✅ 正确做法
- ✅ 直接打开HTML文件即可（推荐）
- ✅ 或在控制台复制完整JS代码
- ✅ 等待初始化完成再操作
- ✅ 按照界面提示进行操作

### ❌ 错误做法
- ❌ 先复制代码再打开HTML（不需要）
- ❌ 部分复制JS代码（会报错）
- ❌ 在没有加载JS的页面运行（找不到函数）
- ❌ 同时使用两种方式（重复加载）

### 🔧 故障排除

#### 问题1：初始化失败
```
解决：检查浏览器是否支持ES6和async/await
推荐：使用Chrome 70+或Firefox 70+
```

#### 问题2：找不到函数
```
解决：确保完整复制了JS代码
检查：控制台是否有语法错误
```

#### 问题3：Token生成失败
```
解决：检查网络连接
重试：清空结果后重新生成
```

#### 问题4：页面样式异常
```
解决：确保HTML和JS文件在同一目录
检查：浏览器是否支持CSS Grid
```

---

## 🎪 实际使用流程

### 快速测试流程
```
1. 双击 test_aws_waf_js.html
2. 等待页面加载完成
3. 点击"生成AWS WAF Token"
4. 查看生成结果
5. （可选）点击其他测试按钮
```

### 开发集成流程
```
1. 复制 complete_js_aws_waf_decoder.js 到项目
2. 在HTML中引入：<script src="complete_js_aws_waf_decoder.js"></script>
3. 创建生成器：const generator = new AWSWAFTokenGenerator();
4. 生成Token：const result = await generator.generateToken(domain);
5. 使用Token：headers['x-aws-waf-token'] = result.token;
```

---

## 🎉 成功标志

看到以下信息表示操作成功：

### 网页界面成功标志
- ✅ 页面正常显示，无样式错误
- ✅ 控制面板按钮可以点击
- ✅ 显示"AWS WAF Token生成器初始化成功"
- ✅ 生成Token后显示详细统计数据

### 控制台成功标志
- ✅ 无语法错误提示
- ✅ 显示"🚀 AWS WAF Token生成器初始化完成"
- ✅ 函数调用正常返回结果
- ✅ 生成的Token格式正确（UUID:base64:base64）

---

*最后更新: 2024年1月 | 适用版本: JavaScript完整版* 