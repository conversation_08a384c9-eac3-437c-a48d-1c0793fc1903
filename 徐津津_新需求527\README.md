# 代币价格爬虫

这个脚本用于获取加密货币的当前价格，并将数据保存到SQLite数据库中。

## 功能

- 调用ValueScan API获取代币的实时价格
- 将价格数据保存到SQLite数据库
- 支持查询最新价格和历史价格记录
- 支持多个链和代币

## 使用方法

### 安装依赖

```bash
pip install requests
```

### 运行脚本

```bash
python coin_current_price.py
```

### 配置代币

在`main`函数中修改`tokens`列表，添加需要监控的代币：

```python
tokens = [
    # 链名称, 代币合约地址
    ("SOL", "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"),  # USDC on Solana
    ("ETH", "******************************************"),     # USDT on Ethereum
    # 添加更多代币...
]
```

### 定时执行

可以使用crontab(Linux/Mac)或Task Scheduler(Windows)设置定时任务，定期执行脚本。

Linux/Mac示例 (每5分钟执行一次):
```
*/5 * * * * cd /path/to/script && python coin_current_price.py >> price_log.txt 2>&1
```

## 数据库结构

脚本会自动创建名为`coin_prices.db`的SQLite数据库，包含以下表结构：

```sql
CREATE TABLE coin_prices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    chain_name TEXT NOT NULL,
    token_contract_address TEXT NOT NULL,
    price TEXT NOT NULL,
    time TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

## API接口

使用的API接口: `/api/v1/dex/market/current-price`

请求参数:
- chainName: 链名称 (如"SOL", "ETH")
- tokenContractAddress: 代币合约地址

响应字段:
- code: 状态码，200表示成功
- data: 包含价格数据的数组
  - chainName: 链名称
  - tokenContractAddress: 代币合约地址
  - time: 时间戳
  - price: 当前价格 