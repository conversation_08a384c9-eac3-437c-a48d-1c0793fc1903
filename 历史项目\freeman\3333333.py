import time
import random
import requests
from lxml import etree
import pandas as pd
from loguru import logger
from deep_translator import GoogleTranslator

class CryptocurrencyRegulationsScraper:
    def __init__(self):
        self.base_url = "https://freemanlaw.com/cryptocurrency/"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        }
        self.translator = GoogleTranslator(source='auto', target='zh-CN')

    def fetch_content(self, url):
        try:
            response = requests.get(url, headers=self.headers, timeout=10)  # 设置超时为10秒
            if response.status_code == 200 and 'text/html' in response.headers['Content-Type']:
                logger.info(f"请求成功，url: {url} 状态码：{response.status_code}")
                time.sleep(0.79 + random.uniform(1,4))
                return response.content
            else:
                logger.info(f"请求失败或非HTML内容，状态码：{response.status_code}")
                return None
        except requests.Timeout:
            logger.info(f"请求超时，url: {url}")
            return None

    def parse_main_page(self):
        content = self.fetch_content(self.base_url)
        if content:
            tree = etree.HTML(content)
            countries = tree.xpath('//html/body/div[3]/div[17]/p/a[1]/text()')
            links = tree.xpath('//html/body/div[3]/div[17]/p/a[1]/@href')
            countries = [country.split(' –')[0] for country in countries]  # 只取“–”前的国家名
            # 翻译国家名
            translated_countries = [self.translator.translate(country) for country in countries]
            return list(zip(translated_countries, links))
        return []

    def parse_article(self, url):
        content = self.fetch_content(url)
        if content:
            tree = etree.HTML(content)
            article_content = tree.xpath('string(/html/body/div[2])')
            cutoff_index = article_content.find("P.S. Insights on Cryptocurrency Legal Issues")
            if cutoff_index != -1:
                article_content = article_content[:cutoff_index]
            try:
                return self.translator.translate(article_content)
            except Exception as e:
                logger.info(f"翻译失败: {e}")
                return article_content
        return "null"

    def save_to_csv(self, data, filename="cryptocurrency.csv"):
        df = pd.DataFrame(data, columns=['Country Name', 'Link Address', 'Content Text'])
        df.to_csv(filename, index=False)
        logger.info(f"数据已保存至 {filename}")

    def run(self):
        results = []
        main_page_data = self.parse_main_page()
        for country, link in main_page_data:
            article_content = self.parse_article(link)
            results.append((country, link, article_content))
        self.save_to_csv(results)

if __name__ == "__main__":
    scraper = CryptocurrencyRegulationsScraper()
    scraper.run()