#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简洁版 - 获取SOL链上指定代币的价格
"""

import requests
import json
import sys


def get_sol_token_price(token_address):
    """
    获取SOL链上指定代币的价格
    
    Args:
        token_address: 代币合约地址
    
    Returns:
        dict: 包含价格信息的字典，失败时返回None
    """
    url = "https://valuescan.ai/api/v1/dex/market/current-price"
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    
    # 请求必须是数组格式
    data = [{
        "chainName": "SOL",
        "tokenContractAddress": token_address
    }]
    
    try:
        print(f"正在获取 SOL/{token_address} 的价格...")
        
        response = requests.post(url, headers=headers, json=data, timeout=15)
        response.raise_for_status()  # 检查请求是否成功
        
        result = response.json()
        
        if result.get("code") == 200 and "data" in result:
            # 查找匹配的数据
            for item in result["data"]:
                if (item.get("chainName") == "SOL" and 
                    item.get("tokenContractAddress") == token_address):
                    
                    # 提取价格信息
                    price_info = {
                        "pricechange_volume": item.get("pricechange_volume"),
                        "time": item.get("time"),
                        "chainName": item.get("chainName"),
                        "tokenAddress": item.get("tokenContractAddress"),
                        "reqId": result.get("reqId")
                    }
                    
                    return price_info
            
            print(f"未找到匹配的数据: SOL/{token_address}")
            return None
        else:
            print(f"请求失败: {result.get('msg')}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None
    except json.JSONDecodeError:
        print(f"JSON解析失败")
        return None
    except Exception as e:
        print(f"发生错误: {e}")
        return None


def main():
    """主函数"""
    # 默认代币地址
    default_token = "HNg5PYJmtqcmzXrv6S9zP1CDKk5BgDuyFBxbvNApump"
    
    # 检查是否有命令行参数传入代币地址
    if len(sys.argv) > 1:
        token_address = sys.argv[1]
    else:
        token_address = default_token
        
    # 获取价格
    price_info = get_sol_token_price(token_address)
    
    if price_info:
        print("\n价格获取成功!")
        print("-" * 30)
        print(f"代币地址: {price_info['tokenAddress']}")
        print(f"价格: {price_info['pricechange_volume']}")
        print(f"时间戳: {price_info['time']}")
        print("-" * 30)
        
        # 只打印价格值，方便其他程序调用
        print(f"\n{price_info['pricechange_volume']}")
    else:
        print("\n获取价格失败")
        

if __name__ == "__main__":
    main() 