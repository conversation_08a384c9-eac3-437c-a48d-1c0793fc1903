import subprocess


def run_scripts():

    scripts = [
        'API_GET_OP_COOKIE.py',
        'API_SCHEDULE_OP.py',
        'API_MAIN_SPIDER_OP.py'
    ]

    for script in scripts:
        try:
            subprocess.run(['python', script], check=True)
        except subprocess.CalledProcessError as e:
            print(f"Error occurred while executing {script}: {e}")
            break


if __name__ == '__main__':
    
    run_scripts()
