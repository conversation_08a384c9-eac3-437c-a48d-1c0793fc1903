import requests
import time
import re


# 创建任务函数
def create_task(url, proxy, client_key):
    """
    创建Cloudflare防护页面的任务

    :param url: 目标网址
    :param proxy: 代理服务器地址
    :param client_key: 客户端密钥
    :return: 创建任务的响应结果
    """
    data = {
        "clientKey": client_key,
        "task": {
            "type": "CloudFlareTaskS2",
            "userAgent": "",  # 指定chrome内核, 可以为空，如："Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36"
            "waitLoad": True,
            "websiteURL": url,
            "proxy": proxy
        }
    }
    api_url = "https://api.yescaptcha.com/createTask"
    response = requests.post(api_url, json=data).json()
    return response

  
# 获取任务结果函数
def get_task(task_id, client_key):
    """
    获取任务的执行结果

    :param task_id: 任务ID
    :param client_key: 客户端密钥
    :return: 任务结果的响应结果
    """
    api_url = "https://api.yescaptcha.com/getTaskResult"
    data = {
        "clientKey": client_key,
        "taskId": task_id
    }
    response = requests.post(api_url, json=data).json()
    return response


# 完整的请求函数
def get_result(url, proxy, client_key):
    """
    完整的创建任务并获取结果的流程

    :param url: 目标网址
    :param proxy: 代理服务器地址
    :param client_key: 客户端密钥
    :return: 任务执行结果
    """
    task_response = create_task(url, proxy, client_key)
    if not task_response or not task_response.get('taskId'):
        return task_response

    print("TaskID:", task_response)
    for _ in range(30):
        time.sleep(3)
        result = get_task(task_response.get('taskId'), client_key)
        if result.get('status') == 'processing':
            continue
        elif result.get('status') == 'ready':
            return result
        else:
            raise Exception(result)
    return {"status": "timeout"}



if __name__ == '__main__':
    # 客户端密钥, 登陆yescaptcha.com获取
    client_key = "a4eb359df78983d82fdbe816f4c699829cddf67856298"
    
    # 代理服务器地址，填您自己的代理地址
    # 不要填本地地址（如：http://127.0.0.1:1080，http://localhost:1080这种，这个只有你自己能用）
    proxy = "*************************************************"
    proxies = {
        'http': proxy, 
        'https': proxy,
    }
    
    # 目标网址
    url = "https://optimistic.etherscan.io/login"

    # 使用普通requests请求
    response = requests.get(url, proxies=proxies)
    print("请求响应：", response.status_code)
    print("网页标题：", re.search(r"<title>(.*?)</title>", response.text)) if response.text else print("网页标题：", response.text[:1000])
    print("是否为CF盾：", "cf_chl" in response.text)
    
    # 使用接口返回的值来请求
    task_result = get_result(url, proxy, client_key)
    if not task_result.get("solution"):
        print("任务失败", task_result)
        exit()
    
    solution = task_result.get("solution")
    # 从solution中获取请求头和cookies
    headers = solution.get("request_headers")
    headers.update(solution.get("headers"))
    cookies = solution.get("cookies")
    print("Headers:", headers)
    print("Cookies：", cookies)
    
    # 使用curl_cffi.requests请求
    # 请先安装curl_cffi库，并且是pre版本：pip install curl_cffi --pre
    from curl_cffi import requests as curl_requests
    response = curl_requests.get(url, headers=headers, cookies=cookies, proxies=proxies, impersonate="chrome110")
    print("请求响应：", response.status_code)
    print("网页标题：", re.search(r"<title>(.*?)</title>", response.text)) if response.text else print("网页标题：", response.text[:1000])
    print("是否为CF盾：", "cf_chl" in response.text)
