import csv
import json
from datetime import datetime
from curl_cffi import requests
from loguru import logger
import time
from datetime import datetime, timezone


proxy = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

class Trump():
    def __init__(self):
        self.headers = {
        "referer": "https://gmgn.ai/sol/token/4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R",
        }
        self.params = {
            "device_id": "1713e771-6640-405d-8c71-fe483feeb742",
            "client_id": "gmgn_web_20250423-332-41c95ba",
            "from_app": "gmgn",
            "tz_name": "Asia/Shanghai",
            "tz_offset": "28800",
            "app_lang": "zh-CN",
            "resolution": "4h",
            "from": "0",
            "to": "1705305621000"
        }
        self.cookies = {
            "_ga": "GA1.1.566489715.1737427304",
            "cf_clearance": "zsdqsC2JRyAdq_.iihaokrA.05XOlZHf3OlEVuqvqnk-1745468334-1.2.1.1-yNP5xGj8szxqcqjvl.MJsNaRqttGLAtPOisiKuDwRDq4Qb9Hs8THZJtPehi7q5SEZBshY6j0wvsG0luyN3dTjJvZpUNDlKG1kGgwDDg.GYvpIwLy1sCZMs2Ux25AS1GzhuTWagLS1OGaiAWAXQQlc0cMRTQojwUNNWHW1xODF3VuBrOF5pvdd7G2TBFmZH9uyDatR7G9RWWSs9TiGzLCuWM4mYFFepbiBs7rlqD3vn2vadLmMLZm.6oL9ID0BmsdusYGAtLPQvNm0Gmjdaqz.Sw6MaU29Ylh9oBtPRMqcW7p4OEAugctj53uG5l.Ygr0qu8nzogIe7PIajYdoOHWn.zAywPEAMyUZAZjTGUsUw4",
            "_ga_0XM0LYXGC8": "GS1.1.1745468340.19.1.1745468414.0.0.0"
        }

    def parse_data(self):
        url = "https://gmgn.ai/api/v1/token_candles/sol/4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R"
        response = requests.get(url, headers=self.headers, cookies=self.cookies, params=self.params, proxies=proxy, impersonate='chrome110')


        if response.status_code == 200:
            print(response.text)
            data = response.json()
            if data.get("message") == "success":
                logger.info(f'访问成功,状态码:{response.status_code},正在获取数据')
                # self.save_to_csv(data["data"]["list"])
        elif response.status_code == 403 and "Just a moment..." in response.text:
            logger.info(f'未通过校验,状态码:{response.status_code},请检查cookie')


    def save_to_csv(self, data_list):
        with open('RAY_s级1111111.csv', mode='w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(["open", "high", "low", "close", "time"])
            for item in data_list:
                open_price = item["open"]
                high = item["high"]
                low = item["low"]
                close = item["close"]
                time_stamp = int(item["time"]) / 1000
                time_str = datetime.fromtimestamp(time_stamp, timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                writer.writerow([open_price, high, low, close, time_str])

            logger.info('数据保存成功')


if __name__ == '__main__':
    trump = Trump()
    trump.parse_data()