# import requests
# from lxml import html
# import csv
# import os
# import time
# import logging
# import random
# import pandas as pd

# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# with open(f'加密货币政策监管.txt', 'r', encoding='utf-8') as file:
#     keywords = file.read().splitlines()

# headers = {
#     "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
# }
# cookies = {
#     "theme-mode": "light",
#     "_ga": "GA1.1.182126096.1735811532",
#     "_ga_KHBYDL8DMV": "GS1.1.1735815402.2.1.1735815606.0.0.0"
# }
# base_url = "https://www.panewslab.com/zh/search/index.html"

# output_folder = r'D:\python_区块链数据爬取\Panews\加密货币政策监管'
# os.makedirs(output_folder, exist_ok=True)

# for keyword in keywords:
#     params = {"key": keyword}
#     response = requests.get(base_url, headers=headers, cookies=cookies, params=params)
    
#     logging.info(f'Response Status Code: {response.status_code}')
#     if response.status_code != 200:
#         logging.error(f'Failed to fetch data for keyword "{keyword}". Status code: {response.status_code}')
#         continue

#     tree = html.fromstring(response.content)

#     title = tree.xpath("//a[@class='n-title pa-news__list-title']/text()")
#     description = tree.xpath("//p[@class='description']")
#     link = tree.xpath("//a[@class='n-title pa-news__list-title']/@href")
#     full_link = ['https://www.panewslab.com' + l for l in link]

#     logging.info(f'Title: {title}')
#     logging.info(f'Description: {description}')
#     logging.info(f'Link: {full_link}')

#     output_file = os.path.join(output_folder, f'{keyword}.csv')
#     with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
#         writer = csv.writer(csvfile)
#         writer.writerow(['文章标题', '文章描述', '文章地址'])
#         writer.writerows(zip(title, description, full_link))

#     logging.info(f'文件已保存: {output_file}')

#     time.sleep(6 + random.random())


import requests
from lxml import html
import os
import time
import logging
import random
import pandas as pd

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

with open(f'安全事件.txt', 'r', encoding='utf-8') as file:
    keywords = file.read().splitlines()

headers = {
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
cookies = {
    "theme-mode": "light",
    "_ga": "GA1.1.182126096.1735811532",
    "_ga_KHBYDL8DMV": "GS1.1.1735815402.2.1.1735815606.0.0.0"
}
base_url = "https://www.panewslab.com/zh/search/index.html"

output_folder = r'D:\python_区块链数据爬取\Panews\安全事件'
os.makedirs(output_folder, exist_ok=True)

for keyword in keywords:
    params = {"key": keyword}
    response = requests.get(base_url, headers=headers, cookies=cookies, params=params)
    
    logging.info(f'Response Status Code: {response.status_code}')
    if response.status_code != 200:
        logging.error(f'Failed to fetch data for keyword "{keyword}". Status code: {response.status_code}')
        continue

    tree = html.fromstring(response.content)

    title = tree.xpath("//div[@class='list-left pa-news__list-left']/a/text()")
    description = tree.xpath("//p[@class='description']/text()")
    
    pagetime = [pt.strip() for pt in tree.xpath("//a[@class='pa-news__list-time']/text()") if pt.strip()]
    link = tree.xpath("//div[@class='list-left pa-news__list-left']/a/@href")
    full_link = ['https://www.panewslab.com' + l for l in link]

    min_length = min(len(title), len(description), len(pagetime), len(full_link))
    title = title[:min_length]
    description = description[:min_length]
    pagetime = pagetime[:min_length]
    full_link = full_link[:min_length]

    logging.info(f'Title: {title}')
    logging.info(f'Description: {description}')
    logging.info(f'pagetime: {pagetime}')
    logging.info(f'Link: {full_link}')

    data = {
        '文章标题': title,
        '文章描述': description,
        '文章时间': pagetime,
        '文章地址': full_link
    }
    df = pd.DataFrame(data)

    output_file = os.path.join(output_folder, f'{keyword}.xlsx')
    df.to_excel(output_file, index=False)

    logging.info(f'文件已保存: {output_file}')

    time.sleep(4.8 + random.random())
