from curl_cffi import requests


headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Pragma": "no-cache",
    "Referer": "https://gmgn.ai/eth/token/******************************************",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
cookies = {
    "_ga": "GA1.1.566489715.1737427304",
    "__cf_bm": "tg9tGVOUNNg8gS9Cvv4aIyVoFGxlEU.3ibXFx8Wkauc-1737686464-1.0.1.1-_hef9qQ7SyJIN7hTqChIABgoNqy9mToGcdXWK5YOTZbfCuMWBST6beH4ZSiOo_MGkffsEHe3AVgnNygHRIerLQ",
    "_ga_0XM0LYXGC8": "GS1.1.1737685640.2.1.1737686469.0.0.0",
    "cf_clearance": ".UYjlvXn0ILBE_4Db.6nb4WidsSo_uZyOPUjsuzNcSE-1737686465-1.2.1.1-BkPxApJsBlTPaZfVN0CIAriX3PpRiunC3S0ce.0edXijjqdD09tKLsQyFdEgz0LFMJwrUJPiZUXxY4Gwu_4BcjmTY886t3ERU.04vusW42lc7g6vtALsPfPThW_37jllLS9wK8G3pYgDgo.oddARzDxuMjTXAtXn1j9SHlvM6z9cK9aRoJ6jyg1dk2R2Y5oGbIrck9pfV1_.dp8SP34rAIhAQnm6U1yl2d7NIOl5cxGF8Tjvjuw.e3.Gj58eCdCkY7JAMMu7ZjZB3C3CMCxZHu1C7TywfkUkZYgFh.vz7sE"
}
url = "https://gmgn.ai/api/v1/token_kline/eth/******************************************"
params = {
    "device_id": "1713e771-6640-405d-8c71-fe483feeb742",
    "client_id": "gmgn_web_2025.0124.001843",
    "from_app": "gmgn",
    "app_ver": "2025.0124.001843",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "en",
    "resolution": "1s",
    "from": "1737651227",
    "to": "1737687227"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params)

print(response.text)
print(response)

0x576e2bed8f7b46d34