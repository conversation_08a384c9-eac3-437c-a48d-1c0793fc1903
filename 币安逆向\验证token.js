// 🎯 直接验证Token代码 - 复制粘贴到币安网站控制台运行

const token = "57cc1a01-8fc6-47ad-8bb0-32c3f63c8240:AAABmBIZN3I=:yuXy+Xw+n89ns9nsdrvdbrfb7fZ7vV6v1+v1+v3+///K5fL5fD6fz2ez2ex2u91ut9vt9nu9Xq/X6/X6/f7//8rl8vl8Pp/PZ7PZ7Ha73W632+32e71er9fr9fr9/v//yuXy+Xw+n89ns9nsdrvdbrfb7fZ7vV6v1+v1+v3+//8=";

// 验证函数
async function verifyAWSWAFToken() {
    console.log("🚀 开始验证Token...");
    
    const endpoints = [
        'https://api.binance.com/api/v3/time',
        'https://api.binance.com/api/v3/ping',
        'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT'
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`\n🎯 测试: ${endpoint}`);
            
            const response = await fetch(endpoint, {
                headers: {
                    'x-aws-waf-token': token,
                    'User-Agent': navigator.userAgent,
                    'Referer': 'https://www.binance.com/',
                    'Origin': 'https://www.binance.com'
                },
                credentials: 'include'
            });
            
            console.log(`📊 状态码: ${response.status}`);
            
            // 检查Set-Cookie头部
            const cookies = response.headers.get('Set-Cookie');
            if (cookies) {
                console.log(`🍪 Set-Cookie: ${cookies}`);
                
                // 查找aws-waf-token
                const match = cookies.match(/aws-waf-token=([^;]+)/);
                if (match) {
                    console.log(`🎉 成功获得aws-waf-token: ${match[1]}`);
                    return match[1];
                }
            }
            
            // 尝试读取响应
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ 响应数据:`, data);
            }
            
        } catch (error) {
            console.log(`❌ 错误: ${error.message}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log("\n🔍 检查浏览器Cookies:");
    console.log(document.cookie);
}

// 立即执行验证
verifyAWSWAFToken(); 