import requests
import json


headers = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "text/plain;charset=UTF-8",
    "origin": "https://www.binance.com",
    "priority": "u=1, i",
    "referer": "https://www.binance.com/",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "cross-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
}
url = "https://fe4385362baa.b3f58947.us-west-1.token.awswaf.com/fe4385362baa/306922cde096/8b22eb923d34/verify"
data = {
    "challenge": {
        "input": "eyJ2ZXJzaW9uIjoxLCJ1YmlkIjoiYjAyZTgyOWEtMzE3OC00NjNlLTg4MGYtYjMyYWU5YjE4YTAyIiwiYXR0ZW1wdF9pZCI6IjU1OWU1YmRlLTg4ZTMtNGI1OC04YWZhLTU4MzVlNjg4NWY0NiIsImNyZWF0ZV90aW1lIjoiMjAyNS0wNy0xOFQwNzoxMDo1OC4xMTE2MDkxMDVaIiwiZGlmZmljdWx0eSI6OCwiY2hhbGxlbmdlX3R5cGUiOiJIYXNoY2FzaFNIQTIifQ==",
        "hmac": "6eDdz1jq4o8BUAMj3WPHwYPBSnJxMNc3c09IhcrN4vE=",
        "region": "us-west-1"
    },
    "solution": "56",
    "signals": [
        {
            "name": "KramerAndRio",
            "value": {
                "Present": "xqmi5hsgi08/qM7L::ae93c34ea49d92f70c660d663a344901::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"
            }
        }
    ],
    "checksum": "091E315D",
    "existing_token": None,
    "client": "Browser",
    "domain": "www.binance.com",
    "metrics": [
    ],
    "goku_props": {
        "key": "a9f0f91c-3475-43eb-8805-0ba37e53b3cd:AAABmByLolk=:dbrd7vd7PR4Ph0Oh0Oh0Oh0Oh0MhEIjE4vH4/P7///91ut3u93s9Hg+HQ6HQ6HQ6HQ6HQyEQiMTi8fj8/v///3W63e73ez0eD4dDodDodDodDodDIRCIxOLx+Pz+////dbrd7vd7PR4Ph0Oh0Oh0Oh0Oh0MhEIjE4vH4/P7///8=",
        "iv": "EkQdSgAvQwAAK/Hg",
        "context": "YTQxqCu5+Qlj2uKhsXpwGjVk4kL1f9n0QH964jIN0JwIJPYUaE/UnxKsMTI8PEk+coB43GijeQME6+0sJIFpqraqbwcj/zFvDQeDf0CWpe4jAlfj8jbAc38Eo9HYVaigpOGU6OF3HX14XzVL5ErPokbSyaPHbhOXCyhK0/zMhqTDSIfrBDP4+chiNcx5EIAALm5gJ4FFabgaE3vF4LXvFuUjYUklvNFt9E7UF2KtWStNvAR62q7LBtteefskEtJiIOAw9bw3bK0tjwGwJ9ZQew1+09P9TcOTAlAm/QPSIUTlZH/kfZLQK5gsVgFxK8EgJLoFtsMb4HuJWgNH/JtiW6a/2wDMsrvQZA8EdwUXcs5sikz7GR82D/BcGQCjZ2aSUfJ+8FcCjXO+ENp9Uan1AnigCCU="
    }
}
data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, data=data)

print(response.text)
print(response)