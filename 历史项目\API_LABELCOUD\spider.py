import time
import httpx
import requests
from loguru import logger
from retrying import retry
from threading import Lock
from Spiders.settings import RETRY_NUM
from Spiders.utils import DB_BASE
import signal


LOCK = Lock()


def check_status(status):
    """
    判断是否需要重试请求
    :param status:
    :return: 返回值用来判断是否继续重试
    """
    # print("-----------")
    # print(status)
    if status and isinstance(status, bool):
        return True
    else:
        # print("===========")
        return False


class Spider_Crawler_HTTP(DB_BASE):

    def __init__(self, version=1, process_id=0, *args, **kwargs):
        # print(kwargs)
        super(Spider_Crawler_HTTP, self).__init__(*args, **kwargs)
        if not isinstance(process_id, int):
            raise "进程号必须是整数类型"
        self.version = version
        self.process_id = str(process_id)
        if version > 2:
            raise "at present, the HTTP version is only 1 or 2"
        if not isinstance(version, int) or version < 0:
            raise "HTTP version must be an integer greater than 0"

    def create_client(self, proxies=None, headers=None, timeout=10):
        """
        创建并且返回HTTP连接,proxies是代理，如果不设置，默认没有代理
        headers是请求头，如果不设置则使用默认请求头
        :param timeout:
        :param proxies: 代理IP
        :param headers: 请求头
        :return client: 创建成功的HTTP连接
        """
        if not proxies:
            proxies = {}
        if not headers:
            headers = {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Connection": "close"}
        if self.version == 1:
            # 创建HTTP1的连接
            client = httpx.Client(http1=True, proxies=proxies, headers=headers, timeout=timeout)
        else:
            # 创建HTTP2的连接
            client = httpx.Client(http2=True, proxies=proxies, headers=headers, timeout=timeout)
        return client  # 返回创建成功的HTTP连接

    def close_client(self, client):
        """
        关闭本次HTTP连接
        :param client: 待关闭的HTTP连接
        :return:
        """
        client.close()

    @retry(stop_max_attempt_number=RETRY_NUM, retry_on_result=check_status)
    def set_request(self, url: str, method="GET", params=None, ip_list_name=None, ip_key=None, is_proxies=False, proxies_type=1, timeout=10, http=True, is_internal=True, *args, **kwargs):
        """
        发送请求，并且进行错误重试
        :param is_internal:
        :param http:
        :param ip_key:
        :param ip_list_name:
        :param timeout:
        :param url: 请求的url,不携带请求参数
        :param method: 请求方法
        :param params: 请求参数
        :param is_proxies: 是否使用代理IP，默认情况下不使用，设置成True则使用
        :param proxies_type: 代理IP类型，1为HTTP代理，2为socks5代理
        :return: 返回值用于判断是否需要重试
        """
        if not isinstance(is_internal, bool):
            raise "必须是布尔类型"
        if not params:
            params = {}
        if not isinstance(method, str):
            raise "method must be str"
        request_method = method.upper()  # 将请求方式转化成大写
        if (is_proxies and ip_list_name and ip_key):
            # print(IP_LIST, IP_LIST_KEY)
            with LOCK:  # 防止多个线程请求IP
                if not self.get_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal):
                    # print("-----------")
                    self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)
                while True:
                    # 如果IP失效，重新获取IP
                    host_and_time = self.get_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)  # 获取IP及获取IP的时间
                    # print(host_and_time)
                    host = host_and_time['host']
                    ip_time = host_and_time['time']
                    request_time = time.time()
                    # print(request_time - ip_time)
                    if is_internal:
                        if request_time - ip_time >= 180:
                            # IP已经失效，重新获取IP
                            self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)
                        else:
                            break
                    else:
                        if request_time - ip_time >= 60:
                            # IP已经失效，重新获取IP
                            self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)
                        else:
                            break

            if isinstance(proxies_type, int) and (proxies_type < 1 or proxies_type > 2):
                raise "代理类型必须是整数1或者2"
            if not isinstance(http, bool):
                raise "必须是布尔类型"
            if proxies_type == 1:
                if http:
                    proxies = {
                        'http//:': f'http://{host}',  # 代理1
                        # 'https//:': f'http://STHB9U24:62E81DC42692@{host}'  # 代理2
                    }
                else:
                    proxies = {
                        # 'http//:': f'http://STHB9U24:62E81DC42692@{host}',  # 代理1
                        'https//:': f'http://{host}'  # 代理2
                    }
            else:
                if http:
                    proxies = {
                        'http//:': f'socks5://{host}',  # 代理1
                        # 'https//:': f'socks5://STHB9U24:62E81DC42692@{host}'  # 代理2
                    }
                else:
                    proxies = {
                        # 'http//:': f'socks5://STHB9U24:62E81DC42692@{host}',  # 代理1
                        'https//:': f'socks5://{host}'  # 代理2
                    }
            # print(proxies)
            client = self.create_client(proxies=proxies, timeout=timeout)
        else:
            client = self.create_client(timeout=timeout)
        if request_method not in ["GET", "POST"]:
            # 请求方法必须是"GET","POST"
            raise "wrong request method. the correct request method should be GET or POST"
        if request_method == "GET":
            try:
                response = client.request(method=request_method, url=url, params=params, *args, **kwargs)  # 发送get请求
                # print(response.text)
                self.close_client(client)
            except Exception as e:
                logger.error(repr(e))
                self.close_client(client)
                return False
        else:
            try:
                response = client.request(method=request_method, url=url, data=params, *args, **kwargs)  # 发送post请求
                self.close_client(client)
            except Exception as e:
                logger.error(repr(e))
                self.close_client(client)
                return False
        return response

    def request(self, url: str, method="GET", params=None, ip_list_name=None, ip_key=None, is_proxies=False, proxies_type=1, timeout=10, http=True, is_internal=True, *args, **kwargs):
        """
        正式发送请求的方法，请求如果超过重试次数，将会把种子存入ERR_SEED_LIST
        :param is_internal:
        :param http:
        :param ip_key:
        :param ip_list_name:
        :param timeout:
        :param proxies_type: 代理IP类型，1是HTTP代理，2是socks5代理
        :param is_proxies: 是否进行代理，默认不使用代理
        :param method: 请求方法，默认是GET方法
        :param url: 请求的url,不携带请求参数
        :param params: 请求参数
        :return: 返回值response是请求成功的响应对象
        """
        try:
            response = self.set_request(url=url, method=method, params=params, ip_list_name=ip_list_name, ip_key=ip_key, is_proxies=is_proxies,
                                        proxies_type=proxies_type, timeout=timeout, http=http, is_internal=is_internal, *args, **kwargs)
            # print(response.text)
            return response
        except Exception as e:
            # self.REDIS_CONN.sadd(ERR_SEED_LIST, json.dumps(params, ensure_ascii=False))  # 将请求失败的种子压入ERR_SEED_LIST
            # logger.info(e)
            return False

    def check_response(self, response):
        """
        验证返回的数据是否正确，因为有些网站返回200的状态码不一定是准确数据
        :param response:
        :return:
        """
        pass

    def parse(self, response):
        """
        对抓取的数据进行解析
        :param response:
        :return:
        """
        pass

    def send_request(self, seed_list=None, seed=None, *args, **kwargs):
        """
        发送请求，获取响应的对外方法
        具体逻辑需要开发人员自己重写
        一定要继承原函数
        :param seed_list:
        :param seed:
        :param args:
        :param kwargs:
        :return:
        """
        pass
        # if not seed:
        #     seed = {}
        # if seed_list and not isinstance(seed_list, str):
        #     raise "种子队列名称的类型必须是字符串"
        #
        # def get_signal(signum, frame):
        #     if seed_list and seed:
        #         self.set_seed("err_" + seed_list, seed)
        #
        # signal.signal(signal.SIGINT, get_signal)
        # signal.signal(signal.SIGABRT, get_signal)
        # signal.signal(signal.SIGFPE, get_signal)
        # signal.signal(signal.SIGKILL, get_signal)


class Spider_Crawler_Request(DB_BASE):
    def __init__(self, process_id=0, *args, **kwargs):
        if not isinstance(process_id, int):
            raise "进程号必须是整数类型"
        self.process_id = str(process_id)
        super(Spider_Crawler_Request, self).__init__(*args, **kwargs)

    @retry(stop_max_attempt_number=RETRY_NUM, retry_on_result=check_status)
    def GET(self, url: str, headers=None, timeout=10, cookies=None, verify=True, params=None, ip_list_name=None, ip_key=None, is_proxies=False, proxies_type=1, http=True, is_internal=True, *args, **kwargs):
        """
        对get方法进行失败重试
        :param is_internal:
        :param http:
        :param ip_key:
        :param ip_list_name:
        :param proxies_type:
        :param is_proxies:
        :param url:
        :param headers:
        :param timeout:
        :param cookies:
        :param verify:
        :param params:
        :param args:
        :param kwargs:
        :return:
        """
        if not isinstance(is_internal, bool):
            raise "必须是布尔类型"
        if not headers:
            headers = {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Connection": "close"}
        if not cookies:
            cookies = {}
        if not params:
            params = {}

        if 'proxies' in kwargs:
            proxies = kwargs['proxies']
            kwargs.pop('proxies')
        elif not (is_proxies and ip_list_name and ip_key):
            proxies = {}
        else:
            if (not isinstance(proxies_type, int)) and (proxies_type not in [1, 2]):
                raise "代理类型必须是整数1或者2"
            with LOCK:
                if not self.get_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal):
                    # print("-----------")
                    self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)
                while True:
                    # 如果IP失效，重新获取IP
                    host_and_time = self.get_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)  # 获取IP及获取IP的时间
                    # print(host_and_time)
                    host = host_and_time['host']
                    ip_time = host_and_time['time']
                    request_time = time.time()
                    if is_internal:
                        if request_time - ip_time >= 180:
                            # IP已经失效，重新获取IP
                            self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)
                        else:
                            break
                    else:
                        if request_time - ip_time >= 60:
                            # IP已经失效，重新获取IP
                            self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)
                        else:
                            break

            if not isinstance(http, bool):
                raise "必须是布尔类型"
            if proxies_type == 1:
                if http:
                    proxies = {
                        # "https": f"http://STHB9U24:62E81DC42692@{host}",
                        "http": f"http://{host}"
                    }
                else:
                    proxies = {
                        "https": f"http://{host}",
                        # "http": f"http://STHB9U24:62E81DC42692@{host}"
                    }
            else:
                if http:
                    proxies = {
                        # "https": f"socks5h://STHB9U24:62E81DC42692@{host}",
                        "http": f"socks5h://{host}"
                    }
                else:
                    proxies = {
                        "https": f"socks5h://{host}",
                        # "http": f"socks5h://STHB9U24:62E81DC42692@{host}"
                    }
        try:
            response = requests.get(url=url, headers=headers, timeout=timeout, cookies=cookies, verify=verify,
                                    params=params, proxies=proxies, *args, **kwargs)
            return response
        except Exception as e:
            logger.error(repr(e))
            return True

    def get(self, url: str, headers=None, timeout=10, cookies=None, verify=True, params=None, ip_list_name=None, ip_key=None, is_proxies=False, proxies_type=1, http=True, is_internal=True, *args, **kwargs):
        """
        重写get方法
        :param is_internal:
        :param http:
        :param ip_list_name:
        :param ip_key:
        :param proxies_type:
        :param is_proxies:
        :param url:
        :param headers:
        :param timeout:
        :param cookies:
        :param verify:
        :param params:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            response = self.GET(url=url, headers=headers, timeout=timeout, cookies=cookies, verify=verify,
                                params=params, ip_list_name=ip_list_name, ip_key=ip_key, is_proxies=is_proxies, proxies_type=proxies_type, http=http, is_internal=is_internal, *args, **kwargs)
            return response
        except Exception as e:
            # 将请求失败的种子放入ERR_SEED_LIST
            # self.REDIS_CONN.sadd(ERR_SEED_LIST, json.dumps(params, ensure_ascii=False))
            return None

    @retry(stop_max_attempt_number=RETRY_NUM, retry_on_result=check_status)
    def POST(self, url: str, headers=None, timeout=10, cookies=None, data=None, ip_list_name=None, ip_key=None, verify=True, is_proxies=False, proxies_type=1, http=True, is_internal=True, *args, **kwargs):
        """
        对post方法进行错误重试
        :param is_internal:
        :param http:
        :param ip_key:
        :param ip_list_name:
        :param proxies_type:
        :param is_proxies:
        :param url:
        :param headers:
        :param timeout:
        :param cookies:
        :param data:
        :param verify:
        :param args:
        :param kwargs:
        :return:
        """
        if not isinstance(is_internal, bool):
            raise '必须是布尔类型'
        if not headers:
            headers = {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Connection": "close"}
        if not cookies:
            cookies = {}
        if not data:
            data = {}
        if not (is_proxies and ip_list_name and ip_key):
            proxies = {}
        else:
            if (not isinstance(proxies_type, int)) and (proxies_type not in [1, 2]):
                raise "代理类型必须是整数1或者2"
            with LOCK:
                if not self.get_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal):
                    # print("-----------")
                    self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)
                while True:
                    # 如果IP失效，重新获取IP
                    host_and_time = self.get_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)  # 获取IP及获取IP的时间
                    # print(host_and_time)
                    host = host_and_time['host']
                    ip_time = host_and_time['time']
                    request_time = time.time()
                    if is_internal:
                        if request_time - ip_time >= 180:
                            # IP已经失效，重新获取IP
                            self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)
                        else:
                            break
                    else:
                        if request_time - ip_time >= 60:
                            # IP已经失效，重新获取IP
                            self.set_ip(ip_list_name=ip_list_name, ip_key=ip_key + self.process_id, is_internal=is_internal)
                        else:
                            break

            if not isinstance(http, bool):
                raise "必须是布尔类型"
            if proxies_type == 1:
                if http:
                    proxies = {
                        # "https": f"http://STHB9U24:62E81DC42692@{host}",
                        "http": f"http://{host}"
                    }
                else:
                    proxies = {
                        "https": f"http://{host}",
                        # "http": f"http://STHB9U24:62E81DC42692@{host}"
                    }
            else:
                if http:
                    proxies = {
                        # "https": f"socks5h://STHB9U24:62E81DC42692@{host}",
                        "http": f"socks5h://{host}"
                    }
                else:
                    proxies = {
                        "https": f"socks5h://{host}",
                        # "http": f"socks5h://STHB9U24:62E81DC42692@{host}"
                    }
        try:
            response = requests.post(url=url, cookies=cookies, data=data, headers=headers, verify=verify, timeout=timeout, proxies=proxies, *args, **kwargs)
            return response
        except Exception as e:
            logger.error(repr(e))
            return True

    def post(self, url: str, headers=None, timeout=10, cookies=None, data=None, ip_list_name=None, ip_key=None, verify=True, is_proxies=False, proxies_type=1, http=True, is_internal=True, *args, **kwargs):
        """
        重写post方法
        :param is_internal:
        :param http:
        :param ip_key:
        :param ip_list_name:
        :param proxies_type:
        :param is_proxies:
        :param url:
        :param headers:
        :param timeout:
        :param cookies:
        :param data:
        :param verify:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            response = self.POST(url=url, cookies=cookies, data=data, ip_list_name=ip_list_name, ip_key=ip_key, headers=headers, verify=verify, timeout=timeout, is_proxies=is_proxies, proxies_type=proxies_type, http=http, is_internal=is_internal, *args, **kwargs)
            return response
        except Exception as e:
            # 将请求失败的种子放入ERR_SEED_LIST
            # self.REDIS_CONN.sadd(ERR_SEED_LIST, json.dumps(data, ensure_ascii=False))
            return None

    def check_response(self, response):
        """
        检验response，因为有时候状态码为200不一定数据正确
        :param response:
        :return:
        """
        pass

    def parse(self, response, *args, **kwargs):
        """
        解析响应数据
        :param response:
        :param args:
        :param kwargs:
        :return:
        """
        pass

    def send_request(self, seed_list=None, seed=None, *args, **kwargs):
        """
        发送请求的方法，需要开发人员重写函数
        一定要继承原函数
        :param seed_list:
        :param seed:
        :param args:
        :param kwargs:
        :return:
        """
        pass
        # atexit.register(self.program_exception_handling, seed)
        # if not seed:
        #     seed = {}
        #
        # if seed_list and not isinstance(seed_list, str):
        #     raise "种子队列名称必须是字符串类型"
        #
        # def get_signal(signum, frame):
        #     if seed_list and seed:
        #         self.set_seed("err_" + seed_list, seed)
        # signal.signal(signal.SIGINT, get_signal)
        # signal.signal(signal.SIGABRT, get_signal)
        # signal.signal(signal.SIGFPE, get_signal)
        # signal.signal(signal.SIGKILL, get_signal)

