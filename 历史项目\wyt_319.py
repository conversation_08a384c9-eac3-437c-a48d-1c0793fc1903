import requests
from lxml import html
import pandas as pd
from loguru import logger
import time

headers = {
    "authority": "www.ncsl.org",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "max-age=0",
    "content-type": "application/x-www-form-urlencoded",
    "origin": "https://www.ncsl.org",
    "referer": "https://www.ncsl.org/financial-services/cryptocurrency-digital-or-virtual-currency-and-digital-assets-2024-legislation?__cf_chl_tk=LtHECIB0LFj8nYRa.dlPC0FMrag9gaTEfUQo5Etk7_s-1742348930-*******-sTNn8lykPs7JJ171Q9wUL4V3RHhKZ_oDYEs.KHkeOmA",
    "sec-ch-ua": "\"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Google Chrome\";v=\"114\"",
    "sec-ch-ua-arch": "\"x86\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"114.0.5735.199\"",
    "sec-ch-ua-full-version-list": "\"Not.A/Brand\";v=\"8.0.0.0\", \"Chromium\";v=\"114.0.5735.199\", \"Google Chrome\";v=\"114.0.5735.199\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-ch-ua-platform-version": "\"15.0.0\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
}
cookies = {
    ".ASPXANONYMOUS": "B_EBY9TyW4gHyakSQE4braoN5FKXGZSdM3UwKCJKuJ5Gd-WmkNCGWeWR5J6-WfyYuQODTgVBt_DgHZy0n8D6GRL2-goJ5nr0tUWc-FPQMrk4ndCv0",
    "_gcl_au": "1.1.1900414457.1742195194",
    "_fbp": "fb.1.1742195195890.618976060670522802",
    "wp46534": "\"XZYWXDDDDDDTVKJCJLY-THJC-XCVZ-CWMX-KZAVYZZXYXZTDgNssDgJls_hknDD\"",
    "cookieconsent_status": "dismiss",
    "_gid": "GA1.2.996559705.1742289671",
    "cf_chl_rc_m": "1",
    "dnn_IsMobile": "False",
    "language": "en-US",
    "__RequestVerificationToken": "dXCRTzNcFXu6xlt_w0X5l7RqPv7kck9_Yuwh_8SFhovf2E490iYXuD5vaGFR_OdDQZriFQ2",
    "_ga_L57VM439KX": "GS1.1.1742348939.6.0.1742348939.0.0.0",
    "cf_clearance": "E4XolIthxM7e8MpMPlVQYTRmIV31JzBZxhfGJsW7_bM-1742348937-*******-ztMz7cUoSeuXSWKynHKud5.H78q8ah_XfuVJTBTfLtQDp0gTQ8tuyHjk2OxWratt.zz3PynWWIy3M88vqtJKI2YxWUYSVN3H6Nhdm1MnlF8a5WKOJMMnT_3VVNuvQ_qdygKExbV4NS5.EaaHSAkdyPvQjbXFz702vNbIp1E9xH8srRqK2VkBegx1LyW3ULSmsRTltTLNw9Wx6Eh99D3E0darejFXhexaD1w0ngyGPh9rzMlT38zkst14efTk7RV4Mdm1_CWj6iRtsEVg7vdTY7OsuD4R_r2TYegyQoFErX.ey60CSOYhQwh4wmfoXAmtryWvZMfJ.mATdFNzMHaiZvTNdhdyCQOS4GAM7yiDtwjdhaX77wyRFEP_2ug.xrS9cb458USLqt2HGE0BPGA1.t5PsvvP6IsiK5k8eFjJCQ4",
    "_ga": "GA1.2.703699783.1742195196",
    "_gat_UA-9062323-1": "1",
    "_clck": "15hk0rm%7C2%7Cfuc%7C0%7C1902",
    "_clsk": "1lm5h6f%7C1742348942340%7C1%7C1%7Cj.clarity.ms%2Fcollect"
}

def fetch_data():
    """发送请求获取数据"""
    url = "https://www.ncsl.org/financial-services/cryptocurrency-digital-or-virtual-currency-and-digital-assets-2024-legislation"
    data = {
        "9075bbf0f4a5d54d428dce8657dccee4b324f36cf4c817600de6aa75a3f18f90": "MDrO__bBFES2BR6fy.bEQebqcHeWwCEnzwuTFKazC4g-1742348930-*******-Fa6qm0QvhEu9QmtAl3iDGGl3CjLMc_Zbd9hw0Z2I_xKeefZcFIsfDHsE4EbJf.Kfpzzu3P7yYsDaNn6O7YtvMOmp.MkDTc8wz_Uc1ILn9m9plLiuodvwBUm1l9NCRflwLO9nEOg_4vbP4erFkZCOgpbHTlgqeSaKrK6A.pJ3lZtrZD28lpLqh4jJlxccmvBVGmKUvXG1Ah2u3_9RB2p8wcs_8.0cbCaAcpm8jgizizl9lDMCOqd8UIwyoU3y8bsFoQPLx7pehldKB_SEBEl.9dQAeIEW1INeCFXULX0k8TlZH6SC2_OOcReUiMWVncgoKn6xfOvis542cVahieMPgYNHRiC0gEnfzgJqc4UV._Ueb2HZRJ9Dt9oD9v2SqThUBF6I9FTaZnn7e6DnhAOrt6vA1kBXD6g11FNv9iXtodZSeEWyRj_ZmM70t6lvaX_I5ODVclmNyIMgFI7ltwDlNnNGi0q_MbukNqRyxJ0E8D6QHC1.WyNUd.tFWfsz.jSYgqkdIPESqWJpIM9J4gDZlewTgs.NC9eVC8A8uZ5tNVmny2ZIQco_FlcA1S2jAzmVnbi8CK9p3VtYY.530qlkzoi0ycE7YrOIMyt3vCmm6C7xz9xgH67nimWyPDReEqCofdLg4O9d94PT6QwFf3SCU24H.G.zQdJ0qEW4_ipPULNIdatgfGg8NPhTjeKuFOu4QmGoq8izFWlxJPLtGvdQDgxDyziBZrZc.UKUX4iCiG233bVJ3hZ09Xlu5V3dDJemsCW7ufCTnZujVnnT2e6a0gHqwORZrW8vw8uNGtleDAIztpP5QDG3CDNTKdlDyjnIB1w.bAUjZXwseolgnZpOsPxR3GTN05K51LJoTCUAuWFI9beBKGJ.luKo9BDX1kT8ChLriMwHhdZktr.k8aNV5ysywKvvdspPiw_SO9FsnzK_U4V5wmyzTX7aYg0uQjNEFesTQlOVnSnG3c9qsvh7tJy2dI.fIqgga9xSWyQ24inHbPyRTyY3KIBe3KcS6WbfPJZ2ZdKxuKPUdYQ5NnTEykE1x4sSycTugg1SGH8glnlJAaYEB067w7ESa4ZseSJeJyFDhFj_8u9lUHGpOoSNQ6qHOwj3d3I_PDA4x71q.1ecqBJs9hLPhS7yzIGvmvKWpeZLLmusQ8uQAvb9dM7CE1rTjUlZjqBtsXkx48nt5bJyVt95cQ8tQCq6E5pra7Arppf7vddGbKNRQI5JOf13Gnlc.St19Bn6g8TqlUGGS7RDWX9Upm.X0tk.GVsGHTzjRsHmjrLy_5m9zPRZDf96TKZI5FFSjXtL9A10POI_yI6MEpyR96mlV11CdJTXnO23YLxtmhBVMObfZS.6gCtpmpzye7MqaEF8sNIAJ7HvxCic5JfydeNAD99mGscAyav0",
        "0ae8936f46a3459b87fd38878a8c05e2ce1ec460de3d1f4b4fc787bf2e66fe15": "yue3dlXsk6AfuqFkkbSrZxE816UmWb_6Rp7aVlHRowY-1742348930-*******-NUcNDWxvEQXn5DxEsR.t0b2HOJgt5VlwgNBMx8T.MPLNwVze_uggwjefgzT6gmxT2TQKqh2tASmnFJ01xm9pcQdKnTm3.PNuZ3XGnMwpD5rIq7zTZXXym1DOuVW6byKyVO6SB1hUEa0C4DQVlm_dSZjqnQsYVueGZuwHWLhSE3OuY63ybNPGyCSZvM91mHYJWKaQSWc.AJAPcT5hcIu4JjXBnAs8q9WMybc0Pqt8C3Z079Kbr8Jcpia._vcJh97uDSb5zyrlzQiw8PMGGGay7lHPgPc5waezgtmeWkOqUHFskVG_T1KpkLm3aRdFNfw4pJ.g6tGYA._csHhlOAxIalbUSQqCIguZy0tox.AhJ1to.xW9W63.akWB3T1W96B.Bhxgl.OLkfJ3kkNgwvGqX2k8BsqMi7L8NtLiWrg1OFl2ySf8C5VROQKI2cjnqMUdTTqEn_ckrCc9C.tK23cEWE4IbhsFlB2xx_JmZRYWSYyGvwH9UFyS0yxmZf4D0GPZTM3ZNnIdhAJAFxijJUx5iviUfNaRowdYqscFtcYl0bTn8zUZ1MCQDMVC0Ptl4g5vg24QvJGPFfkw7IV2w3LRWL_1LcdJHliL4zNFoK7VG_20hPhvCJTAJ_pf.znoA1U3rypDHFbl7S5RYOFhYd6KwFTY5.irnablm65aQULIfGX4q_WxeZFzNsIwvDK3Z.YbDZrBJewfLGSL47P.ZwKbaJsy2MlkmhcPhH7OAHkR0I4GqlQFHWpZQmQr9YdCPgjVYW2QkYthjegeiz4.6nJW9yicMg4VDKvYe0mrnn59mt1hZj7_LmAKwsk6YpetzWutWUhLGxBXVPrGxqARO0OYRjX26wzqJ56e9Hho6cq9Z6tVNrQUMc.77eJhgU3bDa4iz9GCB0RsiwvDcV97Xbg9oD_204YGE7njC7bY_j3Oa1UhMyeVjd24l04qOajsmcLVEgGMVmCV7JYtfOlo1mzTvD6jk913QLv6w6nAO29_x_vbAk88LFzqccBTqCAA0sNfrXbV7vz0R1SrEwS8TNNr0v4QCR7K6zxipz6tuYs6DSbC4nvF.zc4_JO9Jp.nTY8bsikFZGsl1GAsflEet9pRYm8l.8ZIyAunhREXpZp.gJkBvwbj4UkjkylqHoOJE9kIWxIX0kCn0tAqtaE0p.nd5.l25SRt2bBCK78_NEUFSBMLA1QPJSZJ9eBMb5E3LeaksR4hPqW5EwP7Il7Zc3lD6ABD4qwoRbbJmCYYhRjLcxx0Entze66yzJZ1Taf8fr_dnmD91MlXeP5mkA.LRT379y8ND8Xkj7FVeJdUji21U_syqT4Grc79DkpNterGFk3Imjo48Cp4QNfnD_701Y1B53R5fhEg1hPdOYPXOpthh5WlflAqLpkLmd7KqxvgAFnvkrV.TsW3vnW47AsAvcU9tZtSpbPuWYNraMRHa3lyPVq7ibCvb5p55NLmejHpfKkaeB.Y2susKJBYTsSNcYM5Y8MKvQdYgFvdN5X6LK21HklNMeT9AJxenQE0msWIvOLEaaINz6KUa._fnQ3ZuxJTcgrSrgYvPgjByhIw2L7ettZt445Q_Z9CX9ytP9zrfsdc35fWqxmcsBkvw4f5szNIjOF6pkEXV5vC6D_Ytg5YlTBL92laQ6s_iqJakSlnddm7UPDU25qTAfKw9p3EDMZ7YvpHXuRokWLRRzZJuMFFmkyyy5Cw4Z3YDnhifF_zBZxbdmOqA3Exa9YNIePmwJMJYG46pdTrWk08J.YP5McD7sll_er6tjPhS3QJYIsVbv7aVsAB_JwphYcZ9JjW3eVJNXJksWcwewpIi20qy_bah8OZdiVS83AuV8NM0Td17KlnM0MTpj0GN1z76EeVi4huVA31PlwcDSYwAfzIZ5Xq.sKIGAcdB16AUJESWCI1C5FEnSlhONZylgV0BXnMpFyO5sJRuhFol4y556sVTjHidEYXgsEhDZLR1360mb5feAN95ZDgrRsai8y1t7NyWPewAag4ImcOOjxfrc6W_WBkI52EaK42CQ4cgeeH.iRslhDVQi0ZciP9LNG271e38Zz8P6suFq7XpdOYAGovXiyXDPRZ9JmmJNAQqnhd6a5xBRt.87uaIu1hU0.Usgb.rSCUfwTvwsTCXarymlJ7Ep87bdTNhPtXfJw04G52.xE0eFyxcKzW3v.h3pav7wGv30CII8CDw7lB.wu6XB4Gc2uB3CV7Yzb3dpyYxVe.Mlx4yt6oOCB3plYNPC66ZBsay5joD7gnIT7l5YaWlGqLApwjqshVlqloD0bgGSVhG3MhyyCCRYlLbO23pRPMHnuummxLlJiL5PPLlPL68.2x_WrXZflpur0Nu6u1WrBFLHnd0G44D_MzjuvbmrpfkJj4ENX1gJ95Cwj4vTAu2BpXM7UMF5kgNhMRgFZpwuz356xrY6qlxvWRO0wW0Y5VPaKZwaJ8Ux0_BRXSrTOcK_rDyzfXRJsZEt3Pl09DWA.CSjhZ386OA_97DzCsdxqhlIQh2Ok9txhqZythPtfwlWDLrfx41.0lJnwcvpeCOav37q7orW7mA4YG4BzrjQ8vNq.mplUOZ87SKr6N3n_QZok7UdrhzeTlu90MpckTU88KK1j9Pmj_sc4R2zSIXv.FntLh1i9nvRL1011PQKEmTTSljSJL8idljyhyEvMuZLCvoHugaUwfzp3NPld6F4CDcb.J8omKByys6pCcXlLrYAaSdaHeGXU_Qyf2CVjfHfOyWARPheYUOGyd64TPuhw.t41ClHifXFz6ESNwqH5iftEE_NmQE8aYvV0xMEcm4oKWLeVHZQcjzvpooeWuEzgCrPvvSsropIpwShnJxKzWMWpStn7OvVSkc5jAObONRbfo5r96gDB0HdCfV9jkLJTXJ9ZMeIOrreV5xGE6ITk_c1dktxkKWuKx2sM8v8DaMr5HhgPanGFXLlrdThFTGuZCX..wJMporM9Q1wbNm7i70np0tHOYjbjtePpIfDEohu2KIRPoZLnwZTwNRvQ4VwtbqDwXx5jeNOYMqSPOt31hZWia7L5TCrMSZwmN9kJUUZf7heOPomPdowwukr9tejYXwwTW_Kznaiz_HCzE8PVMQ8KDArXH0ZG05zxNlvYI23KF2Tbh7PiHlQ0LntInnnEpNAHnsk9rydG0Sy5gyFRAg0vxqu8q_p.YXbD6xQvK..kKOrvI0Yax1hBagwdALio1vdrkU4r8Td9la2dzgtHB83NG.YvPTKcz3CSoWkn3X2IU0zGXwSIBzzhaSrxjFT3bVsa_icmtFCe25KBOgqmmSPSJbjHy2fWRTVxsBPJWKcdNofTFJqO5zDIl.dYCUHOlc7FYJN5sSS2g0gO2kSCp._iif2Paf7m2KLrD1Yi8mpNW7eMOHuJ2TNwgh4v.dAqmFiZ_QTu1JUfbhUjzl3lGFQo66aSsYneIMFPWYbfXVlw0BKZjZLf5i0A7ueK.H5PN.no39ExtriF3i9GNbkq79W0Iw68A.kujBNpczl1K3P4tUWkut4HJH5aG5_9ZUKueJtGRVLTuum9GCSDhyBzYhwIQg81EwxH9xmy1cZAuuWjjnOMrFnJvHfJbfhKeNRkDQmd7bbhiLoHjBTsaPH_93nMLGYJt0mlhSoV43MdYxlLuieA0ivkwFV_mTzy3g8izi2gU2vy9FmChnj2dy6EvZaIP.LNYPjdxcMXviTLHSkIF4q6.4tTKOv1EMVqeHHZMYrPB4E08oV8FAmad0p4npL4.D.1h3B0Y_J11CYmLNjAEY9.V.Z9v0wMshiEB_3dwdO6MRnsUaRcsOQ_8Qqq0TUBtOKHCoB9XfbN7a7jqaydOhv4hXpVP",
        "d6896b82ccdc447f302c0cf55394eb5eded99a72c0e0d42f347e0cc35f47c12c": "qgsqL.0lZHKosJBXQIp8RESG5PE7sj_FEEjvz.wjn_k-1742348934-*******-mThHi3J6nT3QODpgzWAThK15uaL_VbNrt7mmY4jor_xsJRdZcVkj81EGtzO1mxFZUkqSuapM1sA0EOZQlxANytQhEOHUvR7ZdfUxkXku6u6o5KSaVK7ZdYjQoi4EXytrbjuakgNRePqPVf1288eN5yZX558LUp7LwjH7llVavVU_U7Y.Y7hrW00dRSrc0e8i"
    }
    params = {
        "__cf_chl_tk": "b_7i4sB1ZmzddCzMcH.2aaPTJM9uSNVv0oyODlUJI2U-1742348861-*******-tRUxW1TyRL2tBfYqcMdBdjY36w_wt6GWMEAdJ_zG6wQ"
    }
    try:
        response = requests.post(url, headers=headers, cookies=cookies, params=params, data=data)
        if response.status_code == 200:
            logger.success("请求成功")
            return response.text
        else:
            logger.error(f"请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"请求发生错误: {e}")
        return None

def parse_data(html_content):
    """解析HTML数据"""
    try:
        tree = html.fromstring(html_content)
        
        # 获取所有行
        rows = tree.xpath('//*[@id="bill"]/tbody/tr')
        data_list = []
        
        for row in rows:
            try:
                # 提取每列数据
                jurisdiction = row.xpath('./td[1]/p/text()')
                jurisdiction = jurisdiction[0].strip() if jurisdiction else ""
                
                bill_number = row.xpath('./td[2]/p/a/text()')
                bill_number = ''.join(bill_number).strip()
                
                bill_href = row.xpath('./td[2]/p/a/@href')
                bill_href = bill_href[0] if bill_href else ""

                bill_title = row.xpath('./td[3]/p/text()')
                bill_title = ''.join(bill_title).strip()
                
                bill_status = row.xpath('./td[4]/p/text()')
                bill_status = bill_status[0].strip() if bill_status else ""

                summary = row.xpath('./td[5]//p/text()')
                summary = ''.join(summary).strip() if summary else ""

                
                data_dict = {
                    'Jurisdiction': jurisdiction,
                    'Bill_Number': bill_number,
                    'Bill_Href': bill_href,
                    'Bill_Title': bill_title,
                    'Bill_Status': bill_status,
                    'Summary': summary,
                }
                data_list.append(data_dict)
                logger.info(f"成功提取数据: {jurisdiction} - {bill_number}")
                
            except Exception as e:
                logger.warning(f"提取单行数据时出错: {e}")
                continue
        
        return data_list
    except Exception as e:
        logger.error(f"解析数据时出错: {e}")
        return []

def save_to_csv(data_list, filename="bill_2_data.csv"):
    """保存数据到CSV文件"""
    try:
        df = pd.DataFrame(data_list)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        logger.success(f"成功保存 {len(data_list)} 条数据到 {filename}")
    except Exception as e:
        logger.error(f"保存数据时出错: {e}")

def main():
    """主函数"""
    # 设置日志
    logger.add("spider.log", rotation="500 MB")
    
    # 获取数据
    html_content = fetch_data()
    if not html_content:
        return
    
    # 解析数据
    data_list = parse_data(html_content)
    if not data_list:
        return
    
    # 保存数据
    save_to_csv(data_list)

if __name__ == "__main__":
    main()

'''
币开始到现在有531d的数据
但是只能显示434d的数据
'''