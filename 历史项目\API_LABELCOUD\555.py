import requests
import time

client_key = '86529fa1d01339f34ff2b5f77f5ff0d1622065ed56034'
website_url = 'https://optimistic.etherscan.io/accounts/label/aave'
website_key = '6Le1YycTAAAAAJXqwosyiATvJ6Gs2NLn8VEzTVlS'
# d47307770f3f0a33cb6be66cacb8bd7b
proxy_eth = {
    'http': "socks5://192.168.224.75:30889",
    'https': "socks5://192.168.224.75:30889"
}

def get_task_id(client_key, website_url):
    url = "https://api.yescaptcha.com/createTask"
    headers = {'Content-Type': 'application/json',}
    data = {
        "clientKey": client_key,
        "task":
        {
            "type": "TurnstileTaskProxyless",
            "websiteURL": website_url,
            "websiteKey": "d47307770f3f0a33cb6be66cacb8bd7b",
            "proxy": proxy_eth['http'],
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"
        }
    }
    
    response = requests.post(url, json=data, headers=headers)
    print(response.json())
    if response.status_code == 200:
        response_json = response.json()
        task_id = response_json.get("taskid")
        if task_id:
            print(f"Task ID: {task_id}")
            return task_id
        else:
            print("Task ID not found in the response")
            return None
    else:
        print(f"Error: {response.status_code}")
        return None

def get_task_result(client_key, task_id):
    task_id = task_id
    url = "https://api.yescaptcha.com/getTaskResult"
    headers = {
        'Content-Type': 'application/json',
    }
    data = {
        "clientkey": client_key,
        "taskid": task_id
    }
    
    while True:
        time.sleep(3)
        print('等待获取结果')
        response = requests.post(url, json=data, headers=headers)
        if response.status_code == 200:
            result = response.json()
            for i in range(40):
                if result['errorId'] == 0:
                    if result['status'] == 'ready':
                        print("Task is ready. Solution:", result['solution'])
                        return result
                    elif result['status'] == 'processing':
                        print(f'retrying {i}')
                        time.sleep(3)

                        response = requests.post(url, headers=headers, json=data)
                        result = response.json()
                        continue
                    else:
                        print("Task is not ready yet. Status:", result['status'])
                        return result
                else:
                    print("Error in getting task result:", result['errorDescription'])
                    return result

            print("Max retries reached. Task is still processing.")
            return None

# def main():
#     task_id = get_task_id(client_key, website_url, website_key)
#     if task_id:
#         cookies = get_task_result(client_key, task_id)
#         if cookies:
#             print(f"Successfully obtained cookies: {cookies}")
#         else:
#             print("Failed to get cookies.")
#     else:
#         print("Failed to get task ID.")

if __name__ == "__main__":
    # main()
    get_task_id()
    task_id = get_task_id(client_key, website_url, website_key)
    if task_id:
        cookies = get_task_result(client_key, task_id)
        if cookies:
            print(f"Successfully obtained cookies: {cookies}")
        else:
            print("Failed to get cookies.")
    else:
        print("Failed to get task ID.")
    time.sleep(3)
    get_task_result()

