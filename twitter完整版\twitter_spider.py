import requests
import pandas as pd
import json
import os
import time
import random
from loguru import logger
from datetime import datetime

class XSpider:
    def __init__(self, max_pages=None):
        self.max_pages = max_pages
        self.headers = {
            "authority": "x.com",
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "referer": "",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
            "x-client-transaction-id": "ANYGzRoQ4/6jDlsm3rKsKBcFSeY7zqPy5Nzc3VUyUcpUL9yB61uc/4Vw5EO9om887tqWWwOXhC9+GTWu8/Y/PxlAWMeEAw",
            "x-client-uuid": "809db5e0-ea17-4d61-8eb3-b162d3a607da",
            "x-csrf-token": "a2238fc4a13508a2b50852d4d19f159985aa48a5bca4b14eaaa7c4d5ab67fd2c511b4276f8fd400e4c7a0da317d7157dd7a1530b3b13f9f8091be5c04d3c6fa3bf042a63f04b0c1d8e815dc38b29d93e"
        }
        self.cookies = {
            "guest_id": "v1%3A173674625403880007",
            "auth_token": "a2eb1f5247dbaa8880c5d713328f8935755a30a7",
            "ct0": "a2238fc4a13508a2b50852d4d19f159985aa48a5bca4b14eaaa7c4d5ab67fd2c511b4276f8fd400e4c7a0da317d7157dd7a1530b3b13f9f8091be5c04d3c6fa3bf042a63f04b0c1d8e815dc38b29d93e",
        }

        self.base_params = {
        "variables": "{\"rawQuery\":\"\",\"count\":20,\"querySource\":\"typed_query\",\"product\":\"Top\"}",
        "features": "{\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"responsive_web_graphql_exclude_directive_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"rweb_video_timestamps_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":true,\"responsive_web_enhance_cards_enabled\":false}"
        }

    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间格式到标准格式"""
        try:
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}")
            return None

    def fetch_data(self, keyword, cursor=None):
        """发送请求获取数据"""
        url = "https://x.com/i/api/graphql/QGMTWxm841rbDndB-yQhIw/SearchTimeline"
        variables = {
            "rawQuery": keyword,
            "count": 20,
            "querySource": "typed_query",
            "product": "Top"
        }
        if cursor:
            variables["cursor"] = cursor

        params = self.base_params.copy()
        params["variables"] = json.dumps(variables)
        self.headers["referer"] = f"https://x.com/search?q={keyword}&src=typed_query"

        try:
            response = requests.get(
                url,
                headers=self.headers,
                cookies=self.cookies,
                params=params
            )
            response.raise_for_status()
            logger.success(f"请求成功 [{response.status_code}]")

            # 打印返回的JSON数据
            # logger.info(json.dumps(response.json(), indent=4, ensure_ascii=False))
        
            return response.json()
        except Exception as e:
            logger.error(f"请求失败: {e}")
            return None

    def extract_entries(self, data):
        """从响应数据中提取目标字段"""
        extracted = []
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [])

            for instruction in instructions:
                # entries = []

                if instruction.get('type') == 'TimelineAddEntries':
                    for entry in instruction.get('entries', []):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineItem':
                            item_content = content.get('itemContent', {})
                            tweet_result = item_content.get('tweet_results', {}).get('result', {})
                            user_result = tweet_result.get('core', {}).get('user_results', {}).get('result', {})
                            legacy_user = user_result.get('legacy', {})
                            legacy_tweet = tweet_result.get('legacy', {})

                            # 获取editable_until_msecs并转换为标准时间格式
                            edit_control = tweet_result.get('edit_control', {})
                            editable_until_msecs = edit_control.get('editable_until_msecs')
                            if editable_until_msecs:
                                try:
                                    # 将毫秒时间戳转换为标准时间格式
                                    editable_time = datetime.fromtimestamp(int(editable_until_msecs) / 1000).strftime('%Y-%m-%d %H:%M:%S')
                                except Exception as e:
                                    logger.warning(f"时间转换失败: {e}")
                                    editable_time = None
                            else:
                                editable_time = None

                            # 处理媒体数据
                            media = legacy_tweet.get('entities', {}).get('media', [{}])
                            media_info = media[0] if media else {}

                            extracted.append({
                                'name': legacy_user.get('name'),
                                'rest_id': user_result.get('rest_id'),
                                'is_blue_verified': user_result.get('is_blue_verified', False),
                                'verified_type': user_result.get('verified_type'),
                                'normal_followers_count': legacy_user.get('normal_followers_count', 0),
                                'editable_until': editable_time,  # 替换为editable_until_msecs转换后的时间
                                'description': legacy_user.get('description'),
                                'default_profile_image': legacy_user.get('default_profile_image', False),
                                'followers_count': legacy_user.get('followers_count', 0),
                                'media_url_https': media_info.get('media_url_https')
                            })
            return extracted
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return []

    def get_next_cursor(self, data):
        """更健壮的游标提取方法"""
        try:
            # 深度遍历所有可能的路径
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get('timeline', {}).get('instructions', [])
            
            # 调试日志：打印指令结构
            logger.debug(f"指令数量: {len(instructions)}")
            
            for instruction in instructions:
                # 处理 TimelineAddEntries 和 TimelineReplaceEntry 两种类型
                entries = []
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                elif instruction.get('type') == 'TimelineReplaceEntry':
                    entry = instruction.get('entry')
                    if entry:
                        entries = [entry]
                
                # 调试日志：打印当前处理的条目
                logger.debug(f"处理 {instruction.get('type')} 指令，发现 {len(entries)} 个条目")
                
                for entry in entries:
                    entry_id = entry.get('entryId')
                    # 关键修改：同时检查 cursor-bottom-0 和替换条目
                    if entry_id == 'cursor-bottom-0' or (isinstance(entry_id, str) and 'cursor-bottom' in entry_id):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineCursor':
                            cursor_value = content.get('value')
                            if cursor_value:
                                logger.success(f"成功提取游标: {cursor_value[:30]}...")  # 打印前30字符避免日志过长
                                return cursor_value
            logger.warning("未找到有效游标")
            return None
        except Exception as e:
            logger.error(f"游标解析异常: {str(e)}")
            return None

    def save_to_csv(self, data, filename):
        """保存数据到CSV文件"""
        if not data:
            return

        try:
            df = pd.DataFrame(data)
            df.replace({pd.NA: None}, inplace=True)
            
            # 处理文件存在性
            file_exists = os.path.exists(filename)
            df.to_csv(
                filename,
                mode='a' if file_exists else 'w',
                header=not file_exists,
                index=False,
                encoding='utf-8-sig'
            )
            logger.success(f"成功保存 {len(data)} 条数据到 {filename}")
        except Exception as e:
            logger.error(f"保存失败: {e}")

    def run(self):
        """主运行方法"""
        with open('X/x_keyword/keywords.txt', 'r', encoding='utf-8') as f:
            keywords = [k.strip() for k in f if k.strip()]

        for keyword in keywords:
            logger.info(f"开始处理关键词: {keyword}")
            next_cursor = None
            page = 1
            max_retry = 3

            while True:
                # 添加页数限制检查
                if self.max_pages and page > self.max_pages:
                    logger.info(f"已达到设置的最大页数 {self.max_pages}，停止翻页")
                    break

                logger.info(f"正在爬取第 {page} 页...")
                data = None
                
                # 带重试的请求
                for attempt in range(max_retry):
                    data = self.fetch_data(keyword, next_cursor)
                    if data:
                        break
                    logger.warning(f"第 {attempt+1} 次重试...")
                    time.sleep(5)

                if not data:
                    logger.error("请求失败，停止翻页")
                    break

                # 数据提取和保存
                extracted = self.extract_entries(data)
                if extracted:
                    self.save_to_csv(extracted, f'X/x_output/{keyword}.csv')
                else:
                    logger.warning("未提取到有效数据")
                
                # 获取下一页游标（关键修改点）
                new_cursor = self.get_next_cursor(data)
                
                # 调试日志：打印新旧游标对比
                # logger.debug(f"旧游标: {next_cursor} | 新游标: {new_cursor}")
                
                # if not new_cursor:  # 仅当没有新游标时停止
                #     logger.info("已到达最后一页")
                #     break

                # 更新循环终止条件
                if not new_cursor or (self.max_pages and page >= self.max_pages):
                    logger.info("已到达最后一页或达到设置的最大页数") 
                    break                
                
                # 更新游标并继续
                next_cursor = new_cursor
                page += 1
                
                # 随机等待
                wait_time = random.uniform(5, 7)
                logger.info(f"等待 {wait_time:.1f} 秒后继续")
                time.sleep(wait_time)

            logger.success(f"完成关键词 {keyword} 的采集")

if __name__ == "__main__":
    spider = XSpider(max_pages=5)
    spider.run()
    
